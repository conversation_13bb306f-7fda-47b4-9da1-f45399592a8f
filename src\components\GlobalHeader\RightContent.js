import React, { PureComponent } from 'react';
import { FormattedMessage, formatMessage } from 'umi/locale';
import { Tag, Menu, Icon, Dropdown, Avatar, Card, Tooltip, Row } from 'antd';
import moment from 'moment';
import groupBy from 'lodash/groupBy';
import PictUrl from '@/assets/user/user.png';
import Link from 'umi/link';
import styles from './index.less';
import router from 'umi/router';

import UpdateInformation from '@/pages/UpdateInformation/Information.js' // 系统更新弹窗


export default class GlobalHeaderRight extends PureComponent {

  state = {
    IsSystem: false
  }

  getNoticeData() {
    const { notices = [] } = this.props;
    if (notices.length === 0) {
      return {};
    }
    const newNotices = notices.map(notice => {
      const newNotice = { ...notice };
      if (newNotice.datetime) {
        newNotice.datetime = moment(notice.datetime).fromNow();
      }
      if (newNotice.id) {
        newNotice.key = newNotice.id;
      }
      if (newNotice.extra && newNotice.status) {
        const color = {
          todo: '',
          processing: 'blue',
          urgent: 'red',
          doing: 'gold',
        }[newNotice.status];
        newNotice.extra = (
          <Tag color={color} style={{ marginRight: 0 }}>
            {newNotice.extra}
          </Tag>
        );
      }
      return newNotice;
    });
    return groupBy(newNotices, 'type');
  }

  getUnreadData = noticeData => {
    const unreadMsg = {};
    Object.entries(noticeData).forEach(([key, value]) => {
      if (!unreadMsg[key]) {
        unreadMsg[key] = 0;
      }
      if (Array.isArray(value)) {
        unreadMsg[key] = value.filter(item => !item.read).length;
      }
    });
    return unreadMsg;
  };

  // 跳转到系统更新页面
  onJumpUpdata = () => {
    console.log(this.props)
    const { location } = this.props;
    if (location.pathname != '/updataInformation/detail') {
      router.push('/updataInformation/detail');
    }

  }
  // 系统帮助 弹窗
  SystemClick = () => {
    this.setState({
      IsSystem: true
    }, () => {
      this.UpdateInformation && this.UpdateInformation.getSystemList();
    })
  }
  // 关闭系统帮助弹窗
  SystemHelpModalClose = () => {
    this.setState({
      IsSystem: false
    })
  }
  render() {
    const { IsSystem } = this.state;
    const {
      currentUser,
      fetchingNotices,
      onNoticeVisibleChange,
      onMenuClick,
      onNoticeClear,
      theme,
      visibleType,
    } = this.props;
    const isResources = localStorage.getItem('isResources');  // 是否是资源
    const menu = (
      <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick}>
        <Menu.Item key="userCenter">
          <Link to="/account/center">
            <Icon type="user" />
            个人中心
          </Link>
          <i className={styles.AccountArrow}></i>
        </Menu.Item>
        {isResources == 1 ? (<Menu.Item key="customer">
          <Link to="/account/customer">
            <Icon type="setting" />
            其他设置
          </Link>
        </Menu.Item>) : null}
        <Menu.Divider />
        <Menu.Item key="logout">
          <Icon type="logout" />
          <FormattedMessage id="menu.account.logout" defaultMessage="退出登录" />
        </Menu.Item>

      </Menu>
    );
    const noticeData = this.getNoticeData();
    const unreadMsg = this.getUnreadData(noticeData);
    let className = styles.right;
    if (theme === 'dark') {
      className = `${styles.right}  ${styles.dark}`;
    }
    const userName = localStorage.getItem('userName'); //用户名
    const role = JSON.parse(localStorage.getItem('role')); // 角色
    const useAvatar = localStorage.getItem("avatar"); //用户头像


    return (

      <div className={className} id="Scroll">
        <Row
          type='flex' justify='center' align='middle'
          style={{ cursor: 'pointer', marginRight: 16 }}
          onClick={this.onJumpUpdata}
        >
          <Tooltip
            placement="bottom"
            title={'系统更新说明'}
            overlayClassName={styles.tooltipClass}
          >
            <Icon
              type="info-circle"
              style={{ fontSize: '16px', marginRight: 8, color: '#515A6E' }}

            />

          </Tooltip>
          <span>更新日志</span>
        </Row>
        <Row
          type='flex' justify='center' align='middle'
          style={{ cursor: 'pointer', marginRight: 16 }}
          onClick={this.SystemClick}
        >
          <Tooltip
            placement="bottom"
            title={'系统帮助'}
            overlayClassName={styles.tooltipClass}
          >
            <Icon
              type="question-circle"
              style={{ fontSize: '16px', marginRight: 8, color: '#515A6E' }}

            />
          </Tooltip>
          <span>
            帮助手册
          </span>
        </Row>
        {useAvatar ? (
          <Dropdown overlay={menu}
            getPopupContainer={() => document.getElementById('Scroll')}
          >
            <span className={`${styles.action} ${styles.account}`}>
              <Avatar
                size="small"
                className={styles.avatar}
                src={`${useAvatar}`}
                alt="avatar"
              />
            </span>
          </Dropdown>
        ) : (
          <Dropdown overlay={menu}
            getPopupContainer={() => document.getElementById('Scroll')}
          >
            <span className={`${styles.action} ${styles.account}`}>
              <Avatar
                size="small"
                className={styles.avatar}
                src={PictUrl}
                alt="avatar"
              />
            </span>
          </Dropdown>
        )}

        <span style={{ marginRight: 8, fontSize: 14, color: '#333' }}>{userName}</span>
        {role && role.map(item => (
          <span key={item.roleName} style={{ marginRight: 10, fontSize: 14, color: '#999' }}>{item.roleName}</span>
        ))}
        <UpdateInformation
          onEvent={(that) => { this.UpdateInformation = that; }}
          {...this.props}
          IsSystem={IsSystem}
          SystemHelpModalClose={this.SystemHelpModalClose}
        />
      </div>
    );
  }
}
