/**
 * request 网络请求工具
 * 更详细的 api 文档: https://github.com/umijs/umi-request
 */
import { extend } from 'umi-request';
import { notification } from 'antd';
import Base64 from 'base-64';
import { stringify, parse } from 'querystring';

const codeMessage = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

// 格式化url请求参数
const renderUrl = (url, obj) => {
  let newUrl = url;
  if (url.split('?')[1]) {
    // 存在参数
    let urlParam = parse(url.split('?')[1]);
    newUrl = `${url.split('?')[0]}?${stringify({ ...urlParam, ...obj })}`;
  } else {
    //不存在参数
    newUrl = url.indexOf('?') != -1 ? `${url}${stringify(obj)}` : `${url}?${stringify(obj)}`;
  }
  return newUrl;
};

/**
 * 异常处理程序
 */

const errorHandler = (error) => {
  const { response } = error;
  console.log('response :: ', error);
  if (response && response.status) {
    const errorText = codeMessage[response.status] || response.statusText;
    const { status, url } = response;
    notification.error({
      message: `请求错误 ${status}: ${url}`,
      description: errorText,
    });
  } else if (!response) {
    notification.error({
      description: '您的网络发生异常，无法连接服务器',
      message: '网络异常',
    });
  }

  return response;
};
/**
 * 配置request请求时的默认参数
 */

const request = extend({
  errorHandler,
  // 默认错误处理
  credentials: 'include', // 默认请求是否带上cookie
});
// request拦截器, 改变url 或 options.
request.interceptors.request.use(async (url, options) => {
  let newUrl = url;
  console.log('newUrl123123 :: ', newUrl, options);
  newUrl = renderUrl(url, {
    tenantId: localStorage.getItem('tenantId'),
    userId: localStorage.getItem('userId'),
    organizationId: localStorage.getItem('organizationId'),
    userName: localStorage.getItem('userName'),
  });

  // if(url.indexOf('?')===-1){
  //   newUrl = newUrl+'?tenantId='+localStorage.getItem("tenantId");
  // }else{
  //   if(newUrl.indexOf("tenantId")===-1&&!options.params.tenantId){
  //     newUrl = newUrl+'&tenantId='+localStorage.getItem("tenantId");
  //   }
  // }
  // if(newUrl.indexOf('?')===-1){
  //   newUrl = newUrl+'?userId='+localStorage.getItem("userId");
  // }else{
  //   if(newUrl.indexOf("userId")===-1&&!options.params.userId){
  //     newUrl = newUrl+'&userId='+localStorage.getItem("userId");
  //   }
  // }
  // if(newUrl.indexOf('?')===-1){
  //   newUrl = newUrl+'?userName='+localStorage.getItem("userName");
  // }else{
  //   if(newUrl.indexOf("userName")===-1&&!options.params.userName){
  //     newUrl = newUrl+'&userName='+localStorage.getItem("userName");
  //   }
  // }
  //
  // if(newUrl.indexOf('?')===-1){
  //   newUrl = newUrl+'?organizationId='+localStorage.getItem('organizationId')
  // }else{
  //   if(newUrl.indexOf("organizationId")===-1&&!options.params.organizationId){
  //     newUrl = newUrl+'&organizationId='+localStorage.getItem('organizationId')
  //   }
  // }

  console.log('newUrlnewUrl :: ', newUrl);

  let access_token = localStorage.getItem('access_token');
  let username = localStorage.getItem('username');
  if (access_token) {
    let headers = {
      username: username,
      client: 'PC',
      access_token: access_token,
    };
    if (!!options.headers) {
      headers = {
        username: username,
        client: 'PC',
        access_token: access_token,
        ...options.headers,
      };
    }
    return {
      url: newUrl,
      options: {
        ...options,
        headers: headers,
      },
    };
  } else {
    let headers = {
      username: username,
      client: 'PC',
      access_token: access_token,
    };
    if (!!options.headers) {
      headers = {
        username: username,
        client: 'PC',
        access_token: access_token,
        ...options.headers,
      };
    }

    return {
      url: newUrl,
      options: {
        ...options,
        headers: headers,
      },
    };
  }
});

// response拦截器, 处理response
request.interceptors.response.use((response, options) => {
  let access_token = localStorage.getItem('access_token');
  if (access_token) {
    localStorage.setItem('access_token', access_token);
  }
  return response;
});
export default request;
