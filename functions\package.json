{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "npm run mock && firebase serve --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "mock": "node ../scripts/generateMock.js"}, "dependencies": {"@babel/runtime": "^7.0.0", "body-parser": "^1.18.3", "express": "^4.16.3", "firebase-admin": "^5.12.1", "firebase-functions": "^2.0.5", "mockjs": "^1.0.1-beta3", "moment": "^2.22.2", "path-to-regexp": "^2.2.1"}, "private": true}