.PopoverCalendarItem {
  text-align: center;
  width: 28px;
  height: 28px;
  margin-left: 5px;
  line-height: 28px;
  border-radius: 28px;
  color: #444444;
  cursor: pointer;
}

.PopoverContentWrap {
 width: 348px;
}

.PopoverCalendarItemSelected {
  text-align: center;
  width: 28px;
  height: 28px;
  margin-left: 5px;
  line-height: 28px;
  border-radius: 28px;
  color: #fff;
  background: #4ca2ff;
  cursor: pointer;
}

.PopoverCalendarItemCurrent {
  //border-radius: 28px;
  border:1px solid red;
}

.PopoverOtherItem {
  text-align: center;
  width: 28px;
  height: 28px;
  margin-left: 5px;
  line-height: 28px;
  border-radius: 28px;
  color: #c5c8ce;
  cursor: not-allowed;
}


.PopoverFullItem {
  text-align: center;
  width: 28px;
  height: 28px;
  margin-left: 5px;
  line-height: 28px;
  border-radius: 28px;
  //color: #c5c8ce;
  color: #444444;
  cursor: pointer;
}


.PropverOtherItemIcon {
  font-size: 12px;
  position: absolute;
  right: -3px;
  top: 0px;
  cursor: not-allowed;
}

.PropverUnscheduledItemIcon {
  font-size: 12px;
  -webkit-transform: scale(0.7);
  margin: 0;
  padding: 0;
  position: absolute;
  right: 1px;
  color: #ee6666;
  bottom: -6px;
  cursor: not-allowed;
}

.PropverOtherRedItemIcon {
  font-size: 12px;
  position: absolute;
  right: -3px;
  top: 0px;
  color: #ee6666;
}

.selectCalendarPopover {
  :global {
    .ant-fullcalendar-header .ant-select:first-child{
      margin-right: 10px;
    }
    .ant-select-sm .ant-select-selection--single{
      width: 100px;
    }
  }
}
.PopoverCalendar{
  :global{
    .ant-popover-content .ant-popover-inner-content {
      padding: 0px;
    }
    .ant-fullcalendar-header .ant-radio-group{
      display: none;
    }
  }
}


.reset {
  position: absolute;
  top: 22px;
  left: 15px;
  cursor: pointer;
  //display: block;
  z-index: 10;
}

.PopoverCard {
  left: 0px !important;
}




