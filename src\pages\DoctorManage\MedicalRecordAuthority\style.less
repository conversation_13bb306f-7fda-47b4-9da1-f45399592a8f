@import '~antd/lib/style/themes/default.less';

.imageMagecontent {
  padding: 16px;

  .searchBtn {
    display: flex;
    justify-content: space-between;
  }

  :global {
    .ant-form-item {
      margin-bottom: 0;
    }
  }
}
.txt11 {
  display: inline-block;
  height: 25px;
  padding-right: 10px;
  color: rgba(66, 146, 255, 1);
  font-size: 14px;
  font-family: PingFang SC;
  line-height: 25px;
  white-space: nowrap;
  text-align: right;
  overflow-wrap: break-word;
  cursor: pointer;
}
.displayFlex {
  display: flex;
  justify-content: space-between;
  font-family: PingFang SC;
}

.col8 {
  // height: 24px;
  margin-bottom: 0;
}
.padding20 {
  padding: 0 40px;
}
.AllchooseLine {
  position: relative;
  display: flex;
  margin-bottom: 9px;
}
.operation {
  position: absolute;
  right: -52px;
  display: flex;
}

.searchNameDiv {
  margin-right: 5px;
  margin-bottom: 10px;
}
.searchName {
  padding: 4px 5px;
  color: rgb(160, 159, 162);
  border: 1px solid rgb(217, 217, 217);
  cursor: pointer;
  &:hover {
    color: #40a9ff;
    border: 1px solid #40a9ff;
  }
}
