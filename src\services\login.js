import request from '@/utils/request';

// 登录获取token
export async function getToken(params) {
  return request(
    '/api/emr/emrlogin/businessusers/oauth/token?tenantId=ba67e6cf30dc4f9c9c46adef188bbd04',
    {
      method: 'POST',
      data: params,
    },
  );
}

// ① 根据机构号获取TenantId
export async function getTenantId(params) {
  return request(
    `/Platform/auditing/tenant/get-tenant-id-by-tenant-code?tenantCode=${params.TenantCode}`,
  );
}

// ② 获取token
export async function getTokenC(params) {
  return request(
    `/api/businessusers/oauth/token?tenantId=${params.get('username').split('/')[1]}`,
    {
      method: 'POST',
      body: params,
      headers: {
        Authorization: 'Basic UEM6YXJyYWls',
      },
    },
  );
}

// ③ 保存save-login-info
export async function UploadInfoC(params) {
  return request(
    `/api/businessusers/userLogin/save-login-info?k=${params.UidKey}&tenantId=${params.FormData.tenantId}`,
    {
      method: 'POST',
      data: params.FormData,
    },
  );
}

// ④ 获取登录存的个人信息
export async function getLoginInfo(params) {
  return request(`/api/businessusers/userLogin/get-login-info?k=${params}`);
}

// ⑤ 确认是否去完善信息页面
export async function checkInfoAllReady() {
  return request('/api/businessusers/userLogin/check-user-info-is-complete');
}

// ⑥ 登录 获取账户信息
export async function fakeAccountLogin(params) {
  return request('/api/businessusers/userLogin/login', {
    method: 'POST',
    data: params,
  });
}

// export async function fakeAccountLogin(params) {
//   return request('/api/emr/emrlogin/businessusers/userLogin/login?tenantId=ba67e6cf30dc4f9c9c46adef188bbd04', {
//     method: 'POST',
//     data: params,
//   });
// }
//
export async function getFakeCaptcha(mobile) {
  return request(`/api/login/captcha?mobile=${mobile}`);
}
