import {uploadFile,} from '@/services/public';//引入图片上传，SOP步骤，晨会，辅助检查等相关接口
import {notification} from "antd";
const PublicModel = {
  namespace: 'Public',
  state: {
    returnMsg:{},//oss 文件上传

  },
  //异步
  effects: {

 /**oss 文件上传
     *
     * **/
    *uploadFileService({payload, callback} , { call, put }) {

      const response = yield call(uploadFile ,payload);
      yield put({
        type: 'uploadFileInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }

    },
  },
  reducers:{
      uploadFileInfo(state, action) {
      return {
        ...state,
        returnMsg: action.payload || {},
      };
    },
  }
}
export default PublicModel;
