import React, {Component} from 'react';
import {Card,Radio} from 'antd';
import ReactDOM from 'react-dom';
import $ from 'jquery';
import moment from 'moment';
import 'fullcalendar/dist/locale/zh-cn';
import 'fullcalendar';
//import 'fullcalendar-scheduler';
/*import 'fullcalendar/dist/fullcalendar.css';*/
import PropTypes from 'prop-types';
import styles from './DoctorAgendaCalendar.less'

import {
  getEventElement,
  newCustomerEventType,
  oldCustomerEventType,
  otherEventType,
  backgroundType,
  LarageHeight,
  MediumHeight,
  SmallHeight,
  tittleHight,
  snapDuration,
  schedulerLicenseKey,
  slotLabelFormat,
  timeFormat,
  eventPatientNameEllipsis // 患者名称添加省略号
} from '@/utils/CalendarUtils'
import commonStyles from '../AgendaCaleander.less'
import classNames from 'classnames';
const rowTotal = 7              // 列表项总列数
const lableClickClassName = 'labelTh' // 表头时间行点击className
const workStatusForDayOff =  2 // 休息日
const workStatusForOther  =  3 // 走诊医生
const workStatusForNot    =  4 // 未排班医生

/**
 * 时间制度 改约控件
 * 非医生使用
 */
export default class DoctorAgendaCalendar extends Component {
  static propTypes = {
    onRemoveEvent:PropTypes.func,     // 点击删除事件方法
    onEventClick:PropTypes.func,      // 点击Event预约事件的回调方法
    onTimeBlankClick:PropTypes.func,  // 点击空白区域回调事件的方法
    goNextIcon:PropTypes.func,        // 点击下一页的回调方法
    goLastIcon:PropTypes.func,        // 点击上一页的回调方法
    calendarId:PropTypes.string,      // 当前预约组件绑定的ID名称
    Option:PropTypes.object,          // 需要扩展的预约组件配置字段
    calendarTableList:PropTypes.array, // 预约组件表格表头格式
    changeAgendaList:PropTypes.array,  // 预约组件 改约列表头格式
    appointmentPageInfo:PropTypes.object, // 预约头列表分页信息
    EventList:PropTypes.array,         // 预约组件预约事件集合
    onRef:PropTypes.func,
    onClickDateByHead:PropTypes.func   // 点击时间头 获取会诊时间
  };
  static defaultProps = {
    onRemoveEvent:()=>{},
    onEventClick:()=>{},
    onTimeBlankClick:()=>{},
    goNextIcon:()=>{},
    goLastIcon:()=>{},
    calendarId:'calendar',
    Option:{},
    calendarTableList:[],
    changeAgendaList:[],
    EventList:[],
    appointmentPageInfo:{},
    onRef:()=>{},
    onClickDateByHead:()=>{} // 点击时间头获取时间
  };

  /**
   * 删除Event事件
   * @param eventId
   */
  onRemoveEvent=(eventId)=>{
    this.props.onRemoveEvent(eventId)
  }
  /**
   * 点击Event事件
   * @param calEvent
   */
  onEventClick=(calEvent)=>{
    this.props.onEventClick(calEvent);
  }
  /**
   * 点击空白事件
   * @param date
   */
  onTimeBlankClick=(date)=>{
    this.props.onTimeBlankClick(date.format())

  }

  /**
   * 点击下一页
   */
  goNextIcon=()=>{
    let { current,pageSize,total,pageCount} = this.props.appointmentPageInfo;
    current = current || 1
    pageSize = pageSize || 0
    total = total || 0
    pageCount = pageCount || 0
    let nextPage = current >= pageCount ? pageCount : ++current
    this.props.goNextIcon(nextPage);
  }

  /**
   * 点击上一页
   */
  goLastIcon=()=>{
    let { current,pageSize,total,pageCount} = this.props.appointmentPageInfo;
    current = current || 1
    pageSize = pageSize || 0
    total = total || 0
    pageCount = pageCount || 0
    let lastPage = current <= 1 ? 1 : --current
    this.props.goLastIcon(lastPage);
  }

  /**
   * 回调函数 当事件开始拖动触发
   * @param props
   */
  eventDragStart=(event, jsEvent, ui, view)=>{
    event.draggable({
      zIndex: 999,
      revert: true,      // will cause the event to go back to its
      revertDuration: 0  //  original position after the drag
    });
  }


  constructor(props) {
    super(props);
    this.state = {
      commonOption:{  // calendar默认格式
        schedulerLicenseKey: schedulerLicenseKey,
        slotLabelFormat:slotLabelFormat,
        height: 'auto',
        defaultView: 'agenda',
        groupByResource: true,
        snapDuration:snapDuration,
        allDaySlot: false,       // 确定日历顶部是否显示“全天”广告位。
        slotEventOverlap: false, // 是否可以叠加事件
        header:false,
        ...props.Option,
        //timezone:'local',
        timeFormat:'HH:mm',
      },
      calendarTableList:[...props.calendarTableList],
      changeAgendaList:[...props.changeAgendaList],
      EventList:[...props.EventList]
    };
  }

  componentWillReceiveProps(nextProps){
    this.setState({
      commonOption:{
        ...this.state.commonOption,
        ...nextProps.Option
      },
      calendarTableList:[...nextProps.calendarTableList],
      changeAgendaList:[...nextProps.changeAgendaList],
      EventList:[...nextProps.EventList]
    },()=>{
      $(`#${this.props.calendarId}`).fullCalendar( 'destroy' ); // 初始化组件
      this.refreshBespeakComponent();
    })
  }



  /**
   * 获取resources列表项目数据
   * @returns {Array}
   */
  getResources=()=>{
    let calendarTableListByState = this.state.calendarTableList
    let changeAgendaList = this.state.changeAgendaList
    // 判定出是否是第一值
    let calendarTableList = [...changeAgendaList,...calendarTableListByState];
    let resources = [];                         // 椅位资源列
    let groupTotal = calendarTableList.length   // 医生总数
    calendarTableList.forEach((val,idx)=>{
      if(val.resources){
        //val.resources.length;
        val.resources.forEach((res,resIdx)=>{
            const { staffStatus, type, workStatus, workTimeEnd, workTimeStart, date, doctorId, groupId, name, organizationName,
            } = val || {}
            /*res.first = val.first,
            res.last = val.last,*/
            res.groupId = val.groupId;
            res.groupName = val.name;
            res.type = val.type;  //当前列表预约代表类型 1当前预约 2改约
            res.date = moment(val.date, "YYYY-MM-DD").format('YYYY-MM-DD');
            res.workTimeEnd = workTimeEnd;
            res.workTimeStart = workTimeStart;
            res.workStatus = workStatus;
            res.doctorId = doctorId;
            res.staffStatus = staffStatus;
            res.organizationName = organizationName;
            resources.push(res);
        })
      }
    })

    let newResources  = resources.map((val,idx)=>{
      if (val.type == 2) {
        if (idx == 1) {
          return {...val, first: true}
        }
        if (idx == resources.length - 1) {
          return {...val, last: true}
        }
        if(idx != 1 || idx != resources.length - 1){
          return val
        }
      }else {
        return val
      }
    })

    return newResources;
  }

  /**
   * 休息日 走诊 event事件添加
   */
  setDayOffEvent=(list)=>{
    let eventListPlue = []
    list.forEach((res)=>{
      let { workStatus, workTimeEnd, workTimeStart, staffStatus, type, date, doctorId, groupId, name, organizationName, id, chairNum, title } = res || {}
      if (
        res.workStatus == workStatusForDayOff ||
        res.workStatus == workStatusForOther ||
        res.workStatus == workStatusForNot
      ){
        eventListPlue.push({
          resourceId:res.id,
          start: '00:00:00.008',
          end: '24:00:00.008',
          backgroundColor: "#FD9E24",
          rendering: "background",
          titleInfo: "休息",
          type: 12,
          workStatus,
          color: null,
          id: `dayOff${res.id}`
        })
      }
    })
    return eventListPlue
  }

  /**
   * 获取除当前预约以外的改约列表数量
   */
  getCalendarTableListNumber=()=>{
    let calendarTableListByState = this.state.calendarTableList;
    let total = 0
    calendarTableListByState.map((res,idx)=>{
      total += res.resources.length
    })
    return total
  }

  /**
   * 渲染完成后添加
   * 根据医生数量动态 添加医生椅位头部结构
   */
  PrependTrForHeader=()=>{
    let calendarTableListByState = this.state.calendarTableList;
    let changeAgendaList = this.state.changeAgendaList
    let calendarTableList = [...changeAgendaList,...calendarTableListByState];
    let trforHeaderList = [...changeAgendaList]
    if (calendarTableListByState.length > 0) {
      trforHeaderList.push({type:'2'})
    }
    // 根据传入的calendarTableListByState改约列表 和 changeAgendaList当前预约列表
    // 拼接头部时间+椅位结构
    let thDom = calendarTableList.map((res,idx)=>{  // 头部dom结构处理
      let conSpanNum = res.resources.length;
      console.log('res :: ',res);
      let workStatusText = res.workStatus ==  workStatusForDayOff ?  '(休息)' : ''
      workStatusText = res.workStatus ==  workStatusForOther?  `(${res.organizationName})` : workStatusText
      workStatusText = res.workStatus ==  workStatusForNot ?  `(未排班)` : workStatusText
      let workStatusTextWarp = workStatusText != '' ? `<p date="${titleDate}" class="labelTdsTitle">${workStatusText}</p>` : '';

      let titleDate = moment(res.date, "YYYY-MM-DD").format('YYYY-MM-DD')
      let titleWeek = moment(res.date, "YYYY-MM-DD").format('dddd')
      let lableHtml = `<div class="lableDateWeek" date="${titleDate}">
           <p date="${titleDate}" class="labelTdsTitle">${titleDate}</p>
           <p date="${titleDate}" class="labelTdsTitle">${titleWeek}</p>
           ${workStatusTextWarp}
        </div>`
      if(res.type == 1) {
        return (`<th id="CurrentThDom" date="${titleDate}" class="${lableClickClassName}" colspan='${conSpanNum}'>${lableHtml}</th>`)
      }else {
        return (`<th class="${lableClickClassName}" date="${titleDate}" colspan='${conSpanNum}'>${lableHtml}</th>`)
      }
    })

    let tableListConSpanNum = this.getCalendarTableListNumber();
    // 拼接头部现有预约和改约结构逻辑
    let TitleThDom = trforHeaderList.map((res,idx)=>{  // 头部dom结构处理
      if(res.type == 1) {
        if (this.props.dcotorNameThisAppointment) {
          return (`<th class="CurrentThDom" colspan='${1}'>现有预约(${this.props.dcotorNameThisAppointment})</th>`)
        }else {
          return (`<th class="CurrentThDom" colspan='${1}'>现有预约</th>`)
        }
      }else {
        return (`<th colspan='${tableListConSpanNum}'>改约</th>`)
      }
    })


    //渲染完成后添加 医生单位结构
    const fcAxisDom = $(`#${this.props.calendarId}`).find('.fc-head-container table thead .fc-axis');
    const fcAxisWidth = fcAxisDom.css('width');

    // 表头行 区分现有预约和预约
    const titleTr = `
      <tr style='width: ${fcAxisWidth}'>
        <th class="DoctorIconTr"  style='width: ${fcAxisWidth}'></th>
        ${TitleThDom}
      </tr>
    `

    // 时间行
    const prependTr = `
      <tr style='width: ${fcAxisWidth}'>
        <th style='width: ${fcAxisWidth}'>时间</th>
        ${thDom}
      </tr>
    `;

    //fcAxisDom.text('时间').css({textAlign:'center'});
    let calendarThead = $(`#${this.props.calendarId}`).find('.fc-head-container table thead')
    calendarThead.prepend(prependTr);
    calendarThead.prepend(titleTr);
  }

  // 添加 切换椅位Icon
  PrependSwitchForHeader=()=>{
    const fcAxisDom = $(`#${this.props.calendarId}`).find('.fc-head-container table thead');
    fcAxisDom.find('.fc-resource-cell').append(`<span></span>`)  // 添加空结构 触发页面重绘
    this.bindClickForCalendar();
  }

  // 绑定onClick点击事件
  bindClickForCalendar=()=>{
    const _this = this;
    // 删除事件
    $('.removeIcon').click(function (e) {
      e.stopPropagation()
      let eventId = $(this).attr('eventId');
      let taskId = $(this).attr('taskId')
      _this.onRemoveEvent(taskId)
    })
    //点击下一页事件
    $('.fc-resource-cell #goNextIcon').click(function (e) {
      e.stopPropagation()
      _this.goNextIcon()
    })
    //点击上一页事件
    $('.fc-resource-cell #goLastIcon').click(function (e) {
      e.stopPropagation()
      _this.goLastIcon()
    })
  }
  filterEventList=(eventList)=>{
    eventList = eventList.filter((val)=>{
      if(val.type != 6){
        return val
      }
    })
    return eventList
  }

  // 刷新预约组件
  refreshBespeakComponent=()=>{
    const _this = this;
    //处理resources列表项目数据
    const resources = this.getResources();
    let list = this.setDayOffEvent(resources)

    let EventList = [...this.state.EventList,...list]
    if (localStorage.getItem('doctorIdentification') == 1) {
      EventList = this.filterEventList(this.state.EventList)
    }
    const calendarOption1 = {
      resources: [...resources],
      events: [...EventList],
      // 事件显示之前回调方法
      eventRender: function(event, element) {
        //根据height 动态切换event 中的内容
        //1:老患者预约   2:新患者预约  3:会诊   4:个人时间占用  5:已到诊  6:上班时间
        //根据height 动态切换event 中的内容
        let eventElement = getEventElement(element.height(),event);
        let taskId = event.appointmentInfoDto && event.appointmentInfoDto.taskId;
        if(event.type != 12) {
          element.html(eventElement)
        }

        if (event.appointmentInfoDto) {
          // type 1:老患者预约   2:新患者预约  3:会诊   4:个人时间占用  5:已到诊  6:上班时间
          if (event.operationType != 0 && (event.type == 1 ||  event.type == 2)) {
            let removeElement = `<i class='removeIcon' eventId='${event.id}' taskId='${taskId}'/>`
            element.append(removeElement)
          }
        }
        //element.css({border:'1px solid #ccd'})
        if(event.operationType == 0 || (event.type != 1 &&  event.type != 2 && event.type != 6)){
          element.css({cursor:'no-drop'})
        }
      },
      // 事件显示之后回调方法
      eventAfterRender:function(event, element,view) {
        eventPatientNameEllipsis()
      },
      // 事件被删除取消的回调方法
      // eventDestroy:function( event, element, view ) {}
      // 当点击日历中某个事件的时候触发回调
      eventClick: function(calEvent, jsEvent, view){
        //_this.onEventClick(calEvent);
      },
      // 当点击日历上面的某一天的时候触发
      dayClick: function (date, jsEvent, view,resourceObj){
        _this.onTimeBlankClick(date);
      },

      //可拖动项目
      eventDragStart: function (event, jsEvent, ui, view) {
        //ui.draggable();
        /*view.data('event', {title: '111'})
        view.draggable({
          revert: true,
          /!*revertDuration: 0*!/
        })*/
      },

      windowResize: function(view){
        eventPatientNameEllipsis()
      },

      //用于操作资源DOM的钩子
      resourceRender: function(resourceObj, labelTds, bodyTds) {
        let lableHtml = `<div class="titleLableWrap">
           <p class="titleLable">${resourceObj.title}</p>
        </div>`

        if (resourceObj && resourceObj.type == 2) {
          let { current,pageSize,total,pageCount} = _this.props.appointmentPageInfo || {}
          current = current || 1
          pageCount = pageCount || 0

          if (resourceObj.first) {
            let goPageIcon = current > 1 ?  '<i id="goLastIcon" class="goLastIcon"/>' : '<i id="goDisableLastIcon" class="goDisableLastIcon"/>'
            //if(current > 1 ) {
              lableHtml = `<div class="titleLableWrap">
                         ${goPageIcon}
                         <p class="titleLable">${resourceObj.title}</p>
                       </div>`
            //}
          }


          if (resourceObj.last) {
            //if(current < pageCount) {
              let goPageIcon = current < pageCount ? '<i id="goNextIcon" class="goNextIcon"/>' : '<i id="goDisableNextIcon" class="goDisableNextIcon"/>'
                lableHtml = `<div class="titleLableWrap">
                         <p class="titleLable">${resourceObj.title}</p>
                         ${goPageIcon}
                </div>`
            //}
          }
        }

       /* let lableHtml = `<div>

        </div>`*/

        labelTds.html(lableHtml)
        if(resourceObj.type == 1){ // 判断当前预约列类型 现有预约 改约
          labelTds.css('background','rgba(163, 213, 195, 0.3)')
        }
      },

      //确定是否允许拖拽和调整大小的事件可以互相重叠
      eventOverlap:function(stillEvent, movingEvent){
        return stillEvent.type != 12
      },
      //渲染单元格钩子
      dayRender:function (date,cell) {
      },
      //事件拖动停止时触发。
      eventDragStop:function(event,jsEvent,ui,view,resourceObj){
      },
      eventDrop:function(event,delta,revertFunc,jsEvent,ui,view){
        _this.props.eventDrop && _this.props.eventDrop(event,delta)
      },
      eventResizeStart:function(event,delta,revertFunc,jsEvent,ui,view){
      },
      eventResizeStop:function(event, jsEvent, ui, view){
      },
      eventResize:function(event, jsEvent, ui, view){
      }
    };
    const option = {
      ...this.state.commonOption,
      ...calendarOption1
    };
    $(`#${this.props.calendarId}`).fullCalendar( 'destroy' ); // 初始化组件
    $(`#${this.props.calendarId}`).fullCalendar(option);
    this.PrependTrForHeader();
    this.PrependSwitchForHeader();
  };



  componentDidMount() {
    this.props.onRef && this.props.onRef(this);
    const _this = this;
    this.refreshBespeakComponent();
    $(`#${this.props.calendarId}`).on("click",`th.${lableClickClassName}`,function(event){
      var target = $(event.target);
      //获取当前DOM的元素
      if(target.attr('class') != lableClickClassName){
        target = target.parents(`th.${lableClickClassName}`);
      }
      _this.props.onClickDateByHead(target.attr('date'))
      event.stopPropagation();
    })
  }

  componentWillUnmount(){
    $(`#${this.props.calendarId}`).fullCalendar( 'destroy' ); // 销毁组件
    $(`#${this.props.calendarId}`).off("click",`th.${lableClickClassName}`); // 解除事件绑定
    this.setState = (state,callback)=>{return;};
  }



  render(){
    return (
      <div className={classNames(commonStyles.calendar,styles.calendar)}>
        <div
          className={styles.calendar}
          style={{maxHigth: '900px', margin: '0 auto'}}
          id={this.props.calendarId}
        />
      </div>
    )
  }
}
