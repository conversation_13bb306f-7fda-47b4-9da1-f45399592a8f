// import { PlusOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import { Row, Col, Tag, Dropdown, Space, Menu, Table, Modal, message, DatePicker } from 'antd';
import React, {
  // useState, useRef,
  Component,
  // createContext
} from 'react';
// import { useIntl, FormattedMessage } from 'umi';
import { PrinterOutlined } from '@ant-design/icons';
import {  GridContent } from '@ant-design/pro-layout';
import AllPatientRecord from './AllPatientRecord'; // 全部病历
import MedicalRecordHomePage from './MedicalRecordHomePage'; //病历首页
import PrintContent from '@/components/PrintContent'; //打印预览
//引入图标
import FilterTime from '@/assets/FilterTime.png';
import FilterState from '@/assets/FilterState.png';
import Order from '@/assets/Order.png';
import ZOrder from '@/assets/<EMAIL>';
import preview from '@/assets/Preview.png';
import styles from './style.less';//引入样式
import moment from 'moment';


import { visitIndicatorType, status } from '@/utils/common.js';
import { connect } from "dva";
import {StringUtils} from "@/utils/StringUtils";//公共验证

//时间格式
const dateFormat = "YYYY-MM-DD";
class MedicalRecord extends Component {
  infoFormRef = React.createRef();
  constructor(props) {
    super(props);
    this.state = {
      indexKey: Math.random(),   // 病历首页的key
      emrKey: Math.random(),     // AllPatientRecord 全部病历的key
      fileModal: false,          // 归档弹窗
      infoModalVisible: false,   // 客户信息弹窗
      isPreviewRecord: false,    // 预览弹窗是否显示
      isPrint:false,             // 是否是预览
      datePickerShow: false,     // 日期选择框是否显示
      patientInfo: props.patientInfo,
      modalForm: {
        //客户信息form
        userName: '',
        sex: '',
        birth: '',
        country: '',
        phone: '',
      },
      clientHeight: document.documentElement.clientHeight, // 屏幕高度
      orderStatus: false, //正序倒序切换状态
      statusType: '0', // 状态切换类型
      checkedDate: '', //选中的筛选时间
      // 病历记录请求参数
      medicalListParams: {
        //病历记录入参  不分页
        patientId: props.patientInfo ? props.patientInfo.patientId : null, //患者标识
        emrId: props.patientInfo ? props.patientInfo.fileNumber : null, //大病历号
        tenantId: localStorage.getItem('tenantId'), //平台标识
      },
      allPanelParams: {
        //全部病历入参
        tenantId: localStorage.getItem('tenantId'),//品牌id
        pageNum: 1, //当前页
        pageSize: 10, //限制页
        patientId: props.patientInfo ? props.patientInfo.patientId : null, //患者标识
        emrId: props.patientInfo ? props.patientInfo.fileNumber : null, //大病历号
        beginTime: '', //就诊时间筛选开始时间
        endTime: '', //就诊时间筛选结束时间
        sort: 'DESC', //排序：正序是ASC 倒叙是DESC
        status: null, //就诊状态 状态：1编辑中；2已完成；3归档
      },
      oneErmSearchInfo:null   // 单病例查询数据
    };
    this.resize = this.resize.bind(this);//屏幕高度监听
  }

  // 初始化
  componentDidMount() {
    this.props.onRef(this)

    // const { childKey, patientInfo } = this.props;
    this.setState({
      childKey: 1,
      checkedDate: ''
    });

    window.addEventListener('resize', this.resize); //屏幕高度监听
    if (this.props.patientInfo) {
      this.getMedicalRecords();
    }

  }

  // eslint-disable-next-line react/no-deprecated
  componentWillReceiveProps(nextProps) {
    if (nextProps.patientInfo && (!this.state.patientInfo || nextProps.patientInfo.patientId != this.state.patientInfo.patientId)) {
      // 页面中的操作都初始化一下
      this.refreshShoeList(nextProps);
    }
  }

  //刷新数据变化
  refreshShoeList = (props) => {
    this.setState({
      fileModal: false, // 归档弹窗
      infoModalVisible: false, //客户信息弹窗
      isPreviewRecord: false, //预览
      datePickerShow: false, //日期选择框是否显示
      patientInfo: props.patientInfo, //患者信息
      modalForm: {
        //客户信息form
        userName: '',
        sex: '',
        birth: '',
        country: '',
        phone: '',
      },
      clientHeight: document.documentElement.clientHeight, // 屏幕高度
      orderStatus: false, //正序倒序切换状态
      statusType: '0', // 状态切换类型
      checkedDate: '', //选中的筛选时间
      // 病历记录请求参数
      medicalListParams: {
        //病历记录入参  不分页
        patientId: props.patientInfo ? props.patientInfo.patientId : this.props.wrpatientId, //患者标识
        emrId: props.patientInfo ? props.patientInfo.fileNumber : this.props.wremrId, //大病历号
        tenantId: localStorage.getItem('tenantId'), //平台标识
      },
      allPanelParams: {
        //全部病历入参
        tenantId: localStorage.getItem('tenantId'),//品牌id
        pageNum: 1, //当前页
        pageSize: 10, //限制页
        patientId: props.patientInfo ? props.patientInfo.patientId : this.props.wrpatientId, //患者标识
        emrId: props.patientInfo ? props.patientInfo.fileNumber : this.props.wremrId, //大病历号
        beginTime: '', //就诊时间筛选开始时间
        endTime: '', //就诊时间筛选结束时间
        sort: 'DESC', //排序：正序是ASC 倒叙是DESC
        status: null, //就诊状态 状态：1编辑中；2已完成；3归档
      },
      oneErmSearchInfo:null   // 单病例查询数据
    }, () => this.getMedicalRecords());
  }


  //在组件卸载及销毁之前直接调用
  componentWillUnmount() {
    window.removeEventListener('resize', this.resize); //取消
  }
  //监听高度
  resize() {
    this.setState({ clientHeight: document.documentElement.clientHeight });
  }

  // 编辑客户信息
  editInfo = () => {
    const { infoModalVisible } = this.state;
    this.setState({
      infoModalVisible: true,
    });
    // this.setState({
    //   modal: true,
    // });
    // 获取数据 渲染数据
  };
  // 保存客户信息
  saveHideModal = () => {
    // 保存数据再关闭弹窗

    this.hideModal();
  };
  // 表单重置
  onReset = () => {
    this.infoFormRef.current.resetFields();
  };

  // 关闭预览病历
  hidePreviewModal = () => {
    this.setState({
      isPreviewRecord: false,
      oneErmSearchInfo:null
    });
  };

  // 全部病历 & 病历首页
  showAll = (key = 1) => {

    const { tabsAll, modalForm,patientInfo} = this.state;
    if(!patientInfo){
      return false;
    }

    let keyName = key === 2 ? 'indexKey' : 'emrKey'

    this.setState({
      // tabsAll: 1,
      [keyName]: Math.random(),
      isPrint: key==2 ? true :false,
      onChangeDate: {
        userName: '',
        sex: '',
        birth: '',
        country: '',
        phone: '',
      },
    });

    if (key == 1) {
      // 改变入参
      this.setState({
        allPanelParams: {
          ...this.state.allPanelParams,
          orderStatus: false, // 正序倒序切换状态
          statusType: '0',    // 状态切换类型
          checkedDate: '',    // 选中的筛选时间();
        },
        // 改变状态
        orderStatus: false, // 正序倒序切换状态
        statusType: '0',    // 状态切换类型
        checkedDate: '',    // 选中的筛选时间();
      })
    } else if (key == 2) {
      this.getMedicalRecords(); //病历记录
    }

    this.props.callback(key);
  }


  formFinish = (values) => {

  };
// 预览操作
  previewRecord = (text= null) => {
    // 如果text不为空则是单病例打印
    if(text!=null){
      this.setState({
        oneErmSearchInfo:text
      })
    }
    this.setState({
      isPreviewRecord: true,
    });
  };
  // 筛选就诊日期
  datePickerIsShow = () => {
    this.setState({
      datePickerShow: true,

    },
    //  () => console.log('点击完后999 ', this.state.datePickerShow)
     )
  };
  // 修改状态
  changeStatus = (value) => {
    const { allPanelParams, statusType,patientInfo} = this.state;
    if(!patientInfo){
      return false;
    }
    this.setState({
      statusType: value.key,
    });
    allPanelParams.pageNum = 1;
    allPanelParams.status = value ? parseInt(value.key) : null;
    this.allEmr.getAllMedicalRecords(allPanelParams)

  };
  // 修改日期
  changeDate = (date, dateString) => {
    // 请求接口
    const { allPanelParams ,patientInfo} = this.state;

    if(!patientInfo){
      return false;
    }

    this.setState({
      checkedDate: dateString,
    });
    this.state.allPanelParams = {
      ...allPanelParams,
      beginTime: dateString,
      endTime: dateString,
      pageNum: 1,
    };
    this.allEmr.getAllMedicalRecords(this.state.allPanelParams)
  };
  // 正序倒叙切换
  inverOrder = (status) => {
    const { orderStatus, allPanelParams,patientInfo } = this.state;

    if(!patientInfo){
      return false;
    }
    this.setState({
      orderStatus: status,
    });

    if (orderStatus == false) {
      // 点击正序
      this.state.allPanelParams = {
        ...this.state.allPanelParams,
        sort: 'ASC',
        current: 1,
        pageNum: 1,
      };
    } else {
      this.state.allPanelParams = {
        ...this.state.allPanelParams,
        sort: 'DESC',
        current: 1,
        pageNum: 1,
      };
    }

    this.allEmr.getAllMedicalRecords(allPanelParams)
  };

  // 请求病历首页--患者病历记录患者病历记录
  getMedicalRecords = () => {
    const {medicalListParams} = this.state;
    if(StringUtils.isBlank(medicalListParams.emrId)){
      this.setState({
        medicalRecords: [],
      });
      return false;
    }
    const { dispatch } = this.props;
    if (dispatch) {
      dispatch({
        type: 'homePage/medicalRecords',
        payload: this.state.medicalListParams,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              medicalRecords: res.rows,
            });
          }
        },
      });
    }
  };
  //打印去除页眉页脚各个浏览器验证
  pagesetup_null() {
    let hkey_root, hkey_path, hkey_key;
    hkey_root = "HKEY_CURRENT_USER";
    hkey_path = "\\Software\\Microsoft\\Internet Explorer\\PageSetup\\";
    try {
      let RegWsh = new ActiveXObject("WScript.Shell");
      hkey_key = "header";
      RegWsh.RegWrite(hkey_root + hkey_path + hkey_key, "");
      hkey_key = "footer";
      RegWsh.RegWrite(hkey_root + hkey_path + hkey_key, "");
    } catch (e) { }
  }
  //打印去除页眉页脚各个浏览器验证
  getExplorer() {
    let explorer = window.navigator.userAgent;
    if (explorer.indexOf("MSIE") >= 0) {//ie
      return "IE";
    } else if (explorer.indexOf("Firefox") >= 0) {//firefox
      return "Firefox";
    } else if (explorer.indexOf("Chrome") >= 0) { //Chrome
      return "Chrome";
    } else if (explorer.indexOf("Opera") >= 0) { //Opera
      return "Opera";
    } else if (explorer.indexOf("Safari") >= 0) { //Safari
      return "Safari";
    }
  }
  //打印按钮事件
  print = () => {
    if (this.getExplorer() == "IE") {
      this.pagesetup_null();
    }
    const printContentStr = window.document.getElementById('agreePrint').innerHTML;
    this.setState({
      isPreviewRecord: false,
    });

    setTimeout(()=>{
      window.document.getElementById('root').style.display="none";
      var div = document.createElement("div");
      div.id = "printid";
      document.body.appendChild(div);
      window.document.getElementById('printid').innerHTML = printContentStr
      // div.body.innerHTML = printContentStr
      window.print(); //调用浏览器的打印功能打印指定区域
      //window.document.removeChild(el);
      window.document.getElementById('printid').innerHTML = "";
      window.document.getElementById('root').style.display="block";
    },1000);
    // window.print();
    // window.location.reload();
  }
  render() {
    const {
      isPrint,
      isPreviewRecord,
      clientHeight,
      orderStatus,
      allPanelParams,
      statusType,
      indexKey, emrKey, medicalRecords,
      checkedDate,
      oneErmSearchInfo
    } = this.state;

    const { childKey, patientInfo, clickPatientInfo, rightPatientInfos, patientInfoDto,
      // todyWaite
     } = this.props;

    // 列表标题
    const columns = [
      {
        width: '15%',
        title: '就诊日期',
        dataIndex: 'clinicTime',
        key: 'clinicTime',
        align:'center'
      },
      {
        width: '15%',
        title: '就诊诊所',
        dataIndex: 'organizationName',
        key: 'organizationName',
        align:'center'
      },
      {
        width: '15%',
        title: '医生',
        dataIndex: 'userName',
        key: 'userName',
        align:'center'
      },
      {
        width: '10%',
        title: '就诊类型',
        dataIndex: 'visitIndicator',
        key: 'visitIndicator',
        align:'center',
        render: (text) => <span>{visitIndicatorType[text]}</span>,
      },
      {
        width: '25%',
        title: '主诉',
        dataIndex: 'illnessDesc',
        key: 'illnessDesc',
        align:'center'
      },
      {
        width: '10%',
        title: '病历状态',
        dataIndex: 'status',
        key: 'status',
        align:'center',
        render: (text) => (
          <Tag
            color="success"
            className={text == '4' ? styles.tagSuccess : text == '2' ? styles.tagRed : styles.tagWarn}
          >
            {status[text]}
          </Tag>
        ),
      },
      {
        width: '15%',
        title: '操作',
        key: 'action',
        align:'center',
        render: (text) => (
          <a  onClick={()=>this.previewRecord(text)}>预览</a>
        )
      },
    ];

    return (
      <GridContent>
        <Row
          style={{
            paddingLeft: '16px',
            paddingBottom: '12px',
            borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <Col span={8}>
            <span className={childKey == 1 ? styles.tagChecked : styles.tagNoCheck} onClick={() => this.showAll(1)}>
              全部病历
            </span>
            <span
              className={childKey == 2 ? styles.tagChecked : styles.tagNoCheck}
              onClick={() => this.showAll(2)}
            >
              病历首页
            </span>
          </Col>

          {childKey == 1 ? (
            <Col style={{ float: 'right', marginRight: 32 }}>
              <div className={styles.screen}>
                <Dropdown
                  overlay={
                    <Menu onClick={this.changeStatus.bind(this)}>
                      <Menu.Item key={null}>全部状态</Menu.Item>
                      <Menu.Item key="2">未完成</Menu.Item>
                      <Menu.Item key="3">未归档</Menu.Item>
                      <Menu.Item key="4">已归档</Menu.Item>
                    </Menu>
                  }
                >
                  <a>
                    <Space>
                      <span style={{ marginLeft: 10 }} className={styles.txt11}>
                        <img src={FilterState} className={styles.siftImg} />
                        {statusType == 0
                          ? '筛选状态'
                          : statusType == '2'
                            ? '未完成'
                            : statusType == '3'
                              ? '未归档'
                              : statusType == '4'
                                ? '已归档'
                                : '全部状态'}
                      </span>
                    </Space>
                  </a>
                </Dropdown>
                <span
                  style={{ marginLeft: 20, position: 'relative' }}
                  className={styles.txt11}
                  onClick={() => this.datePickerIsShow()}
                >
                  <img src={FilterTime} className={styles.siftImg} />
                  <DatePicker
                    bordered={false}
                    placeholder='筛选就诊日期'
                    value={checkedDate ? moment(checkedDate, dateFormat) : null}
                    format={dateFormat}
                    disabledDate={current => current && current > moment().endOf('day')}
                    onChange={this.changeDate} />
                </span>
                {orderStatus ? (
                  <span
                    onClick={() => this.inverOrder(false)}
                    className={styles.txt11}
                  >
                    <img src={ZOrder} className={styles.siftImg} />
                    就诊时间正序
                  </span>
                ) : (
                  <span
                    onClick={() => this.inverOrder(true)}
                    className={styles.txt11}
                  >
                    <img src={Order} className={styles.siftImg} />
                    就诊时间倒序
                  </span>
                )}

              </div>
            </Col>
          ) : (
            <Col span={2} style={{ float: 'right' }} onClick={this.previewRecord}>
              <span style={{ marginLeft: 10 }} className={styles.txt11}>
                <img src={preview} className={styles.siftImg} />
                预览
              </span>
            </Col>
          )}
        </Row>
        <div style={{ overflowY: 'auto', height: clientHeight - 215 }}>
          {childKey == 1 ? (
            <Row >
              <AllPatientRecord
                key={emrKey}
                onRef={(e) => this.allEmr = e}
                patientData={patientInfo}
                // todyWaite={todyWaite}
                isPrint={false}
                getTodayWaitInfo={this.props.getTodayWaitInfo}
                clickPatientInfo={clickPatientInfo}
              />
            </Row>
            // // 全部病历
            // allPanelList.length > 0 ?
            //
            //   : <div>
            //     {/*暂无数据*/}
            //     <div className={commonStyle.nodataContent} style={{ marginTop: '20%' }}>
            //       <img src={noData} className={commonStyle.imgStyle} alt="" />
            //       <div className={commonStyle.fontStyle}>暂无数据</div>
            //     </div>
            //   </div>

          )
            : (
              <div className={styles.content}>
                <MedicalRecordHomePage
                  key={indexKey}
                  patientInfo={patientInfo}
                  rightPatientInfos={rightPatientInfos}
                  patientInfoDto={patientInfoDto}
                  clickPatientInfo={clickPatientInfo}
                  medicalRecords={medicalRecords}
                  getTodayWaitInfo={this.props.getTodayWaitInfo}
                  getTopPatientInfo={this.props.getTopPatientInfo}
                // getPatientOverview={this.props.getPatientOverview}
                />

                <Modal
                  visible={isPreviewRecord}
                  title="病历打印预览"
                  className={styles.printModal}
                  width={1145}
                  onCancel={this.hidePreviewModal}
                  destroyOnClose={true}
                  footer={[
                      <span
                        className={`${styles.txt11} ${styles.width100} ${styles.printText}`}
                        onClick={this.print}
                        style={{}}
                      >
                        <PrinterOutlined />
                        <span
                          className={styles.printMedicl}
                        >打印病历</span>
                      </span>
                  ]}
                >
                  <div style={{ padding: '21px 20px 20px' }} id='agreePrint'>
                    <PrintContent
                      patientInfo={patientInfo}
                      oneErmSearchInfo={oneErmSearchInfo!=null ? oneErmSearchInfo : null}
                    />
                    {/*<MedicalRecordHomePage*/}
                    {/*  notOptions={false}*/}
                    {/*  allPanelParams={allPanelParams}*/}
                    {/*  hidePreviewModal={this.hidePreviewModal}*/}
                    {/*  patientInfo={patientInfo}*/}
                    {/*  medicalRecords={medicalRecords} />*/}
                    {/*<AllPatientRecord*/}
                    {/*  notOptions={false}*/}
                    {/*  isPrint={isPrint}*/}
                    {/*  patientData={patientInfo}*/}
                    {/*  clickPatientInfo={clickPatientInfo}*/}
                    {/*/>*/}
                  </div>
                </Modal>


                {/* <Row> 表格</Row> */}
                <Row className={styles.userInfo}>
                  <Row className={`${styles.displayFlex} ${styles.width100}`}>
                    <Col span={20}>
                      <h3 style={{ fontWeight: 900 }}> 病历记录</h3>
                    </Col>
                  </Row>

                  <Row className={`${styles.width100}`}>
                    <Table
                      rowKey={record => record.emrSubId}
                      className={`${styles.width100}`}
                      columns={columns}
                      dataSource={medicalRecords}
                      // onChange={this.pageNumChange}
                      pagination={false}
                    ></Table>
                  </Row>
                </Row>
              </div>
            )}
        </div>
      </GridContent>
    );
  }
}
export default connect(
  ({ MedicalRecord, }) => ({
    MedicalRecord,
  }),
)(MedicalRecord);
