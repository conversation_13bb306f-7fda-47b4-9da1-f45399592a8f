import request from '@/utils/request';

//获取病历模版分组列表
export async function findEmrTmptClass(params) {
  return request('/api/emr/system/findEmrTmptClass', {
    method: 'POST',
    data: params,
  });
}
//新建模板文件夹
export async function saveContentClass(params) {
  return request('/api/emr/system/saveContentClass', {
    method: 'POST',
    data: params,
  });
}
//删除文件夹
export async function deleteContentClass(params) {
  return request('/api/emr/system/deleteContentClass', {
    method: 'POST',
    data: params,
  });
}
//编辑文件夹
export async function updateContentClass(params) {
  return request('/api/emr/system/updateContentClass', {
    method: 'POST',
    data: params,
  });
}
//获取病历模板详情
export async function EmrMessage(params) {
  return request('/api/emr/system/getEmrTmptInfoById', {
    method: 'POST',
    data: params,
  });
}
//编辑保存病历模版
export async function saveEmrTmpt(params) {
  return request('/api/emr/system/saveEmrTmpt', {
    method: 'POST',
    data: params,
  });
}
//删除病历模板
export async function deleteEmrTmpt(params) {
  return request('/api/emr/system/deleteEmrTmpt', {
    method: 'POST',
    data: params,
  });
}
//病历模版分组列表搜索
export async function findEmrTmptClassSearch(params) {
  return request('/api/emr/system/findEmrTmptClassSearch', {
    method: 'POST',
    data: params,
  });
}
//根据模板分类获取模板名
export async function findEmrTmptNamesByClass(params) {
  return request('/api/emr/system/findEmrTmptNamesByClass', {
    method: 'POST',
    data: params,
  });
}

