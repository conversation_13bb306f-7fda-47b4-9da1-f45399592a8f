@import '~antd/es/style/themes/default.less';

.ListHeight{
  margin-top: 16px;
  height:500px;
  overflow-y: scroll;
  border: 1px solid rgba(0, 0, 0, 0.06);
}
.ListRightHeight{
  margin-top: 16px;
  height:500px;
  overflow-y: scroll;
}
.imageBorder{
  .date{
    width: 100%;
    height: 28px;
    background: #F7F8FA;
    font-size: 14px;
    font-family: PingFang SC;
    color: rgba(0, 0, 0, 0.45);
    line-height:28px;
    padding-left: 8px;
  }
}
.ctImageList{
  .title{
    margin-top: 16px;
    margin-left: 13px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }
}
.imgCard{
  margin-left: 8px;
  width: 165px;
  height: 177px;
  border-radius: 2px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  .img{
    width: 100%;
    height: 110px;
  }
  .contentStyle{
    padding:11px 4px 4px 4px;
    font-size: 14px;
    font-family: PingFang SC;
    color: rgba(0, 0, 0, 0.45);
    line-height: 20px;
  }
}
.showImgborder{
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
}
.showImg{
  border-radius: 2px;
  width: 160px;
  height: 110px;
  margin-right: 8px;
  margin-bottom: 8px;
}
.imgborder{
  position: relative;
}
.ctimgdelete {
  color: #F0F0F0;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  width: 165px;
  height: 110px;
  background: rgba(0, 0, 0, 0.3);
  text-align: center;
  align-items: center,
}
.icon_delete{
  width: 20px;
  height: 20px;
}
.deleteFont{
  font-size: 14px;
  margin-left: 4px;
  margin-top: 1px;
}
.maxLine{
  white-space:nowrap;
  overflow:hidden;
  text-overflow:ellipsis;
}
.loading_more {
  padding: 0 0 25px;
  color: #4292ff;
  font-size: 15px;
  text-align: center;
  //text-decoration: underline;
  cursor: pointer;
}
.width100 {
  width: 100%
}
.typeTitle {
  padding: 2px 8px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 24px;
  border-radius: 2px;
  margin: 5px 0 15px;
}
.textInputStyle{
  :global{
    .ant-input-textarea-show-count.ant-input-textarea-in-form-item::after{
      position: absolute;
      bottom: 25px;
      right: 5px;
    }
  }
}
