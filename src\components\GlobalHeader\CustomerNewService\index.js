import React, { PureComponent } from 'react';
import { Icon, Menu, Dropdown } from 'antd';
import Link from 'umi/link';
import Debounce from 'lodash-decorators/debounce';
import styles from './index.less';
import RightContent from './RightContent';
import { getOrganizationInfo } from "@/utils/utils";
import HomeImg from '@/assets/customerServiceNew/home.png';
import changemenu from '@/assets/changemenu.png';
import router from 'umi/router';


export default class GlobalHeader extends PureComponent {
  state = {
    obj: {}
  }
  componentWillUnmount() {
    this.triggerResizeEvent.cancel();
  }
  /* eslint-disable*/
  @Debounce(600)
  triggerResizeEvent() {
    // eslint-disable-line
    const event = document.createEvent('HTMLEvents');
    event.initEvent('resize', true, false);
    window.dispatchEvent(event);
  }

  menu = () => {
    router.push(`/customerService/appointment`)
  }
  render() {
    const { obj } = this.state;
    return (
      <div className={styles.header}>
        <i className={styles.trigger} onClick={this.menu}>
          <img src={HomeImg} />
        </i>
        <h2 style={{ display: 'inline-block' }}>400客服</h2>
        <RightContent {...this.props} />

      </div>
    );
  }
}
