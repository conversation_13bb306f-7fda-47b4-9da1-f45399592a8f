/**
 * @Description: 病历质检设置
 * @author: sy
 */
import React, { Component } from 'react';
import { connect } from 'dva';
import { Select, Table, Switch, Button, InputNumber } from 'antd';
import styles from './index.less';

const { Option } = Select;

@connect(({ loading, caseQualitySetting }) => ({
  loading,
  caseQualitySetting,
}))
class CaseQualitySetting extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // 基础设置
      evaluationCycle: '北京市标准', // 评估周期

      // 质检评分配置数据
      scoreConfigData: [
        {
          key: '1',
          category: '主诉',
          ruleDescription: '未填写主诉',
          score: 15,
          status: true,
          children: [
            {
              key: '1-1',
              category: '',
              ruleDescription: '目的型主诉，需填写病变部位',
              score: 5,
              status: true,
            },
            {
              key: '1-2',
              category: '',
              ruleDescription: '目的型主诉，需填写主要症状',
              score: 5,
              status: false,
            },
            {
              key: '1-3',
              category: '',
              ruleDescription: '目的型主诉，需填写发病时间（或病程日期）',
              score: 5,
              status: true,
            },
          ],
        },
        {
          key: '2',
          category: '现病史',
          ruleDescription: '未填写现病史',
          score: 15,
          status: true,
          children: [
            {
              key: '2-1',
              category: '',
              ruleDescription: '需记录主诉牙（主诉病）病史的发生',
              score: 3,
              status: true,
            },
            {
              key: '2-2',
              category: '',
              ruleDescription: '需记录主诉牙（主诉病）病史的发展',
              score: 3,
              status: true,
            },
            {
              key: '2-3',
              category: '',
              ruleDescription: '需记录主诉牙（主诉病）病史的曾经治疗',
              score: 3,
              status: false,
            },
            {
              key: '2-4',
              category: '',
              ruleDescription: '需记录主诉牙（主诉病）病史的目前情况',
              score: 3,
              status: true,
            },
            {
              key: '2-5',
              category: '',
              ruleDescription: '未填写现病史',
              score: 3,
              status: true,
            },
          ],
        },
      ],

      // 学科红线规则
      redlineRules: [
        {
          key: '1',
          rule: '治疗药品的药品（医嘱药品共同使用的，退药、药品及药品处方）必须要求',
          status: true,
        },
        {
          key: '2',
          rule: '医疗器械设备及其配件的药品及其配件分析',
          status: true,
        },
        {
          key: '3',
          rule:
            '正确的药品、医疗工具及治疗药品的，医生的治疗药品的治疗药品及其配件的治疗药品及其配件',
          status: false,
        },
        {
          key: '4',
          rule: '药品正确的诊疗器械设备的药品及其配件的治疗药品及其配件',
          status: true,
        },
      ],

      // 学科选择
      selectedSubject: '妇科',

      // 表格展开状态
      expandedRowKeys: [], // 默认不展开任何行
    };
  }

  componentDidMount() {
    // 初始化数据
    this.getSettingData();
  }

  // 获取设置数据
  getSettingData = () => {
    // 这里可以调用API获取数据
    // const { dispatch } = this.props;
    // dispatch({ type: 'caseQualitySetting/getSettingData' });
  };

  // 保存设置
  handleSave = () => {
    // 这里可以调用API保存数据
    // const { dispatch } = this.props;
    // dispatch({ type: 'caseQualitySetting/saveSettingData', payload: this.state });
  };

  // 处理评估周期变化
  handleEvaluationCycleChange = value => {
    this.setState({
      evaluationCycle: value,
    });
  };

  // 处理学科选择变化
  handleSubjectChange = value => {
    this.setState({
      selectedSubject: value,
    });
  };

  // 处理评分配置状态变化
  handleScoreStatusChange = (key, checked) => {
    const { scoreConfigData } = this.state;
    const updateData = data => {
      return data.map(item => {
        if (item.key === key) {
          return { ...item, status: checked };
        }
        if (item.children) {
          return { ...item, children: updateData(item.children) };
        }
        return item;
      });
    };

    this.setState({
      scoreConfigData: updateData(scoreConfigData),
    });
  };

  // 处理评分变化
  handleScoreChange = (key, value) => {
    const { scoreConfigData } = this.state;

    // 检查是否为子项
    const isChildItem = key.includes('-');

    if (isChildItem) {
      // 子项修改，需要验证总和
      const parentKey = key.split('-')[0];
      let parentItem = null;
      let childrenSum = 0;

      // 找到父项和计算子项总和
      const findParentAndSum = data => {
        const parent = data.find(item => item.key === parentKey);
        if (parent) {
          parentItem = parent;
          // 计算修改后的子项总和
          childrenSum = parent.children.reduce((sum, child) => {
            if (child.key === key) {
              return sum + (value || 0);
            }
            return sum + (child.score || 0);
          }, 0);
        }
      };

      findParentAndSum(scoreConfigData);

      // 验证子项总和是否等于父项分数
      if (parentItem && childrenSum !== parentItem.score) {
        // 可以在这里添加提示信息或阻止修改
        // 暂时允许修改，但可以在UI上显示警告
      }
    }

    const updateData = data => {
      return data.map(item => {
        if (item.key === key) {
          return { ...item, score: value };
        }
        if (item.children) {
          return { ...item, children: updateData(item.children) };
        }
        return item;
      });
    };

    this.setState({
      scoreConfigData: updateData(scoreConfigData),
    });
  };

  // 处理行点击展开/收起
  handleRowClick = record => {
    const { expandedRowKeys } = this.state;
    const isExpanded = expandedRowKeys.includes(record.key);


    if (isExpanded) {
      this.setState({
        expandedRowKeys: [],
      }, () => {
      });
    } else {
      // 如果当前行未展开，则展开当前行，关闭其他所有行
      this.setState({
        expandedRowKeys: [record.key],
      }, () => {
      });
    }
  };

  // 处理红线规则状态变化
  handleRedlineStatusChange = (key, checked) => {
    const { redlineRules } = this.state;
    const newRules = redlineRules.map(rule => {
      if (rule.key === key) {
        return { ...rule, status: checked };
      }
      return rule;
    });

    this.setState({
      redlineRules: newRules,
    });
  };

  // 质检评分配置表格列定义
  getScoreConfigColumns = () => {
    return [
      {
        title: '模块',
        dataIndex: 'category',
        key: 'category',
        width: 200,
        render: (text, record) => {
          // 只在父级显示类别名称
          return record.category || '';
        },
      },
      {
        title: '质检项目',
        dataIndex: 'ruleDescription',
        key: 'ruleDescription',
        render: (text) => text
      },
      {
        title: '扣分标准',
        dataIndex: 'score',
        key: 'score',
        width: 200,
        render: (text, record) => {
          // 判断是否为子项（第二级）
          const isChildItem = record.key.includes('-');

          if (isChildItem) {
            // 子项可编辑，最小值为1
            return (
              <InputNumber
                min={1}
                max={100}
                value={text}
                onChange={value => this.handleScoreChange(record.key, value)}
                size="small"
                style={{ width: 80 }}
              />
            );
          } else {
            // 父项不可编辑，只显示数值
            return <span>{text}</span>;
          }
        },
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 200,
        render: (text, record) => (
          <div className={styles.switchWrapper}>
            <Switch
              checked={text}
              onChange={checked => this.handleScoreStatusChange(record.key, checked)}
              size="small"
            />
            <span className={styles.switchText}>{text ? '启用' : '禁用'}</span>
          </div>
        ),
      },
    ];
  };

  // 获取展开后的数据源
  getExpandedDataSource = () => {
    const { scoreConfigData, expandedRowKeys } = this.state;
    const result = [];

    scoreConfigData.forEach(item => {
      result.push(item);
      // 如果该项在展开列表中，添加其子项
      if (expandedRowKeys.includes(item.key) && item.children) {
        result.push(...item.children);
      }
    });

    return result;
  };

  render() {
    const {
      evaluationCycle,
      redlineRules,
      selectedSubject,
    } = this.state;

    return (
      <div className={styles.settingContainer}>
        <div className={styles.header}>
          <h2>设置</h2>
          <Button type="primary" onClick={this.handleSave}>
            保存设置
          </Button>
        </div>

        {/* 基础设置 */}
        <div className={styles.settingCard}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>基础设置</h3>
            <span className={styles.cardDes}>配置基础评分规则</span>
          </div>
          <div className={styles.cardBody}>
            <div className={styles.settingContent}>
              <span className={styles.fieldLabel}>评分规则：</span>
              <Select
                value={evaluationCycle}
                onChange={this.handleEvaluationCycleChange}
                style={{ width: 240 }}
                // size="small"
              >
                <Option value="北京市标准">北京市标准</Option>
                <Option value="上海市标准">上海市标准</Option>
                <Option value="广州市标准">广州市标准</Option>
              </Select>
            </div>
          </div>
        </div>

        {/* 质检评分配置 */}
        <div className={styles.settingCard}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>质检评分配置</h3>
            <span className={styles.cardDes}>配置各项质检的评分标准和扣分规则</span>
          </div>
          <div className={styles.cardBody}>
            <div className={styles.tableWrapper}>
              <Table
                columns={this.getScoreConfigColumns()}
                dataSource={this.getExpandedDataSource()}
                pagination={false}
                size="small"
                rowKey="key"
                className={styles.scoreConfigTable}
                onRow={record => ({
                  onClick: (event) => {
                    event.stopPropagation(); // 阻止事件冒泡
                    // 只有父级行（有children的行）才处理点击事件
                    if (record.children && record.children.length > 0) {
                      this.handleRowClick(record);
                    }
                  },
                  style: {
                    cursor: record.children && record.children.length > 0 ? 'pointer' : 'default',
                  },
                })}
              />
            </div>
          </div>
        </div>

        {/* 学科红线规则 */}
        <div className={styles.settingCard}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>学科红线规则</h3>
          </div>
          <div className={styles.cardBody}>
            <div className={styles.settingItem}>
              <span className={styles.settingDesc}>为不同学科配置红线检查规则的启用状态</span>
            </div>
            <div className={styles.settingContent}>
              <span className={styles.fieldLabel}>选择学科</span>
              <Select
                value={selectedSubject}
                onChange={this.handleSubjectChange}
                style={{ width: 200 }}
                size="small"
              >
                <Option value="妇科">妇科</Option>
                <Option value="内科">内科</Option>
                <Option value="外科">外科</Option>
                <Option value="儿科">儿科</Option>
              </Select>
            </div>

            <div className={styles.redlineRules}>
              <div className={styles.rulesHeader}>
                <span className={styles.rulesTitle}>红线规则</span>
                <span className={styles.rulesTitle}>状态</span>
              </div>
              {redlineRules.map(rule => (
                <div key={rule.key} className={styles.ruleItem}>
                  <div className={styles.ruleText}>{rule.rule}</div>
                  <div className={styles.ruleStatus}>
                    <Switch
                      checked={rule.status}
                      onChange={checked => this.handleRedlineStatusChange(rule.key, checked)}
                      size="small"
                    />
                    <span className={styles.switchText}>{rule.status ? '启用' : '禁用'}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default CaseQualitySetting;
