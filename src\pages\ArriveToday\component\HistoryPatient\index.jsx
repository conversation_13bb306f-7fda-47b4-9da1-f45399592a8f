import React, { Component } from "react";
import { history } from 'umi';
import { Input, Space, Table, Pagination } from 'antd';
import { GridContent } from "@ant-design/pro-layout";
import { connect } from "dva";
import { StringUtils } from "@/utils/StringUtils";//公共验证

const { Search } = Input;

class HistoryPatient extends Component {
  constructor(props) {
    super(props);
    this.state = {
      historyPatientList: [], //历史患者接口
      HistoryPatientCount: 20, //总条数
      loading: false,//loading状态
      newKey: new Date() + Math.random(),//key值
      historyParams: {
        // tenantId: localStorage.getItem('tenantId'),//品牌id
        pageNum: 1, //当前页
        pageSize: 10, //限制页
        tenantId: localStorage.getItem("tenantId"), //平台标识
        userId: localStorage.getItem("userId"), //当前登录医生标识
        searchValue: '',//筛选条件
        // organizationId: localStorage.getItem("organizationId"), //机构id
      },
    };
  }
  //初始化调用
  componentDidMount() {
    this.getHistoryPatient()// 历史患者请求接口
  }
  // 筛选条件查询
  onSearch = (value) => {
    // console.log("value===", value)
    this.state.historyParams.pageNum=1;
    this.setState({
      historyParams: {
        ...this.state.historyParams,
        searchValue: StringUtils.trim(value)
      },
      newKey: new Date() + Math.random(),//key值
    }, () => this.getHistoryPatient())
  }
  // 历史患者请求接口
  getHistoryPatient() {
    const { dispatch } = this.props;
    const { historyParams } = this.state;
    this.setState({
      loading: true
    })
    if (dispatch) {
      dispatch({
        type: 'todayVisit/historyPatients',
        payload: historyParams,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              historyPatientList: res.content.resultList,
              historyPatientCount: res.content.total,
              loading: false
            });
          }
        },
      });
    }
  }
  // 分页查询
  changePageNum = (pageNum, pageSize) => {
    this.setState(
      {
        historyParams: {
          ...this.state.historyParams,
          pageNum: pageNum,
          pageSize: pageSize,
        },
      },
      () => this.getHistoryPatient(this.state.historyParams),
    );
  }
  // 查看操作
  onCheckJump = (value, e) => {
    // console.log("查看", value, e)
    history.push({
      pathname: '/emr/MedicalCenter/AllPanel',
      state: {
        patientId: value.patientId,
        patientData: {
          name: value.name,  //患者姓名
          patientId: value.patientId,//患者id
          fileNumber: value.fileNumber//病历号
        }
      }
    })
  }
  render() {
    const { historyPatientList, historyPatientCount = 20, historyParams,loading,newKey } = this.state
    const columns = [
      {
        title: '患者姓名',
        dataIndex: 'name',
        key: 'name',

      },
      {
        title: '病历号',
        dataIndex: 'fileNumber',
        key: 'fileNumber',
      },
      {
        title: '手机号',
        dataIndex: 'oftenTel',
        key: 'oftenTel',
      },
      {
        title: '已归档病例数',
        key: 'archivedNum',
        dataIndex: 'archivedNum',
      },
      {
        title: '未完成病例数',
        key: 'unfinishedNum',
        dataIndex: 'unfinishedNum'
      },
      {
        title: '操作',
        key: 'action',
        render: (text, record) => (
          <Space size="middle">
            <a onClick={this.onCheckJump.bind(this, record)}>查看</a>
          </Space>
        ),
      },
    ];


    return (
      <GridContent>
        <Search
          placeholder="请输入患者姓名/病历号/手机号搜索"
          onSearch={this.onSearch}
          style={{
            width: 400,
            float: 'right',
            marginTop: -55
          }}
        />
        <Table
          rowKey={record => record.emrId}
          columns={columns}
          loading={loading}
          dataSource={historyPatientList}
          pagination={false}
        />

        <Pagination
          key={newKey}
          showSizeChanger
          style={{ float: 'right', marginTop: 16 }}
          total={historyPatientCount}
          showTotal={(total) => `共 ${total} 条记录`}
          defaultPageSize={historyParams.pageSize}
          defaultCurrent={historyParams.pageNum}
          onChange={(pageNum, pageSize) => this.changePageNum(pageNum, pageSize)}
        />
      </GridContent>
    );
  }
}

export default connect(({ historyPatientsList }) => ({
  historyPatientsList
}))(HistoryPatient);
