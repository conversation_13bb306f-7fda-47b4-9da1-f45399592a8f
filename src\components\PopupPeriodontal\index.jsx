import {Radio, Row, Col, Input, Tabs, Form, Checkbox, Select, Tag, Button, Modal, message} from 'antd';
import React, {Component} from 'react';
import styles from './style.less';//样式
//图片
import Phone from "@/assets/phone.png";
import sex from "@/assets/nan.png";
import Nv from "@/assets/girl.png";
import {toothUtils} from "@/utils/toothUtils";//牙位选择显示
import ToothSelect from "@/components/ToothSelect";//牙位选择

const {TabPane} = Tabs;
const { TextArea } = Input;

//新建牙周检查
class Periodontal extends Component {
  constructor(props) {
    super(props)
    this.state = {
      update: false,//是否显示弹框
      visibleToothBit2: false,//是否显示弹框
      visibleToothBit: false,//是否显示弹框
      visibleToothBit3: false,//是否显示弹框
      visibleToothBit4: false,//是否显示弹框
      odontolith: 0,//'牙周检查牙石等级 0未知；1/2/3等级',
      cleanStatus: 0,//'牙周检查洁治等级 0未知；1洁治前；2洁治后',
      examRemark: "",//备注
      rightPatientInfos:{...props.rightPatientInfos},//获取右侧患者信息数据
      patientInfoDtos:{...props.patientInfoDtos},//获取患者信息数据
      pagination6: {...props.state},//获取传参数据
      patientData: {...props.patientData},//获取患者信息数据
      activeTdId: "",//动态id
      nextActiveDentition: "",//下一个点亮数据值
      currentToothCol: "",//获取当前牙位数据
      nextTdId: "",//下一个牙位id
      nextTdIndex: "",//下一个牙位id点亮状态
      params:{},//获取牙周数据
      tdData: {},//牙周大表没给单元格对应的数据，需提交的数据
      missesTooth: [],//牙周大表中设置为缺失牙的牙位集合，需给交的数据
      triangleTd: [1, 2, 3, 4, 5, 6, 7, 8, 9, 20, 21, 22, 23, 24, 25, 26, 27, 28, 509, 510, 511, 512, 513, 514, 525, 526, 527, 528, 529, 530],
      toothCell: {//牙周单元格
        denture18: [1, 2, 3, 29, 45, 61, 77, 108, 109, 140, 141, 172, 173, 174, 175, 266, 267, 268],
        denture17: [4, 5, 6, 30, 46, 62, 78, 107, 110, 139, 142, 171, 176, 177, 178, 263, 264, 265],
        denture16: [7, 8, 9, 31, 47, 63, 79, 106, 111, 138, 143, 170, 179, 180, 181, 260, 261, 262],
        denture15: [10, 32, 48, 64, 80, 105, 112, 137, 144, 169, 182, 183, 184, 257, 258, 259],
        denture14: [11, 33, 49, 65, 81, 104, 113, 136, 145, 168, 185, 186, 187, 254, 255, 256],
        denture13: [12, 34, 50, 66, 82, 103, 114, 135, 146, 167, 188, 189, 190, 251, 252, 253],
        denture12: [13, 35, 51, 67, 83, 102, 115, 134, 147, 166, 191, 192, 193, 248, 249, 250],
        denture11: [14, 36, 52, 68, 84, 101, 116, 133, 148, 165, 194, 195, 196, 245, 246, 247],
        denture21: [15, 37, 53, 69, 85, 100, 117, 132, 149, 164, 197, 198, 199, 242, 243, 244],
        denture22: [16, 38, 54, 70, 86, 99, 118, 131, 150, 163, 200, 201, 202, 239, 240, 241],
        denture23: [17, 39, 55, 71, 87, 98, 119, 130, 151, 162, 203, 204, 205, 236, 237, 238],
        denture24: [18, 40, 56, 72, 88, 97, 120, 129, 152, 161, 206, 207, 208, 233, 234, 235],
        denture25: [19, 41, 57, 73, 89, 96, 121, 128, 153, 160, 209, 210, 211, 230, 231, 232],
        denture26: [20, 21, 22, 42, 58, 74, 90, 95, 122, 127, 154, 159, 212, 213, 214, 227, 228, 229],
        denture27: [23, 24, 25, 43, 59, 75, 91, 94, 123, 126, 155, 158, 215, 216, 217, 224, 225, 226],
        denture28: [26, 27, 28, 44, 60, 76, 92, 93, 124, 125, 156, 157, 218, 219, 220, 221, 222, 223],
        denture31: [340, 339, 338, 295, 294, 293, 388, 373, 420, 405, 452, 437, 469, 485, 501, 520],
        denture32: [337, 336, 335, 298, 297, 296, 387, 374, 419, 406, 451, 438, 470, 486, 502, 521],
        denture33: [334, 333, 332, 301, 300, 299, 386, 375, 418, 407, 450, 439, 471, 487, 503, 522],
        denture34: [331, 330, 329, 304, 303, 302, 385, 376, 417, 408, 449, 440, 472, 488, 504, 523],
        denture35: [328, 327, 326, 307, 306, 305, 384, 377, 416, 409, 448, 441, 473, 489, 505, 524],
        denture36: [325, 324, 323, 310, 309, 308, 383, 378, 415, 410, 447, 442, 474, 490, 506, 525, 526],
        denture37: [322, 321, 320, 224, 312, 311, 382, 379, 414, 411, 446, 443, 475, 491, 507, 527, 528],
        denture38: [319, 318, 317, 316, 315, 314, 381, 380, 413, 412, 445, 444, 476, 492, 508, 529, 530],
        denture41: [343, 342, 341, 292, 291, 290, 389, 372, 421, 404, 453, 436, 468, 484, 500, 519],
        denture42: [346, 345, 344, 289, 288, 287, 390, 371, 422, 403, 454, 435, 467, 483, 499, 518],
        denture43: [349, 348, 347, 286, 285, 284, 391, 370, 423, 402, 455, 434, 466, 482, 498, 517],
        denture44: [352, 351, 350, 283, 282, 281, 392, 369, 424, 401, 456, 433, 465, 481, 497, 516],
        denture45: [355, 354, 353, 280, 279, 278, 393, 368, 425, 400, 457, 432, 464, 480, 496, 515],
        denture46: [358, 357, 356, 277, 276, 275, 394, 367, 426, 399, 458, 431, 463, 479, 495, 513, 514],
        denture47: [361, 360, 359, 274, 273, 272, 395, 366, 427, 398, 459, 430, 462, 478, 494, 511, 512],
        denture48: [364, 363, 362, 271, 270, 269, 396, 365, 428, 397, 460, 429, 461, 477, 493, 509, 510],
      },
      missesData: {//牙缺失状态
        dentureStatus18: false,
        dentureStatus17: false,
        dentureStatus16: false,
        dentureStatus15: false,
        dentureStatus14: false,
        dentureStatus13: false,
        dentureStatus12: false,
        dentureStatus11: false,
        dentureStatus21: false,
        dentureStatus22: false,
        dentureStatus23: false,
        dentureStatus24: false,
        dentureStatus25: false,
        dentureStatus26: false,
        dentureStatus27: false,
        dentureStatus28: false,
        dentureStatus48: false,
        dentureStatus47: false,
        dentureStatus46: false,
        dentureStatus45: false,
        dentureStatus44: false,
        dentureStatus43: false,
        dentureStatus42: false,
        dentureStatus41: false,
        dentureStatus31: false,
        dentureStatus32: false,
        dentureStatus33: false,
        dentureStatus34: false,
        dentureStatus35: false,
        dentureStatus36: false,
        dentureStatus37: false,
        dentureStatus38: false,
      },
      tableCell: {},
      checklist: [],
      hoverIndex: '',//ya
      clickIndex1: false,
      // clickIndex1: '',
      clickIndex: false,
      clickColor: false,
      toothPosition: {//牙位
        occRelation: {
          deepOverbite: {//深复合
            toothPosition: "",
          },
          deepCover: {//深复盖
            toothPosition: "",
          },
          malocclusionCrowded: {//错合拥挤
            toothPosition: "",
          },
          toTheBlade: {//对刃合
            toothPosition: "",
          },

        },
        other: {
          crossBite: {//反合
            toothPosition: "",
          },
          migrationTooth: {//牙移位
            toothPosition: "",
          },
          foodImpaction: {//食物嵌塞
            toothPosition: "",
          },
          restorativeOverhanging: {//充填体悬突
            toothPosition: "",
          },
          movableTeeth: {//开合
            toothPosition: "",
          },
          decayed: {//龋齿
            toothPosition: "",
          },
          wedgeShapedDefect: {//楔形缺损
            toothPosition: "",
          },
          defectiveProsthesis: {//不良修复体
            toothPosition: "",
          },
        }
      },
      tabKey: "occRelation",
      typeKey: "deepOverbite",
      newKey: Math.random()//key值
    }
  }
  //牙石
  changeYS(value) {
    this.state.params.odontolith = value;
    //this.props.ok(this.state.odontolith, this.state.cleanStatus, this.state.examRemark, this.state.missesData, this.state.tdData, this.state.update, this.state.patientData, this.state.toothPosition, this.state.toothPosition);
  }
//洁治
  changeJZ(value) {
    this.state.params.cleanStatus = value;
    //this.props.ok(this.state.odontolith, this.state.cleanStatus, this.state.examRemark, this.state.missesData, this.state.tdData, this.state.update, this.state.patientData, this.state.toothPosition, this.state.toothPosition);
  }
  //检查描述
  changeMS(value) {
    this.state.params.examRemark = value;
    //this.props.ok(this.state.odontolith, this.state.cleanStatus, this.state.examRemark, this.state.missesData, this.state.tdData, this.state.update, this.state.patientData, this.state.toothPosition, this.state.toothPosition);
  }

  _objToStrMap(obj) {
    let strMap = new Map();
    if (obj) {
      for (let k of Object.keys(obj)) {
        strMap.set(k, obj[k]);
      }
    }
    return strMap;
  }
  //为空验证
  isEmptyObject(obj) {

    for (var key in obj) {
      return false
    }
    ;
    return true
  };
  //生命周期初始化
  componentDidMount() {

    this.props.onRef(this);
    this.state.update = false;

    let tdData = {};
    let toothPosition = {
        occRelation: {
          deepOverbite: {//深复合
            name: "深复合",
            toothPosition: "",
          },
          deepCover: {//深复盖
            name: "深复盖",
            toothPosition: "",
          },
          malocclusionCrowded: {//错合拥挤
            name: "错合拥挤",
            toothPosition: "",
          },
          toTheBlade: {//对刃合
            name: "对刃合",
            toothPosition: "",
          },

        },
        other: {
          crossBite: {//反合
            name: "反合",
            toothPosition: "",
          },
          migrationTooth: {//牙移位
            name: "牙移位",
            toothPosition: "",
          },
          foodImpaction: {//食物嵌塞
            name: "食物嵌塞",
            toothPosition: "",
          },
          restorativeOverhanging: {//充填体悬突
            name: "充填体悬突",
            toothPosition: "",
          },
          movableTeeth: {//开合
            name: "开合",
            toothPosition: "",
          },
          decayed: {//龋齿
            name: "龋齿",
            toothPosition: "",
          },
          wedgeShapedDefect: {//楔形缺损
            name: "楔形缺损",
            toothPosition: "",
          },
          defectiveProsthesis: {//不良修复体
            name: "不良修复体",
            toothPosition: "",
          },
        }
      };
    let missesData;
    let params;
    if(this.props.data){
      tdData = this.props.data.perChart.tdData;
      missesData = this.props.data.perChart.missesData
      toothPosition = this.props.data.toothDesc;
      params = this.props.data;
    }else{
      for (let i = 1; i < 530; i++) {
        let tdId = "td" + i;
        tdData[tdId] = ""
      }
      params = {};
      missesData = this.getMissesData();
    }
    this.setState(
      {
        params: params,
        tdData: tdData,
        missesData: missesData,
        toothPosition:toothPosition,
        newKey: Math.random()
      }
    )





    document.addEventListener('keydown', this.handleKeyDown);
  }
  //获取牙缺失状态
  getMissesData = ()=>{
    return {//牙缺失
      dentureStatus18: false,
      dentureStatus17: false,
      dentureStatus16: false,
      dentureStatus15: false,
      dentureStatus14: false,
      dentureStatus13: false,
      dentureStatus12: false,
      dentureStatus11: false,
      dentureStatus21: false,
      dentureStatus22: false,
      dentureStatus23: false,
      dentureStatus24: false,
      dentureStatus25: false,
      dentureStatus26: false,
      dentureStatus27: false,
      dentureStatus28: false,
      dentureStatus48: false,
      dentureStatus47: false,
      dentureStatus46: false,
      dentureStatus45: false,
      dentureStatus44: false,
      dentureStatus43: false,
      dentureStatus42: false,
      dentureStatus41: false,
      dentureStatus31: false,
      dentureStatus32: false,
      dentureStatus33: false,
      dentureStatus34: false,
      dentureStatus35: false,
      dentureStatus36: false,
      dentureStatus37: false,
      dentureStatus38: false,
    };
  }

  //获取口腔检查结果数据
  getResult=()=>{
    const {params} = this.state;
    params.perChart = {
      tdData: this.state.tdData,
      missesData: this.state.missesData
    }
    params.toothDesc=this.state.toothPosition;
    return params;
  }
  //生命周期初始化前
  componentWillUnmount() {
    document.removeEventListener('keydown', this.handleKeyDown);
  }
  //牙周检查表格点击事件
  handleClickTd = (tdId, nextTd, currentToothCol, nextDentitionNum) => {
    let isMisses = this.state.missesData["dentureStatus" + currentToothCol];
    if (!isMisses) {
      this.setState({
        activeTdId: tdId,
        nextActiveDentition: nextDentitionNum,
        currentToothCol: currentToothCol,
        nextTdId: "td" + (nextTd + 1),
        nextTdIndex: nextTd
      }, () => {
      })
    } else {
      let tdId = "td" + (nextTd + 1);
      this.handleClickTd(tdId, (nextTd + 1), this.getCurrentToothCol(nextTd + 1), this.getCurrentToothCol(nextTd + 2));
    }
    //this.props.ok(this.state.odontolith, this.state.cleanStatus, this.state.examRemark, this.state.missesData, this.state.tdData, this.state.update, this.state.patientData, this.state.toothPosition, this.state.toothPosition);
  }
  //每一格点击事件获取值
  getCurrentToothCol = (index) => {
    for (let i = 11; i <= 18; i++) {
      let array = this.state.toothCell["denture" + i];
      if (this.isInArray(array, index) != -1) {
        return i
      }
    }
    for (let i = 21; i <= 28; i++) {
      let array = this.state.toothCell["denture" + i];
      if (this.isInArray(array, index) != -1) {
        return i
      }
    }
    for (let i = 31; i <= 38; i++) {
      let array = this.state.toothCell["denture" + i];
      if (this.isInArray(array, index) != -1) {
        return i
      }
    }
    for (let i = 41; i <= 48; i++) {
      let array = this.state.toothCell["denture" + i];
      if (this.isInArray(array, index) != -1) {
        return i
      }
    }
  }
  isInArray = (array, index) => {
    return array.indexOf(index);
  }
  //牙周中按钮点击事件
  handleClick = (e, item) => {
    let tdId = this.state.activeTdId;
    this.state.tdData[tdId] = item.descripts;
    this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
  }
  //获取key状态值  动态id值
  handleKeyDown = (e) => {
    let keyCode = e.keyCode;
    let tdId = this.state.activeTdId;
    if (tdId !== "") {
      if (keyCode === 48 || keyCode === 96) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) === -1)
          this.state.tdData[tdId] = 0;
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 49 || keyCode === 97) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "Ⅰ";
        } else {
          this.state.tdData[tdId] = 1;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 50 || keyCode === 98) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "Ⅱ";
        } else {
          this.state.tdData[tdId] = 2;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 51 || keyCode === 99) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "Ⅲ";
        } else {
          this.state.tdData[tdId] = 3;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 52 || keyCode === 100) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "Ⅳ";
        } else {
          this.state.tdData[tdId] = 4;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 53 || keyCode === 101) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "";
        } else {
          this.state.tdData[tdId] = 5;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 54 || keyCode === 102) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "";
        } else {
          this.state.tdData[tdId] = 6;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 55 || keyCode === 103) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "";
        } else {
          this.state.tdData[tdId] = 7;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 56 || keyCode === 104) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "";
        } else {
          this.state.tdData[tdId] = 8;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 57 || keyCode === 105) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "";
        } else {
          this.state.tdData[tdId] = 9;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if(keyCode === 8){
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "";
        } else {
          this.state.tdData[tdId] = "";
        }
        this.setState({
          activeTdId:""
        })
        //this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex+1, this.getCurrentToothCol(this.state.nextTdIndex+1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
    }
  }
  //牙缺失
  toothMissesChange = (e, toothIndex) => {
    if (e.target.checked) {
      this.state.missesData["dentureStatus" + toothIndex] = true
    } else {
      this.state.missesData["dentureStatus" + toothIndex] = false
    }
    let missesTooth = this.state.missesTooth;

    if (this.isInArray(missesTooth, toothIndex) != -1) {
      let index = missesTooth.indexOf(toothIndex);
      missesTooth.splice(index, 1);
    } else {
      missesTooth.push(toothIndex);
    }
    this.setState({
      missesData: this.state.missesData,
      missesTooth: missesTooth
    }, () => {
      let td = this.state.toothCell["denture" + toothIndex];
      for (let i = 0; i < td.length; i++) {
        this.state.tdData["td" + td[i]] = "";
      }
      this.setState({
        tdData: this.state.tdData
      })
    })
    //this.props.ok(this.state.odontolith, this.state.cleanStatus, this.state.examRemark, this.state.missesData, this.state.tdData, this.state.update, this.state.patientData, this.state.toothPosition, this.state.toothPosition);
  }
  //保存选择牙位
  handleOkSel = (e) => {
    let {tabKey, typeKey} = this.state;

    this.state.toothPosition[tabKey][typeKey].toothPosition = this.tooth.getTooth();
    this.setState({
      toothPosition: this.state.toothPosition,
      visibleToothBit2: false,
    });

  };


  //取消弹框
  handleCancelSelTooth = () => {
    this.setState({
      visibleToothBit2: false,
    });
  };


  //展示选择检查的牙位
  showModalCheckTooth = (key1, key2) => {
    let toothPosition = this.state.toothPosition[key1][key2].toothPosition;

    this.setState({
      tabKey: key1,
      typeKey: key2,
      toothP: toothPosition,
      visibleToothBit2: true,
    });
    this.setState({
      visibletoothPosition: true,
    });
  }

  //取消弹框状态
  handleCancelSelTooth = () => {
    this.setState({
      visibleToothBit2: false,
    });
  };

  render() {
    //牙周表按钮内容
    const numberArr = [
      {
        descriptsSPCode: "00",
        descripts: "0",
        id: "0",
        descriptsEN: "zero",
        seq: "0"
      },
      {
        descriptsSPCode: "01",
        descripts: "1",
        id: "1",
        descriptsEN: "one",
        seq: "1"
      },
      {
        descriptsSPCode: "02",
        descripts: "2",
        id: "2",
        descriptsEN: "two",
        seq: "2"
      },
      {
        descriptsSPCode: "03",
        descripts: "3",
        id: "3",
        descriptsEN: "three",
        seq: "3"
      },
      {
        descriptsSPCode: "04",
        descripts: "4",
        id: "4",
        descriptsEN: "four",
        seq: "4"
      },
      {
        descriptsSPCode: "05",
        descripts: "5",
        id: "5",
        descriptsEN: "five",
        seq: "5"
      },
      {
        descriptsSPCode: "06",
        descripts: "6",
        id: "6",
        descriptsEN: "six",
        seq: "6"
      },
      {
        descriptsSPCode: "07",
        descripts: "7",
        id: "7",
        descriptsEN: "seven",
        seq: "7"
      },
      {
        descriptsSPCode: "08",
        descripts: "8",
        id: "8",
        descriptsEN: "eight",
        seq: "8"
      }, {
        descriptsSPCode: "09",
        descripts: "9",
        id: "9",
        descriptsEN: "nine",
        seq: "9"
      }, {
        descriptsSPCode: "10",
        descripts: "10",
        id: "10",
        descriptsEN: "ten",
        seq: "10"
      },
      {
        descriptsSPCode: "11",
        descripts: "11",
        id: "11",
        descriptsEN: "eleven",
        seq: "11"
      },
      {
        descriptsSPCode: "12",
        descripts: "12",
        id: "12",
        descriptsEN: "twelve",
        seq: "12"
      },
      {
        descriptsSPCode: "13",
        descripts: "13",
        id: "13",
        descriptsEN: "thirteen",
        seq: "13"
      },
      {
        descriptsSPCode: "14",
        descripts: "14",
        id: "14",
        descriptsEN: "fourteen",
        seq: "14"
      },
      {
        descriptsSPCode: "I",
        descripts: "I",
        id: "15",
        descriptsEN: "fifteen",
        seq: "15"
      },
      {
        descriptsSPCode: "II",
        descripts: "II",
        id: "16",
        descriptsEN: "sixteen",
        seq: "16"
      },
      {
        descriptsSPCode: "III",
        descripts: "III",
        id: "17",
        descriptsEN: "seventeen",
        seq: "17"
      },
      {
        descriptsSPCode: "IV",
        descripts: "IV",
        id: "18",
        descriptsEN: "eighteen",
        seq: "18"
      },
    ]
    //按钮展示
    let numberBlock = numberArr && numberArr.map((item, i) => {
      return (
        <button
          className="number-item"
          style={{
            WebkitUserSelect: "none",
            userSelect: "none",
            width: item.descripts == "0" && this.props.integer && this.props.integer ? "228px" : item.descripts == "0" ? "35px" : "35px",
            height: item.id == "c" ? "142px" : "40px",
            display: this.props.integer && this.props.integer && item.seq == "10" ? "none" : "inline-block",
            textAlign: item.descripts == "0" && !this.props.integer ? "center" : "center",
            paddingLeft: item.descripts == "0" && !this.props.integer ? "0" : "0",
            paddingRight: "0",
            border: "2px solid #6EB9B5",
            lineHeight: item.id == "c" ? "138px" : "34px",
            borderRadius: item.id == "c" ? "0 6px 6px 0" : "4px",
            position: item.id == "c" ? "absolute" : "static",
            top: item.id == "c" ? "74px" : "0",
            right: item.id == "c" ? "-14px" : "0",
            background: "#F5F5F5",
            color: "#6EB9B5",
            cursor: "pointer",
            margin: item.descriptsSPCode == "ok" ? "6px 4px" : "6px 4px",
            fontSize: "22px",
          }}
          key={i}
          onClick={(e) => this.handleClick(e, item)}
        >
          {item.descripts}
        </button>
      )
    })
    const {newKey} = this.state;

    return (
      <div key={newKey} style={{display:'flex'}}>
        <div style={{width:950}}>
          <div className={styles.all_content}>
            <div className={styles.header_infor}>
              <span style={{fontWeight: 'bold'}}>{this.state.rightPatientInfos.name?this.state.rightPatientInfos.name:""}</span>
              <span>
                {this.state.rightPatientInfos.sex==1?<img className={styles.infor_icon} src={sex}/>:this.state.rightPatientInfos.sex==2?<img className={styles.infor_icon} src={Nv}/>:null}
                {this.state.patientInfoDtos.birthday}
                {this.state.patientInfoDtos.age==null||this.state.patientInfoDtos.age==""?"":
                  /^[\u4e00-\u9fa5]+$/.test(this.state.patientInfoDtos.age)||this.state.patientInfoDtos.age.indexOf("岁") !== -1?
                    <>({this.state.patientInfoDtos.age})</>:
                    <>({this.state.patientInfoDtos.age}岁)</>
                }
              </span><span
              style={{fontWeight: 'bold'}}>{this.props.emrId}</span><span><img
              className={styles.infor_icon} src={Phone}
              alt=""/>{this.state.patientInfoDtos.oftenTel}{this.state.patientInfoDtos.oftenTelRelation?'('+this.state.patientInfoDtos.oftenTelRelation+')':""}</span>
            </div>
            <div style={{display: 'flex', marginTop: 10}}>
              <div className={styles.label}>牙石：</div>
              <Radio.Group
                defaultValue={this.state.params.odontolith}
                onChange={e => this.changeYS(e.target.value)}
              >
                <Radio value={1}>Ⅰ</Radio>
                <Radio value={2}>Ⅱ</Radio>
                <Radio value={3}>Ⅲ</Radio>
              </Radio.Group>
              <div style={{marginLeft: 15}} className={styles.label}>洁治：</div>
              <Radio.Group
                defaultValue={this.state.params.cleanStatus}
                onChange={e => this.changeJZ(e.target.value)}

              >
                <Radio value={1}>前</Radio>
                <Radio value={2}>后</Radio>
              </Radio.Group>
            </div>
            <div className={styles.toothMap}>
              <div className={styles.smallBox}>
                <span className={styles.leftTitle}>牙缺失</span>
                <Checkbox style={{"width": "55px", textAlign: "center"}}
                          checked={this.state.missesData.dentureStatus18}
                          onChange={(e, i) => this.toothMissesChange(e, 18)}>18</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus17}
                          onChange={(e, i) => this.toothMissesChange(e, 17)}>17</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus16}
                          onChange={(e, i) => this.toothMissesChange(e, 16)}>16</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus15}
                          onChange={(e, i) => this.toothMissesChange(e, 15)}>15</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus14}
                          onChange={(e, i) => this.toothMissesChange(e, 14)}>14</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus13}
                          onChange={(e, i) => this.toothMissesChange(e, 13)}>13</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus12}
                          onChange={(e, i) => this.toothMissesChange(e, 12)}>12</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus11}
                          onChange={(e, i) => this.toothMissesChange(e, 11)}>11</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: "6px"}}
                          checked={this.state.missesData.dentureStatus21}
                          onChange={(e, i) => this.toothMissesChange(e, 21)}>21</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus22}
                          onChange={(e, i) => this.toothMissesChange(e, 22)}>22</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus23}
                          onChange={(e, i) => this.toothMissesChange(e, 23)}>23</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus24}
                          onChange={(e, i) => this.toothMissesChange(e, 24)}>24</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus25}
                          onChange={(e, i) => this.toothMissesChange(e, 25)}>25</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus26}
                          onChange={(e, i) => this.toothMissesChange(e, 26)}>26</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus27}
                          onChange={(e, i) => this.toothMissesChange(e, 27)}>27</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus28}
                          onChange={(e, i) => this.toothMissesChange(e, 28)}>28</Checkbox>
              </div>
              <div className={styles.table_inner}>
                <div className={styles.table_box}>
                  <table className={styles.lineBlueLeft}>
                    <tbody>
                    <tr>
                      <th>FI</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>角化龈宽</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>溢脓</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>动度</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>PLI</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>龈缘CEJ</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>BI</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th style={{height: '80px'}} className={styles.pdl}>
                        <div>B</div>
                        <div>PD</div>
                        <div>L</div>
                      </th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th >
                        {/*style={{height: '61px'}}*/}
                        <table className={styles.abscissa}>
                          <tbody>
                          <tr>
                            <td className={styles.ableTd}>8</td>
                            <td className={styles.ableTd}>7</td>
                            <td className={styles.ableTd}>6</td>
                            <td className={styles.ableTd}>5</td>
                            <td className={styles.ableTd}>4</td>
                            <td className={styles.ableTd}>3</td>
                            <td className={styles.ableTd}>2</td>
                            <td className={styles.ableTd}>1</td>
                          </tr>
                          </tbody>
                        </table>
                        <table className={styles.abscissa} style={{left: 508}}>
                          <tbody>
                          <tr>
                            <td className={styles.ableTd}>1</td>
                            <td className={styles.ableTd}>2</td>
                            <td className={styles.ableTd}>3</td>
                            <td className={styles.ableTd}>4</td>
                            <td className={styles.ableTd}>5</td>
                            <td className={styles.ableTd}>6</td>
                            <td className={styles.ableTd}>7</td>
                            <td className={styles.ableTd}>8</td>
                          </tr>
                          </tbody>
                        </table>
                      </th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th style={{height: '57px',position: 'relative',top:' -12px'}} className={styles.lpd}>
                        <div>L</div>
                        <div>PD</div>
                        <div>B</div>
                      </th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>BI</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>龈缘CEJ</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>PLI</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>动度</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>溢脓</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>角化龈宽</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>FI</th>
                    </tr>
                    </tbody>
                  </table>
                  <div className={styles.lineBlueH} id="table1"></div>
                  <table className={styles.upperLeftTable} cellSpacing={0} cellPadding={0}>
                    <tbody>
                    <tr className={styles.trH40}>
                      <td colSpan={3}>
                        <div
                          className={[this.state.missesData.dentureStatus18 ? styles.divBorder2Misses : styles.divBorder2, this.state.activeTdId == "td1" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td1", 1, 18, 18)}>
                          {this.state.tdData.td1>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td1}</div>:this.state.tdData.td1}
                        </div>
                        <div
                          className={[this.state.activeTdId == "td2" ? styles.triangleActive : " ", this.state.missesData.dentureStatus18 ? styles.triangleMisses18 : styles.triangle18].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td2", 2, 18, 18)}><span
                          className={styles.word18}>{this.state.tdData.td2}</span></div>
                        <div className={styles.triangleBg18}></div>
                        {/*此行代码只是为了显示顶部三角形的边框，不可删除*/}

                        <div
                          className={[this.state.activeTdId == "td3" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.divBorder3Misses : styles.divBorder3].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td3", 3, 18, 17)}>
                          {this.state.tdData.td3>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td3}</div>:this.state.tdData.td3}
                        </div>
                      </td>
                      <td colSpan={3}>
                        <div
                          className={[this.state.missesData.dentureStatus17 ? styles.divBorder2Misses : styles.divBorder2, this.state.activeTdId == "td4" ? styles.triangleActive2 : " "].join(' ')}
                          id="td15" onClick={(e, i) => this.handleClickTd("td4", 4, 17, 17)}>
                          {this.state.tdData.td4>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td4}</div>:this.state.tdData.td4}
                        </div>
                        <div
                          className={[this.state.missesData.dentureStatus17 ? styles.triangleMisses17 : styles.triangle17, this.state.activeTdId == "td5" ? styles.triangleActive : " "].join(' ')}
                          id="td14" onClick={(e, i) => this.handleClickTd("td5", 5, 17, 17)}><span
                          className={styles.word17}>{this.state.tdData.td5}</span></div>
                        <div className={styles.triangleBg17}></div>
                        {/*此行代码只是为了显示顶部三角形的边框，不可删除*/}

                        <div
                          className={[this.state.missesData.dentureStatus17 ? styles.divBorder3Misses : styles.divBorder3, this.state.activeTdId == "td6" ? styles.triangleActive2 : " "].join(' ')}
                          id="td16" onClick={(e, i) => this.handleClickTd("td6", 6, 17, 16)}>
                          {this.state.tdData.td6>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td6}</div>:this.state.tdData.td6}
                        </div>
                      </td>
                      <td colSpan={3}>
                        <div
                          className={[this.state.missesData.dentureStatus16 ? styles.divBorder2Misses : styles.divBorder2, this.state.activeTdId == "td7" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td7", 7, 16, 16)}>
                          {this.state.tdData.td7>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td7}</div>:this.state.tdData.td7}
                        </div>
                        <div
                          className={[this.state.missesData.dentureStatus16 ? styles.triangleMisses16 : styles.triangle16, this.state.activeTdId == "td8" ? styles.triangleActive : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td8", 8, 16, 16)}><span
                          className={styles.word16}>{this.state.tdData.td8>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td8}</div>:this.state.tdData.td8}</span></div>
                        <div className={styles.triangleBg16}></div>
                        {/*此行代码只是为了显示顶部三角形的边框，不可删除*/}

                        <div
                          className={[this.state.missesData.dentureStatus16 ? styles.divBorder3Misses : styles.divBorder3, this.state.activeTdId == "td9" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td9", 9, 16, 15)}>
                          {this.state.tdData.td9>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td9}</div>:this.state.tdData.td9}
                        </div>
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td10" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td10", 10, 15, 14)}>
                        {this.state.tdData.td10>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td10}</div>:this.state.tdData.td10}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td11" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td11", 11, 14, 13)}>
                        {this.state.tdData.td11>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td11}</div>:this.state.tdData.td11}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td12" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td12", 13, 13, 12)}>
                        {this.state.tdData.td12>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td12}</div>:this.state.tdData.td12}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td13" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td13", 13, 12, 11)}>
                        {this.state.tdData.td13>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td13}</div>:this.state.tdData.td13}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td14" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td14", 14, 11, 21)}>
                        {this.state.tdData.td14>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td14}</div>:this.state.tdData.td14}
                      </td>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td29" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td29", 29, 18, 17)}>
                        {this.state.tdData.td29>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td29}</div>:this.state.tdData.td29}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td30" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td30", 30, 17, 16)}>
                        {this.state.tdData.td30>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td30}</div>:this.state.tdData.td30}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td31" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td31", 31, 16, 15)}>
                        {this.state.tdData.td31>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td31}</div>:this.state.tdData.td31}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td32" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td32", 32, 15, 14)}>
                        {this.state.tdData.td32>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td32}</div>:this.state.tdData.td32}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td33" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td33", 33, 14, 13)}>
                        {this.state.tdData.td33>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td33}</div>:this.state.tdData.td33}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td34" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td34", 34, 13, 12)}>
                        {this.state.tdData.td34>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td34}</div>:this.state.tdData.td34}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td35" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td35", 35, 12, 11)}>
                        {this.state.tdData.td35>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td35}</div>:this.state.tdData.td35}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td36" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td36", 36, 11, 21)}>
                        {this.state.tdData.td36>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td36}</div>:this.state.tdData.td36}
                      </td>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td45" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td45", 45, 18, 17)}>
                        {this.state.tdData.td45>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td45}</div>:this.state.tdData.td45}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td46" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td46", 46, 17, 16)}>
                        {this.state.tdData.td46>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td46}</div>:this.state.tdData.td46}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td47" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td47", 47, 16, 15)}>
                        {this.state.tdData.td47>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td47}</div>:this.state.tdData.td47}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td48" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td48", 48, 15, 16)}>
                        {this.state.tdData.td48>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td48}</div>:this.state.tdData.td48}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td49" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td49", 49, 14, 13)}>
                        {this.state.tdData.td49>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td49}</div>:this.state.tdData.td49}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td50" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td50", 50, 13, 12)}>
                        {this.state.tdData.td50>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td50}</div>:this.state.tdData.td50}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td51" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td51", 51, 12, 11)}>
                        {this.state.tdData.td51>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td51}</div>:this.state.tdData.td51}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td52" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td52", 52, 11, 21)}>
                        {this.state.tdData.td52>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td52}</div>:this.state.tdData.td52}
                      </td>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td61" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td61", 61, 18, 17)}>
                        {this.state.tdData.td61>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td61}</div>:this.state.tdData.td61}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td62" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td62", 62, 17, 16)}>
                        {this.state.tdData.td62>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td62}</div>:this.state.tdData.td62}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td63" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td63", 63, 16, 15)}>
                        {this.state.tdData.td63>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td63}</div>:this.state.tdData.td63}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td64" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td64", 64, 15, 14)}>
                        {this.state.tdData.td64>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td64}</div>:this.state.tdData.td64}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td65" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td65", 65, 14, 13)}>
                        {this.state.tdData.td65>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td65}</div>:this.state.tdData.td65}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td66" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td66", 66, 13, 12)}>
                        {this.state.tdData.td66>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td66}</div>:this.state.tdData.td66}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td67" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td67", 67, 12, 11)}>
                        {this.state.tdData.td67>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td67}</div>:this.state.tdData.td67}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td68" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td68", 68, 11, 21)}>
                        {this.state.tdData.td68>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td68}</div>:this.state.tdData.td68}
                      </td>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td77" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td77", 77, 18, 17)}>
                        {this.state.tdData.td77>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td77}</div>:this.state.tdData.td77}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td78" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td78", 78, 17, 16)}>
                        {this.state.tdData.td78>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td78}</div>:this.state.tdData.td78}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td79" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td79", 79, 16, 15)}>
                        {this.state.tdData.td79>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td79}</div>:this.state.tdData.td79}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td80" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td80", 80, 15, 14)}>
                        {this.state.tdData.td80>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td80}</div>:this.state.tdData.td80}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td81" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td81", 81, 14, 13)}>
                        {this.state.tdData.td81>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td81}</div>:this.state.tdData.td81}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td82" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td82", 82, 13, 12)}>
                        {this.state.tdData.td82>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td82}</div>:this.state.tdData.td82}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td83" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td83", 83, 12, 11)}>
                        {this.state.tdData.td83>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td83}</div>:this.state.tdData.td83}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td84" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td84", 84, 11, 21)}>
                        {this.state.tdData.td84>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td84}</div>:this.state.tdData.td84}
                      </td>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td108" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td108", 108, 18, 17)}>
                        {this.state.tdData.td108>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td108}</div>:this.state.tdData.td108}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td107" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td107", 107, 17, 16)}>
                        {this.state.tdData.td107>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td107}</div>:this.state.tdData.td107}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td106" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td106", 106, 16, 15)}>
                        {this.state.tdData.td106>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td106}</div>:this.state.tdData.td106}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td105" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td105", 105, 15, 14)}>
                        {this.state.tdData.td105>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td105}</div>:this.state.tdData.td105}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td104" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td104", 104, 14, 13)}>
                        {this.state.tdData.td104>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td104}</div>:this.state.tdData.td104}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td103" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td103", 103, 13, 12)}>
                        {this.state.tdData.td103>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td103}</div>:this.state.tdData.td103}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td102" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td102", 102, 12, 11)}>
                        {this.state.tdData.td102>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td102}</div>:this.state.tdData.td102}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td101" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td101", 101, 11, 21)}>
                        {this.state.tdData.td101>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td101}</div>:this.state.tdData.td101}
                      </td>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td109" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td109", 109, 18, 17)}>
                        {this.state.tdData.td109>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td109}</div>:this.state.tdData.td109}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td110" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td110", 110, 17, 16)}>
                        {this.state.tdData.td110>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td110}</div>:this.state.tdData.td110}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td111" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td111", 111, 16, 15)}>
                        {this.state.tdData.td111>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td111}</div>:this.state.tdData.td111}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td112" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td112", 112, 15, 14)}>
                        {this.state.tdData.td112>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td112}</div>:this.state.tdData.td112}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td113" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td113", 113, 14, 13)}>
                        {this.state.tdData.td113>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td113}</div>:this.state.tdData.td113}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td114" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td114", 114, 13, 12)}>
                        {this.state.tdData.td114>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td114}</div>:this.state.tdData.td114}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td115" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td115", 115, 12, 11)}>
                        {this.state.tdData.td115>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td115}</div>:this.state.tdData.td115}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td116" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td116", 116, 11, 21)}>
                        {this.state.tdData.td116>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td116}</div>:this.state.tdData.td116}
                      </td>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td140" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td140", 140, 18, 17)}>
                        {this.state.tdData.td140>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td140}</div>:this.state.tdData.td140}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td139" ? styles.triangleActive2 : " ",].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td139", 139, 17, 16)}>
                        {this.state.tdData.td139>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td139}</div>:this.state.tdData.td139}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td138" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td138", 138, 16, 15)}>
                        {this.state.tdData.td138>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td138}</div>:this.state.tdData.td138}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td137" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td137", 137, 15, 14)}>
                        {this.state.tdData.td137>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td137}</div>:this.state.tdData.td137}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td136" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td136", 136, 14, 13)}>
                        {this.state.tdData.td136>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td136}</div>:this.state.tdData.td136}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td135" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td135", 135, 13, 12)}>
                        {this.state.tdData.td135>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td135}</div>:this.state.tdData.td135}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td134" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td134", 134, 12, 11)}>
                        {this.state.tdData.td134>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td134}</div>:this.state.tdData.td134}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td133" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td133", 133, 11, 21)}>
                        {this.state.tdData.td133>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td133}</div>:this.state.tdData.td133}
                      </td>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td141" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td141", 141, 18, 17)}>
                        {this.state.tdData.td141>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td141}</div>:this.state.tdData.td141}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td142" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td142", 142, 17, 16)}>
                        {this.state.tdData.td142>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td142}</div>:this.state.tdData.td142}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td143" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td143", 143, 16, 15)}>
                        {this.state.tdData.td143>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td143}</div>:this.state.tdData.td143}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td144" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td144", 144, 15, 14)}>
                        {this.state.tdData.td144>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td144}</div>:this.state.tdData.td144}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td145" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td145", 145, 14, 13)}>
                        {this.state.tdData.td145>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td145}</div>:this.state.tdData.td145}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td146" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td146", 146, 13, 12)}>
                        {this.state.tdData.td146>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td146}</div>:this.state.tdData.td146}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td147" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td147", 147, 12, 11)}>
                        {this.state.tdData.td147>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td147}</div>:this.state.tdData.td147}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td148" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td148", 148, 11, 21)}>
                        {this.state.tdData.td148>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td148}</div>:this.state.tdData.td148}
                      </td>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td172" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td172", 172, 18, 18)}>
                        {this.state.tdData.td172>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td172}</div>:this.state.tdData.td172}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td171" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td171", 171, 17, 18)}>
                        {this.state.tdData.td171>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td171}</div>:this.state.tdData.td171}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td170" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td170", 170, 16, 17)}>
                        {this.state.tdData.td170>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td170}</div>:this.state.tdData.td170}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td169" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td169", 169, 15, 16)}>
                        {this.state.tdData.td169>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td169}</div>:this.state.tdData.td169}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td168" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td168", 168, 14, 15)}>
                        {this.state.tdData.td168>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td168}</div>:this.state.tdData.td168}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td167" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td167", 167, 13, 14)}>
                        {this.state.tdData.td167>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td167}</div>:this.state.tdData.td167}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td166" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td166", 166, 12, 13)}>
                        {this.state.tdData.td166>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td166}</div>:this.state.tdData.td166}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td165" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td165", 165, 11, 12)}>
                        {this.state.tdData.td165>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td165}</div>:this.state.tdData.td165}
                      </td>
                    </tr>
                    </tbody>

                    <tbody>
                    <tr>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td173" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td173", 173, 18, 18)}>
                        {this.state.tdData.td173>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td173}</div>:this.state.tdData.td173}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td174" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td174", 174, 18, 18)}>
                        {this.state.tdData.td174>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td174}</div>:this.state.tdData.td174}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td175" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td175", 175, 18, 17)}>
                        {this.state.tdData.td175>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td175}</div>:this.state.tdData.td175}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td176" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td176", 176, 17, 17)}>
                        {this.state.tdData.td176>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td176}</div>:this.state.tdData.td176}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td177" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td177", 177, 17, 17)}>
                        {this.state.tdData.td177>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td177}</div>:this.state.tdData.td177}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td178" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td178", 178, 17, 16)}>
                        {this.state.tdData.td178>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td178}</div>:this.state.tdData.td178}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td179" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus16 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td179", 179, 16, 16)}>
                        {this.state.tdData.td179>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td179}</div>:this.state.tdData.td179}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td180" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus16 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td180", 180, 16, 16)}>
                        {this.state.tdData.td180>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td180}</div>:this.state.tdData.td180}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td181" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus16 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td181", 181, 16, 15)}>
                        {this.state.tdData.td181>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td181}</div>:this.state.tdData.td181}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td182" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus15 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td182", 182, 15, 15)}>
                        {this.state.tdData.td182>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td182}</div>:this.state.tdData.td182}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td183" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus15 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td183", 183, 15, 15)}>
                        {this.state.tdData.td183>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td183}</div>:this.state.tdData.td183}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td184" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus15 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td184", 184, 15, 14)}>
                        {this.state.tdData.td184>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td184}</div>:this.state.tdData.td184}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td185" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus14 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td185", 185, 14, 14)}>
                        {this.state.tdData.td185>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td185}</div>:this.state.tdData.td185}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td186" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus14 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td186", 186, 14, 14)}>
                        {this.state.tdData.td186>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td186}</div>:this.state.tdData.td186}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td187" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus14 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td187", 187, 14, 13)}>
                        {this.state.tdData.td187>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td187}</div>:this.state.tdData.td187}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td188" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus13 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td188", 188, 13, 13)}>
                        {this.state.tdData.td188>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td188}</div>:this.state.tdData.td188}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td189" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus13 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td189", 189, 13, 13)}>
                        {this.state.tdData.td189>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td189}</div>:this.state.tdData.td189}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td190" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus13 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td190", 190, 13, 12)}>
                        {this.state.tdData.td190>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td190}</div>:this.state.tdData.td190}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td191" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus12 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td191", 191, 12, 12)}>
                        {this.state.tdData.td191>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td191}</div>:this.state.tdData.td191}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td192" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus12 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td192", 192, 12, 12)}>
                        {this.state.tdData.td192>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td192}</div>:this.state.tdData.td192}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td193" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus12 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td193", 193, 12, 11)}>
                        {this.state.tdData.td193>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td193}</div>:this.state.tdData.td193}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td194" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus11 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td194", 194, 11, 11)}>
                        {this.state.tdData.td194>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td194}</div>:this.state.tdData.td194}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td195" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus11 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td195", 195, 11, 11)}>
                        {this.state.tdData.td195>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td195}</div>:this.state.tdData.td195}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td196" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus11 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td196", 196, 11, 21)}>
                        {this.state.tdData.td196>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td196}</div>:this.state.tdData.td196}
                      </td>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td268" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td268", 268, 18, 18)}>
                        {this.state.tdData.td268>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td268}</div>:this.state.tdData.td268}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td267" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td267", 267, 18, 18)}>
                        {this.state.tdData.td267>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td267}</div>:this.state.tdData.td267}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td266" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td266", 266, 18, 18)}>
                        {this.state.tdData.td266>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td266}</div>:this.state.tdData.td266}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td265" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td265", 265, 17, 18)}>
                        {this.state.tdData.td265>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td265}</div>:this.state.tdData.td265}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td264" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td264", 264, 17, 17)}>
                        {this.state.tdData.td264>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td264}</div>:this.state.tdData.td264}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td263" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td263", 263, 17, 17)}>
                        {this.state.tdData.td263>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td263}</div>:this.state.tdData.td263}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td262" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus16 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td262", 262, 16, 17)}>
                        {this.state.tdData.td262>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td262}</div>:this.state.tdData.td262}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td261" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus16 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td261", 261, 16, 16)}>
                        {this.state.tdData.td261>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td261}</div>:this.state.tdData.td261}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td260" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus16 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td260", 260, 16, 16)}>
                        {this.state.tdData.td260>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td260}</div>:this.state.tdData.td260}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td259" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus15 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td259", 259, 15, 16)}>
                        {this.state.tdData.td259>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td259}</div>:this.state.tdData.td259}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td258" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus15 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td258", 258, 15, 15)}>
                        {this.state.tdData.td258>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td258}</div>:this.state.tdData.td258}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td257" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus15 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td257", 257, 15, 15)}>
                        {this.state.tdData.td257>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td257}</div>:this.state.tdData.td257}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td256" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus14 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td256", 256, 14, 15)}>
                        {this.state.tdData.td256>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td256}</div>:this.state.tdData.td256}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td255" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus14 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td255", 255, 14, 14)}>
                        {this.state.tdData.td255>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td255}</div>:this.state.tdData.td255}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td254" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus14 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td254", 254, 14, 14)}>
                        {this.state.tdData.td254>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td254}</div>:this.state.tdData.td254}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td253" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus13 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td253", 253, 13, 14)}>
                        {this.state.tdData.td253>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td253}</div>:this.state.tdData.td253}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td252" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus13 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td252", 252, 13, 13)}>
                        {this.state.tdData.td252>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td252}</div>:this.state.tdData.td252}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td251" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus13 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td251", 251, 13, 13)}>
                        {this.state.tdData.td251>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td251}</div>:this.state.tdData.td251}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td250" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus12 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td250", 250, 12, 13)}>
                        {this.state.tdData.td250>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td250}</div>:this.state.tdData.td250}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td249" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus12 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td249", 249, 12, 12)}>
                        {this.state.tdData.td249>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td249}</div>:this.state.tdData.td249}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td248" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus12 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td248", 248, 12, 12)}>
                        {this.state.tdData.td248>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td248}</div>:this.state.tdData.td248}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td247" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus11 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td247", 247, 11, 12)}>
                        {this.state.tdData.td247>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td247}</div>:this.state.tdData.td247}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td246" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus11 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td246", 246, 11, 11)}>
                        {this.state.tdData.td246>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td246}</div>:this.state.tdData.td246}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td245" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus11 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td245", 245, 11, 11)}>
                        {this.state.tdData.td245>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td245}</div>:this.state.tdData.td245}
                      </td>
                    </tr>
                    </tbody>
                  </table>

                  <table className={styles.upperRightTable} cellSpacing={0} cellPadding={0}>
                    {/*<tbody>*/}
                    <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[, this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td15" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td15", 15, 21, 22)}>
                        {this.state.tdData.td15>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td15}</div>:this.state.tdData.td15}
                      </td>
                      <td colSpan={3}
                          className={[, this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td16" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td16", 16, 22, 23)}>
                        {this.state.tdData.td16>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td16}</div>:this.state.tdData.td16}
                      </td>
                      <td colSpan={3}
                          className={[, this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td17" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td17", 17, 23, 24)}>
                        {this.state.tdData.td17>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td17}</div>:this.state.tdData.td17}
                      </td>
                      <td colSpan={3}
                          className={[, this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td18" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td18", 18, 24, 25)}>
                        {this.state.tdData.td18>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td18}</div>:this.state.tdData.td18}
                      </td>
                      <td colSpan={3}
                          className={[, this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td19" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td19", 19, 25, 26)}>
                        {this.state.tdData.td19>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td19}</div>:this.state.tdData.td19}
                      </td>
                      <td colSpan={3}>
                        <div
                          className={[this.state.missesData.dentureStatus26 ? styles.divBorder2Misses : styles.divBorder2, this.state.activeTdId == "td20" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td20", 20, 26, 26)}>
                          {this.state.tdData.td20>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td20}</div>:this.state.tdData.td20}
                        </div>
                        <div
                          className={[this.state.missesData.dentureStatus26 ? styles.triangleMisses26 : styles.triangle26, this.state.activeTdId == "td21" ? styles.triangleActive : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td21", 21, 26, 26)}><span
                          className={styles.word26}>{this.state.tdData.td21}</span></div>
                        <div className={styles.triangleBg26}></div>
                        {/*此行代码只是为了显示顶部三角形的边框，不可删除*/}

                        <div
                          className={[this.state.missesData.dentureStatus26 ? styles.divBorder3Misses : styles.divBorder3, this.state.activeTdId == "td22" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td22", 22, 26, 27)}>
                          {this.state.tdData.td22>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td22}</div>:this.state.tdData.td22}
                        </div>
                      </td>
                      <td colSpan={3}>
                        <div
                          className={[this.state.missesData.dentureStatus27 ? styles.divBorder2Misses : styles.divBorder2, this.state.activeTdId == "td23" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td23", 23, 27, 27)}>
                          {this.state.tdData.td23>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td23}</div>:this.state.tdData.td23}
                        </div>
                        <div
                          className={[this.state.missesData.dentureStatus27 ? styles.triangleMisses27 : styles.triangle27, this.state.activeTdId == "td24" ? styles.triangleActive : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td24", 24, 27, 27)}><span
                          className={styles.word27}>{this.state.tdData.td24}</span></div>
                        <div className={styles.triangleBg27}></div>
                        {/*此行代码只是为了显示顶部三角形的边框，不可删除*/}

                        <div
                          className={[this.state.missesData.dentureStatus27 ? styles.divBorder3Misses : styles.divBorder3, this.state.activeTdId == "td25" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td25", 25, 27, 26)}>
                          {this.state.tdData.td25>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td25}</div>:this.state.tdData.td25}
                        </div>
                      </td>
                      <td colSpan={3}>
                        <div
                          className={[this.state.missesData.dentureStatus28 ? styles.divBorder2Misses : styles.divBorder2, this.state.activeTdId == "td26" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td26", 26, 28, 28)}>
                          {this.state.tdData.td26>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td26}</div>:this.state.tdData.td26}
                        </div>
                        <div
                          className={[this.state.missesData.dentureStatus28 ? styles.triangleMisses28 : styles.triangle28, this.state.activeTdId == "td27" ? styles.triangleActive : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td27", 27, 28, 28)}><span
                          className={styles.word28}>{this.state.tdData.td27}</span></div>
                        <div className={styles.triangleBg28}></div>
                        {/*此行代码只是为了显示顶部三角形的边框，不可删除*/}

                        <div
                          className={[this.state.missesData.dentureStatus28 ? styles.divBorder3Misses : styles.divBorder3, this.state.activeTdId == "td28" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td28", 28, 28, 18)}>
                          {this.state.tdData.td28>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td28}</div>:this.state.tdData.td28}
                        </div>
                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td37" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td37", 37, 21, 22)}>
                        {this.state.tdData.td37>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td37}</div>:this.state.tdData.td37}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td38" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td38", 38, 22, 23)}>
                        {this.state.tdData.td38>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td38}</div>:this.state.tdData.td38}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td39" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td39", 39, 23, 24)}>
                        {this.state.tdData.td39>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td39}</div>:this.state.tdData.td39}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td40" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td40", 40, 24, 25)}>
                        {this.state.tdData.td40>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td40}</div>:this.state.tdData.td40}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td41" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td41", 41, 25, 26)}>
                        {this.state.tdData.td41>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td41}</div>:this.state.tdData.td41}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td42" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td42", 42)}>
                        {this.state.tdData.td42>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td42}</div>:this.state.tdData.td42}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td43" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td43", 43, 27, 28)}>
                        {this.state.tdData.td43>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td43}</div>:this.state.tdData.td43}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td44" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td44", 44, 28, 18)}>
                        {this.state.tdData.td44>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td44}</div>:this.state.tdData.td44}
                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td53" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td53", 53, 21, 22)}>
                        {this.state.tdData.td53>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td53}</div>:this.state.tdData.td53}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td54" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td54", 54, 22, 23)}>
                        {this.state.tdData.td54>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td54}</div>:this.state.tdData.td54}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td55" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td55", 55, 23, 24)}>
                        {this.state.tdData.td55>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td55}</div>:this.state.tdData.td55}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td56" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td56", 56, 24, 25)}>
                        {this.state.tdData.td56>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td56}</div>:this.state.tdData.td56}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td57" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td57", 57, 25, 26)}>
                        {this.state.tdData.td57>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td57}</div>:this.state.tdData.td57}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td58" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td58", 58, 26, 27)}>
                        {this.state.tdData.td58>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td58}</div>:this.state.tdData.td58}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td59" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td59", 59, 27, 28)}>
                        {this.state.tdData.td59>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td59}</div>:this.state.tdData.td59}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td60" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td60", 60, 28, 18)}>
                        {this.state.tdData.td60>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td60}</div>:this.state.tdData.td60}
                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td69" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td69", 69, 21, 22)}>
                        {this.state.tdData.td69>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td69}</div>:this.state.tdData.td69}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td70" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td70", 70, 22, 23)}>
                        {this.state.tdData.td70>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td70}</div>:this.state.tdData.td70}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td71" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td71", 71, 23, 24)}>
                        {this.state.tdData.td71>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td71}</div>:this.state.tdData.td71}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td72" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td72", 24, 25)}>
                        {this.state.tdData.td72>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td72}</div>:this.state.tdData.td72}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td73" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td73", 73, 25, 26)}>
                        {this.state.tdData.td73>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td73}</div>:this.state.tdData.td73}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td74" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td74", 74, 26, 27)}>
                        {this.state.tdData.td74>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td74}</div>:this.state.tdData.td74}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td75" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td75", 75, 27, 28)}>
                        {this.state.tdData.td75>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td75}</div>:this.state.tdData.td75}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td76" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td76", 76, 28, 18)}>
                        {this.state.tdData.td76>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td76}</div>:this.state.tdData.td76}
                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td85" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td85", 85, 21, 22)}>
                        {this.state.tdData.td85>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td85}</div>:this.state.tdData.td85}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td86" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td86", 86, 22, 23)}>
                        {this.state.tdData.td86>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td86}</div>:this.state.tdData.td86}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td87" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td87", 87, 23, 24)}>
                        {this.state.tdData.td87>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td87}</div>:this.state.tdData.td87}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td88" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td88", 88, 24, 25)}>
                        {this.state.tdData.td88>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td88}</div>:this.state.tdData.td88}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td89" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td89", 89, 25, 26)}>
                        {this.state.tdData.td89>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td89}</div>:this.state.tdData.td89}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td90" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td90", 90, 26, 27)}>
                        {this.state.tdData.td90>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td90}</div>:this.state.tdData.td90}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td91" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td91", 91, 27, 28)}>
                        {this.state.tdData.td91>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td91}</div>:this.state.tdData.td91}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td92" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td92", 92, 28, 18)}>
                        {this.state.tdData.td92>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td92}</div>:this.state.tdData.td92}
                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td100" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td100", 100, 21, 22)}>
                        {this.state.tdData.td100>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td100}</div>:this.state.tdData.td100}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td99" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td99", 99, 22, 23)}>
                        {this.state.tdData.td99>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td99}</div>:this.state.tdData.td99
                        }
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td98" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td98", 98, 23, 24)}>
                        {this.state.tdData.td98>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td98}</div>:this.state.tdData.td98}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td97" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td97", 97, 24, 25)}>
                        {this.state.tdData.td97>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td97}</div>:this.state.tdData.td97}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td96" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td96", 96, 25, 26)}>
                        {this.state.tdData.td96>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td96}</div>:this.state.tdData.td96}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td95" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td95", 95, 26, 27)}>
                        {this.state.tdData.td95>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td95}</div>:this.state.tdData.td95}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td94" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td94", 94, 27, 28)}>
                        {this.state.tdData.td94>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td94}</div>:this.state.tdData.td94}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td93" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td93", 93, 28, 28)}>
                        {this.state.tdData.td93>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td93}</div>:this.state.tdData.td93}
                      </td>
                    </tr>
                    {/*</tbody>*/}


                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td117" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td117", 117, 21, 22)}>
                        {this.state.tdData.td117>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td117}</div>:this.state.tdData.td117}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td118" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td118", 118, 22, 23)}>
                        {this.state.tdData.td118>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td118}</div>:this.state.tdData.td118}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td119" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td119", 119, 23, 24)}>
                        {this.state.tdData.td119>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td119}</div>:this.state.tdData.td119}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td120" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td120", 120, 24, 25)}>
                        {this.state.tdData.td120>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td120}</div>:this.state.tdData.td120}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td121" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td121", 121, 25, 26)}>
                        {this.state.tdData.td121>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td121}</div>:this.state.tdData.td121}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td122" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td122", 122, 26, 27)}>
                        {this.state.tdData.td122>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td122}</div>:this.state.tdData.td122}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td123" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td123", 123, 27, 28)}>
                        {this.state.tdData.td123>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td123}</div>:this.state.tdData.td123}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td124" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td124", 124, 28, 28)}>
                        {this.state.tdData.td124>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td124}</div>:this.state.tdData.td124}
                      </td>
                    </tr>

                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td132" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td132", 132, 21, 11)}>
                        {this.state.tdData.td132>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td132}</div>:this.state.tdData.td132}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td131" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td131", 131, 22, 21)}>
                        {this.state.tdData.td131>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td131}</div>:this.state.tdData.td131}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td130" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td130", 130, 23, 22)}>
                        {this.state.tdData.td130>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td130}</div>:this.state.tdData.td130}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td129" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td129", 129, 24, 23)}>
                        {this.state.tdData.td129>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td129}</div>:this.state.tdData.td129}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td128" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td128", 128, 25, 24)}>
                        {this.state.tdData.td128>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td128}</div>:this.state.tdData.td128}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td127" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td127", 127, 26, 25)}>
                        {this.state.tdData.td127>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td127}</div>:this.state.tdData.td127}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td126" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td126", 126, 27, 26)}>
                        {this.state.tdData.td126>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td126}</div>:this.state.tdData.td126}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td125" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td125", 125, 28, 27)}>
                        {this.state.tdData.td125>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td125}</div>:this.state.tdData.td125}
                      </td>
                    </tr>
                    {/*</tbody>*/}


                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td149" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td149", 149, 21, 22)}>
                        {this.state.tdData.td149>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td149}</div>:this.state.tdData.td149}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td150" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td150", 150, 22, 23)}>
                        {this.state.tdData.td150>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td150}</div>:this.state.tdData.td150}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td151" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td151", 151, 23, 24)}>
                        {this.state.tdData.td151>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td151}</div>:this.state.tdData.td151}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td152" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td152", 152, 24, 25)}>
                        {this.state.tdData.td152>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td152}</div>:this.state.tdData.td152}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td153" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td153", 153, 25, 26)}>
                        {this.state.tdData.td153>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td153}</div>:this.state.tdData.td153}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td154" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td154", 154, 26, 27)}>
                        {this.state.tdData.td154>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td154}</div>:this.state.tdData.td154}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td155" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td155", 155, 27, 28)}>
                        {this.state.tdData.td155>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td155}</div>:this.state.tdData.td155}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td156" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td156", 156, 28, 28)}>
                        {this.state.tdData.td156>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td156}</div>:this.state.tdData.td156}
                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td164" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td164", 164, 21, 11)}>
                        {this.state.tdData.td164>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td164}</div>:this.state.tdData.td164}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td163" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td163", 163, 22, 21)}>
                        {this.state.tdData.td163>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td163}</div>:this.state.tdData.td163}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td162" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td162", 162, 23, 22)}>
                        {this.state.tdData.td162>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td162}</div>:this.state.tdData.td162}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td161" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td161", 161, 24, 23)}>
                        {this.state.tdData.td161>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td161}</div>:this.state.tdData.td161}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td160" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td160", 160, 25, 24)}>
                        {this.state.tdData.td160>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td160}</div>:this.state.tdData.td160}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td159" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td159", 159, 26, 25)}>
                        {this.state.tdData.td159>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td159}</div>:this.state.tdData.td159}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td158" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td158", 158, 27, 26)}>
                        {this.state.tdData.td158>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td158}</div>:this.state.tdData.td158}
                      </td>
                      <td colSpan={3}
                          className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td157" ? styles.triangleActive2 : " "].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td157", 157, 28, 27)}>
                        {this.state.tdData.td157>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td157}</div>:this.state.tdData.td157}
                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td197" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus21 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td197", 197, 21, 21)}>
                        {this.state.tdData.td197>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td197}</div>:this.state.tdData.td197}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td198" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus21 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td198", 198, 21, 21)}>
                        {this.state.tdData.td198>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td198}</div>:this.state.tdData.td198}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td199" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus21 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td199", 199, 21, 22)}>
                        {this.state.tdData.td199>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td199}</div>:this.state.tdData.td199}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td200" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus22 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td200", 200, 22, 22)}>
                        {this.state.tdData.td200>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td200}</div>:this.state.tdData.td200}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td201" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus22 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td201", 201, 22, 22)}>
                        {this.state.tdData.td201>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td201}</div>:this.state.tdData.td201}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td202" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus22 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td202", 202, 22, 23)}>
                        {this.state.tdData.td202>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td202}</div>:this.state.tdData.td202}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td203" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus23 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td203", 203, 23, 23)}>
                        {this.state.tdData.td203>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td203}</div>:this.state.tdData.td203 }
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td204" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus23 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td204", 204, 23, 23)}>
                        {this.state.tdData.td204>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td204}</div>:this.state.tdData.td204}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td205" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus23 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td205", 205, 23, 24)}>
                        {this.state.tdData.td205>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td205}</div>:this.state.tdData.td205}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td206" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus24 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td206", 206, 24, 24)}>
                        {this.state.tdData.td206>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td206}</div>:this.state.tdData.td206}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td207" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus24 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td207", 207, 24, 24)}>
                        {this.state.tdData.td207>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td207}</div>:this.state.tdData.td207}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td208" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus24 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td208", 208, 24, 25)}>
                        {this.state.tdData.td208>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td208}</div>:this.state.tdData.td208}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td209" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus25 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td209", 209, 25, 25)}>
                        {this.state.tdData.td209>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td209}</div>:this.state.tdData.td209}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td210" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus25 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td210", 210, 25, 25)}>
                        {this.state.tdData.td210>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td210}</div>:this.state.tdData.td210}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td211" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus25 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td211", 211, 25, 26)}>
                        {this.state.tdData.td211>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td211}</div>:this.state.tdData.td211}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td212" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus26 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td212", 212, 26, 26)}>
                        {this.state.tdData.td212>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td212}</div>:this.state.tdData.td212}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td213" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus26 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td213", 213, 26, 26)}>
                        {this.state.tdData.td213>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td213}</div>:this.state.tdData.td213}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td214" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus26 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td214", 214, 26, 27)}>
                        {this.state.tdData.td214>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td214}</div>:this.state.tdData.td214}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td215" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus27 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td215", 215, 27, 27)}>
                        {this.state.tdData.td215>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td215}</div>:this.state.tdData.td215}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td216" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus27 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td216", 216, 27, 27)}>
                        {this.state.tdData.td216>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td216}</div>:this.state.tdData.td216}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td217" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus27 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td217", 217, 27, 28)}>
                        {this.state.tdData.td217>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td217}</div>:this.state.tdData.td217}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td218" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus28 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td218", 218, 28, 28)}>
                        {this.state.tdData.td218>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td218}</div>:this.state.tdData.td218}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td219" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus28 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td219", 219, 28, 28)}>
                        {this.state.tdData.td219>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td219}</div>:this.state.tdData.td219}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td220" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus28 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td220", 220, 28, 28)}>
                        {this.state.tdData.td220>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td220}</div>:this.state.tdData.td220}
                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td244" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus21 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td244", 244, 21, 11)}>
                        {this.state.tdData.td244>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td244}</div>:this.state.tdData.td244}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td243" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus21 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td243", 243, 21, 21)}>
                        {this.state.tdData.td243>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td243}</div>:this.state.tdData.td243}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td242" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus21 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td242", 242, 21, 21)}>
                        {this.state.tdData.td242>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td242}</div>:this.state.tdData.td242}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td241" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus22 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td241", 241, 22, 21)}>
                        {this.state.tdData.td241>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td241}</div>:this.state.tdData.td241}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td240" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus22 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td240", 240, 22, 22)}>
                        {this.state.tdData.td240>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td240}</div>:this.state.tdData.td240}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td239" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus22 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td239", 239, 22, 22)}>
                        {this.state.tdData.td239>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td239}</div>:this.state.tdData.td239}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td238" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus23 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td238", 238, 23, 22)}>
                        {this.state.tdData.td238>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td238}</div>:this.state.tdData.td238}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td237" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus23 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td237", 237, 23, 23)}>
                        {this.state.tdData.td237>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td237}</div>:this.state.tdData.td237}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td236" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus23 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td236", 236, 23, 23)}>
                        {this.state.tdData.td236>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td236}</div>:this.state.tdData.td236}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td235" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus24 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td235", 235, 24, 23)}>
                        {this.state.tdData.td235>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td235}</div>:this.state.tdData.td235}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td234" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus24 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td234", 234, 24, 24)}>
                        {this.state.tdData.td234>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td234}</div>:this.state.tdData.td234}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td233" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus24 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td233", 233, 24, 23)}>
                        {this.state.tdData.td233>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td233}</div>:this.state.tdData.td233}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td232" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus25 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td232", 232, 25, 24)}>
                        {this.state.tdData.td232>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td232}</div>:this.state.tdData.td232}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td231" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus25 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td231", 231, 25, 25)}>
                        {this.state.tdData.td231>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td231}</div>:this.state.tdData.td231}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td230" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus25 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td230", 230, 25, 25)}>
                        {this.state.tdData.td230>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td230}</div>:this.state.tdData.td230}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td229" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus26 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td229", 229, 26, 25)}>
                        {this.state.tdData.td229>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td229}</div>:this.state.tdData.td229}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td228" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus26 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td228", 228, 26, 26)}>
                        {this.state.tdData.td228>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td228}</div>:this.state.tdData.td228}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td227" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus26 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td227", 227, 26, 26)}>
                        {this.state.tdData.td227>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td227}</div>:this.state.tdData.td227}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td226" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus27 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td226", 226, 27, 26)}>
                        {this.state.tdData.td226>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td226}</div>:this.state.tdData.td226}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td225" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus27 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td225", 225, 27, 27)}>
                        {this.state.tdData.td225>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td225}</div>:this.state.tdData.td225}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td224" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus27 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td224", 224, 27, 27)}>
                        {this.state.tdData.td224>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td224}</div>:this.state.tdData.td224}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td223" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus28 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td223", 223, 28, 27)}>
                        {this.state.tdData.td223>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td223}</div>:this.state.tdData.td223}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td222" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus28 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td222", 222, 28, 28)}>
                        {this.state.tdData.td222>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td222}</div>:this.state.tdData.td222}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td221" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus28 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td221", 221, 28, 28)}>
                        {this.state.tdData.td221>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td221}</div>:this.state.tdData.td221}
                      </td>
                    </tr>
                    {/*</tbody>*/}
                  </table>
                  <table className={styles.lowerRightTable} cellSpacing={0} cellPadding={0}>
                    <tr>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td340" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td340", 340, 31, 41)}>
                        {this.state.tdData.td340>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td340}</div>:this.state.tdData.td340}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td339" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td339", 339, 31, 31)}>
                        {this.state.tdData.td339>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td339}</div>:this.state.tdData.td339}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td338" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td338", 338, 31, 31)}>
                        {this.state.tdData.td338>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td338}</div>:this.state.tdData.td338}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td337" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td337", 337, 32, 31)}>
                        {this.state.tdData.td337>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td337}</div>:this.state.tdData.td337}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td336" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td336", 336, 32, 32)}>
                        {this.state.tdData.td336>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td336}</div>:this.state.tdData.td336}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td335" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td335", 335, 32, 32)}>
                        {this.state.tdData.td335>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td335}</div>:this.state.tdData.td335}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td334" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td334", 334, 33, 32)}>
                        {this.state.tdData.td334>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td334}</div>:this.state.tdData.td334}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td333" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td333", 333, 33, 33)}>
                        {this.state.tdData.td333>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td333}</div>:this.state.tdData.td333}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td332" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td332", 332, 33, 33)}>
                        {this.state.tdData.td332>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td332}</div>:this.state.tdData.td332}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td331" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td331", 331, 34, 33)}>
                        {this.state.tdData.td331>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td331}</div>:this.state.tdData.td331}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td330" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td330", 330, 34, 34)}>
                        {this.state.tdData.td330>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td330}</div>:this.state.tdData.td330}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td329" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td329", 329, 34, 34)}>
                        {this.state.tdData.td329>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td329}</div>:this.state.tdData.td329}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td328" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td328", 328, 35, 34)}>
                        {this.state.tdData.td328>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td328}</div>:this.state.tdData.td328}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td327" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td327", 327, 35, 35)}>
                        {this.state.tdData.td327>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td327}</div>:this.state.tdData.td327}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td326" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td326", 326, 35, 35)}>
                        {this.state.tdData.td326>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td326}</div>:this.state.tdData.td326}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td325" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td325", 325, 36, 35)}>
                        {this.state.tdData.td325>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td325}</div>:this.state.tdData.td325}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td324" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td324", 324, 36, 36)}>
                        {this.state.tdData.td324>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td324}</div>:this.state.tdData.td324}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td323" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td323", 323, 36, 36)}>
                        {this.state.tdData.td323>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td323}</div>:this.state.tdData.td323}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td322" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td322", 322, 37, 36)}>
                        {this.state.tdData.td322>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td322}</div>:this.state.tdData.td322}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td321" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td321", 321, 37, 37)}>
                        {this.state.tdData.td321>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td321}</div>:this.state.tdData.td321}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td320" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td320", 320, 37, 37)}>
                        {this.state.tdData.td320>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td320}</div>:this.state.tdData.td320}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td319" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td319", 319, 38, 37)}>
                        {this.state.tdData.td319>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td319}</div>:this.state.tdData.td319}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td318" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td318", 318, 38, 38)}>
                        {this.state.tdData.td318>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td318}</div>:this.state.tdData.td318}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td317" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td317", 317, 38, 38)}>
                        {this.state.tdData.td317>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td317}</div>:this.state.tdData.td317}
                      </td>
                    </tr>
                    <tr>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td293" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td293", 293, 31, 31)}>
                        {this.state.tdData.td293>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td293}</div>:this.state.tdData.td293}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td294" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td294", 294, 31, 31)}>
                        {this.state.tdData.td294>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td294}</div>:this.state.tdData.td294}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td295" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td295", 295, 31, 32)}>
                        {this.state.tdData.td295>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td295}</div>:this.state.tdData.td295}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td296" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td296", 296, 32, 32)}>
                        {this.state.tdData.td296>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td296}</div>:this.state.tdData.td296}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td297" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td297", 297, 32, 32)}>
                        {this.state.tdData.td297>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td297}</div>:this.state.tdData.td297}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td298" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td298", 298, 32, 33)}>
                        {this.state.tdData.td298>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td298}</div>:this.state.tdData.td298}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td299" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td299", 299, 33, 33)}>
                        {this.state.tdData.td299>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td299}</div>:this.state.tdData.td299}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td300" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td300", 300, 33, 33)}>
                        {this.state.tdData.td300>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td300}</div>:this.state.tdData.td300}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td301" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td301", 301, 33, 34)}>
                        {this.state.tdData.td301>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td301}</div>:this.state.tdData.td301}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td302" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td302", 302, 34, 34)}>
                        {this.state.tdData.td302>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td302}</div>:this.state.tdData.td302}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td303" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td303", 303, 34, 34)}>
                        {this.state.tdData.td303>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td303}</div>:this.state.tdData.td303}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td304" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td304", 304, 34, 35)}>
                        {this.state.tdData.td304>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td304}</div>:this.state.tdData.td304}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td305" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td305", 305, 35, 35)}>
                        {this.state.tdData.td305>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td305}</div>:this.state.tdData.td305}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td306" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td306", 306, 35, 35)}>
                        {this.state.tdData.td306>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td306}</div>:this.state.tdData.td306}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td307" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td307", 307, 35, 36)}>
                        {this.state.tdData.td307>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td307}</div>:this.state.tdData.td307}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td308" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td308", 308, 36, 36)}>
                        {this.state.tdData.td308>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td308}</div>:this.state.tdData.td308}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td309" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td309", 309, 36, 36)}>
                        {this.state.tdData.td309>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td309}</div>:this.state.tdData.td309}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td310" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td310", 310, 36, 37)}>
                        {this.state.tdData.td310>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td310}</div>:this.state.tdData.td310}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td311" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td311", 311, 37, 37)}>
                        {this.state.tdData.td311>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td311}</div>:this.state.tdData.td311}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td312" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td312", 312, 37, 37)}>
                        {this.state.tdData.td312>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td312}</div>:this.state.tdData.td312}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td313" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd(        "td313", 224, 37, 38)}>
                        {this.state.tdData.td313>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td313}</div>:this.state.tdData.td313}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td314" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td314", 314, 38, 38)}>
                        {this.state.tdData.td314>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td314}</div>:this.state.tdData.td314}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td315" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td315", 315, 38, 38)}>
                        {this.state.tdData.td315>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td315}</div>:this.state.tdData.td315}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td316" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td316", 316, 38, 38)}>
                        {this.state.tdData.td316>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td316}</div>:this.state.tdData.td316}
                      </td>
                    </tr>
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td388" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td388", 388, 31, 41)}>
                        {this.state.tdData.td388>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td388}</div>:this.state.tdData.td388}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td387" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td387", 387, 32, 31)}>
                        {this.state.tdData.td387>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td387}</div>:this.state.tdData.td387}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td386" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td386", 386, 33, 32)}>
                        {this.state.tdData.td386>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td386}</div>:this.state.tdData.td386}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td385" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td385", 385, 34, 33)}>
                        {this.state.tdData.td385>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td385}</div>:this.state.tdData.td385}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td384" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td384", 384, 35, 34)}>
                        {this.state.tdData.td384>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td384}</div>:this.state.tdData.td384}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td383" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td383", 383, 36, 35)}>
                        {this.state.tdData.td383>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td383}</div>:this.state.tdData.td383}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td382" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td382", 382, 37, 36)}>
                        {this.state.tdData.td382>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td382}</div>:this.state.tdData.td382}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td381" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td381", 381, 38, 37)}>
                        {this.state.tdData.td381>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td381}</div>:this.state.tdData.td381}

                      </td>
                    </tr>
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td373" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td373", 373, 31, 31)}>
                        {this.state.tdData.td373>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td373}</div>:this.state.tdData.td373}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td374" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td374", 374, 32, 33)}>
                        {this.state.tdData.td374>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td374}</div>:this.state.tdData.td374}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td375" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td375", 375, 33, 34)}>
                        {this.state.tdData.td375>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td375}</div>:this.state.tdData.td375}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td376" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td376", 376, 34, 35)}>
                        {this.state.tdData.td376>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td376}</div>:this.state.tdData.td376}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td377" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td377", 377, 35, 36)}>
                        {this.state.tdData.td377>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td377}</div>:this.state.tdData.td377}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td378" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td378", 378, 36, 37)}>
                        {this.state.tdData.td378>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td378}</div>:this.state.tdData.td378}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td379" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td379", 379, 37, 38)}>
                        {this.state.tdData.td379>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td379}</div>:this.state.tdData.td379}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td380" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td380", 380, 38, 38)}>
                        {this.state.tdData.td380>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td380}</div>:this.state.tdData.td380}

                      </td>
                    </tr>
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td420" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td420", 420, 31, 31)}>
                        {this.state.tdData.td420>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td420}</div>:this.state.tdData.td420}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td419" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td419", 419, 32, 33)}>
                        {this.state.tdData.td419>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td419}</div>:this.state.tdData.td419}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td418" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td418", 418, 33, 34)}>
                        {this.state.tdData.td418>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td418}</div>:this.state.tdData.td418}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td417" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td417", 417, 34, 35)}>
                        {this.state.tdData.td417>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td417}</div>:this.state.tdData.td417}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td416" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td416", 416, 35, 36)}>
                        {this.state.tdData.td416>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td416}</div>:this.state.tdData.td416}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td415" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td415", 415, 36, 37)}>
                        {this.state.tdData.td415>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td415}</div>:this.state.tdData.td415}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td414" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td414", 414, 37, 38)}>
                        {this.state.tdData.td414>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td414}</div>:this.state.tdData.td414}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td413" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td413", 413, 38, 38)}>
                        {this.state.tdData.td413>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td413}</div>:this.state.tdData.td413}

                      </td>
                    </tr>
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td405" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td405", 405, 31, 41)}>
                        {this.state.tdData.td405>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td405}</div>:this.state.tdData.td405}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td406" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td406", 406, 32, 31)}>
                        {this.state.tdData.td406>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td406}</div>:this.state.tdData.td406}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td407" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td407", 407, 33, 32)}>
                        {this.state.tdData.td407>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td407}</div>:this.state.tdData.td407}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td408" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td408", 408, 34, 33)}>
                        {this.state.tdData.td408>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td408}</div>:this.state.tdData.td408}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td409" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td409", 409, 35, 34)}>
                        {this.state.tdData.td409>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td409}</div>:this.state.tdData.td409}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td410" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td410", 410, 36, 35)}>
                        {this.state.tdData.td410>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td410}</div>:this.state.tdData.td410}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td411" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td411", 411, 37, 36)}>
                        {this.state.tdData.td411>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td411}</div>:this.state.tdData.td411}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td412" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td412", 412, 38, 37)}>
                        {this.state.tdData.td412>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td412}</div>:this.state.tdData.td412}

                      </td>
                    </tr>
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td452" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td452", 452, 31, 41)}>
                        {this.state.tdData.td452>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td452}</div>:this.state.tdData.td452}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td451" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td451", 451, 32, 31)}>
                        {this.state.tdData.td451>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td451}</div>:this.state.tdData.td451}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td450" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td450", 450, 33, 32)}>
                        {this.state.tdData.td450>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td450}</div>:this.state.tdData.td450}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td449" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td449", 449, 34, 33)}>
                        {this.state.tdData.td449>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td449}</div>:this.state.tdData.td449}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td448" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td448", 448, 35, 34)}>
                        {this.state.tdData.td448>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td448}</div>:this.state.tdData.td448}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td447" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td447", 447, 36, 35)}>
                        {this.state.tdData.td447>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td447}</div>:this.state.tdData.td447}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td446" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td446", 446, 37, 36)}>
                        {this.state.tdData.td446>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td446}</div>:this.state.tdData.td446}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td445" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td445", 445, 38, 37)}>
                        {this.state.tdData.td445>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td445}</div>:this.state.tdData.td445}

                      </td>
                    </tr>
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td437" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td437", 437, 31, 32)}>
                        {this.state.tdData.td437>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td437}</div>:this.state.tdData.td437}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td438" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td438", 438, 32, 33)}>
                        {this.state.tdData.td438>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td438}</div>:this.state.tdData.td438}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td439" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td439", 439, 33, 34)}>
                        {this.state.tdData.td439>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td439}</div>:this.state.tdData.td439}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td440" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td440", 440, 34, 35)}>
                        {this.state.tdData.td440>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td440}</div>:this.state.tdData.td440}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td441" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td441", 441, 35, 36)}>
                        {this.state.tdData.td441>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td441}</div>:this.state.tdData.td441}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td442" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td442", 442, 36, 37)}>
                        {this.state.tdData.td442>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td442}</div>:this.state.tdData.td442}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td443" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td443", 443, 37, 38)}>
                        {this.state.tdData.td443>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td443}</div>:this.state.tdData.td443}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td444" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td444", 444, 38, 38)}>
                        {this.state.tdData.td444>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td444}</div>:this.state.tdData.td444}

                      </td>
                    </tr>
                    <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td469" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td469", 469, 31, 32)}>
                        {this.state.tdData.td469>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td469}</div>:this.state.tdData.td469}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td470" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td470", 470, 32, 33)}>
                        {this.state.tdData.td470>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td470}</div>:this.state.tdData.td470}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td471" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td471", 471, 33, 34)}>
                        {this.state.tdData.td471>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td471}</div>:this.state.tdData.td471}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td472" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td472", 472, 34, 35)}>
                        {this.state.tdData.td472>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td472}</div>:this.state.tdData.td472}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td473" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td473", 473, 35, 36)}>
                        {this.state.tdData.td473>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td473}</div>:this.state.tdData.td473}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td474" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td474", 474, 36, 37)}>
                        {this.state.tdData.td474>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td474}</div>:this.state.tdData.td474}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td475" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td475", 475, 37, 38)}>
                        {this.state.tdData.td475>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td475}</div>:this.state.tdData.td475}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td476" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td476", 476, 38, 38)}>
                        {this.state.tdData.td476>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td476}</div>:this.state.tdData.td476}

                      </td>
                    </tr>
                    <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td485" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td485", 485, 31, 32)}>
                        {this.state.tdData.td485>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td485}</div>:this.state.tdData.td485}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td486" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td486", 486, 32, 33)}>
                        {this.state.tdData.td486>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td486}</div>:this.state.tdData.td486}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td487" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td487", 487, 33, 34)}>
                        {this.state.tdData.td487>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td487}</div>:this.state.tdData.td487}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td488" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td488", 488, 34, 35)}>
                        {this.state.tdData.td488>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td488}</div>:this.state.tdData.td488}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td489" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td489", 489, 35, 36)}>
                        {this.state.tdData.td489>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td489}</div>:this.state.tdData.td489}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td490" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td490", 490, 36, 37)}>
                        {this.state.tdData.td490>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td490}</div>:this.state.tdData.td490}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td491" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td491", 491, 37, 38)}>
                        {this.state.tdData.td491>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td491}</div>:this.state.tdData.td491}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td492" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td492", 492, 38, 48)}>
                        {this.state.tdData.td492>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td492}</div>:this.state.tdData.td492}

                      </td>
                    </tr>
                    <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td501" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td501", 501, 31, 32)}>
                        {this.state.tdData.td501>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td501}</div>:this.state.tdData.td501}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td502" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td502", 502, 32, 33)}>
                        {this.state.tdData.td502>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td502}</div>:this.state.tdData.td502}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td503" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td503", 503, 33, 34)}>
                        {this.state.tdData.td503>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td503}</div>:this.state.tdData.td503}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td504" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td504", 504, 34, 35)}>
                        {this.state.tdData.td504>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td504}</div>:this.state.tdData.td504}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td505" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td505", 505, 35, 36)}>
                        {this.state.tdData.td505>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td505}</div>:this.state.tdData.td505}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td506" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td506", 506, 36, 37)}>
                        {this.state.tdData.td506>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td506}</div>:this.state.tdData.td506}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td507" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td507", 507, 37, 38)}>
                        {this.state.tdData.td507>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td507}</div>:this.state.tdData.td507}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td508" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td508", 508, 38, 48)}>
                        {this.state.tdData.td508>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td508}</div>:this.state.tdData.td508}

                      </td>
                    </tr>
                    <tr className={styles.trH20}>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td520" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td520", 520, 31, 32)}>
                        {this.state.tdData.td520>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td520}</div>:this.state.tdData.td520}</td>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td521" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td521", 521, 32, 33)}>
                        {this.state.tdData.td521>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td521}</div>:this.state.tdData.td521}</td>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td522" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td522", 522, 33, 34)}>
                        {this.state.tdData.td522>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td522}</div>:this.state.tdData.td522}</td>

                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td523" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td523", 523, 34, 35)}>
                        {this.state.tdData.td523>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td523}</div>:this.state.tdData.td523}</td>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td524" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td524", 524, 35, 36)}>
                        {this.state.tdData.td524>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td524}</div>:this.state.tdData.td524}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td525" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td525", 525, 36, 36)}>
                        {this.state.tdData.td525>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td525}</div>:this.state.tdData.td525}</td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td527" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td527", 527, 37, 37)}>
                        {this.state.tdData.td527>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td527}</div>:this.state.tdData.td527}
                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td529" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td529", 529, 38, 38)}>
                        {this.state.tdData.td529>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td529}</div>:this.state.tdData.td529}</td>
                    </tr>
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td526" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td526", 526, 36, 37)}>
                        {this.state.tdData.td526>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td526}</div>:this.state.tdData.td526}</td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td528" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td528", 528, 37, 38)}>
                        {this.state.tdData.td528>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td528}</div>:this.state.tdData.td528}</td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td530" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td530", 530, 38, 38)}>
                        {this.state.tdData.td530>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td530}</div>:this.state.tdData.td530}
                      </td>
                    </tr>
                  </table>
                  <table className={styles.lowerLeftTable} cellSpacing={0} cellPadding={0}>
                    {/*<tbody>*/}
                    <tr>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td364" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td364", 364, 48, 48)}>
                        {this.state.tdData.td364>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td364}</div>:this.state.tdData.td364}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td363" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td363", 363, 48, 48)}>
                        {this.state.tdData.td363>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td363}</div>:this.state.tdData.td363}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td362" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td362", 362, 48, 48)}>
                        {this.state.tdData.td362>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td362}</div>:this.state.tdData.td362}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td361" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td361", 361, 47, 48)}>
                        {this.state.tdData.td361>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td361}</div>:this.state.tdData.td361}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td360" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td360", 360, 47, 47)}>
                        {this.state.tdData.td360>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td360}</div>:this.state.tdData.td360}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td359" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td359", 359, 47, 47)}>
                        {this.state.tdData.td359>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td359}</div>:this.state.tdData.td359}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td358" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td358", 358, 46, 47)}>
                        {this.state.tdData.td358>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td358}</div>:this.state.tdData.td358}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td357" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td357", 357, 46, 46)}>
                        {this.state.tdData.td357>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td357}</div>:this.state.tdData.td357}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td356" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td356", 356, 46, 46)}>
                        {this.state.tdData.td356>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td356}</div>:this.state.tdData.td356}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td355" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td355", 355, 45, 46)}>
                        {this.state.tdData.td355>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td355}</div>:this.state.tdData.td355}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td354" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td354", 354, 45, 45)}>
                        {this.state.tdData.td354>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td354}</div>:this.state.tdData.td354}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td353" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td353", 353, 45, 45)}>
                        {this.state.tdData.td353>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td353}</div>:this.state.tdData.td353}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td352" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td352", 352, 44, 45)}>
                        {this.state.tdData.td352>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td352}</div>:this.state.tdData.td352}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td351" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td351", 351, 44, 44)}>
                        {this.state.tdData.td351>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td351}</div>:this.state.tdData.td351}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td350" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td350", 350, 44, 44)}>
                        {this.state.tdData.td350>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td350}</div>:this.state.tdData.td350}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td349" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td349", 349, 43, 44)}>
                        {this.state.tdData.td349>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td349}</div>:this.state.tdData.td349}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td348" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td348", 348, 43, 43)}>
                        {this.state.tdData.td348>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td348}</div>:this.state.tdData.td348}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td347" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td347", 347, 43, 43)}>
                        {this.state.tdData.td347>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td347}</div>:this.state.tdData.td347}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td346" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td346", 346, 42, 43)}>
                        {this.state.tdData.td346>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td346}</div>:this.state.tdData.td346}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td345" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td345", 345, 42, 42)}>
                        {this.state.tdData.td345>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td345}</div>:this.state.tdData.td345}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td344" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td344", 344, 42, 42)}>
                        {this.state.tdData.td344>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td344}</div>:this.state.tdData.td344}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td343" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td343", 343, 41, 42)}>
                        {this.state.tdData.td343>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td343}</div>:this.state.tdData.td343}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td342" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td342", 342, 41, 41)}>
                        {this.state.tdData.td342>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td342}</div>:this.state.tdData.td342}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td341" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td341", 341, 41, 41)}>
                        {this.state.tdData.td341>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td341}</div>:this.state.tdData.td341}
                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td269" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td269", 269, 48, 48)}>
                        {this.state.tdData.td269>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td269}</div>:this.state.tdData.td269}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td270" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td270", 270, 48, 48)}>
                        {this.state.tdData.td270>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td270}</div>:this.state.tdData.td270}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td271" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td271", 271, 48, 47)}>
                        {this.state.tdData.td271>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td271}</div>:this.state.tdData.td271}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td272" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td272", 272, 47, 47)}>
                        {this.state.tdData.td272>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td272}</div>:this.state.tdData.td272}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td273" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td273", 273, 47, 47)}>
                        {this.state.tdData.td273>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td273}</div>:this.state.tdData.td273}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td274" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td274", 274, 47, 46)}>
                        {this.state.tdData.td274>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td274}</div>:this.state.tdData.td274}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td275" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td275", 275, 46, 46)}>
                        {this.state.tdData.td275>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td275}</div>:this.state.tdData.td275}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td276" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td276", 276, 46, 46)}>
                        {this.state.tdData.td276>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td276}</div>:this.state.tdData.td276}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td277" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td277", 277, 46, 45)}>
                        {this.state.tdData.td277>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td277}</div>:this.state.tdData.td277}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td278" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td278", 278, 45, 45)}>
                        {this.state.tdData.td278>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td278}</div>:this.state.tdData.td278}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td279" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td279", 279, 45, 45)}>
                        {this.state.tdData.td279>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td279}</div>:this.state.tdData.td279}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td280" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td280", 280, 45, 44)}>
                        {this.state.tdData.td280>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td280}</div>:this.state.tdData.td280}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td281" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td281", 281, 44, 44)}>
                        {this.state.tdData.td281>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td281}</div>:this.state.tdData.td281}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td282" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td282", 282, 44, 44)}>
                        {this.state.tdData.td282>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td282}</div>:this.state.tdData.td282}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td283" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td283", 283, 44, 43)}>
                        {this.state.tdData.td283>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td283}</div>:this.state.tdData.td283}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td284" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td284", 284, 43, 43)}>
                        {this.state.tdData.td284>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td284}</div>:this.state.tdData.td284}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td285" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td285", 285, 43, 43)}>
                        {this.state.tdData.td285>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td285}</div>:this.state.tdData.td285}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td286" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td286", 286, 43, 42)}>
                        {this.state.tdData.td286>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td286}</div>:this.state.tdData.td286}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td287" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td287", 287, 42, 42)}>
                        {this.state.tdData.td287>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td287}</div>:this.state.tdData.td287}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td288" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td288", 288, 42, 42)}>
                        {this.state.tdData.td288>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td288}</div>:this.state.tdData.td288}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td289" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td289", 289, 42, 41)}>
                        {this.state.tdData.td289>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td289}</div>:this.state.tdData.td289}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td290" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td290", 290, 41, 41)}>
                        {this.state.tdData.td290>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td290}</div>:this.state.tdData.td290}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td291" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td291", 291, 41, 41)}>
                        {this.state.tdData.td291>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td291}</div>:this.state.tdData.td291}
                      </td>
                      <td
                        className={[styles.tdW18, this.state.activeTdId == "td292" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                        onClick={(e, i) => this.handleClickTd("td292", 292, 41, 38)}>
                        {this.state.tdData.td292>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td292}</div>:this.state.tdData.td292}
                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td396" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td396", 396, 48, 48)}>
                        {this.state.tdData.td396>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td396}</div>:this.state.tdData.td396}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td395" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td395", 395, 47, 48)}>
                        {this.state.tdData.td395>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td395}</div>:this.state.tdData.td395}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td394" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td394", 394, 46, 47)}>
                        {this.state.tdData.td394>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td394}</div>:this.state.tdData.td394}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td393" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td393", 393, 45, 46)}>
                        {this.state.tdData.td393>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td393}</div>:this.state.tdData.td393}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td392" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td392", 392, 44, 45)}>
                        {this.state.tdData.td392>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td392}</div>:this.state.tdData.td392}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td391" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td391", 391, 43, 44)}>
                        {this.state.tdData.td391>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td391}</div>:this.state.tdData.td391}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td390" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td390", 390, 42, 43)}>
                        {this.state.tdData.td390>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td390}</div>:this.state.tdData.td390}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td389" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td389", 389, 41, 42)}>
                        {this.state.tdData.td389>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td389}</div>:this.state.tdData.td389}

                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td365" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td365", 365, 48, 47)}>
                        {this.state.tdData.td365>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td365}</div>:this.state.tdData.td365}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td366" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td366", 366, 47, 46)}>
                        {this.state.tdData.td366>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td366}</div>:this.state.tdData.td366}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td367" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td367", 367, 46, 45)}>
                        {this.state.tdData.td367>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td367}</div>:this.state.tdData.td367}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td368" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td368", 368, 45, 44)}>
                        {this.state.tdData.td368>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td368}</div>:this.state.tdData.td368}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td369" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td369", 369, 44, 43)}>
                        {this.state.tdData.td369>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td369}</div>:this.state.tdData.td369}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td370" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td370", 370, 43, 42)}>
                        {this.state.tdData.td370>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td370}</div>:this.state.tdData.td370}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td371" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td371", 371, 42, 41)}>
                        {this.state.tdData.td371>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td371}</div>:this.state.tdData.td371}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td372" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td372", 372, 41, 31)}>
                        {this.state.tdData.td372>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td372}</div>:this.state.tdData.td372}

                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td428" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td428", 428, 48, 48)}>
                        {this.state.tdData.td428>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td428}</div>:this.state.tdData.td428}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td427" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td427", 427, 47, 48)}>
                        {this.state.tdData.td427>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td427}</div>:this.state.tdData.td427}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td426" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td426", 426, 47, 46)}>
                        {this.state.tdData.td426>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td426}</div>:this.state.tdData.td426}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td425" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td425", 425, 45, 46)}>
                        {this.state.tdData.td425>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td425}</div>:this.state.tdData.td425}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td424" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td424", 424, 44, 45)}>
                        {this.state.tdData.td424>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td424}</div>:this.state.tdData.td424}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td423" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td423", 423, 43, 44)}>
                        {this.state.tdData.td423>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td423}</div>:this.state.tdData.td423}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td422" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td422", 422, 42, 43)}>
                        {this.state.tdData.td422>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td422}</div>:this.state.tdData.td422}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td421" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td421", 421, 41, 42)}>
                        {this.state.tdData.td421>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td421}</div>:this.state.tdData.td421}

                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td397" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td397", 397, 48, 47)}>
                        {this.state.tdData.td397>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td397}</div>:this.state.tdData.td397}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td398" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td398", 398, 47, 46)}>
                        {this.state.tdData.td398>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td398}</div>:this.state.tdData.td398}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td399" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td399", 399, 46, 45)}>
                        {this.state.tdData.td399>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td399}</div>:this.state.tdData.td399}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td400" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td400", 400, 45, 44)}>
                        {this.state.tdData.td400>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td400}</div>:this.state.tdData.td400}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td401" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td401", 401, 44, 43)}>
                        {this.state.tdData.td401>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td401}</div>:this.state.tdData.td401}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td402" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td402", 402, 43, 42)}>
                        {this.state.tdData.td402>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td402}</div>:this.state.tdData.td402}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td403" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td403", 403, 42, 41)}>
                        {this.state.tdData.td403>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td403}</div>:this.state.tdData.td403}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td404" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td404", 404, 41, 31)}>
                        {this.state.tdData.td404>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td404}</div>:this.state.tdData.td404}

                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td460" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td460", 460, 48, 48)}>
                        {this.state.tdData.td460>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td460}</div>:this.state.tdData.td460}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td459" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td459", 459, 47, 48)}>
                        {this.state.tdData.td459>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td459}</div>:this.state.tdData.td459}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td458" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td458", 458, 46, 47)}>
                        {this.state.tdData.td458>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td458}</div>:this.state.tdData.td458}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td457" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td457", 457, 45, 46)}>
                        {this.state.tdData.td457>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td457}</div>:this.state.tdData.td457}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td456" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td456", 456, 44, 45)}>
                        {this.state.tdData.td456>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td456}</div>:this.state.tdData.td456}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td455" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td455", 455, 43, 44)}>
                        {this.state.tdData.td455>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td455}</div>:this.state.tdData.td455}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td454" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td454", 454, 42, 43)}>
                        {this.state.tdData.td454>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td454}</div>:this.state.tdData.td454}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td453" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td453", 453, 41, 42)}>
                        {this.state.tdData.td453>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td453}</div>:this.state.tdData.td453}

                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td429" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td429", 429, 48, 47)}>
                        {this.state.tdData.td429>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td429}</div>:this.state.tdData.td429}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td430" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td430", 430, 47, 48)}>
                        {this.state.tdData.td430>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td430}</div>:this.state.tdData.td430}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td431" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td431", 431, 46, 47)}>
                        {this.state.tdData.td431>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td431}</div>:this.state.tdData.td431}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td432" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td432", 432, 45, 46)}>
                        {this.state.tdData.td432>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td432}</div>:this.state.tdData.td432}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td433" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td433", 433, 44, 45)}>
                        {this.state.tdData.td433>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td433}</div>:this.state.tdData.td433}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td434" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td434", 434, 43, 44)}>
                        {this.state.tdData.td434>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td434}</div>:this.state.tdData.td434}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td435" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td435", 435, 42, 43)}>
                        {this.state.tdData.td435>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td435}</div>:this.state.tdData.td435}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td436" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td436", 436, 41, 42)}>
                        {this.state.tdData.td436>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td436}</div>:this.state.tdData.td436}

                      </td>
                    </tr>

                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td461" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td461", 461, 48, 47)}>
                        {this.state.tdData.td461>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td461}</div>:this.state.tdData.td461}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td462" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td462", 462, 47, 46)}>
                        {this.state.tdData.td462>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td462}</div>:this.state.tdData.td462}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td463" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td463", 463, 46, 45)}>
                        {this.state.tdData.td463>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td463}</div>:this.state.tdData.td463}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td464" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td464", 464, 45, 44)}>
                        {this.state.tdData.td464>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td464}</div>:this.state.tdData.td464}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td465" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td465", 465, 44, 43)}>
                        {this.state.tdData.td465>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td465}</div>:this.state.tdData.td465}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td466" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td466", 466, 43, 42)}>
                        {this.state.tdData.td466>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td466}</div>:this.state.tdData.td466}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td467" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td467", 467, 42, 41)}>
                        {this.state.tdData.td467>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td467}</div>:this.state.tdData.td467}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td468" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td468", 468, 41, 31)}>
                        {this.state.tdData.td468>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td468}</div>:this.state.tdData.td468}

                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td477" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td477", 477, 48, 47)}>
                        {this.state.tdData.td477>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td477}</div>:this.state.tdData.td477}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td478" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td478", 478, 47, 46)}>
                        {this.state.tdData.td478>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td478}</div>:this.state.tdData.td478}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td479" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td479", 479, 46, 45)}>
                        {this.state.tdData.td479>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td479}</div>:this.state.tdData.td479}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td480" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td480", 480, 45, 44)}>
                        {this.state.tdData.td480>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td480}</div>:this.state.tdData.td480}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td481" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td481", 481, 44, 43)}>
                        {this.state.tdData.td481>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td481}</div>:this.state.tdData.td481}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td482" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td482", 482, 43, 42)}>
                        {this.state.tdData.td482>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td482}</div>:this.state.tdData.td482}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td483" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td483", 483, 42, 41)}>
                        {this.state.tdData.td483>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td483}</div>:this.state.tdData.td483}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td484" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td484", 484, 41, 31)}>
                        {this.state.tdData.td484>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td484}</div>:this.state.tdData.td484}

                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td493" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td493", 493, 48, 47)}>
                        {this.state.tdData.td493>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td493}</div>:this.state.tdData.td493}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td494" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td494", 494, 47, 46)}>
                        {this.state.tdData.td494>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td494}</div>:this.state.tdData.td494}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td495" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td495", 495, 46, 45)}>
                        {this.state.tdData.td495>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td495}</div>:this.state.tdData.td495}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td496" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td496", 496, 45, 44)}>
                        {this.state.tdData.td496>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td496}</div>:this.state.tdData.td496}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td497" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td497", 497, 44, 43)}>
                        {this.state.tdData.td497>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td497}</div>:this.state.tdData.td497}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td498" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td498", 498, 43, 42)}>
                        {this.state.tdData.td498>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td498}</div>:this.state.tdData.td498}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td499" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td499", 499, 42, 41)}>
                        {this.state.tdData.td499>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td499}</div>:this.state.tdData.td499}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td500" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td500", 500, 41, 31)}>
                        {this.state.tdData.td500>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td500}</div>:this.state.tdData.td500}

                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td509" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td509", 509, 48, 48)}>
                        {this.state.tdData.td509>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td509}</div>:this.state.tdData.td509}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td511" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td511", 511, 47, 47)}>
                        {this.state.tdData.td511>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td511}</div>:this.state.tdData.td511}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td513" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td513", 513, 46, 46)}>
                        {this.state.tdData.td513>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td513}</div>:this.state.tdData.td513}

                      </td>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td515" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td515", 515, 45, 44)}>
                        {this.state.tdData.td515>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td515}</div>:this.state.tdData.td515}
                      </td>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td516" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td516", 516, 44, 43)}>
                        {this.state.tdData.td516>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td516}</div>:this.state.tdData.td516}
                      </td>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td517" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td517", 517, 43, 42)}>
                        {this.state.tdData.td517>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td517}</div>:this.state.tdData.td517}
                      </td>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td518" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td518", 518, 42, 41)}>
                        {this.state.tdData.td518>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td518}</div>:this.state.tdData.td518}
                      </td>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td519" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td519", 519, 41, 31)}>
                        {this.state.tdData.td519>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td519}</div>:this.state.tdData.td519}
                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                    <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td510" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td510", 510, 48, 47)}>
                        {this.state.tdData.td510>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td510}</div>:this.state.tdData.td510}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td512" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td512", 512, 47, 46)}>
                        {this.state.tdData.td512>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td512}</div>:this.state.tdData.td512}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td514" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          onClick={(e, i) => this.handleClickTd("td514", 514, 46, 45)}>
                        {this.state.tdData.td514>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td514}</div>:this.state.tdData.td514}

                      </td>
                    </tr>
                    {/*</tbody>*/}
                  </table>
                </div>
              </div>
              <div className={styles.smallBox2}>
                <span className={styles.leftTitle}>牙缺失</span>
                <Checkbox style={{"width": "55px", textAlign: "center"}}
                          checked={this.state.missesData.dentureStatus48}
                          onChange={(e, i) => this.toothMissesChange(e, 48)}>48</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus47}
                          onChange={(e, i) => this.toothMissesChange(e, 47)}>47</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus46}
                          onChange={(e, i) => this.toothMissesChange(e, 46)}>46</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus45}
                          onChange={(e, i) => this.toothMissesChange(e, 45)}>45</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus44}
                          onChange={(e, i) => this.toothMissesChange(e, 44)}>44</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus43}
                          onChange={(e, i) => this.toothMissesChange(e, 43)}>43</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus42}
                          onChange={(e, i) => this.toothMissesChange(e, 42)}>42</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus41}
                          onChange={(e, i) => this.toothMissesChange(e, 41)}>41</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: "6px"}}
                          checked={this.state.missesData.dentureStatus31}
                          onChange={(e, i) => this.toothMissesChange(e, 31)}>31</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus32}
                          onChange={(e, i) => this.toothMissesChange(e, 32)}>32</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus33}
                          onChange={(e, i) => this.toothMissesChange(e, 33)}>33</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus34}
                          onChange={(e, i) => this.toothMissesChange(e, 34)}>34</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus35}
                          onChange={(e, i) => this.toothMissesChange(e, 35)}>35</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus36}
                          onChange={(e, i) => this.toothMissesChange(e, 36)}>36</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus37}
                          onChange={(e, i) => this.toothMissesChange(e, 37)}>37</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus38}
                          onChange={(e, i) => this.toothMissesChange(e, 38)}>38</Checkbox>
              </div>
            </div>
            <div style={{display: "flex", marginTop: '20px'}}>
              <div style={{marginRight: 5, width: '7%'}}>检查描述:</div>
              <TextArea
                autoSize={{minRows: 2}}
                maxLength={100}
                placeholder="请输入检查描述"
                defaultValue={this.state.params.examRemark}
                onChange={e => this.changeMS(e.target.value)}
                style={{width: 850}}/>
            </div>
          </div>
        </div>
        <div style={{marginLeft:10}}>
          <div style={{marginTop: 40}} className={styles.label}>快捷区</div>
          <div style={{marginTop: 10}}>
            {/*<img src={quite} alt=""/>*/}
            <div>
              <div id="tags"
                // style={{ display: this.state.showkey ? 'block' : 'block', position: 'absolute', zIndex: '1', width: '320px', background: 'rgba(0, 0, 0, 0.65)', borderRadius: '6px', textAlign: 'left'}}
              >
                {numberBlock}
              </div>
            </div>
          </div>
          <div style={{marginTop: 10}}>
            {/*<img src={quiter} alt=""/>*/}
          </div>
          <div className={styles.tabs}>
            {/*  toothPosition:{*/}
            {/*  occRelation:{*/}
            {/*    deepOverbite:{//深复合*/}
            {/*  },*/}
            {/*    deepCover:{//深复盖*/}
            {/*  },*/}
            {/*    malocclusionCrowded:{//错合拥挤*/}
            {/*  },*/}
            {/*    toTheBlade:{//对刃合*/}
            {/*  },*/}
            {/*  },*/}

            {/*  tabKey:"occRelation",*/}
            {/*  typeKey:"deepOverbite",*/}
            <Tabs type="card">
              <TabPane tab="咬合关系" key="1">
                <Form style={{height:500,overflow:'scroll'}}>
                    <div>
                      <div style={{display:'flex'}}>
                        <div>
                          深覆合:
                        </div>
                        <table className={styles.table_row}
                               onClick={() => this.showModalCheckTooth("occRelation", "deepOverbite")}>
                          <tbody>
                          <tr>
                            <th className={styles.line_th_row} id='leftTopTooth'>
                              {toothUtils.showTooth(1, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.deepOverbite.toothPosition : '')}
                            </th>
                            <th className={styles.line_th_col} id='rightTopTooth'>
                              {toothUtils.showTooth(2, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.deepOverbite.toothPosition : "")}
                            </th>
                          </tr>
                          </tbody>
                          <tbody>
                          <tr>
                            <td className={styles.line_row} id='leftBotTooth'>
                              {toothUtils.showTooth(4, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.deepOverbite.toothPosition : '')}
                            </td>
                            <td className={styles.line_col} id='rightBotTooth'>
                              {toothUtils.showTooth(3, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.deepOverbite.toothPosition : '')}
                            </td>
                          </tr>
                          </tbody>
                        </table>
                      </div>
                      <Input
                        placeholder='请输入'
                        key={this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.deepOverbite.value : ''}
                        defaultValue={this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.deepOverbite.value : ''}
                        onChange={e => {
                          this.state.toothPosition.occRelation.deepOverbite.value = e.target.value
                        }}
                        onFocus={e=>{
                          this.setState({activeTdId:""})
                        }}
                        className={styles.row_deep_input}/>
                    </div>

                  <div style={{marginTop:10}}>
                    <div style={{display:'flex'}}>
                      <div>
                        深覆盖:
                      </div>
                      <table className={styles.table_row}
                             onClick={() => this.showModalCheckTooth("occRelation", "deepCover")}>
                        <tbody>
                        <tr>
                          <th className={styles.line_th_row} id='leftTopTooth'>
                            {toothUtils.showTooth(1, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.deepCover.toothPosition : "")}
                          </th>
                          <th className={styles.line_th_col} id='rightTopTooth'>
                            {toothUtils.showTooth(2, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.deepCover.toothPosition : '')}
                          </th>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row} id='leftBotTooth'>
                            {toothUtils.showTooth(4, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.deepCover.toothPosition : '')}
                          </td>
                          <td className={styles.line_col} id='rightBotTooth'>
                            {toothUtils.showTooth(3, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.deepCover.toothPosition : '')}
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                    <Input placeholder='请输入'
                           key={this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.deepCover.value : ''}
                           defaultValue={this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.deepCover.value : ''}
                           onChange={e => {
                             this.state.toothPosition.occRelation.deepCover.value = e.target.value
                           }}
                           onFocus={e=>{
                             this.setState({activeTdId:""})
                           }}
                           className={styles.row_deep_input}/>
                  </div>
                  <div style={{marginTop:10}}>
                    <div style={{display:'flex'}}>
                      <div>
                        错合拥挤:
                      </div>
                      <table className={styles.table_row}
                             onClick={() => this.showModalCheckTooth("occRelation", "malocclusionCrowded")}>
                        <tbody>
                        <tr>
                          <th className={styles.line_th_row} id='leftTopTooth'>
                            {toothUtils.showTooth(1, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.malocclusionCrowded.toothPosition : '')}
                          </th>
                          <th className={styles.line_th_col} id='rightTopTooth'>
                            {toothUtils.showTooth(2, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.malocclusionCrowded.toothPosition : "")}
                          </th>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row} id='leftBotTooth'>
                            {toothUtils.showTooth(4, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.malocclusionCrowded.toothPosition : '')}
                          </td>
                          <td className={styles.line_col} id='rightBotTooth'>
                            {toothUtils.showTooth(3, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.malocclusionCrowded.toothPosition : '')}
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                    <Input placeholder='请输入'
                           key={this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.malocclusionCrowded.value : ''}
                           defaultValue={this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.malocclusionCrowded.value : ''}
                           onChange={e => {
                             this.state.toothPosition.occRelation.malocclusionCrowded.value = e.target.value
                           }}
                           onFocus={e=>{
                             this.setState({activeTdId:""})
                           }}
                           className={styles.row_deep_input}/>
                  </div>

                  <div style={{marginTop:10}}>
                    <div style={{display:'flex'}}>
                      <div>
                        对刃合:
                      </div>
                      <table className={styles.table_row}
                             onClick={() => this.showModalCheckTooth("occRelation", "toTheBlade")}>
                        <tbody>
                        <tr>
                          <th className={styles.line_th_row} id='leftTopTooth'>
                            {toothUtils.showTooth(1, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.toTheBlade.toothPosition : '')}
                          </th>
                          <th className={styles.line_th_col} id='rightTopTooth'>
                            {toothUtils.showTooth(2, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.toTheBlade.toothPosition : '')}
                          </th>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row} id='leftBotTooth'>
                            {toothUtils.showTooth(4, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.toTheBlade.toothPosition : '')}
                          </td>
                          <td className={styles.line_col} id='rightBotTooth'>
                            {toothUtils.showTooth(3, this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.toTheBlade.toothPosition : '')}
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                    <Input placeholder='请输入'
                           key={this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.toTheBlade.value : ''}
                           defaultValue={this.state.toothPosition.occRelation ? this.state.toothPosition.occRelation.toTheBlade.value : ""}
                           onChange={e => {
                             this.state.toothPosition.occRelation.toTheBlade.value = e.target.value
                           }}
                           onFocus={e=>{
                             this.setState({activeTdId:""})
                           }}
                           className={styles.row_deep_input}/>
                  </div>
                </Form>
              </TabPane>
              <TabPane tab="其他" key="2">
                <Form style={{height:500,overflow:'scroll'}}>
                  <div style={{marginTop:10}}>
                    <div style={{display:'flex'}}>
                      <div>
                        反合:
                      </div>
                      <table className={styles.table_row} onClick={() => this.showModalCheckTooth("other", "crossBite")}>
                        <tbody>
                        <tr>
                          <th className={styles.line_th_row} id='leftTopTooth'>
                            {toothUtils.showTooth(1, this.state.toothPosition.other ? this.state.toothPosition.other.crossBite.toothPosition : '')}
                          </th>
                          <th className={styles.line_th_col} id='rightTopTooth'>
                            {toothUtils.showTooth(2, this.state.toothPosition.other ? this.state.toothPosition.other.crossBite.toothPosition : '')}
                          </th>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row} id='leftBotTooth'>
                            {toothUtils.showTooth(4, this.state.toothPosition.other ? this.state.toothPosition.other.crossBite.toothPosition : '')}
                          </td>
                          <td className={styles.line_col} id='rightBotTooth'>
                            {toothUtils.showTooth(3, this.state.toothPosition.other ? this.state.toothPosition.other.crossBite.toothPosition : '')}
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                    <Input placeholder='请输入'
                           defaultValue={this.state.toothPosition.other ? this.state.toothPosition.other.crossBite.value : ''}
                           onChange={e => {
                             this.state.toothPosition.other.crossBite.value = e.target.value
                           }}
                           onFocus={e=>{
                             this.setState({activeTdId:""})
                           }}
                           className={styles.row_deep_input}/>
                  </div>
                  <div style={{marginTop:10}}>
                    <div style={{display:'flex'}}>
                      <div>
                        牙移位:
                      </div>
                      <table className={styles.table_row}
                             onClick={() => this.showModalCheckTooth("other", "migrationTooth")}>
                        <tbody>
                        <tr>
                          <th className={styles.line_th_row} id='leftTopTooth'>
                            {toothUtils.showTooth(1, this.state.toothPosition.other ? this.state.toothPosition.other.migrationTooth.toothPosition : '')}
                          </th>
                          <th className={styles.line_th_col} id='rightTopTooth'>
                            {toothUtils.showTooth(2, this.state.toothPosition.other ? this.state.toothPosition.other.migrationTooth.toothPosition : '')}
                          </th>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row} id='leftBotTooth'>
                            {toothUtils.showTooth(4, this.state.toothPosition.other ? this.state.toothPosition.other.migrationTooth.toothPosition : '')}
                          </td>
                          <td className={styles.line_col} id='rightBotTooth'>
                            {toothUtils.showTooth(3, this.state.toothPosition.other ? this.state.toothPosition.other.migrationTooth.toothPosition : '')}
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                    <Input placeholder='请输入'
                           defaultValue={this.state.toothPosition.other ? this.state.toothPosition.other.migrationTooth.value : ''}
                           onChange={e => {
                             this.state.toothPosition.other.migrationTooth.value = e.target.value
                           }}
                           onFocus={e=>{
                             this.setState({activeTdId:""})
                           }}
                           className={styles.row_deep_input}/>
                  </div>
                  <div style={{marginTop:10}}>
                    <div style={{display:'flex'}}>
                      <div>
                        食物嵌塞:
                      </div>
                      <table className={styles.table_row}
                             onClick={() => this.showModalCheckTooth("other", "foodImpaction")}>
                        <tbody>
                        <tr>
                          <th className={styles.line_th_row} id='leftTopTooth'>
                            {toothUtils.showTooth(1, this.state.toothPosition.other ? this.state.toothPosition.other.foodImpaction.toothPosition : '')}
                          </th>
                          <th className={styles.line_th_col} id='rightTopTooth'>
                            {toothUtils.showTooth(2, this.state.toothPosition.other ? this.state.toothPosition.other.foodImpaction.toothPosition : '')}
                          </th>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row} id='leftBotTooth'>
                            {toothUtils.showTooth(4, this.state.toothPosition.other ? this.state.toothPosition.other.foodImpaction.toothPosition : '')}
                          </td>
                          <td className={styles.line_col} id='rightBotTooth'>
                            {toothUtils.showTooth(3, this.state.toothPosition.other ? this.state.toothPosition.other.foodImpaction.toothPosition : '')}
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                    <Input placeholder='请输入'
                           defaultValue={this.state.toothPosition.other ? this.state.toothPosition.other.foodImpaction.value : ''}
                           onChange={e => {
                             this.state.toothPosition.other.foodImpaction.value = e.target.value
                           }}
                           onFocus={e=>{
                             this.setState({activeTdId:""})
                           }}
                           className={styles.row_deep_input}/>
                  </div>

                  <div style={{marginTop:10}}>
                    <div style={{display:'flex'}}>
                      <div>
                        充填体悬突:
                      </div>
                      <table className={styles.table_row}
                             onClick={() => this.showModalCheckTooth("other", "restorativeOverhanging")}>
                        <tbody>
                        <tr>
                          <th className={styles.line_th_row} id='leftTopTooth'>
                            {toothUtils.showTooth(1, this.state.toothPosition.other ? this.state.toothPosition.other.restorativeOverhanging.toothPosition : '')}
                          </th>
                          <th className={styles.line_th_col} id='rightTopTooth'>
                            {toothUtils.showTooth(2, this.state.toothPosition.other ? this.state.toothPosition.other.restorativeOverhanging.toothPosition : '')}
                          </th>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row} id='leftBotTooth'>
                            {toothUtils.showTooth(4, this.state.toothPosition.other ? this.state.toothPosition.other.restorativeOverhanging.toothPosition : '')}
                          </td>
                          <td className={styles.line_col} id='rightBotTooth'>
                            {toothUtils.showTooth(3, this.state.toothPosition.other ? this.state.toothPosition.other.restorativeOverhanging.toothPosition : '')}
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                    <Input placeholder='请输入'
                           defaultValue={this.state.toothPosition.other ? this.state.toothPosition.other.restorativeOverhanging.value : ''}
                           onChange={e => {
                             this.state.toothPosition.other.restorativeOverhanging.value = e.target.value
                           }}
                           onFocus={e=>{
                             this.setState({activeTdId:""})
                           }}
                           className={styles.row_deep_input}/>
                  </div>

                  <div style={{marginTop:10}}>
                    <div style={{display:'flex'}}>
                      <div>
                        开合:
                      </div>
                      <table className={styles.table_row}
                             onClick={() => this.showModalCheckTooth("other", "movableTeeth")}>
                        <tbody>
                        <tr>
                          <th className={styles.line_th_row} id='leftTopTooth'>
                            {toothUtils.showTooth(1, this.state.toothPosition.other ? this.state.toothPosition.other.movableTeeth.toothPosition : '')}

                          </th>
                          <th className={styles.line_th_col} id='rightTopTooth'>
                            {toothUtils.showTooth(2, this.state.toothPosition.other ? this.state.toothPosition.other.movableTeeth.toothPosition : '')}

                          </th>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row} id='leftBotTooth'>
                            {toothUtils.showTooth(4, this.state.toothPosition.other ? this.state.toothPosition.other.movableTeeth.toothPosition : '')}
                          </td>
                          <td className={styles.line_col} id='rightBotTooth'>
                            {toothUtils.showTooth(3, this.state.toothPosition.other ? this.state.toothPosition.other.movableTeeth.toothPosition : '')}
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                    <Input placeholder='请输入'
                           defaultValue={this.state.toothPosition.other ? this.state.toothPosition.other.movableTeeth.value : ''}
                           onChange={e => {
                             this.state.toothPosition.other.movableTeeth.value = e.target.value
                           }}
                           onFocus={e=>{
                             this.setState({activeTdId:""})
                           }}
                           className={styles.row_deep_input}/>
                  </div>

                  <div style={{marginTop:10}}>
                    <div style={{display:'flex'}}>
                      <div>
                        龋:
                      </div>
                      <table className={styles.table_row} onClick={() => this.showModalCheckTooth("other", "decayed")}>
                        <tbody>
                        <tr>
                          <th className={styles.line_th_row} id='leftTopTooth'>
                            {toothUtils.showTooth(1, this.state.toothPosition.other ? this.state.toothPosition.other.decayed.toothPosition : "")}

                          </th>
                          <th className={styles.line_th_col} id='rightTopTooth'>
                            {toothUtils.showTooth(2, this.state.toothPosition.other ? this.state.toothPosition.other.decayed.toothPosition : '')}

                          </th>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row} id='leftBotTooth'>
                            {toothUtils.showTooth(4, this.state.toothPosition.other ? this.state.toothPosition.other.decayed.toothPosition : '')}
                          </td>
                          <td className={styles.line_col} id='rightBotTooth'>
                            {toothUtils.showTooth(3, this.state.toothPosition.other ? this.state.toothPosition.other.decayed.toothPosition : '')}
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                    <Input placeholder='请输入'
                           defaultValue={this.state.toothPosition.other ? this.state.toothPosition.other.decayed.value : ''}
                           onChange={e => {
                             this.state.toothPosition.other.decayed.value = e.target.value
                           }}
                           onFocus={e=>{
                             this.setState({activeTdId:""})
                           }}
                           className={styles.row_deep_input}/>
                  </div>

                  <div style={{marginTop:10}}>
                    <div style={{display:'flex'}}>
                      <div>
                        楔形缺损:
                      </div>
                      <table className={styles.table_row}
                             onClick={() => this.showModalCheckTooth("other", "wedgeShapedDefect")}>
                        <tbody>
                        <tr>
                          <th className={styles.line_th_row} id='leftTopTooth'>
                            {toothUtils.showTooth(1, this.state.toothPosition.other ? this.state.toothPosition.other.wedgeShapedDefect.toothPosition : '')}
                          </th>
                          <th className={styles.line_th_col} id='rightTopTooth'>
                            {toothUtils.showTooth(2, this.state.toothPosition.other ? this.state.toothPosition.other.wedgeShapedDefect.toothPosition : '')}

                          </th>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row} id='leftBotTooth'>
                            {toothUtils.showTooth(4, this.state.toothPosition.other ? this.state.toothPosition.other.wedgeShapedDefect.toothPosition : '')}
                          </td>
                          <td className={styles.line_col} id='rightBotTooth'>
                            {toothUtils.showTooth(3, this.state.toothPosition.other ? this.state.toothPosition.other.wedgeShapedDefect.toothPosition : '')}

                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                    <Input placeholder='请输入'
                           defaultValue={this.state.toothPosition.other ? this.state.toothPosition.other.wedgeShapedDefect.value : ''}
                           onChange={e => {
                             this.state.toothPosition.other.wedgeShapedDefect.value = e.target.value
                           }}
                           onFocus={e=>{
                             this.setState({activeTdId:""})
                           }}
                           className={styles.row_deep_input}/>
                  </div>

                  <div style={{marginTop:10}}>
                    <div style={{display:'flex'}}>
                      <div>
                        不良修复体:
                      </div>
                      <table className={styles.table_row}
                             onClick={() => this.showModalCheckTooth("other", "defectiveProsthesis")}>
                        <tbody>
                        <tr>
                          <th className={styles.line_th_row} id='leftTopTooth'>
                            {toothUtils.showTooth(1, this.state.toothPosition.other ? this.state.toothPosition.other.defectiveProsthesis.toothPosition : '')}
                          </th>
                          <th className={styles.line_th_col} id='rightTopTooth'>
                            {toothUtils.showTooth(2, this.state.toothPosition.other ? this.state.toothPosition.other.defectiveProsthesis.toothPosition : '')}
                          </th>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row} id='leftBotTooth'>
                            {toothUtils.showTooth(4, this.state.toothPosition.other ? this.state.toothPosition.other.defectiveProsthesis.toothPosition : '')}

                          </td>
                          <td className={styles.line_col} id='rightBotTooth'>
                            {toothUtils.showTooth(3, this.state.toothPosition.other ? this.state.toothPosition.other.defectiveProsthesis.toothPosition : '')}
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                    <Input placeholder='请输入'
                           defaultValue={this.state.toothPosition.other ? this.state.toothPosition.other.defectiveProsthesis.value : ''}
                           onChange={e => {
                             this.state.toothPosition.other.defectiveProsthesis.value = e.target.value
                           }}
                           onFocus={e=>{
                             this.setState({activeTdId:""})
                           }}
                           className={styles.row_deep_input}/>
                  </div>
                </Form>
              </TabPane>
            </Tabs>
            <Modal
              title="选择牙位"
              visible={this.state.visibleToothBit2}
              destroyOnClose={true}
              onOk={this.handleOkSel}
              onCancel={this.handleCancelSelTooth}
              className={styles.select_tooth}
              width={1070}
            >
              <ToothSelect toothPosition = {this.state.toothP} onRef={(ref) => this.tooth = ref}></ToothSelect>
            </Modal>
          </div>
        </div>
      </div>
    );
  }
}

export default Periodontal;
