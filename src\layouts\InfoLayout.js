import React, { Fragment } from 'react';
import Link from 'umi/link';
import { connect } from 'dva';
import styles from './InfoLayout.less';


@connect(({ login }) => ({
    login
}))

class InfoLoayout extends React.Component {
    state = {

    }
    componentDidMount() {
    }

    render() {
        const { children } = this.props;
        return (
            <div className={styles.container}>
                <div className={styles.content}>
                    {children}
                </div>
            </div>
        );
    }
}

export default InfoLoayout;
