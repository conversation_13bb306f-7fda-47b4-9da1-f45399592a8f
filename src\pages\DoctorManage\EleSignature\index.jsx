import { Table } from 'antd';
import { AudioOutlined } from '@ant-design/icons';
import React, { useState, useRef, Component } from 'react';
import { useIntl, FormattedMessage } from 'umi';
import { GridContent } from '@ant-design/pro-layout';
import styles from './style.less';//引入样式


class majorManage extends Component {
  constructor(props) {
    super(props);
    this.state = {
    };
  }
  render() {
    const columns = [
      {
        title: '专业代码',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '中文名称',
        dataIndex: 'age',
        key: 'age',
      },
      {
        title: '英文名称',
        dataIndex: 'age',
        key: 'age',
      },
      {
        title: '描述',
        dataIndex: 'age',
        key: 'age',
      }
    ];

    const data = [
      {
        key: '1',
        name: '<PERSON>',
        age: 32,
        address: 'New York No. 1 Lake Park',
        tags: ['nice', 'developer'],
      },
      {
        key: '2',
        name: '<PERSON>',
        age: 42,
        address: 'London No. 1 Lake Park',
        tags: ['loser'],
      },
      {
        key: '3',
        name: '<PERSON>',
        age: 32,
        address: 'Sidney No. 1 Lake Park',
        tags: ['cool', 'teacher'],
      },
    ];
    return (
      <GridContent>
        <div className={styles.imageMagecontent}>
          <Table columns={columns} dataSource={data} />
        </div>
      </GridContent>
    );
  }
}

export default majorManage;
