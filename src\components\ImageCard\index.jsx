import React, { Component, useState } from 'react';
import { Tooltip, Row, Col, Image, Modal, Form, message, Spin } from 'antd';
import { ExclamationCircleFilled } from '@ant-design/icons';

import styles from './style.less';//样式引入
//图标
import deleteImg from '@/assets/Delete.png';
import blueDeleteImg from '@/assets/<EMAIL>'
import narrow from '@/assets/narrow.png';
import enlarge from '@/assets/enlarge.png';
import rotate from '@/assets/rotate.png';
import img from '@/assets/img1.png';
import connectImg from '@/assets/connect.png'
import Preview from '@/assets/<EMAIL>';
import { connect } from 'dva';
import commonStyle from '@/components/common.less';//公共样式引入
import { visitIndicatorType } from '@/utils/common.js';
import PropTypes from "prop-types";
//公共字典

//图片转换
class ImageCard extends React.Component {
  static propsType = {
    disableDelete:PropTypes.any,   // 是否禁用删除
    type:PropTypes.any,
    key:PropTypes.any,
    connected:PropTypes.any,
    imageData:PropTypes.any,
    getList:PropTypes.any,
  }
  static defaultProps = {
    disableDelete: false,
    type:null,
    key:null,
    connected:null,
    imageData:null,
    getList:null,
  }

  constructor(props) {
    super(props);
    this.state = {
      mouseIn: false, //移入垃圾桶标识
      deleteModal: false, //确认删除的弹窗
      previewVisible: false, //预览的弹窗
      loading: false,//图片转换时的loading
      key: '',
      R: 0, //做旋转操作的时候旋转参数
      SS: 1, //做放大缩小操作的时候的参数
      S: 1,
      i: 0,
      // deleteImgByIdParams: {
      //   id: '1',//影像标识  std_check表中的id
      // },
      imagePreviewParams: {
        //影像预览入参
        id: '',
        tenantId: localStorage.getItem('tenantId'), //平台标识
      },
      previewData: {}, //预览的返回数据
      connectDoctorList: [], //删除关联病例时查询的关联医生信息
      url: null, //解析后的url
    };
  }
  // 初始化
  componentDidMount() {
    console.log('imageCard123123 : ',this.props);
    /*
      connected: 0
      disableDelete: true
      dispatch: ƒ (action)
      getList: ƒ (pageNum)
      imageData: {id: 31, tenantId: '3a9d9c680c364df9a84bfd5de91e9324', fileUrl: 'tenant/3a9d9c680c364df9a84bfd5de91e9324/inspectionImage/ef3bfb22a9414775b4eef7bbbf89b1a9.jpg', fileDesc: '', createdGmtAt: '2022-07-28', …}
      key: null
      type: "dateType"
    * */
    const {
      connected,  //
      disableDelete,
      dispatch,
      getList,
      imageData,  // {id: 31,
      key,
      type,
    } = this.props
    this.cdnImg();// cdn图片转换
  }
  // }
  // 删除关联及其影像
  deleteCheckImg = (value) => {
    const { dispatch, imageData } = this.props;
    let params = {
      id: imageData.id,
    };
    // console.log(params, this.props, '删除关联及其影像入参99---');
    if (dispatch) {
      dispatch({
        type: 'homePage/deleteCheckImg',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.hideFileModal();
            this.props.getList(1);
          }
        },
      });
    }
  };

  // 删除影像关联
  deleteCheckLink = (value) => {
    const { dispatch } = this.props;
    let params = this.state.deleteImgByIdParams;
    // this.props.getCheckImgByDate()  //重新查询影像资料--时间 列表数据
    if (dispatch) {
      dispatch({
        type: 'homePage/deleteCheckLink',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            if (this.props.type == 'dateType') {
              // this.props.getCheckImgByDate()  //重新查询影像资料--时间 列表数据
            } else {
              // this.props.getCheckImgByClass()  //重新查询影像资料 --分类列表数据
            }
          }
        },
      });
    }
  };
  // 删除  先请求查询关联医生信息
  openDeleteModal = (id) => {
    const { connected, dispatch } = this.props;
    const { deleteModal } = this.state;
    let params = {
      id: id,
      tenantId: localStorage.getItem("tenantId"),
    };

    if (dispatch) {
      dispatch({
        type: 'homePage/findLinkEmrs',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              connectDoctorList: res.rows,
            },
              () => {
                Modal.confirm({
                  width: '492px',
                  visible: {
                    deleteModal,
                  },
                  // centered:true, //弹窗居中
                  title: (
                    <div style={{ fontSize: '14px' }}>
                      {connected != 1
                        ? ' 删除影像系统会将对应关联病历中的这张影像同步删除！您是否继续对此份影像资料执行【删除】操作？'
                        : '影像删除后将无法再找回，您是否继续执行【删除】操作？'}
                    </div>
                  ),
                  icon: <ExclamationCircleFilled />,
                  content:
                    connected != 1 ? (
                      <div style={{ color: 'rgba(0,0,0,0.45)', fontSize: '12px' }}>
                        注：
                        <span>
                          {this.state.connectDoctorList &&
                            this.state.connectDoctorList.map((item, index) => (
                              <span
                                key={index}
                                style={{
                                  display: 'inline-block',
                                  width: '93%',
                                  marginLeft: index > 0 ? '24px' : '0',
                                }}
                              >
                                当前影像已关联病历【{item.createdGmtAt}{' '}
                                {item.userName}{' '}
                                {visitIndicatorType[item.isFirstVisit] || '复诊'}】
                              </span>
                            ))}
                        </span>
                      </div>
                    ) : (
                      <></>
                    ),
                  okText: '删除',
                  cancelText: '取消',
                  onOk: () => this.saveFileModal(),
                  onCancel: () => this.hideFileModal(),
                });
              },
            );


          }
        },
      });
    }


  };
  // 确认删除
  saveFileModal = () => {
    this.deleteCheckImg(); //删除关联及影像
  };
  // 关闭确认删除弹窗
  hideFileModal = () => {
    this.setState({
      deleteModal: false,
    });
  };
  // 关闭影像预览弹窗
  hideModal = () => {
    this.setState({
      previewVisible: false,
    });
  };
  // 打开影像预览弹窗
  setVisible = (value, imageData) => {
    // console.log(value, imageData,'9999---yulan')
    // this.imagePreview(this.props.imageData.id)
    this.setState(
      {
        previewVisible: true,
        R: 0,
        SS: 1,
      },
      () => {
        this.imgstyle.style.transform =
          'rotate(' + this.state.R + 'deg) scale(' + this.state.SS + ',' + this.state.SS + ')';
      },
    );
  };

  // 影像预览接口调用
  imagePreview = (id) => {
    const { dispatch } = this.props;
    let params = {
      ...this.state.imagePreviewParams,
      id: id,
    };
    // this.props.getCheckImgByDate()  //重新查询影像资料--时间 列表数据
    if (dispatch) {
      dispatch({
        type: 'homePage/previewImage',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            // console.log(res, '预览结果999----');
            this.setState({
              previewData: (res.content = {
                fileUrl: '', //文件存储地址 阿里云OSS地址
                createdGmtAt: '', //上传时间
                fileDesc: '', //文件描述
                shootingTime: '', //拍摄时间
                classCode: '', //分类编码
                className: '', //分类名称
              }),
            });
          }
        },
      });
    }
  };
  // startDrag=()=>(
  //   document.getElementById("img")
  // );

  //获取相关CSS属性
  // getCss = (o, key) => {
  //   return o.currentStyle
  //     ? o.currentStyle[key]
  //     : document.defaultView.getComputedStyle(o, false)[key];
  // };
  // //拖拽的实现
  // startDrag = (bar, target, callback) => {
  //   bar = document.getElementById("img")
  //   target = document.getElementById("img");
  //   if (this.getCss(target, "left") !== "auto") {
  //     params.left = this.getCss(target, "left");
  //   }
  //   if (this.getCss(target, "top") !== "auto") {
  //     params.top = this.getCss(target, "top");
  //   }
  //   //o是移动对象
  //   bar.onmousedown = event => {
  //     params.flag = true;
  //     if (!event) {
  //       event = window.event;
  //       //防止IE文字选中
  //       bar.onselectstart = () => {
  //         return false;
  //       };
  //     }
  //     var e = event;
  //     params.currentX = e.clientX;
  //     params.currentY = e.clientY;
  //   };

  //   document.onmouseup = () => {
  //     params.flag = false;
  //     if (this.getCss(target, "left") !== "auto") {
  //       params.left = this.getCss(target, "left");
  //     }
  //     if (this.getCss(target, "top") !== "auto") {
  //       params.top = this.getCss(target, "top");
  //     }
  //   };

  //   document.onmousemove = event => {
  //     var e = event ? event : window.event;

  //     if (params.flag) {
  //       var nowX = e.clientX,
  //         nowY = e.clientY;
  //       var disX = nowX - params.currentX,
  //         disY = nowY - params.currentY;
  //       target.style.left = parseInt(params.left, 10) + disX + "px";
  //       target.style.top = parseInt(params.top, 10) + disY + "px";

  //       if (typeof callback === "function") {
  //         callback(
  //           (parseInt(params.left, 10) || 0) + disX,
  //           (parseInt(params.top, 10) || 0) + disY
  //         );
  //       }

  //       if (event.preventDefault) {
  //         event.preventDefault();
  //       }
  //       return false;
  //     }
  //   };
  // };

  // 缩小图片
  narrowImg = () => {
    const { S = 1, i, SS, R } = this.state;
    if (i <= 0) {
      this.setState(
        {
          S: S + 1,
          i: i - 1,
          SS: 1 / (S + 1),
        },
        () => {
          // 解决异步改变state的值
          // console.log(this.state.S, this.state.i, this.state.R, this.state.SS, 'mmmmm');
          this.imgstyle.style.transform =
            'rotate(' + this.state.R + 'deg) scale(' + this.state.SS + ',' + this.state.SS + ')';
        },
      );
    } else {
      this.setState(
        () => ({
          S: S - 1,
          i: i - 1,
          SS: 1 * (S - 1),
        }),
        () =>
        (this.imgstyle.style.transform =
          'rotate(' + this.state.R + 'deg) scale(' + this.state.SS + ',' + this.state.SS + ')'),
      );
    }
  };

  // 放大图片
  showbig = () => {
    const { S, i, SS, R } = this.state;
    // console.log('放大', this.imgstyle, S, i, SS, R);
    if (i >= 0) {
      this.setState(
        () => ({
          S: S + 1,
          i: i + 1,
          SS: 1 * (S + 1),
        }),
        () =>
        (this.imgstyle.style.transform =
          'rotate(' + this.state.R + 'deg) scale(' + this.state.SS + ',' + this.state.SS + ')'),
      );
    } else {
      this.setState(
        () => ({
          S: S - 1,
          i: i + 1,
          SS: 1 / (S - 1),
        }),
        () =>
        (this.imgstyle.style.transform =
          'rotate(' + this.state.R + 'deg) scale(' + this.state.SS + ',' + this.state.SS + ')'),
      );
    }
  };
  // 顺时针旋转
  rotateright = () => {
    const { R, SS } = this.state;
    this.setState(
      () => ({ R: R + 90 }),
      () =>
      (this.imgstyle.style.transform =
        'rotate(' + this.state.R + 'deg) scale(' + this.state.SS + ',' + this.state.SS + ')'),
    );
  };

  // cdn图片转换
  cdnImg = () => {
    const { dispatch } = this.props;
    console.log('dbPathTransformServiceres123123 : ',this.props);
    let params = {
      // tenantId:localStorage.getItem('tenantId'),
      filePath: this.props.imageData.fileUrl,
    };
    this.setState({ loading: true });

    if (dispatch) {
      dispatch({
        type: 'WriteMedical/dbPathTransformService',
        payload: params,
        callback: (res) => {
          console.log('dbPathTransformServiceres :1: ',res);
          this.setState({
            loading: false,
            url: res,
          });
        },
      });
    }
  };
  //鼠标移入移出影像图片
  onMouseIn = (mouseIn, uid) => {
    this.setState({
      ['mouseIn' + uid]: mouseIn,
    });
  };
  //鼠标移入移出事件
  chooseonMouseIn = (mouseIn, id) => {
    this.setState({
      mouseIn: mouseIn
    })
  }
  render() {
    const {
      connected = 0,
      type,
      imageData,
      disableDelete, // 是否禁用
    } = this.props;
    // console.log("imageDataimageData==",imageData)
    const { loading, deleteModal, previewVisible, R, SS, previewData, url,mouseIn } = this.state;
    console.log('imageData123123 :: ',url);

    return (
      <div className={styles.imageDiv}>
        {connected != 1 ? (
          <img
            src={connectImg}
            alt=""
            style={{ position: 'absolute', top: '0', left: '0', zIndex: 99 }}
          />
        ) : (
          <></>
        )}

        <Spin spinning={loading}>
          <div
            onMouseOver={() => this.onMouseIn(true, url)}
            onMouseOut={() => this.onMouseIn(false, url)}
            className={styles.imgborder}
            onClick={this.setVisible.bind(this, url)}
          >
            <img src={url} className={styles.showImg} alt="" />
            <div hidden={!this.state['mouseIn' + url]} className={styles.ctimgdelete}>
              <div style={{ marginTop: '30%', display: 'flex' }}>
                <div style={{ cursor: 'pointer', margin: '0 auto' }}>
                  <img
                    src={Preview}
                    className={styles.icon_delete}
                    alt=""
                  />
                  <span className={styles.deleteFont}>预览</span>
                </div>

              </div>
            </div>
          </div>
        </Spin>
        <div style={{ padding: '8px', color: 'rgb(0,0,0,0.45)' }}>
          <p className={`${styles.pLineHeight} ${styles.ellipse}`}>
            影像分析：{imageData.fileDesc}
          </p>
          <p style={{ lineHeight: '20px', margin: 0 }}>上传时间：{imageData.createdGmtAt}</p>
          {(!disableDelete && imageData.createId == localStorage.getItem("userId") && imageData.organizationId == localStorage.getItem("organizationId")) ?
            <img
              src={mouseIn ? blueDeleteImg :deleteImg}
              alt=""
              className={styles.deleteImg}
              onMouseOver={() => this.chooseonMouseIn(true)}
              onMouseOut={() => this.chooseonMouseIn(false)}
              onClick={() => {
                this.openDeleteModal(imageData.id);
              }}
            />:""
          }
        </div>

        <Modal
          width={790}
          title="预览影像"
          visible={previewVisible}
          onOk={this.hideModal}
          onCancel={this.hideModal}
          footer={null}
        >
          <Row gutter={16}>
            <Col className="gutter-row" span={14}>
              <div>
                <div className={styles.title}>
                  <Tooltip title={imageData.className}>
                    <Col
                      span={12}
                      className={commonStyle.font_16}
                      style={{
                        fontWeight: 600, marginBottom: '8px', overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        width: '174px',
                        whiteSpace: 'nowrap' }}
                    >
                      {imageData.className}
                    </Col>
                  </Tooltip>
                  <Col>
                    <span style={{ cursor: 'pointer' }} onClick={this.narrowImg}>
                      <img src={narrow} alt="" className={styles.image} />
                      <span className={styles.tipsFont}>缩小</span>
                    </span>
                    <span style={{ cursor: 'pointer' }} onClick={this.showbig}>
                      <img src={enlarge} alt="" className={styles.image} />
                      <span className={styles.tipsFont}>放大</span>
                    </span>
                    <span style={{ cursor: 'pointer' }} onClick={this.rotateright}>
                      <img src={rotate} alt="" className={styles.image} />
                      <span className={styles.tipsFont}>旋转</span>
                    </span>
                  </Col>
                </div>
                <div className={styles.ListHeight}>
                  <div className={styles.imageBorder}>
                    <div>
                      <img
                        id="img"
                        ref={(dom) => {
                          this.imgstyle = dom;
                        }}
                        src={url}
                        alt=""
                        // style={{ transform:'rotate(0deg) scale(1,1)'}} width: 450, height: 309
                        style={{
                          transform: 'rotate(' + R + 'deg) scale(' + SS + ',' + SS + ')',
                          width: '100%', objectFit: 'cover'
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </Col>
            <Col className="gutter-row" span={9}>
              <div style={{ marginLeft: '20px' }}>
                <div
                  className={commonStyle.font_16}
                  style={{ fontWeight: 600, marginBottom: '8px' }}
                >
                  影像信息
                </div>
                <Form
                  name="basic"
                  colon={false}
                  labelCol={{ span: 7, }}
                  wrapperCol={{ span: 17, }}
                >
                  <Form.Item label="拍摄时间：" name="type"
                  >
                    <span style={{ color: 'rgba(0,0,0,0.45)' }}> {imageData.shootingTime}</span>
                  </Form.Item>
                  <Form.Item label="上传时间：" name="type" style={{ marginTop: '-10px' }}>
                    <span style={{ color: 'rgba(0,0,0,0.45)' }}>{imageData.createdGmtAt}
                    </span>
                  </Form.Item>
                </Form>
                {/* <div style={{ color: 'rgba(0,0,0,0.45)', lineHeight: '20px', marginBottom: '8px' }}>
                  <span>拍摄时间：{imageData.shootingTime}</span>
                </div>
                <div style={{ color: 'rgba(0,0,0,0.45)' }}>
                  <span>上传时间：{imageData.createdGmtAt}</span>
                </div> */}
                <div
                  className={commonStyle.font_16}
                  style={{ fontWeight: 600, marginTop: '12px', marginBottom: '8px' }}
                >
                  影像分析
                </div>
                <Form name="basic" colon={false}
                  // labelCol={{ span: 7, }}
                  wrapperCol={{ span: 24, }}
                >
                  <Form.Item
                    label=""
                    name="type"
                  >
                    <span style={{ display: 'block', height: '200px', overflowY: 'auto', color: 'rgba(0,0,0,0.45)', fontFamily: 'PingFangSC-Regular, PingFang SC' }}>{imageData.fileDesc}</span>
                  </Form.Item>
                </Form>
                {/* <div>
                  <span style={{ color: 'rgba(0,0,0,0.45)' }}>{imageData.fileDesc}</span>
                </div> */}
              </div>
            </Col>
          </Row>
        </Modal>
      </div>
    );
  }
}
export default connect(() => ({}))(ImageCard);
