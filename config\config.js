// https://umijs.org/config/
import { defineConfig } from 'umi';
import defaultSettings from './defaultSettings';
import proxy from './proxy';
import routes from "./routes";
const CompressionWebpackPlugin = require('compression-webpack-plugin'); //引入gzip压缩
const prodGzipList = ['js', 'css']; //gzip压缩要压缩的文件

const { REACT_APP_ENV } = process.env;


export default defineConfig({
  publicPath: process.env.NODE_ENV === 'production' ? 'https://js9-test.5i5ya.com/' : '/',
  hash: true,
  antd: {},
  dva: {
    hmr: true,
  },
  locale: {
    // default zh-CN
    default: 'zh-CN',
    // default true, when it is true, will use `navigator.language` overwrite default
    antd: true,
    baseNavigator: false,
  },
  dynamicImport: {
    loading: '@/components/PageLoading/index',
  },
  targets: {
    ie: 11,
  },
  // webpack 拆包后将包引入index.js
  chunks:
    process.env.NODE_ENV === 'production'
      ? ['umi', 'react', 'react-dom', 'react-router', 'antdesigns', 'vendors', 'react_module']
      : undefined,
  //umi 拆分及gzip压缩配置
  chainWebpack: (config) => {
    config.module
      .rule('otf') //自己取的名字
      .test(/\.(otf|ttf|TTF)(\?.*)?$/) //匹配规则
      .use('url-loader') //使用的loader不是file-loader
      .loader(require.resolve('url-loader')) //	倒入loader
      .tap((options) => ({ // 定义打包的一些规则
        ...options,
        name: 'static/fonts/[name].[hash:8].[ext]',
      }));
    // webpack 拆包
    if (process.env.NODE_ENV === 'production') {
      config.optimization.splitChunks({
        chunks: 'all', // 提取 chunks 的时候从哪里提取，如果为 all 那么不管是不是 async 的都可能被抽出 chunk，为 initial 则会从非 async 里面提取。
        automaticNameDelimiter: '.', // 文件名分隔符
        name: true, // chunk 的名称，如果设置为固定的字符串那么所有的 chunk 都会被合并成一个，这就是为什么 umi 默认只有一个 vendors.async.js。
        minSize: 30000, // byte, == 30 kb，越大那么单个文件越大，chunk 数就会变少（针对于提取公共 chunk 的时候，不管再大也不会把动态加载的模块合并到初始化模块中）当这个值很大的时候就不会做公共部分的抽取了
        maxSize: 0, // 文件的最大尺寸，优先级：maxInitialRequest/maxAsyncRequests < maxSize < minSize，需要注意的是这个如果配置了，umi.js 就可能被拆开，最后构建出来的 chunkMap 中可能就找不到 umi.js 了。
        minChunks: 1, // 被提取的一个模块至少需要在几个 chunk 中被引用，这个值越大，抽取出来的文件就越小
        maxAsyncRequests: 10, // 在做一次按需加载的时候最多有多少个异步请求，为 1 的时候就不会抽取公共 chunk 了
        maxInitialRequests: 5, // 针对一个 entry 做初始化模块分隔的时候的最大文件数，优先级高于 cacheGroup，所以为 1 的时候就不会抽取 initial common 了。
        cacheGroups: {
          antdesigns: {
            // antd pro相关
            name: 'antdesigns', //打包后的名称
            chunks: 'all', //提取 chunks 的时候从哪里提取，如果为 all 那么不管是不是 async 的都可能被抽出 chunk，为 initial 则会从非 async 里面提取。
            test: /(@antd|antd|@ant-design|@antv)/, //资源匹配
            priority: 10, //优先级
          },
          vendors: {
            //第三方依赖
            name: 'vendors',
            chunks: 'all',
            test: /(lodash|lodash-decorators|bizcharts|moment|dva|immutable)/,
            priority: 10,
          },
          react: {
            //react相关
            chunks: 'all',
            name: 'react',
            test: /(react)[\\/]/,
            enforce: true, //选项：true/false。为true时，忽略minSize，minChunks
          },
          react_dom: {
            //react-dom相关
            chunks: 'all',
            name: 'react-dom',
            test: /(react-dom)[\\/]/,
            enforce: true,
          },
          react_router: {
            //react-router相关
            chunks: 'all',
            name: 'react-router',
            test: /(react-router)[\\/]/,
            enforce: true,
          },
          react_module: {
            //其余用到相对较少的react相关
            chunks: 'all',
            name: 'react_module',
            test: /(react-umeditor|react-beautiful-dnd)/,
            enforce: true,
          },
        },
      });
      config.plugin('compression-webpack-plugin').use(
        //执行
        new CompressionWebpackPlugin({
          // filename: 文件名称，让它保持和未压缩的文件同一个名称，不设置。
          algorithm: 'gzip', // 指定生成gzip格式
          test: new RegExp('\\.(' + prodGzipList.join('|') + ')$'), // 匹配哪些格式文件需要压缩
          threshold: 10240, //对超过10k的数据进行压缩
          minRatio: 0.6, // 压缩比例，值为0 ~ 1
        }),
      );
    }
  },


  // 路由
  routes,
  // Theme for antd: https://ant.design/docs/react/customize-theme-cn
  theme: {
    // ...darkTheme,
    'primary-color': defaultSettings.primaryColor,
  },
  // @ts-ignore
  title: false,
  ignoreMomentLocale: true,
  proxy: proxy[REACT_APP_ENV || 'dev'],
  manifest: {
    basePath: '/',
  },
});
