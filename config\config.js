// https://umijs.org/config/
import os from 'os';
import pageRoutes from './router.config';
import webpackPlugin from './plugin.config';
import defaultSettings from '../src/defaultSettings';
import EnvAddress from './env_path'



const plugins = [
  [
    'umi-plugin-react',
    {
      antd: true,
      dva: {
        hmr: true,
      },
      targets: {
        ie: 11,
      },
      locale: {
        enable: true, // default false
        default: 'zh-CN', // default zh-CN
        baseNavigator: false, // default true, when it is true, will use `navigator.language` overwrite default
      },
      dynamicImport: {
        loadingComponent: './components/PageLoading/index',
        level: 2
      },
      // externals: {
      //   '@antv/data-set': 'DataSet',
      //   bizcharts: 'BizCharts',
      // },
      ...(!process.env.TEST && os.platform() === 'darwin'
        ? {
          dll: {
            include: ['dva', 'dva/router', 'dva/saga', 'dva/fetch'],
            exclude: ['@babel/runtime'],
          },
        }
        : {}),
    },
  ],
];

export default {
  // add for transfer to umi
  plugins,
  targets: {
    ie: 11,
    chrome: 45,
  },
  define: {
    APP_TYPE: process.env.APP_TYPE || '',
    ENV_PATH: process.env.ENV_PATH || '',
  },
  // 路由配置
  routes: pageRoutes,
  // Theme for antd
  // https://ant.design/docs/react/customize-theme-cn
  theme: {
    'primary-color': defaultSettings.primaryColor,
  },
  externals: {
    'react':'window.React',
    'react-dom': 'window.ReactDOM',
    'moment':'moment',
    'jquery':'jQuery',
    '@antv/data-set': 'DataSet',
    'popper.js':'Popper',
    'tooltip.js':'Tooltip',
    '@antv/g2':'G2',
    'bizcharts':'BizCharts',
    'mathjs':'window.math',
    'uuid':'uuid',
    'echarts':'echarts'
  },
  proxy: EnvAddress,
  ignoreMomentLocale: true,
  lessLoaderOptions: {
    javascriptEnabled: true,
  },
  disableRedirectHoist: true,
  cssLoaderOptions: {
    modules: true,
    getLocalIdent: (context, localIdentName, localName) => {
      if (
        context.resourcePath.includes('node_modules') ||
        context.resourcePath.includes('ant.design.pro.less') ||
        context.resourcePath.includes('global.less')
      ) {
        return localName;
      }
      const match = context.resourcePath.match(/src(.*)/);
      if (match && match[1]) {
        const antdProPath = match[1].replace('.less', '');
        const arr = antdProPath
          .split('/')
          .map(a => a.replace(/([A-Z])/g, '-$1'))
          .map(a => a.toLowerCase());
        return `antd-pro${arr.join('-')}-${localName}`.replace(/--/g, '-');
      }
      return localName;
    },
  },
  manifest: {
    basePath: process.env.NODE_ENV === 'development'?'/':'https://js4-test.5i5ya.com/',
  },
  publicPath: process.env.NODE_ENV === 'development'?'/':'https://js4-test.5i5ya.com/',
  outputPath: './dist',
  urlLoaderExcludes: [/.svg$/],
  chainWebpack: webpackPlugin,
  hash: true,
  autoprefixer: {
    flexbox: true
  }
};
