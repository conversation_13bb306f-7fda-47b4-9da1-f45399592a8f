import React,{ Component } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import withRouter from 'umi/withRouter';
import confirm from '@/assets/AppointmentRules/confirm.png';
import router from 'umi/router';
import QRCode from 'qrcode';
import styles from './AppointmentDetails.less';
import { Button, Dropdown, Form, Icon, Input, Modal, Popover, Spin, message } from 'antd';
import AppointmentLeft from '@/pages/Appointment/components/AppointmentModal/AppointmentLeft';
import orangeSigh from '@/assets/AppointmentRules/orangeSigh.png';
import shortMessage from '@/assets/AppointmentRules/shortMessage.png';
import shortMessageBlue from '@/assets/AppointmentRules/shortMessageBlue.png';
import greenYes from '@/assets/AppointmentRules/greenYes.png';
import ashAsk from '@/assets/AppointmentRules/ashAsk.png';
import redDifference from '@/assets/AppointmentRules/redDifference.png';
import legend5 from '@/assets/customerServiceNew/legend5.png';
import xin from '@/assets/AppointmentRules/xin.png';
import contentsTextImg1 from '@/assets/AppointmentRules/fuzhenfang.png';
import Ellipsis from '@/components/Ellipsis';
import iphonHui from '@/assets/AppointmentRules/iphonHui.png';
import jizhenfang from '@/assets/AppointmentRules/jizhenfang.png';
import { getOrganizationInfo, noEmoji, SMS_YYQR } from '@/utils/utils';
import Manualicon from "@/components/AgendaCaleandar/Elements/Manualicon"; // 治疗标签
import {
  getSmsTemplateInfo  // 根据模板code、用户ID、租户和机构查询模板信息，管理权限
} from '@/services/newSettlement'
import classNames from 'classnames';
import timeoutIcon from '@/assets/home/<USER>';
import timeIcon from '@/assets/home/<USER>';
import { formatTime } from '@/utils/CalendarUtils';
const { TextArea } = Input;
@Form.create()
@withRouter
@connect(({ Getscreen, homeIndex, loading }) => ({
  Getscreen, homeIndex,
  loading
}))
export default class AppointmentDetails extends Component {
  state = {
    remarksValue: '',         // 备注
    visiblePopover: false,    // 确认浮窗状态
    editRemarkType: false,    // 编辑备注状态
    remarkValue: null,        // 备注值
    sendSMS: false,           // 发送信息状态
    oftenTel: null,           // 手机号
    editOftenTelType: false,  // 编辑手机号状态
    msgName:null,             // 发送信息称呼
    editMsgNameType:null,     // 编辑发送信息称呼
    smsTemplateInfo: null,     // 信息权限控制
    WaitFormatTime:null,    // 格式化后的等待时间
    isWaitStute: null,      // 是否是等待状态 0:不等待 1:等待 2:等待超时
    materialQRImgUrl:'',     // 进销存获取患者信息的二维码
  }

  componentDidMount() {
    let { query } = this.props.location;
    const { customerId, appointmentId, bookingDetails } = query || {}
    if (bookingDetails) {
      this.setState({
        visiblePopover: true
      })
    }
    this.getSmsTemplateInfo();
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    if (this.props.maximizeInfo && prevProps.maximizeInfo != this.props.maximizeInfo) {
      this.IntervalByformatTime(this.props.maximizeInfo)
      // 获取患者信息的二维码
      if(this.props.maximizeInfo.materialQRKey!=null&&this.props.maximizeInfo.materialQRKey!=''){
        this.getMaterialQRCode(this.props.maximizeInfo.materialQRKey)
      }
    }
  }

  // 开启等待时长的正计时
  IntervalByformatTime=(appointmentInfoOfTableDto)=>{
    // const { appointmentInfoOfTableDto } = this.props
    let {
      isComeVisit,               // 是否已到诊
      isConsultationVisit,       // 0:未接诊 1:已接诊
      waitingTimes,              // 等待时长(秒)
      isWaitingOver,             // 等待超时(1是，0否)
    } = appointmentInfoOfTableDto || {}

    console.log('appointmentInfoOfTableDto123123 :: ',appointmentInfoOfTableDto);
    const OrganizationInfo = getOrganizationInfo()// 收费模式(1规范化，2简洁化，3无医生)


    if(
      isComeVisit == 1 &&
      isConsultationVisit == 0 &&
      OrganizationInfo.chargeModel == 1 &&
      waitingTimes != null
    ) {
      // 开始等待时长的计时
      this.setState({
        isWaitStute: isWaitingOver ? 2 : 1,  // 是否是等待状态 0:不等待 1:等待 2:等待超时
      },()=>{
        if (this.IntervalId) { clearInterval(this.IntervalId) }
        let seconds = waitingTimes ? waitingTimes : 0; // 开始的秒数
        this.IntervalId = setInterval(() => {
          this.setState({
            WaitFormatTime: formatTime(seconds) // 每秒输出一次格式化后的时间
          })
          seconds++;
        }, 1000);
      })
    }else  {
      this.setState({
        isWaitStute: null,   // 补充如果是已接诊状态则清除等待时长的计时
        WaitFormatTime:null,
      })
    }
  }

  // 根据模板code、用户ID、租户和机构查询模板信息，管理权限
  getSmsTemplateInfo = async () => {
    let params = { smsCode: SMS_YYQR }
    let dataBySmsTemplateInfo = await getSmsTemplateInfo(params);
    /*
      dataId: null
      id: 517                       // 主键ID
      isSmsManage: true             // 信息是否有管理权限 true 有 false 没有
      parentSortNum: 2
      sendMode: "新建预约——发送信息"    // 发送方式
      sendTemplateContent: "尊敬的#patientName#，#organization#提醒您在#date#的预约，就诊时间#time#。诊所位于#address# 您的预约时间会为您保留15分钟， 期待您的到来。电话：#telephone#"
      showEnTemplateContent: null
      showEnTemplateContentPreview: null
      showTemplateContent: "尊敬的【患者姓名】，【诊所名称】提醒您在yyyy-mm-dd的预约，就诊时间hh:mm。诊所位于【诊所地址】，驾车路线：【驾车路线】，乘车路线：【乘车路线】。 您的预约时间会为您保留15分钟， 期待您的到来。电话：【诊所电话】"
      showTemplateContentPreview: null
      smsNum: 0                 // 信息余量（C端查询模板信息用）
      smsTemplateSonDtoList: null
      sonSortNum: 1
      templateCode: "YYTX"        // 模板code
      templateName: "预约提醒"      // 模板名称
      templateStatus: 1           // 模板状态：0禁用 1.启用
      templateType: 1             // 模板类型：0免费，1.收费
     */
    const {
      code,
      content,
    } = dataBySmsTemplateInfo || {}
    if (code == 200 && content) {
      const {
        templateType,    // 模板类型：0免费，1.收费
        templateStatus,  // 模板状态：0禁用 1.启用
        isSmsManage,     //  信息是否有管理权限 true 有 false 没有
        smsNum,
      } = content || {}
      this.setState({
        smsTemplateInfo: {
          ...content,
        }
      })
    } else {
      this.setState({ smsTemplateInfo: null })
    }
  }

  // 关闭
  handleCancel = () => {
    router.push({ // 清除任务跳转过来的参数
      pathname: `/subscribe/fullscreen`
    })
    const { closeModal, minimizeDetailsIndex, clearMinimize } = this.props
    if (minimizeDetailsIndex || minimizeDetailsIndex == 0) {
      clearMinimize && clearMinimize(minimizeDetailsIndex)
    } else {
      closeModal && closeModal()
    }
  }
  // 添加等待列表弹窗
  addWaitList = () => {
    const { maximizeInfo, addWaitList } = this.props
    addWaitList && addWaitList(maximizeInfo)
  }
  // 移除等待列表
  onConfirmIsWaitingList = () => {
    const { maximizeInfo, onWaitingListItmeClose } = this.props
    onWaitingListItmeClose && onWaitingListItmeClose(maximizeInfo)
  }
  // 改约
  appointments = (appointmentInfoOfTableDto) => {
    const { changeContract } = this.props
    router.push({ // 清除任务跳转过来的参数
      pathname: `/subscribe/fullscreen`
    })
    changeContract && changeContract(appointmentInfoOfTableDto)
  }
  // 到诊
  arrival = (appointmentInfoOfTableDto) => {
    const { arrival, halfSizeRefresh } = this.props  // halfSizeRefresh  1 半屏进入的预约详情  到诊弹窗关闭刷新半屏日历否则刷新全屏
    arrival && arrival(appointmentInfoOfTableDto, halfSizeRefresh)
  }
  // 点击最小化
  detailsMinimize = () => {
    const { maximizeInfo, operationType, staffStatus, detailsMinimize, minimizeDetailsIndex, closeModal } = this.props
    let { query } = this.props.location;
    const { appointmentId, bookingDetails, customerId, referer, date } = query || {} // 从任务过来点击最小化保存携带的参数
    const { patientInfoDto } = maximizeInfo || {}
    const { name } = patientInfoDto || {}
    detailsMinimize && detailsMinimize(maximizeInfo, operationType, staffStatus, name, appointmentId, bookingDetails, customerId, referer, minimizeDetailsIndex)
    router.push({  // 清除任务跳转过来的参数
      pathname: `/subscribe/fullscreen`,
      query: {
        date
      }
    })
    // if (minimizeDetailsIndex) {
    //   closeModal && closeModal()
    // } else {
    //   detailsMinimize && detailsMinimize(maximizeInfo, operationType, staffStatus, name)
    // }
  }
  // 预约备注改变
  remarksChange = (e) => {
    console.log(e.target.value);
    this.setState({
      remarksValue: e.target.value
    })
  }
  // 意向回显日期格式化
  getFormentClaimTime = (claimTime) => {
    const Claim = /^\d{4}-\d{2}-\d{2}$/
    const dataforment = 'YYYY-MM-DD'
    if (claimTime && claimTime && claimTime.indexOf(',') != -1) {
      let claimTimeList = claimTime.split(',')
      if (claimTimeList.length == 2 && Claim.test(claimTimeList[0]) && Claim.test(claimTimeList[1])) {
        if (claimTimeList[0] == claimTimeList[1]) {
          return claimTimeList[0]
        } else {
          return `${claimTimeList[0]} - ${claimTimeList[1]}`
        }
      }
    }
    return claimTime
  }
  onOpen = () => {
    this.setState({
      visiblePopover: true,
      remarksValue: null
    },()=>{

    })
  }
  onHide = () => {
    this.setState({
      visiblePopover: false
    })
  }
  getWeek(date) { // 参数时间戳
    let week = moment(date).day()
    switch (week) {
      case 1:
        return '周一'
      case 2:
        return '周二'
      case 3:
        return '周三'
      case 4:
        return '周四'
      case 5:
        return '周五'
      case 6:
        return '周六'
      case 0:
        return '周日'
    }
  }

  // 编辑备注
  editRemark = () => {
    const { maximizeInfo } = this.props
    const { remark, isShuangAbout, isComeVisit, doctorUserId } = maximizeInfo || {}
    const DoctorType = localStorage.getItem('doctorIdentification');
    const id = localStorage.getItem('id');
    if (DoctorType == 1 && (doctorUserId != id)) {
      message.warning('您没有权限！');
      return;
    }

    if (isShuangAbout == 1) {
      message.warning('此预约单已经过期，不可变更预约备注！');
      return;
    }

    if (isComeVisit == 1) {
      message.warning('此预约单已经到诊，不可变更预约备注！');
      return;
    }
    this.setState({
      editRemarkType: true,
      remarkValue: remark
    })
  }
  // 备注修改
  remarkChange = (e) => {
    this.setState({
      remarkValue: e.target.value
    })
  }
  // 备注确认
  okRemark = () => {
    const { dispatch, maximizeInfo } = this.props;
    const { appointmentId } = maximizeInfo || {}
    const { remarkValue } = this.state;
    if (remarkValue && remarkValue.length > 200) {
      message.warning('不能大于200字符！');
      return;
    }
    if (remarkValue && (remarkValue.match(noEmoji))) {
      message.warning('输入格式错误！');
      return;
    }
    dispatch({
      type: 'Getscreen/remarkReplace',
      payload: {
        appointmentId, // 预约id
        remark: remarkValue, // 填写的 备注
      },
    }).then((res) => {
      let code = res.code
      if (res !== undefined && code == 200) {
        this.setState({
          editRemarkType: false
        })
        console.log(this.props.appointmentInfoCard)
        this.props.appointmentInfoCard && this.props.appointmentInfoCard()
      }
    }).catch((err) => {
      console.log(err);
      message.error('编辑失败')
    })
  }
  // 点击发送短息
  sendSMSClick = () => {
    const { isModalVisible, maximizeInfo, operationType, staffStatus, loading } = this.props
    const { patientInfoDto, reservationCustomerTel } = maximizeInfo || {}
    const { name } = patientInfoDto || {}
    console.log('patientInfoDto 123 :: ',patientInfoDto);
    const { oftenTel } = patientInfoDto || {}
    const { smsTemplateInfo } = this.state || {}
    const {
      templateType,    // 模板类型：0免费，1.收费
      templateStatus,  // 模板状态：0禁用 1.启用
      isSmsManage,     // 信息是否有管理权限 true 有 false 没有
      smsNum,          // 信息余量
      showTemplateContent, // 信息临时
    } = smsTemplateInfo || {}
    this.setState({
      sendSMS: true,
      oftenTel: oftenTel || reservationCustomerTel,
      msgName:name,
    })
  }
  // 编辑手机号
  editOftenTel = () => {
    this.setState({
      editOftenTelType: true
    }, () => {
      this.props.form.setFieldsValue({
        ['phone']: this.state.oftenTel,
      })
    })
  }
  // 编辑信息称呼
  editMsgName=()=>{
    this.setState({
      editMsgNameType: true
    }, () => {
      this.props.form.setFieldsValue({
        ['msgName']: this.state.msgName,
      })
    })
  }
  // 发送信息取消
  sendSMSCancel = () => {
    this.setState({
      sendSMS: false,
      editOftenTelType: false
    })
  }
  // 手机号改变
  phoneChange = (e) => {
    this.setState({
      oftenTel: e.target.value
    })
  }
  // 信息称呼改变
  msgNameChange = (e)=>{
    this.setState({
      msgName: e.target.value
    })
  }
  // 发送信息确认
  sendSMSOk = () => {
    const { dispatch, maximizeInfo, sendSMSOk } = this.props
    const { appointmentId, patientInfoDto } = maximizeInfo || {}
    const { name } = patientInfoDto || {}
    dispatch({
      type: 'homeIndex/appointmentShortMsgSend',
      payload: {
        appointmentId,
        patientName: this.state.msgName,
        patientTel: this.state.oftenTel
      }
    }).then((res) => {
      const { code,msg } = res || {}
      if (res && code == 200) {
        this.setState({
          sendSMS: false
        }, () => {
          sendSMSOk && sendSMSOk()
        })
      } else {
        message.error(msg ? msg : '信息发送失败')
      }
    }).catch((err) => {
      console.log(err);
    })
  }
  title = () => {
    const { loading } = this.props
    let updateAppointmentAffirmt = loading.effects['Getscreen/updateAppointmentAffirmt']
    return (
      <div className={styles.title}>
        <div className={styles.Warp_AppintmentIcon}>
          <div className={styles.AppintmentIcon}></div>
          <div>预约详情</div>
        </div>
        <Spin spinning={!!updateAppointmentAffirmt}>
          <div>
            <span onClick={this.detailsMinimize} style={{ fontSize: 16, color: '#959595' }}><Icon type="minus" /></span>
            <span onClick={this.handleCancel} style={{ fontSize: 16, color: '#959595' }}><Icon type="close" /></span>
          </div>
        </Spin>
      </div>
    )
  }

  // 是否可用SendSMSClick
  isAvailabilitySendSMSClick=()=>{
    const { maximizeInfo } = this.props
    const { smsTemplateInfo } = this.state
    const { isSendMsg } = maximizeInfo || {};
    const {
      templateType,    // 模板类型：0免费，1.收费
      templateStatus,  // 模板状态：0禁用 1.启用
      isSmsManage,     // 信息是否有管理权限 true 有 false 没有
      templateName,    // 模板名称
      smsNum,          // 信息余量
    } = smsTemplateInfo || {}

    // isSendMsg == true
    // templateType == 1
    // templateStatus == 1
    // smsNum != 0

    if(isSendMsg){
      if(templateType == 1){ // 收费
        if(templateStatus == 1 && smsNum != 0){ // 启用模板
          return true
        }else {
          return false
        }
      }else { // 免费
        return true
      }
    }else {
      return false
    }
    return false
  }

  // 【FRIDAY】尊敬的#patientName#，
  // 市场化测试第二诊所提醒您在#date#的预约，就诊时间#time#。
  // 诊所位于紫月路18号院11号楼6层测试 您的预约时间会为您保留15分钟，
  // 期待您的到来。电话：***********
  formatByShowTemplateContent=(showTemplateContent)=>{
    console.log('formatByShowTemplateContent123123 :: ',this.state,this.props);
    const { maximizeInfo } = this.props || {}
    const { patientInfoDto,appointmentDate,timeStart } = maximizeInfo || {}
    const { name } = patientInfoDto || {}

    let TestByShowTemplateContent = showTemplateContent
    if(!!showTemplateContent){
      TestByShowTemplateContent = TestByShowTemplateContent
        .replace(/#patientName#/g,name)
        .replace(/#patientname#/g,name)
        .replace(/#date#/g,appointmentDate)
        .replace(/#time#/g,timeStart)
    }
    return TestByShowTemplateContent
  }


  // 生成进销存获取用户信息的二维码
  getMaterialQRCode = (text) => {
    QRCode.toDataURL(JSON.stringify({
      materialQRKey:text
    })).then(url => {
      this.setState({
        materialQRImgUrl: url,
      });
    }).catch(err => {
      message.error('生成二维码失败～!');
    })
  }

  render() {
    const { isModalVisible, maximizeInfo, operationType, staffStatus, loading } = this.props
    const { editOftenTelType,editMsgNameType, editRemarkType, remarkValue,smsTemplateInfo, materialQRImgUrl } = this.state
    let appointmentInfoCard = loading.effects['Getscreen/appointmentInfoCard']
    let appointmentShortMsgSend = loading.effects['homeIndex/appointmentShortMsgSend']
    let updateAppointmentAffirmt = loading.effects['Getscreen/updateAppointmentAffirmt']
    const { getFieldDecorator } = this.props.form;
    const {
      appointmentDate,
      timeStart,
      timeEnd,
      appointmentConfirmStatus,
      appointmentTaskNotesList,
      patientInfoDto,
      isToday,
      isShuangAbout,
      isWaitingList,
      isComeVisit,
      waitingListBaseDto,
      appointmentDateAndStartTimeStr,
      visitButtonStatus,   // 是否有权限到诊 1有权限  0无权限
      appointmentDatetimeStr,
      timeLength,
      complaintList,
      createdGmtAt,
      createUserName,
      remark,
      taskType,
      doctorNameOfAppointment,
      taskId,
      appointmentId,
      appointmentIconNewDtoList, // 预约标签
      emergency,
      isSendTaskShortMessage,
      reservationCustomerTel,
      inTime,
    } = maximizeInfo || {};
    const { claimTime, claimTimeLength, resourceDtoList, complaintDtoList, claimRemark } = waitingListBaseDto || {}
    const DoctorType = localStorage.getItem('doctorIdentification');
    const operationTypeState = operationType == 0;  // 当前event是否可操作 1为可操作 0为不可操作
    const visitButtonStatusValue = visitButtonStatus != 1 // 当前用户是否权限到诊 1有权限  0无权限
    const {
      name,
      sex,
      isFirstVisit,
      fileNumber,
      age,
      oftenTel,
      labelList,
      preferenceList,
      patientRecommendName,
      refereeRelationship,
      recommendId
    } = patientInfoDto || {}

    let arr = labelList || []
    let arr2 = preferenceList || []
    let arr3 = arr2.concat(arr)
    let labelArr = []
    arr3.map((item) => {
      if (item.checked) {
        labelArr.push(item)
      }
    })
    let { query } = this.props.location;
    const { bookingDetails } = query || {}
    const {
      templateType,    // 模板类型：0免费，1.收费
      templateStatus,  // 模板状态：0禁用 1.启用
      isSmsManage,     // 信息是否有管理权限 true 有 false 没有
      smsNum,          // 信息余量
      showTemplateContent, // 信息临时
      templateName,
    } = smsTemplateInfo || {}
    let isSendMsg = this.isAvailabilitySendSMSClick()


    return (
      <Modal
        title={this.title()}
        visible={isModalVisible}
        onOk={this.handleOk}
        // onCancel={this.handleCancel}
        className={styles.AppointmentDetails}
        width={'100%'}
        closable={false}
        footer={null}
      >
        <Spin spinning={!!appointmentInfoCard || !patientInfoDto || !!updateAppointmentAffirmt}>
          <div className={styles.bigBox}>
            <div className={styles.left}>
              {
                patientInfoDto &&
                <AppointmentLeft
                  WaitingForSomeoneAddData={patientInfoDto}
                  whetherClick={1} // 是否存在点击事件 1不存在
                  headPortrait={'yes'} // 头像是否可以点击跳转
                  whetherUpdatePatientInfo={1} // 是否更新患者信息 1不更新
                  comeFrom={4}
                />
              }
            </div>
            <div className={styles.right}>
              <div className={styles.rightTitle}>
                <div className={styles.title}>预约信息</div>
                <div>
                  {
                    taskType == 3 ? null :
                      taskType == 1
                        ?
                        <>
                          <Spin spinning={!!updateAppointmentAffirmt}>
                          <span className={styles.confirmStatus} style={{ color: '#52C41A' }}>
                            <img src={greenYes} alt='' />预约已确认
                            {
                              isShuangAbout == 1 || operationTypeState || appointmentConfirmStatus == 2 ?
                                null :
                                <span onClick={(e) => {
                                  e.stopPropagation();
                                  this.onHide();
                                  this.props.RadioonChange(4, appointmentDateAndStartTimeStr, taskId, appointmentId)
                                }}>，<span style={{ textDecoration: 'underline', cursor: 'pointer' }}>取消</span></span>
                            }
                          </span>
                            {
                              maximizeInfo && maximizeInfo.isSendTaskShortMessage == 1 ?
                                null :
                                <span style={{
                                  cursor: 'pointer',
                                  color: '#4292FF',
                                  marginRight: 8,
                                  marginLeft: 12,
                                  display: !isSendMsg ? 'none' : 'inline-block'
                                }} onClick={this.sendSMSClick}>发送信息</span>
                            }
                            {
                              maximizeInfo && maximizeInfo.isSendTaskShortMessage == 1 ?
                                <img src={shortMessageBlue} alt='' /> :
                                <img src={shortMessage} alt='' />
                            }
                          </Spin>
                        </>
                        :
                        taskType == 2 ?
                          <span className={styles.confirmStatus} style={{ color: '#666666' }}>
                            <span><img src={ashAsk} alt='' />已联系未确认
                              {
                                isShuangAbout == 1 || operationTypeState || appointmentConfirmStatus == 2 ?
                                  null :
                                  <Popover
                                    placement={'bottomRight'}
                                    trigger={'click'}
                                    visible={this.state.visiblePopover}
                                    overlayStyle={{ zIndex: 1000 }}
                                    content={
                                      <div>
                                        <div style={{
                                          display: 'flex',
                                          justifyContent: 'space-between',
                                          borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
                                          padding: 16
                                        }}>
                                          <div><img src={iphonHui} alt='' style={{
                                            width: 18,
                                            height: 18,
                                            verticalAlign: 'sub'
                                          }} />{oftenTel || reservationCustomerTel}
                                            {
                                              isSendTaskShortMessage ?
                                                <span style={{ color: '#52C41A', marginLeft: 24 }}><img src={greenYes}
                                                                                                        alt='' style={{
                                                  width: 16,
                                                  height: 16,
                                                  verticalAlign: 'sub',
                                                  marginRight: 4
                                                }} />信息已发送</span> :
                                                <span style={{ color: '#ED6A09', marginLeft: 24 }}><img src={orangeSigh}
                                                                                                        alt='' style={{
                                                  width: 16,
                                                  height: 16,
                                                  verticalAlign: 'sub',
                                                  marginRight: 4
                                                }} />信息未发送</span>
                                            }
                                          </div>
                                          <div><Icon type="close" onClick={this.onHide} /></div>
                                        </div>
                                        <div className={styles.remarks}>
                                          <div style={{ marginBottom: 6 }}>预约确认备注</div>
                                          <div>
                                            <TextArea rows={4} placeholder={'请填写备注信息'} value={this.state.remarksValue}
                                                      onChange={this.remarksChange} />
                                          </div>
                                        </div>
                                        <div className={styles.button}>
                                          {/*[信息详情]*/}
                                          {
                                            bookingDetails && (maximizeInfo && maximizeInfo.isSendTaskShortMessage != 1) ?
                                              <div>
                                                <Popover
                                                  trigger="hover"
                                                  placement="bottom"
                                                  overlayStyle={smsNum == 0 ? {display:'none'} : null}
                                                  content={
                                                    <div className={styles.PopoverWarp}>
                                                      {/*模板状态：0禁用 1.启用*/}
                                                      {templateStatus == 0 &&
                                                        <div className={styles.ItemMsg}>
                                                          <Icon className={styles.iconByItemMsg} type="info-circle" />
                                                          <div className={styles.TestByItemMsg}>
                                                            您尚未启用{templateName}信息模板，无法使用！
                                                          </div>
                                                        </div>
                                                      }
                                                      <div className={styles.PopoverItem}>
                                                        <div className={styles.PopoverItem_Left}>接收手机号:</div>
                                                        <div className={styles.PopoverItem_Right}>{oftenTel || reservationCustomerTel}</div>
                                                      </div>
                                                      <div className={styles.PopoverItem}>
                                                        <div className={styles.PopoverItem_Left}>信息内容:</div>
                                                        <div className={styles.PopoverItem_Right}>{this.formatByShowTemplateContent(showTemplateContent)}</div>
                                                      </div>
                                                    </div>
                                                  }
                                                  title={null}
                                                >
                                                  <Button style={{ float: 'left', marginLeft: 16 }}
                                                          onClick={this.sendSMSClick} disabled={!isSendMsg}>发送信息</Button>
                                                </Popover>
                                              </div>
                                              :
                                              null
                                          }
                                          <Button type={'primary'} onClick={(e) => {
                                            e.stopPropagation();
                                            this.onHide();
                                            this.props.RadioonChange(1, appointmentDateAndStartTimeStr, taskId, appointmentId, this.state.remarksValue);
                                          }}>已确认</Button>
                                        </div>
                                        {/* 信息余额提醒 */}
                                        <div className={styles.SMSnumMsgBox}>
                                          { (isSendTaskShortMessage != 1 && smsNum == 0) && '信息余额不足，请充值' }
                                        </div>
                                      </div>
                                    }
                                    overlayClassName={styles.appointmentConfirm}
                                  >
                                    ，<span style={{ textDecoration: 'underline', cursor: 'pointer' }}
                                           onClick={this.onOpen}>去确认</span>
                                  </Popover>
                              }
                            </span>
                          </span> :
                          taskType == 4 ?
                            <span className={styles.confirmStatus} style={{ color: '#FF2E2E' }}>
                            <span><img src={redDifference} alt='' />预约确认已取消
                              {
                                isShuangAbout == 1 || operationTypeState || appointmentConfirmStatus == 2 ?
                                  null :
                                  <Popover
                                    placement={'bottomRight'}
                                    trigger={'click'}
                                    visible={this.state.visiblePopover}
                                    overlayStyle={{ zIndex: 1000 }}
                                    content={
                                      <div>
                                        <div style={{
                                          display: 'flex',
                                          justifyContent: 'space-between',
                                          borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
                                          padding: 16
                                        }}>
                                          <div><img src={iphonHui} alt='' style={{
                                            width: 18,
                                            height: 18,
                                            verticalAlign: 'sub'
                                          }} />{oftenTel || reservationCustomerTel}
                                            {
                                              isSendTaskShortMessage ?
                                                <span style={{ color: '#52C41A', marginLeft: 24 }}><img src={greenYes}
                                                                                                        alt='' style={{
                                                  width: 16,
                                                  height: 16,
                                                  verticalAlign: 'sub',
                                                  marginRight: 4
                                                }} />信息已发送</span> :
                                                <span style={{ color: '#ED6A09', marginLeft: 24 }}><img src={orangeSigh}
                                                                                                        alt='' style={{
                                                  width: 16,
                                                  height: 16,
                                                  verticalAlign: 'sub',
                                                  marginRight: 4
                                                }} />信息未发送</span>
                                            }
                                          </div>
                                          <div><Icon type="close" onClick={this.onHide} /></div>
                                        </div>
                                        <div className={styles.remarks}>
                                          <div style={{ marginBottom: 6 }}>预约确认备注</div>
                                          <div>
                                            <TextArea rows={4} placeholder={'请填写备注信息'} value={this.state.remarksValue}
                                                      onChange={this.remarksChange} />
                                          </div>
                                        </div>
                                        <div className={styles.button}>
                                          {
                                            (maximizeInfo && maximizeInfo.isSendTaskShortMessage != 1) ?
                                              <div>
                                                <Popover
                                                  trigger="hover"
                                                  placement="bottom"
                                                  overlayStyle={smsNum == 0 ? {display:'none'} : null}
                                                  content={
                                                    <div className={styles.PopoverWarp}>
                                                      {/*模板状态：0禁用 1.启用*/}
                                                      {templateStatus == 0 &&
                                                      <div className={styles.ItemMsg}>
                                                        <Icon className={styles.iconByItemMsg} type="info-circle" />
                                                        <div className={styles.TestByItemMsg}>
                                                          您尚未启用{templateName}信息模板，无法使用！
                                                        </div>
                                                      </div>
                                                      }
                                                      <div className={styles.PopoverItem}>
                                                        <div className={styles.PopoverItem_Left}>接收手机号:</div>
                                                        <div className={styles.PopoverItem_Right}>{oftenTel || reservationCustomerTel}</div>
                                                      </div>
                                                      <div className={styles.PopoverItem}>
                                                        <div className={styles.PopoverItem_Left}>信息内容:</div>
                                                        <div className={styles.PopoverItem_Right}>{this.formatByShowTemplateContent(showTemplateContent)}</div>
                                                      </div>
                                                    </div>
                                                  }
                                                  title={null}
                                                >
                                                  <Button style={{ float: 'left', marginLeft: 16 }}
                                                          onClick={this.sendSMSClick}
                                                          disabled={!isSendMsg}>发送信息</Button>
                                                </Popover>
                                              </div>  : null
                                          }
                                          <Button type={'primary'} onClick={(e) => {
                                            e.stopPropagation();
                                            this.onHide();
                                            this.props.RadioonChange(1, appointmentDateAndStartTimeStr, taskId, appointmentId, this.state.remarksValue);
                                          }}>已确认</Button>
                                          <Button onClick={(e) => {
                                            e.stopPropagation();
                                            this.onHide();
                                            this.props.RadioonChange(2, appointmentDateAndStartTimeStr, taskId, appointmentId, this.state.remarksValue);
                                          }}>已联系未确认</Button>
                                        </div>
                                        {/* 信息余额提醒 */}
                                        <div className={styles.SMSnumMsgBox}>
                                          { (isSendTaskShortMessage != 1 && smsNum == 0) && '信息余额不足，请充值' }
                                        </div>
                                      </div>
                                    }
                                    overlayClassName={styles.appointmentConfirm}
                                  >
                                    ，<span style={{ textDecoration: 'underline', cursor: 'pointer' }}
                                           onClick={this.onOpen}>去确认</span>
                                  </Popover>
                              }
                            </span>
                          </span> :
                            <span className={styles.confirmStatus} style={{ color: '#ED6A09' }}>
                            <span><img src={orangeSigh} alt='' />预约未确认
                              {
                                isShuangAbout == 1 || operationTypeState || taskType == 3 || appointmentConfirmStatus == 2 ?
                                  null :
                                  <Popover
                                    placement={'bottomRight'}
                                    trigger={'click'}
                                    visible={this.state.visiblePopover}
                                    overlayStyle={{ zIndex: 1000 }}
                                    content={
                                      <div>
                                        <div style={{
                                          display: 'flex',
                                          justifyContent: 'space-between',
                                          borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
                                          padding: 16
                                        }}>
                                          <div><img src={iphonHui} alt='' style={{
                                            width: 18,
                                            height: 18
                                          }} />{oftenTel || reservationCustomerTel}
                                            {
                                              isSendTaskShortMessage ?
                                                <span style={{ color: '#52C41A', marginLeft: 24 }}><img src={greenYes}
                                                                                                        alt='' style={{
                                                  width: 16,
                                                  height: 16,
                                                  verticalAlign: 'sub',
                                                  marginRight: 4
                                                }} />信息已发送</span> :
                                                <span style={{ color: '#ED6A09', marginLeft: 24 }}><img src={orangeSigh}
                                                                                                        alt='' style={{
                                                  width: 16,
                                                  height: 16,
                                                  verticalAlign: 'sub',
                                                  marginRight: 4
                                                }} />信息未发送</span>
                                            }
                                          </div>
                                          <div><Icon type="close" onClick={this.onHide} /></div>
                                        </div>
                                        <div className={styles.remarks}>
                                          <div style={{ marginBottom: 6 }}>预约确认备注</div>
                                          <div>
                                            <TextArea rows={4} placeholder={'请填写备注信息'} value={this.state.remarksValue}
                                                      onChange={this.remarksChange} />
                                          </div>
                                        </div>
                                        <div className={styles.button}>
                                          {
                                            (maximizeInfo && maximizeInfo.isSendTaskShortMessage != 1) ?
                                              <div>
                                              <Popover
                                                trigger="hover"
                                                placement="bottom"
                                                overlayStyle={smsNum == 0 ? {display:'none'} : null}
                                                content={
                                                  <div className={styles.PopoverWarp}>
                                                    {/*模板状态：0禁用 1.启用*/}
                                                    {templateStatus == 0 &&
                                                    <div className={styles.ItemMsg}>
                                                      <Icon className={styles.iconByItemMsg} type="info-circle" />
                                                      <div className={styles.TestByItemMsg}>
                                                        您尚未启用{templateName}信息模板，无法使用！
                                                      </div>
                                                    </div>
                                                    }
                                                    <div className={styles.PopoverItem}>
                                                      <div className={styles.PopoverItem_Left}>接收手机号:</div>
                                                      <div className={styles.PopoverItem_Right}>{oftenTel || reservationCustomerTel}</div>
                                                    </div>
                                                    <div className={styles.PopoverItem}>
                                                      <div className={styles.PopoverItem_Left}>信息内容:</div>
                                                      <div className={styles.PopoverItem_Right}>{this.formatByShowTemplateContent(showTemplateContent)}</div>
                                                    </div>
                                                  </div>
                                                }
                                                title={null}
                                              >
                                              <Button style={{ float: 'left', marginLeft: 16 }}
                                                      onClick={this.sendSMSClick}
                                                      disabled={!isSendMsg}>发送信息</Button>
                                              </Popover>
                                              </div> : null
                                          }
                                          <Button type={'primary'} onClick={(e) => {
                                            e.stopPropagation();
                                            this.onHide();
                                            this.props.RadioonChange(1, appointmentDateAndStartTimeStr, taskId, appointmentId, this.state.remarksValue);
                                          }}>已确认</Button>
                                          <Button onClick={(e) => {
                                            e.stopPropagation();
                                            this.onHide();
                                            this.props.RadioonChange(2, appointmentDateAndStartTimeStr, taskId, appointmentId, this.state.remarksValue);
                                          }}>已联系未确认</Button>
                                        </div>
                                        {/* 信息余额提醒 */}
                                        <div className={styles.SMSnumMsgBox}>
                                          { (isSendTaskShortMessage != 1 && smsNum == 0) && '信息余额不足，请充值' }
                                        </div>
                                      </div>
                                    }
                                    overlayClassName={styles.appointmentConfirm}
                                  >
                                    ，<span style={{ textDecoration: 'underline', cursor: 'pointer' }}
                                           onClick={this.onOpen}>去确认</span>
                                  </Popover>
                              }
                            </span>
                          </span>
                  }
                  {
                    taskType == 3 || taskType == 1 ? null :
                      maximizeInfo && maximizeInfo.isSendTaskShortMessage == 1 ?
                        <img src={shortMessageBlue} alt='' /> :
                        <img src={shortMessage} alt='' />
                  }
                </div>
              </div>
              <div className={styles.appointmentInfo}>
                <div className={styles.middleInfo}>
                  <div className={styles.infoItem}>
                    <div>预约时间:</div>
                    <div>{appointmentDate ?
                      <span>{moment(appointmentDate).format('YYYY/MM/DD')} &nbsp;({this.getWeek(appointmentDate)}) &nbsp;{timeStart}-{timeEnd}</span> : null}</div>
                  </div>
                  <div className={styles.infoItem}>
                    <div>预约时长:</div>
                    <div>{timeLength}分钟</div>
                  </div>
                  <div className={styles.infoItem}>
                    <div>预约医生:</div>
                    <div>{doctorNameOfAppointment}</div>
                    <div style={{ float: 'right' }}>
                      {emergency == 1 ?
                        <img src={jizhenfang} alt='' style={{ width: 14, height: 14 }} /> : null}
                    </div>
                  </div>
                  <div className={styles.infoItem}>
                    <div>预约主诉:</div>
                    <div>
                      {
                        complaintList && complaintList.map((k, index) => {
                          if (k.treatType == 1 || k.treatType == 2) {
                            return (<span className={styles.contentsTextSpan} key={index}>
                                          {k.complaintMergeName ? k.complaintMergeName : k.complaintName}
                              <img className={styles.contentsTextImg1}
                                   style={{ width: 14, height: 14, marginBottom: 3, marginTop: '0px', marginLeft: 4 }}
                                   src={xin}
                                   alt="" />
                              {index == (complaintList && complaintList.length - 1) ? null : '、'}
                                                </span>)
                          }
                          return (<span className={styles.contentsTextSpan} key={index}>
                                          {k.medicalDictionaryName}
                            <img className={styles.contentsTextImg1}
                                 style={{ width: 14, height: 14, marginBottom: 3, marginLeft: 4 }}
                                 src={contentsTextImg1} alt="" />
                            {index == (complaintList && complaintList.length - 1) ? null : '、'}
                                                </span>)
                        })
                      }
                    </div>
                  </div>
                  <div className={styles.infoItem}>
                    <div>操作人:</div>
                    <div>{createUserName}</div>
                  </div>
                  <div className={styles.infoItem}>
                    <div>操作时间:</div>
                    <div>{createdGmtAt}</div>
                  </div>
                  <div className={styles.infoItem_WaitItem}>
                    <div>到诊时间:</div>
                    <div className={styles.inTime}>
                      {isComeVisit == 1 && inTime ? inTime : '-'}
                    </div>
                    {!!this.state.isWaitStute  &&
                      <div className={classNames({
                        [styles.redContent]: true,
                        [styles.timeoutContent]: this.state.isWaitStute == 2,
                      })}>
                        <img src={this.state.isWaitStute == 2 ?  timeoutIcon : timeIcon} />
                        <span>已等待: {this.state.WaitFormatTime}</span>
                      </div>
                    }
                  </div>
                  <div className={styles.infoItem1}
                       style={editRemarkType ? { lineHeight: '32px' } : { lineHeight: '22px' }}>
                    <div className={styles.infoItemLeft}>备注:</div>
                    <div className={styles.infoItemRight} style={{ display: 'flex', width: '100%'}}>
                      {
                        editRemarkType ?
                          <div style={{flex: 1}}>
                          <TextArea rows={3} value={remarkValue} onChange={this.remarkChange} style={{width: '100%' }}
                                  placeholder="请输入备注" /></div>
                                  :
                          <div>
                          <Popover placement="topLeft"
                                    content={<div style={{ maxWidth: 400, wordBreak: 'break-all' }}>{remark}</div>}
                                    trigger="hover">
                            <Ellipsis lines={2}><span>{remark}</span></Ellipsis>
                          </Popover>
                          </div>
                      }
                      <div style={editRemarkType || remark ? { minWidth: 30, marginLeft: 8 } : { minWidth: 30 }}>
                        {
                          editRemarkType ?
                            <span style={{ color: '#4292FF', cursor: 'pointer', flexShrink:0 }} onClick={this.okRemark}>确定</span>
                            : isComeVisit != 1 ?
                            <span style={{ color: '#4292FF', cursor: 'pointer', flexShrink:0 }} onClick={this.editRemark}>编辑</span>
                            : null
                        }
                      </div>
                    </div>
                  </div>
                  <div className={styles.infoItem1}>
                    <div className={styles.infoItemLeft}>治疗标签:</div>
                    <div className={styles.infoItemRight}>
                      <Manualicon
                        appointmentIconNewDtoList={appointmentIconNewDtoList}
                        lineHeight={'20px'}
                        height={'22px'}
                        padding={'0 8px'}
                        marginBottom={'6px'}
                      />
                    </div>
                  </div>
                  {
                    materialQRImgUrl&&materialQRImgUrl!=null&&<div className={styles.materialQRBody}>
                      <img src={materialQRImgUrl} />
                      <div>
                        <h6>扫码获取患者信息</h6>
                        <p>请使用佳沃思APP内的进销存模块扫码。</p>
                      </div>
                    </div>
                  }
                </div>
                <div className={styles.bottom}>
                  <div>
                    {
                      isWaitingList === 0 ?
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            this.addWaitList(1);
                          }}
                          disabled={operationTypeState || isComeVisit == 1}
                        >加入排队</Button> :
                        <Button onClick={(e) => {
                          e.stopPropagation();
                          this.onConfirmIsWaitingList();
                        }}
                                disabled={operationTypeState || isComeVisit == 1}> {/*  判断 是否在 等待列表里面   移除排队列表  */}移除排队</Button>
                    }
                    <Button
                      disabled={isShuangAbout == 1 || operationTypeState || !!(maximizeInfo && maximizeInfo.isComeVisit == 1)}
                      onClick={(e) => {
                        e.stopPropagation();
                        this.appointments(maximizeInfo);
                      }}>改约</Button>
                    <Button onClick={(e) => {
                      e.stopPropagation();
                      this.props.cancelSubscribe(maximizeInfo);
                    }}
                            disabled={isShuangAbout == 1 || operationTypeState || !!(maximizeInfo && maximizeInfo.isComeVisit == 1)}
                    >取消预约</Button>
                  </div>
                  <div>
                    {(DoctorType == 1) || (isToday == 0) ? null : <Button
                      disabled={visitButtonStatusValue || isShuangAbout == 1 || operationTypeState || !!(maximizeInfo && maximizeInfo.isComeVisit == 1) || staffStatus == 1 || bookingDetails}
                      onClick={(e) => {
                        e.stopPropagation();
                        this.arrival(maximizeInfo);
                      }} type="primary">到诊</Button>}
                  </div>
                </div>
              </div>
              {
                isWaitingList === 0 ? null :
                  <div>
                    <div className={styles.rightTitle} style={{ marginTop: 24 }}>
                      <div className={styles.title}>排队列表</div>
                    </div>
                    <div className={styles.appointmentInfo}>
                      <div className={styles.middleInfo}>
                        <div className={styles.infoItem}>
                          <div>意向时间:</div>
                          <div>{this.getFormentClaimTime(claimTime)}</div>
                        </div>
                        <div className={styles.infoItem}>
                          <div>预约时长:</div>
                          <div>{claimTimeLength}分钟</div>
                        </div>
                        <div className={styles.infoItem}>
                          <div>意向医生:</div>
                          <div>
                            {
                              resourceDtoList && resourceDtoList.map((item, index) => {
                                return (
                                  <span
                                    key={index}>{item.resourceName}{resourceDtoList && resourceDtoList.length - 1 == index ? null :
                                    <span>，</span>}</span>
                                )
                              })
                            }
                          </div>
                        </div>
                        <div className={styles.infoItem}>
                          <div>预约主诉:</div>
                          <div>
                            {
                              complaintDtoList && complaintDtoList.map((k, index) => {
                                if (k.treatType == 1 || k.treatType == 2) {
                                  return (<span className={styles.contentsTextSpan} key={index}>
                                          {k.complaintMergeName ? k.complaintMergeName : k.complaintName}
                                    <img className={styles.contentsTextImg1}
                                         style={{ width: 14, height: 14, marginBottom: 3 }} src={xin} alt="" />
                                    {index == (complaintDtoList && complaintDtoList.length - 1) ? null : '、'}
                                  </span>)
                                }
                                return (<span className={styles.contentsTextSpan} key={index}>
                                          {k.medicalDictionaryName}
                                  <img className={styles.contentsTextImg1}
                                       style={{ width: 14, height: 14, marginBottom: 3 }} src={contentsTextImg1}
                                       alt="" />
                                  {index == (complaintDtoList && complaintDtoList.length - 1) ? null : '、'}
                                </span>)
                              })
                            }
                          </div>
                        </div>
                        <div className={styles.infoItem1}>
                          <div className={styles.infoItemLeft}>备注:</div>
                          <div className={styles.infoItemRight}>
                            <Popover placement="topLeft" content={<span
                              style={{ maxWidth: 200, wordBreak: 'break-all' }}>{claimRemark}</span>} trigger="hover">
                              <Ellipsis lines={2}><span>{claimRemark}</span></Ellipsis>
                            </Popover>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
              }
            </div>
          </div>
        </Spin>
        {/*预约确认结果弹窗*/}
        <Modal
          title="发送信息"
          visible={this.state.sendSMS}
          onOk={this.sendSMSOk}
          onCancel={this.sendSMSCancel}
          cancelText={'取消'}
          okText={'发送信息'}
          className={styles.sendSMS}
          confirmLoading={!!appointmentShortMsgSend}
        >
          <div style={{ display: 'flex' }}>
            <Form.Item>
              <div style={{ color: '#9F9F9F', marginRight: 16, display: 'inline-block' }}>接收手机号:</div>
            </Form.Item>
            {
              editOftenTelType ?
                <div style={{ display: 'inline-block' }}>
                  <Form.Item>
                    {getFieldDecorator('phone', {
                      rules: [
                        {
                          required: true,
                          message: '请输入接收手机号',
                          transform: value => value ? value.trim() : value
                        },
                        {
                          pattern: /^1[3456789]\d{9}$/, message: '请输入正确的接收手机号'
                        },
                      ],
                    })(
                      <Input autoComplete="off" placeholder="请输入接收手机号" onChange={this.phoneChange}
                             style={{ width: 280 }} />
                    )}
                  </Form.Item>
                </div> :
                <Form.Item>
                  <span style={{ color: 'rgba(0, 0, 0, 0.85)', marginRight: 16 }}>{this.state.oftenTel}</span><span
                  style={{ color: '#4292FF', cursor: 'pointer' }} onClick={this.editOftenTel}>编辑</span>
                </Form.Item>
            }
          </div>

          <div style={{ display: 'flex' }}>
            <Form.Item>
              <div style={{ color: '#9F9F9F', marginRight: 16, display: 'inline-block' }}>
                <div style={{display:'flex'}}>
                  <div style={{ color: '#FF5C56',marginRight:5 }}>*</div>
                  <div>信息称呼:</div>
                </div>
              </div>
            </Form.Item>
            {
              editMsgNameType ?
                <div style={{ display: 'inline-block' }}>
                  <Form.Item>
                    {getFieldDecorator('msgName', {
                      rules: [
                        {
                          required: true,
                          message: '请输入信息称呼',
                          transform: value => value ? value.trim() : value
                        },
                      ],
                    })(
                      <Input autoComplete="off" placeholder="请输入信息称呼" onChange={this.msgNameChange}
                             style={{ width: 280 }} />
                    )}
                  </Form.Item>
                </div> :
                <Form.Item>
                  <span style={{ color: 'rgba(0, 0, 0, 0.85)', marginRight: 16 }}>{this.state.msgName}</span><span
                  style={{ color: '#4292FF', cursor: 'pointer' }} onClick={this.editMsgName}>编辑</span>
                </Form.Item>
            }
          </div>
          <div>
             <div className={styles.text_shortMessage}>
               <Icon className={styles.Rigth_ShortMessage} type="info-circle" />
               患者接收系统信息时所能见到的称呼
             </div>
          </div>


          <div style={{ display: 'flex' }}>
            <div style={{  width:'261px', color: '#9F9F9F', marginRight: 9, paddingLeft:12, display: 'inline-block' }}>信息内容:</div>
            <div>
              <span style={{ color: 'rgba(0, 0, 0, 0.85)', marginRight: 16 }}>{ this.formatByShowTemplateContent(showTemplateContent) }</span>
            </div>
          </div>

        </Modal>
      </Modal>
    );
  }
}
