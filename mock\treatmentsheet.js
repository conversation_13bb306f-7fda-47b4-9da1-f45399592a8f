const getTreatmentsheet = (req, res) =>
    res.json({
        title:"治疗  2018-11-08 王伟 FRIDAY国际大厦",
        name:"庄三",
        sex:"1",
        birthday:"2011-12-01",
        price:"22222",
        fileNumber:"GGHHGJHGHU",
        exclusiveCrmName:"庄三",
        firstDoctorName:"大爷",
        billingDate:"2018-12-12",
        billingDoctor:"王伟",
        refuseReason:"历史价格已经谈好，目前已经改价患者要求用老价格使用",
        content:[
            {
                therapyName:"隐形正畸. 隐适美",
                id: '1',
                illness: "隐形正畸. 隐适美",
                practitioner: "普通医生计费",
                process: "按治疗流程收费",
                
                specEntities:[
                    {
                        netReceiptsThisName:"普通医计费",
                        netReceiptsThis: "60000",
                    }
                ],
                OriginalPrice: "70000",
                advance: "60000",
                Discount:"8",
                minimum:"80",
                Withdrawal:"1",
                DrugDetailed:[
                    {
                        drugName:"口腔检查",
                        desc:"",
                        status:1
                    },
                    {
                        drugName:"审核动画",
                        desc:"检查后2~4周， 患者需到诊所",
                        status:""
                    },
                ],
            },
        ]
    });

export default {
    'GET /api/treatmentsheet': getTreatmentsheet,
};
