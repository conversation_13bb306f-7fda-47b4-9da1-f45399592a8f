import { Tag, Tooltip } from 'antd';
import React, { Component } from 'react';
import { GridContent } from '@ant-design/pro-layout';
import styles from './style.less';//样式
//图片
import diamond from '@/assets/<EMAIL>';
import boy from '@/assets/<EMAIL>';
import girl2 from '@/assets/girl2.png';
import commonStyle from '@/pages/common.less'; //引入公共样式
//今日就诊-患者头部信息
class PatientInfo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      patientInfos: {
        name: "",//姓名
        fileNumber: '',//病历号
        oftenTel: "",//手机号
        birthday: "",//出生日期
        sex: "1", //性别1男2女
        age: '',//年龄
        labelList: [],//文本标识
        vipGrade: '', //vip标识
      },
    }
  }
  //生命周期初始化
  componentDidMount() {
    const p = this.props;
    if (p.patientInfos) {
      this.refreshShoeList(p);
    }
  }
  //监听患者信息数据变化
  componentWillReceiveProps(props) {
    const p = this.props;
    if (props.patientInfos&&props.patientInfos.patientId!=this.state.patientInfos.patientId) {
      this.refreshShoeList(props);
    }
  }
  //刷新数据变化
  refreshShoeList = (props) => {
    this.setState({
      patientInfos: {
        patientId: props.patientInfos.patientId,//姓名
        name: props.patientInfos.name,//姓名
        fileNumber: props.patientInfos.fileNumber,//病历号
        oftenTel: props.patientInfos.oftenTel,//手机号
        birthday: props.patientInfos.birthday,//出生日期
        sex: props.patientInfos.sex, //性别1男2女
        age: props.patientInfos.age,//年龄
        labelList: props.patientInfos.labelList, //文本标识
        vipGrade: props.patientInfos.vipGrade, //  是否是vip
      },
    })
  }

  render() {
    const { patientInfos } = this.state;
    const patientInfoData=[];//获取患者信息数据
    if(patientInfos.labelList && patientInfos.labelList.length>0){
      patientInfos.labelList.forEach((text,index)=>{
        patientInfoData.push(text+'，');
      })
    }
    return (
      <GridContent>
        <div className={styles.background}>
          <div className={styles.contentTop}>
            <Tooltip placement="bottom" title={patientInfos.name}>
              <span className={`${styles.fontWeight} ${styles.nameEllipse}`} style={{ marginRight: 4 }}>
                {patientInfos.name}
              </span>
            </Tooltip>
            <span className={styles.pd_lr}>
              {patientInfos.fileNumber ? `(${patientInfos.fileNumber})` : ''}
            </span>
            {patientInfos.vipGrade == 1 ? <img src={diamond} alt="" className={styles.icon_diamond} /> : <></>}
            <div className={styles.line}></div>
            <span className={styles.pd_lr}>{patientInfos.age ? patientInfos.age : ''}{/\d$/.test(patientInfos.age) ? '岁':''}</span>
            {patientInfos.sex == 1 ?
              <img src={boy} alt="" className={styles.icon_boy} />
              : patientInfos.sex == 2 ?
                <img src={girl2} alt="" className={styles.icon_girl} />
                : <></>}
            <div className={styles.line}></div>
            <span className={styles.pd_lr}>{patientInfos.oftenTel}</span>
          </div>
          {this.props.position==1?
            <div className={styles.mg_top} style={{width:'70%'}}>
              {(patientInfos.labelList || []).map((item, index) =>
                  <span key={index}>
                <Tag style={{marginBottom:5}} color={(index % 2) == 1 ? 'orange' : (index / 2) == 0 ? 'blue' : 'blue'}>{item}</Tag>
              </span>
              )}
            </div>:
              <div className={styles.titleBorderStyle}>
                <Tooltip title={patientInfoData?patientInfoData:""}>
                {(patientInfos.labelList || []).map((item, index) =>
                <span key={index}>
                  <Tag style={{marginBottom:5}} color={(index % 2) == 1 ? 'orange' : (index / 2) == 0 ? 'blue' : 'blue'}>{item}</Tag>
                </span>
                )}
                </Tooltip>
              </div>
          }
        </div>
      </GridContent>
    );
  }
}

export default PatientInfo;
