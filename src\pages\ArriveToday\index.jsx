import React, { Component } from 'react';
import { Tabs } from 'antd';
import 'antd/dist/antd.css';
import HistoryPatient from './component/HistoryPatient';//历史患者
import VisitBlock from './component/TodaySeeDoctor';//今日患者
import styles from './style.less';//样式引入
import { GridContent } from '@ant-design/pro-layout';
import Menus from '@/pages/Menu';

const { TabPane } = Tabs;
//今日就诊
class ArriveToday extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }
  //初始化
  componentDidMount() {
    window.addEventListener('resize', this.resize); //监听屏幕高度
  }

  render() {
    const {} = this.state;

    return (
      <GridContent className={styles.TodaySeecards}>
        {/*<div style={{ position: 'absolute', top: 0, left: 0 }}>*/}
        {/*  <Menus />*/}
        {/*</div>*/}
        <Tabs defaultActiveKey="1">
          <TabPane tab="今日就诊" key="1">
            <VisitBlock />
          </TabPane>
          <TabPane tab="历史患者" key="2">
            <HistoryPatient />
          </TabPane>
        </Tabs>
      </GridContent>
    );
  }
}

export default ArriveToday;
