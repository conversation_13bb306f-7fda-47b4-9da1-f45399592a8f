import { stringify } from 'querystring';
import { history } from 'umi';
import { fakeAccountLogin, getToken } from '@/services/login';
import { setAuthority } from '@/utils/authority';
import { getPageQuery } from '@/utils/utils';
import { message } from 'antd';
const Model = {
  namespace: 'login',
  state: {
    status: undefined,
  },
  effects: {
    *getToken({ payload, callback }, { call, put }) {
      const response = yield call(getToken, payload);
      yield put({
        type: 'setLocalStorage',
        payload: response,
      });
      if (response) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      }
    },
    *getLoginInfo({ payload }, { call, put }) {
      const response = yield call(fakeAccountLogin, payload);
      yield put({
        type: 'changeLoginStatus',
        payload: response,
      });
      if (response.code === 200) {
        localStorage.setItem('loginInfo', JSON.stringify(response.content));
        const urlParams = new URL(window.location.href);
        const params = getPageQuery();
        message.success('🎉 🎉 🎉  登录成功！');

        let loginInfo = JSON.parse(localStorage.getItem('loginInfo'));
        let organizationlnfoJson = loginInfo.organizationInfo;
        let userRoleList = loginInfo.userRoleList;
        let roleCodes = [];
        for (let i = 0; i < userRoleList.length; i++) {
          roleCodes.push(userRoleList[i].roleCode);
        }
        // todo 系统未对接，此处模拟登录信息
        localStorage.setItem('id', loginInfo.id);
        localStorage.setItem('userName', loginInfo.userName);
        localStorage.setItem('tenantId', loginInfo.tenantId);
        localStorage.setItem('organizationId', organizationlnfoJson.id);
        localStorage.setItem('organizationInfoId', organizationlnfoJson.id);
        localStorage.setItem('organizationName', organizationlnfoJson.organizationName);
        localStorage.setItem('pool', loginInfo.organizationInfo.pool);
        localStorage.setItem('organizationlnfoJson', JSON.stringify(organizationlnfoJson));
        localStorage.setItem('organizationlnfo', JSON.stringify(organizationlnfoJson));
        localStorage.setItem('roleCodes', JSON.stringify(roleCodes));

        let { redirect } = params;

        if (redirect) {
          const redirectUrlParams = new URL(redirect);

          if (redirectUrlParams.origin === urlParams.origin) {
            redirect = redirect.substr(urlParams.origin.length);

            if (redirect.match(/^\/.*#/)) {
              redirect = redirect.substr(redirect.indexOf('#') + 1);
            }
          } else {
            window.location.href = '/emr/DiagnosisTreat/homepage';
            return;
          }
        }

        history.replace(redirect || '/emr/DiagnosisTreat/homepage');
      }
    },
    logout() {
      const { redirect } = getPageQuery(); // Note: There may be security issues, please note

      if (window.location.pathname !== '/user/login' && !redirect) {
        setAuthority('');
        localStorage.clear();
        history.replace({
          pathname: '/user/login',
          search: stringify({
            redirect: window.location.href,
          }),
        });
      }
    },
  },
  reducers: {
    changeLoginStatus(state, { payload }) {
      setAuthority('admin');
      return { ...state, status: payload.code, type: payload.type };
    },
    setLocalStorage(state, { payload }) {
      return { ...state, tokenInfo: payload };
    },
  },
};
export default Model;
