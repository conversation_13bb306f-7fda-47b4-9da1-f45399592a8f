import React, { Component, useState } from 'react';
import { connect } from 'dva';
import { Spin } from 'antd';
//图片显示
class cdnImg extends Component{
  constructor(props) {
    super(props);
    this.state = {
      url: null,//路径
      height: props.height ? props.height : 110,//高度
      loading: false,//加载状态
    };
  }
  //初始化事件
  componentDidMount() {
    this.cdnImg();
  }
  //获取图片路径事件
  cdnImg = () => {
    const { dispatch } = this.props;
    let params = {
      // tenantId:localStorage.getItem('tenantId'),
      filePath: this.props.fileUrl,
    };
    this.setState({
      loading: true,
    });
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/dbPathTransformService',
        payload: params,
        callback: (res) => {
          this.setState({
            url: res,
            loading: false,
          });
        },
      });
    }
  };
  render() {
    return (
      <Spin spinning={this.state.loading}>
        <img style={{ width: '100%', height: this.state.height }} src={this.state.url} />
      </Spin>
    );
  }
}
export default connect(() => ({}))(cdnImg);
