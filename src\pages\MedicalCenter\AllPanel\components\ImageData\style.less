@import '~antd/lib/style/themes/default.less';
// 按时间顺序开始

.displayFlex {
  display: flex;
  // justify-content: space-between;
}
.width100 {
  width: 100%
}
.icon_boy {
  width: 14px;
  height: 14px;
  margin-top: 5px;
  // margin-bottom: 4px;
  cursor: pointer;
}

.typeTitle {
  padding: 2px 8px;
  background: #F7F8FA;
  color: rgba(0, 0, 0, 0.45);
  line-height: 24px;
  border-radius: 2px;
  margin: 5px 0 15px;
}

:global{
  .ant-modal-body{
    padding:7px 15px 12px;
  }
}

.txt11 {
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(66, 146, 255, 1);
  font-size: 14px;
  font-family: PingFang SC;
  text-align: right;
  white-space: nowrap;
  line-height: 25px;
  display: inline-block;
  padding-right: 10px;
  cursor: pointer;
}

.tagChecked {
  display: inline-block;
  width: 80px;
  height: 26px;
  text-align: center;
  color: #1890FF;
  background: rgba(24, 144, 255, 0.06);
  border-radius: 2px;
  border: 0;
  line-height: 26px;
  cursor: pointer;
}

.tagNoCheck {
  display: inline-block;
  width: 80px;
  height: 26px;
  color: rgba(0, 0, 0, 0.45);
  text-align: center;
  border-radius: 2px;
  line-height: 26px;
  background: transparent;
  border: 0;
  cursor: pointer;
}

.block9 {
  // background-color: rgba(247, 248, 250, 1);
  border-radius: 2px;
  height: 44px;
  width: 97%;
  margin: 0 0 10px 16px;
}


//时间顺序结束


.content {
  padding: 20px;
}

// 弹窗样式
:global {
  .ant-modal-title {
    font-weight: 600;
  }

  .ant-btn ant-btn-default {
    display: none;
  }
  // .ant-form-item-control-input-content {
  //   width: 70%;
  //   min-height: 44px;
  // }
}


// 预览
.showImgborder {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
}

.showImg {
  border-radius: 2px;
  width: 160px;
  height: 110px;
  margin-right: 8px;
  margin-bottom: 8px;
}

.imgborder {
  position: relative;
}

.ctimgdelete {
  color: #F0F0F0;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  width: 160px;
  height: 110px;
  background: rgba(0, 0, 0, 0.3);
  text-align: center;
  align-items: center,
}

.icon_delete {
  width: 20px;
  height: 20px;
}

.deleteFont {
  font-size: 14px;
  margin-left: 4px;
  margin-top: 1px;
}
.loading_more {
  padding: 0 0 25px;
  color: #4292ff;
  font-size: 15px;
  // text-align: center;
  // text-decoration: underline;
}
// .textInputStyle{
  :global{
    .ant-input-textarea-show-count.ant-input-textarea-in-form-item::after{
      position: absolute;
      bottom: 25px;
      right: 5px;
    }
  }
// }
//


.operationBox {
  display: flex;
}
