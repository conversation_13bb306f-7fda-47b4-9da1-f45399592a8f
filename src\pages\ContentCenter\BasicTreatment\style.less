@import '~antd/lib/style/themes/default.less';

.leftMenu{
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-left: 0;
  border-bottom: 0;
  .MenuTitle{
    padding: 16px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  }
}
.Rightcontent{
  padding: 16px;
}
.btnstyle{
  float: right;
  margin-bottom: 16px;
}
.modelContent{
  margin: 0 8px;
}
.menuFirst{
  .pointer{
    position: relative;
    display: flex;
  }
  .arrows{
    width: 12px;
    height: 12px;
    margin-top: 3px;
  }
  .fileIcon{
    width: 20px;
    height: 20px;
    margin-left: 4px;
  }
  .filetitle{
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.45);
    line-height: 23px;
  }
  .fileName{
    display: flex;
  }
  .Unselected{
    width: 20px;
    height: 20px;
  }
}
.chooseBgcolor{
  background: rgba(216, 216, 216, 0.5);
  border-radius: 2px;
}
.hidden{
  display: none;
}
.show{
  display: block;
}
.chooseFontcolor{
  color:#4292FF !important;
}
.SelectedStyle{
  color:#4292FF;
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 23px;
}
.UnselectedStyle{
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 23px;
}
.addBtns{
  cursor: pointer;
  position: absolute;
  right: 4px;
  top:-5px;
  &:hover{
    color: #4292FF;
  }
}
.addFile{
  position:absolute;
  z-index: 999;
  right:-115px;
  width: 132px;
  height: 104px;
  padding: 4px;
  background: #FFF;
  box-shadow: 0 5px 13px 8px rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  .iconStyle{
    width: 20px;
    height: 20px;
  }
  .addFileName{
    width: 56px;
    height: 20px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    line-height: 20px;
    margin-left: 4px;
  }
}
.addFileLine:hover{
  background: rgba(0, 0, 0, 0.06);
}
