import React, {Component} from 'react';
import {Card,Radio,Calendar,Popover,Spin,message,Icon} from 'antd';
import $ from 'jquery';
import moment from 'moment'
import styles from './PopoverForCalendar.less'
import classNames from 'classnames';
import PropTypes from 'prop-types';
import zhCN from 'antd/lib/calendar/locale/zh_CN';
import { connect } from 'dva'
import { getOrganizationInfo } from '@/utils/utils';
import CalendarIconPopover from '@/pages/Appointment/components/AppointmentModal/components/calendarIconPopover';
import 'moment/locale/zh-cn'
moment.locale('zh-cn')

@connect(({ TimeCalendar, loading }) => ({
  TimeCalendar,
  loading
}))
class ChangeDateCalendar extends Component {
  static propTypes = {
    onRef:PropTypes.any,
    selectListForOne:PropTypes.any,
    doctorId:PropTypes.any,
    selectedDate:PropTypes.any,
    onSelect:PropTypes.any,
    onPanelChange:PropTypes.any,
    selectWeeksMode:PropTypes.bool,
  }

  static defaultProps = {
    onRef:()=>{},
    selectListForOne:false,  // 是否是月模式
    doctorId:null,           // 医生id
    selectedDate:null,       // 当前选中时间 或时间 或数组
    onSelect:()=>{},         // 选择的日期
    onPanelChange:()=>{},    // 当前案板日期
    selectWeeksMode:false,    // 是否开启只选中周模式
  }

  constructor(props){
    super(props);
    moment.locale('zh-cn')
    //清空mode
    const { dispatch } = this.props;

    dispatch({
      type:'TimeCalendar/save',
      payload:{appointmentAsTimeCalendar:[]}
    })

    console.log('selectedDate123 :: ',this.props.selectedDate);
    let de = moment()
    if (Array.isArray(this.props.selectedDate) && this.props.selectedDate.length != 0){
      de = moment(this.props.selectedDate[0],'YYYY-MM-DD')
    }else if (!Array.isArray(this.props.selectedDate) && this.props.selectedDate){
      de = moment(this.props.selectedDate,'YYYY-MM-DD')
    }

    // 初始看板为当前月
    const panelMonth = de.format('MM');
    // 当前月
    const nextMonth = moment().add(1,'month').format('MM');
    const currentMonth = moment().format('MM');
    let currentPageDate = moment().format('YYYY-MM-DD')
    // 当选择的月是当前月的下一月,或当前月,则初始化展示面板为当前月为首
    if (panelMonth == nextMonth || panelMonth == currentMonth) {
      currentPageDate = moment().format('YYYY-MM-DD')
    }else {
      currentPageDate = moment(de).format('YYYY-MM-DD')
    }



    // 1正常 2约满 3休息
    /* const disableDay = [ { date:'2018-12-10', type:'1',} ] */
    // console.log('钱313212312312 :: ',de);
    // console.log('defaultValue123123 :: ',de);

    this.state = {
      defaultValue: de,
      panelMonth:panelMonth,
      disableDay:this.props.disableDay || [],
      startDate:this.props.startDate,
      endDate:this.props.endDate,
      selectedDate: this.props.selectedDate,   // 当前选中的日期
      doctorId:this.props.doctorId || null,
      currentPageDate:currentPageDate,
      // year:de.format('YYYY') == 'Invalid date' ? moment().format('YYYY') : de.format('YYYY'),  // 年
      // month:de.month() ? de.month()+1 : moment().month() + 1, // 月
      calendarLoading:false
    }
  }

  // 父组件更新
  componentWillReceiveProps(nextProps){
    if (!Array.isArray(nextProps.selectedDate)) {
      if(nextProps.selectedDate != this.state.selectedDate){
        this.setState({
          selectedDate:nextProps.selectedDate,
        })
      }
    }

    if(this.props.doctorId != nextProps.doctorId) {
      this.setState({
        // disableDay:nextProps.disableDay || [],
        startDate: nextProps.startDate,
        endDate: nextProps.endDate,
        doctorId:nextProps.doctorId,
        selectedDate:nextProps.selectedDate,
      },()=>{
        this.refreshCalendar()
      })
    }
  }

  /**
   * 点击选择日期回调
   * @param value
   */
  onSelect=(value)=>{
    const { TimeCalendar }  = this.props
    const { appointmentAsTimeCalendar } = TimeCalendar || {}
    const { currentPageDate } = this.state
    let formatCurrentPageDateMonth = moment(currentPageDate,'YYYY-MM').format('YYYY-MM');
    let formatNextCurrentPageDateMonth = moment(currentPageDate,'YYYY-MM').add(1, 'months').format('YYYY-MM');
    console.log('appointmentAsTimeCalendar :: ',appointmentAsTimeCalendar);




    // 当选择非当前面板内月份日期 切换月份刷新面板
    if(value.format('YYYY-MM') != formatCurrentPageDateMonth &&  value.format('YYYY-MM') != formatNextCurrentPageDateMonth){
       this.setState({
         currentPageDate:value.format('YYYY-MM-DD'),
      },()=>{
        this.props.onPanelChange(value)
        this.refreshCalendar();
      })
    }else {
      // 月模式只支持多选
      if (this.props.selectWeeksMode && Array.isArray(this.state.selectedDate)) {
        let selectedDate = [...this.state.selectedDate]
        // 是否是周模式
        let weekStartDay = moment(value, "YYYY-MM-DD").startOf('week').format('YYYY-MM-DD')
        let weekEndDay = moment(value, "YYYY-MM-DD").endOf('week').format('YYYY-MM-DD')
        // 获取出一周的日期,

        let selectWeek = Array.from([0,1,2,3,4,5,6],(value,index)=>{
          // 获取出一周的日期,并且对比数据是否是正常排班
          console.log('weekStartDay12321 :: ',moment(weekStartDay, 'YYYY-MM-DD').add(value, 'day').format('YYYY-MM-DD'));
          return moment(weekStartDay, "YYYY-MM-DD").add(value,'day').format('YYYY-MM-DD')
        })
        let filterBySelectWeek = [...selectWeek]
        // 比数据是否是正常排班
        if (Array.isArray(appointmentAsTimeCalendar) && Array.isArray(selectWeek)) {
          let filterArr = []
          appointmentAsTimeCalendar.forEach((item)=>{
            if(selectWeek.indexOf(item.appointmentDate) != -1){
              const { status } = item || {}
              // 如果当前没有获取到跨月的排班情况,则默认选中
              if (status == 1 || status == 2 || status == 4) {
                const { appointmentDate } = item || {}
                filterArr.push(appointmentDate)
              }else {
                return false
              }
            }
          });
          filterBySelectWeek = filterArr.length != 0 ? filterArr : filterBySelectWeek
        }
        // 判断是否选择此周
        let isSelect =  selectedDate.find((ItemBySelectData)=>{
          return filterBySelectWeek.indexOf(ItemBySelectData) != -1
        })
        // 是否被选中次星期
        if(!isSelect) {
          // 未被选中, 记录一周数据
          selectedDate = filterBySelectWeek
        }else {
          // 已被选中 清空选中数据
          selectedDate = []
        }
        // 记录当前选中周
        this.setState({
          selectedDate: selectedDate,
        })

        return;
      }

      if (Array.isArray(this.state.selectedDate)) {
          let selectedDate = [...this.state.selectedDate]
          // 当前添加数据不存在
          if (selectedDate.indexOf(value.format('YYYY-MM-DD')) == -1) {
            if(selectedDate.length <= 30) {
              if (this.props.selectListForOne) { // 限制当前选中只可替换一天
                selectedDate = [value.format('YYYY-MM-DD')]
              }else {
                selectedDate.push(value.format('YYYY-MM-DD'))
              }
            }else{
              message.warning('选中日期不可超出30天');
            }
          } else {
            selectedDate = selectedDate.filter((res, idx) => {
              return res != value.format('YYYY-MM-DD')
            })
          }
          this.setState({selectedDate: selectedDate}, () => {
            this.props.onSelect(selectedDate)
          },()=>{
            console.log('selectedDateselectedDate :: ',selectedDate);
          })
        }else {
          this.setState({selectedDate: value.format('YYYY-MM-DD')}, () => {
            this.props.onSelect(value)
          })
        }
    }
  }

  /**
   * 更新日历插件 日历插件内部结构回调展示
   * */
  getPopoverOtherItem=(value)=>{
    const { appointmentAsTimeCalendar } = this.props.TimeCalendar
    let isCurrent = false;
    if (moment().isSame(value, 'day')) {isCurrent = true}

    let renderDom= <div className={classNames(
      {
        [styles.PopoverOtherItem]:true,
        [styles.PopoverCalendarItemCurrent]:isCurrent,
      }
    )}>{value.format('DD')}</div>
    const OtherDate = appointmentAsTimeCalendar;
    const isExist = OtherDate.find((item,idx)=>{
      if(item.appointmentDate == value.format('YYYY-MM-DD')){
        return item
      }
    })

    // 判定时间是否可选
    let isBefore = false
    isBefore = moment().isSame(value,'day')
    if(!isBefore) {
      isBefore = moment().isBefore(value,'day')
    }

    if(isExist){
      // 1正常 2约满 3休息
      if (isExist.status == 1 && isBefore) {
        renderDom = (
          <div className={classNames(
            {
              [styles.PopoverCalendarItem]:true,
              [styles.PopoverCalendarItemCurrent]:isCurrent,
            }
          )}>
            <span>{value.format('DD')}</span>
          </div>)
      }else if (isExist.status == 2) {
        renderDom = (
          <div className={classNames({
            [styles.PopoverFullItem]:isBefore,
            [styles.PopoverOtherItem]:!isBefore
          })}>
            <span>{value.format('DD')}</span>
            {/* {isExist.status == 1 && <span className={styles.PropverOtherItemIcon}>休</span>} */}
            {isExist.status == 2 && <span className={styles.PropverOtherRedItemIcon}>满</span>}
          </div>)
      }else if (isExist.status == 3) {
        renderDom = (
          <div className={styles.PopoverOtherItem}>
            <span>{value.format('DD')}</span>
            {/* {isExist.status == 1 && <span className={styles.PropverOtherItemIcon}>休</span>} */}
            {isExist.status == 3 && <span className={styles.PropverUnscheduledItemIcon}>未排班</span>}
          </div>)
      }else if (isExist.status == 4){
        renderDom = (
          <div className={classNames({
            [styles.PopoverFullItem]:isBefore,
            [styles.PopoverOtherItem]:!isBefore
          })}>
            <span>{value.format('DD')}</span>
            {/* {isExist.status == 1 && <span className={styles.PropverOtherItemIcon}>休</span>} */}
            {isExist.status == 4 && <span className={styles.PropverOtherRedItemIcon}>休</span>}
          </div>)
      }
    }
    return renderDom;
  }

  /**
   * 展示日期结构
   * @param value
   * @returns {XML}
   */
  dateCellRender=(value)=> {
    const { appointmentAsTimeCalendar } = this.props.TimeCalendar
    let isCurrent = false;
    if (moment().isSame(value, 'day')) {isCurrent = true}

    let renderDom = <div className={classNames({
      [styles.PopoverCalendarItem]:true,
      [styles.PopoverCalendarItemCurrent]:isCurrent,
    })}>{ value.format('DD') }</div>;

    if(this.state.panelMonth == value.format('MM')) {
      if(Array.isArray(this.state.selectedDate) && !this.props.selectListForOne){
        renderDom = this.getPopoverOtherItem(value)
        if(this.state.selectedDate.indexOf(value.format('YYYY-MM-DD')) != -1){
          renderDom = <div className={classNames({
            [styles.PopoverCalendarItemSelected]:true,
            [styles.PopoverCalendarItemCurrent]:isCurrent,
          })}> { value.format('DD') } </div>
          // renderDom = <div className={styles.PopoverCalendarItemSelected}>{value.format('DD')}</div>
        }
      }else if (this.state.selectedDate == value.format('YYYY-MM-DD')){
        renderDom = <div className={classNames({
          [styles.PopoverCalendarItemSelected]:true,
          [styles.PopoverCalendarItemCurrent]:isCurrent,
        })}>{ value.format('DD') }</div>
        // renderDom = <div className={styles.PopoverCalendarItemSelected}>{value.format('DD')}</div>
      }
    }else {
      renderDom = <div className={classNames({
        [styles.PopoverOtherItem]:true,
        [styles.PopoverCalendarItemCurrent]:isCurrent,
      })}>{ value.format('DD') }</div>
    }
    return renderDom
  }

  setForwardYears=()=>{
    const { currentPageDate } = this.state
    this.changeCalendarMoment(moment(currentPageDate).subtract(1,'years').format('YYYY-MM'))
  }

  setForwardMonth=()=>{
    const { currentPageDate } = this.state
    this.changeCalendarMoment(moment(currentPageDate).subtract(2,'months').format('YYYY-MM'))
  }

  setBackwardMonth=()=>{
    const { currentPageDate } = this.state
    this.changeCalendarMoment(moment(currentPageDate).add(2,'months').format('YYYY-MM'))
  }

  setBackwardYears=()=>{
    const { currentPageDate } = this.state
    this.changeCalendarMoment(moment(currentPageDate).add(1,'years').format('YYYY-MM'))
  }

  // 改变时间
  changeCalendarMoment=(momentStr)=>{ this.setState({ currentPageDate:momentStr },()=>{
    this.props.onPanelChange(momentStr)
  })}

  render() {
    moment.locale('zh-cn')
    let startMonent = moment()
    if (!Array.isArray(this.state.selectedDate)) {
      let startMon = moment().subtract('10','year').format('YYYY')+'-01-01'
      startMonent = moment(startMon,'YYYY-MM-DD')
    }
    let startDate = this.state.startDate? moment(this.state.startDate) : startMonent;
    let endDate = this.state.startDate?moment(this.state.endDate) : moment().add(10, 'year')
    const { loading } = this.props
    const { year, month,currentPageDate } = this.state
    const momentNextMonthByCurrentDate = moment(currentPageDate).add(1, 'months')

    return (
      <div style={{ width: 350 ,borderRadius: 4 }}>
        <Spin spinning={this.state.calendarLoading}>
          {/* <Calendar
            mode={'month'}
            className={styles.selectCalendarPopover}
            fullscreen={false}
            dateFullCellRender={this.dateCellRender}
            onPanelChange={this.onPanelChange}
            onSelect={this.onSelect}
            //value={this.state.de}
            //defaultValue={this.state.de}
            validRange={[startDate,endDate]}
            locale={zhCN}
          />
          <Calendar
            mode={'month'}
            className={styles.selectCalendarPopover}
            fullscreen={false}
            dateFullCellRender={this.dateCellRender}
            onPanelChange={this.onPanelChange}
            onSelect={this.onSelect}
            //value={this.state.de}
            //defaultValue={this.state.de}
            validRange={[startDate,endDate]}
            locale={zhCN}
          /> */}

          <div className={styles.controlBoxWarp}>
            <div>
              <Icon type="double-left" onClick={this.setForwardYears} className={styles.DoubleLeftOutlined} />
              <Icon type="left" onClick={this.setForwardMonth} className={styles.LeftOutlined} />
            </div>
            <div className={styles.CalendarControl}>
              <span className={styles.CalendarControlYears}>{moment(currentPageDate,'YYYY-MM-DD').format('YYYY')}年</span>
              <span className={styles.CalendarControlMonth}>{moment(currentPageDate,'YYYY-MM-DD').format('MM')}月</span>
            </div>
            <div>
              <Icon type="right" onClick={this.setBackwardMonth} className={styles.DoubleLeftOutlined} />
              <Icon type="double-right" onClick={this.setBackwardYears} className={styles.LeftOutlined} />
            </div>
          </div>
          <CalendarIconPopover
            currentDate={currentPageDate}
            selectedDate={this.state.selectedDate}
            onSelectData={(selectData)=>{
              const { dayMoment, } = selectData || {}
              this.onSelect(moment(dayMoment,'YYYY-MM-DD'))
            }}
          />
          <div className={styles.controlBoxLastWarp}>
            <div className={styles.CalendarControl}>
              <span className={styles.CalendarControlYears}>{momentNextMonthByCurrentDate.format('YYYY')}年</span>
              <span className={styles.CalendarControlMonth}>{momentNextMonthByCurrentDate.format('MM')}月</span>
            </div>
          </div>
          <CalendarIconPopover
            currentDate={momentNextMonthByCurrentDate.format('YYYY-MM-DD')}
            selectedDate={this.state.selectedDate}
            onSelectData={(selectData)=>{
              const { dayMoment, } = selectData || {}
              this.onSelect(moment(dayMoment,'YYYY-MM-DD'))
            }}
          />
        </Spin>
      </div>
    )
  }

  /**
   * TimeCalendar
   * year	是	年	展开
   month	是	月	展开
   doctorId 复制	是	资源id
   */
  refreshCalendar = async ()=>{
    const {
      dispatch,
    } = this.props
    const { currentPageDate } = this.state  // 当前案板的展示月份


    if(Array.isArray(this.state.selectedDate) && !this.props.selectListForOne) {
        // 时间制度
        this.setState({
          calendarLoading: true,
        }, () => {
          dispatch({
            type: 'TimeCalendar/appointmentAsTimeCalendar',
            payload: {
              resourceId: this.state.doctorId,
              year: moment(currentPageDate,'YYYY-MM-DD').format('YYYY'),
              month: moment(currentPageDate,'YYYY-MM-DD').format('MM')
            }
          }).then(() => {
            this.setState({calendarLoading: false,})
          })
        })
    }
  }

  clearSelect=(callBack)=>{
    this.setState({
      selectedDate:[]
    },()=>{
      callBack && callBack()
    })
  }

  componentDidMount() {
    this.props.onRef && this.props.onRef(this)
    this.refreshCalendar()
  }
}


/**
 *
 */
export default class PopoverForCalendar extends Component{
  static propTypes = {
    doctorId:PropTypes.any,
    onSelect:PropTypes.func,
    onPanelChange:PropTypes.func,     //年/月份面板被切换回调
    selectListForOne:PropTypes.bool   // 当可选多天日期时是否限制只可选中一天 横向日模式使用
    /* disableDay:PropTypes.array,
    startDate:PropTypes.string(),
    selectedDate:PropTypes.string(),
    endDate:PropTypes.string(), */
  };
  static defaultProps = {
    selectListForOne: false // 当可选多天日期时是否限制只可选中一天 横向日模式使用
  }

  componentDidMount() {
    this.props.onRef && this.props.onRef(this)
  }

  constructor(props){
    super(props);
    this.state = {
      visible:false
    }
  }

  /**
   * 打开悬窗回调
   */
  onVisibleChange=(visible)=>{
    this.setState({
      visible:visible
    },()=>{
      if(visible){
        this.ChangeDateCalendar && this.ChangeDateCalendar.refreshCalendar()
      }
    })
  }

  /**
   * 选中多天情况下 重置选中日期数组
   */
  clearSelect=()=>{
    this.ChangeDateCalendar.clearSelect(()=>{
      this.ChangeDateCalendar.refreshCalendar()
      this.ChangeDateCalendar.props.onSelect && this.ChangeDateCalendar.props.onSelect([])
    })
  }

  /**
   * 单天选择情况下 点击选择当前一天
   * 跳转当天
   */
  toCurrentDate=()=>{

    this.setState({selectedDate: moment().format('YYYY-MM-DD')}, () => {
      this.props.onSelect(moment())
    })
  }



  visibleTrigger=()=>{
    this.setState({
      visible:!this.state.visible
    })
  }

  selectedDateForPopover=(value)=>{
    if (value && !Array.isArray(value)) {
      this.setState({
        visible:false
      })
    }
    this.props.onSelect(value)
  }

  render() {
    const {
      children,     //传入的子组件
      onSelect,
      onPanelChange,
      disableDay,
      startDate,
      selectedDate, // 已选中日期
      endDate,
      doctorId,
    } = this.props;
    const content = (
      <div>
        {/* {Array.isArray(selectedDate) ?
          <a href="javascript:;" onClick={this.clearSelect} className={styles.reset}>重置</a>
          : <a href="javascript:;" onClick={this.toCurrentDate} className={styles.reset}>当日</a>
        } */}
        <ChangeDateCalendar
          onRef={(that)=>{this.ChangeDateCalendar =  that}}
          disableDay = {disableDay}                      // 禁止点击的日期
          startDate  = {startDate}                       // 可展示日期开始时间
          endDate    = {endDate}                         // 可展示日期结束时间
          onSelect = {this.selectedDateForPopover}       // 当前选中日期回调
          doctorId = {doctorId}                          // 医生id
          selectListForOne={this.props.selectListForOne} //
          // defaultPickerValue={moment()}
          value={moment().add('10','days')}
          defaultValue={moment().add('10','days')}
          onPanelChange={onPanelChange}  // 年/月份面板被切换回调
          selectedDate={selectedDate}    // 已选中日期
        />
      </div>
    )
    return (
      <Popover
        overlayClassName={styles.PopoverCalendar}
        placement="bottom"
        content={content}
        // onClick={this.visibleTrigger}
        visible={ this.state.visible }
        // visible={true}
        trigger="click"
        onVisibleChange={this.onVisibleChange}
      >
        {children}
      </Popover>
    )
  }
}
