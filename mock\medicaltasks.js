const getMedical = (req, res) =>
  res.json([
    {
      id: '000000001',
      name: '张三',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '1',
      sex: '男',
      age: '35',
      Doctor: '无菌',
      complaint: '隐形正畸、隐适美、佩带隐形矫治器',
      type: '1',
      status:'1',
      Handle:'1',
    },
    {
      id: '000000002',
      name: '里斯',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '56',
      Doctor: '无菌',
      complaint: '隐形正畸、隐适美、佩带隐形矫治器',
      title: '你推荐的 曲妮妮 已通过第三轮面试',
      satrdatetime: '2017-08-08',
      enddatetime: '2017-08-08',
      type: '2',
      status:'0',
      Handle:'1',
    },
    {
      id: '000000003',
      name: '里斯',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '56',
      Doctor: '无菌',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      type: '3',
      status:'0',
      Handle:'1',

    },
    {
      id: '000000004',
      name: '里斯',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '56',
      Doctor: '无菌',
      information: '已经支付',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      type: '4',
      status:'1',
      Handle:'1',
    },
    {
      id: 203,
      name: '里斯',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '56',
      Doctor: '无菌',
      information: '已经支付',
      price: '10000',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      prescription: '麻药 * 3，复方玄驹胶囊 * 5，苯甲酸钠溶解计 * 3 ',
      type: '5',
      status:'1',
      Handle:'1',
    },
    {
      id: '000000006',
      name: '里斯',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '56',
      Doctor: '无菌',
      information: '已经支付',
      price: '10000',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      desc: '上次洗牙洗完了各种出血，麻烦问 问什么情况！',
      welfare: '招商银行洁牙卡，医生折扣90% ',
      type: '6',
      topcomplaint: '',
      status:'1',
      Handle:'1',
    },
    {
      id: '000000007',
      name: '里斯里斯',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '1',
      sex: '男',
      age: '56',
      Doctor: '无菌',
      information: '已经支付',
      price: '10000',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      topcomplaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      desc: '上次洗牙洗完了各种出血，麻烦问 问什么情况！',
      type: '7',
      status:'0', 
      Handle:'1',
    
    },
    {
      id: '7',
      name: '里斯里斯',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '1',
      sex: '男',
      age: '56',
      Doctor: '无菌',
      information: '已经支付',
      price: '10000',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      topcomplaint: '上次洗牙洗完了各种出血，麻烦问 问什么情况',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      desc: '上次洗牙洗完了各种出血，麻烦问 问什么情况！',
      type: '8',
      status:'0',
      Handle:'1',
      priceType:"9",
    },
    {
      id: '000000008',
      name: '张三',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '35',
      Doctor: '无菌',
      satrdatetime: '2019-08-08',
      complaint: '隐形正畸、隐适美、佩带隐形矫治器',
      type: '9',
      Handle:'1',
      status:'1',
      priceType:"9",
    },
    {
      id: '00000008',
      name: '张三',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '35',
      Doctor: '无菌',
      satrdatetime: '2019-08-08',
      complaint: '隐形正畸、隐适美、佩带隐形矫治器',
      type: '9',
      Handle:'0',
      status:'1',
      priceType:"9",
    },
    {
      id: '000000009',
      name: '里斯1',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '56',
      Doctor: '无菌',
      information: '已经支付',
      price: '10000',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      visit: '他目前的牙齿情况？',
      type: '10',
      status:'1',
      Handle:'1',

    },
    {
      id: '000000010',
      name: '里斯2',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '56',
      Doctor: '无菌',
      information: '已经支付',
      price: '10000',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      visit: '他目前的牙齿情况？',
      type: '10',
      status:'1',
      Handle:'0',

    },
    {
      id: '000000011',
      name: '里斯里斯里斯',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '56',
      Doctor: '无菌',
      information: '已经支付',
      price: '10000',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      visit: '他目前的牙齿情况？',
      con:'我我这牙各种疼，请问能做贴片吗？',
      type: '11',
      status:'1',
      Handle:'0',


    },
    {
      id: '000000012',
      name: '里斯里斯里斯',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '56',
      Doctor: '无菌',
      information: '已经支付',
      price: '10000',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      visit: '他目前的牙齿情况？',
      examineCon:'医生操作不当医生操作不当',
      examine: '2',
      priceType:"4",
      type: '12',
      Handle:'0',
      status:'0',
    },
    {
      id: '000000013',
      name: '里斯里斯里斯',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '56',
      Doctor: '无菌',
      information: '已经支付',
      price: '10000',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      visit: '他目前的牙齿情况？',
      examineCon:'医生操作不当医生操作不当',
      examine: '0',
      priceType:"2",
      type: '12',
      Handle:'0',
      status:'0',
    },
    {
      id: '000000014',
      name: '庄三',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '12',
      Doctor: '无菌',
      information: '已经支付',
      price: '10000',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      visit: '他目前的牙齿情况？',
      examineCon:'医生操作不当医生操作不当',
      examine: '1',
      priceType:"3",
      type: '12',
      Handle:'0',
      status:'0',
    },
    {
      id: '000000015',
      name: '庄三',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '12',
      Doctor: '无菌',
      information: '已经支付',
      price: '10000',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      visit: '他目前的牙齿情况？',
      examineCon:'医生操作不当医生操作不当',
      examine: '0',
      priceType:"5",
      type: '12',
      Handle:'1',
      status:'0',
    },
    {
      id: '00000015',
      name: '庄三',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '12',
      Doctor: '无菌',
      information: '已经支付',
      price: '10000',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      visit: '他目前的牙齿情况？',
      examineCon:'医生操作不当医生操作不当',
      examine: '2',
      priceType:"5",
      type: '12',
      Handle:'0',
      status:'0',
    },
    {
      id: '000000016',
      name: '庄三',
      number: 'BJS01000129',
      Birthday: '10-12',
      BirthdayType: '0',
      sex: '男',
      age: '12',
      Doctor: '无菌',
      information: '已经支付',
      price: '10000',
      Treatment: '孙理，2018-11-11，贴片，洁牙， 正畸牙周治疗',
      complaint: '种植手术种植手术种植手术，最后牙齿弄坏了，医生被投诉， 患者挂掉了',
      satrdatetime: '2019-08-08',
      enddatetime: '2018-08-08',
      visit: '他目前的牙齿情况？',
      examineCon:'医生操作不当医生操作不当',
      examine: '1',
      priceType:"1",
      type: '12',
      Handle:'0',
      status:'0',
    },
  ]);

export default {
  'GET /api/medical': getMedical,
};
