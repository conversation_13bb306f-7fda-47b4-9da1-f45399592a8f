import {
  getCustomerListAllOrMy,
  customerPoolFilterDict,
  updateCrmSortType,
} from '@/services/CustomerProfile';


export default {
  namespace: 'customerProfile',

  state: {
    filterCriteria: [], // 客户列表筛选数据
    inputSearchVal: '', // input搜索
    follow: undefined, // 关注
    programme: undefined, // 方案
    beginAge: '', // 年龄开始段
    endAge: '', // 年龄结束段
    beginOffer: '', // 方案报价开始
    endOffer: '', // 方案报价结束
    lastVisitTimeStr: [null, null], // 上次就诊时间
    lastReturnVisitTimeStr: [null, null], // 上次回访时间
    potentialTreatmentInit: [], // 潜在治疗
    consultantInit: [], // 健康顾问
    customerServiceInit: [], // 客服
    responsibilityDoctorsInit: [], // 责任医生
    lastVisitDoctorsInit: [], // 上次就诊医生
    initKey: '01', // 初始化tab
    source: undefined, // 客户来源一级
    subSource: undefined, // 客户来源二级
    wayOfKnowing: [],
    wayOfChannel: [],
  },

  effects: {
    // 我的客户列表或客户列表
    *getCustomerListAllOrMy({ payload }, { call }) {
      const res = yield call(getCustomerListAllOrMy, payload);
      return res;
    },

    // 市场化客户池的过滤条件
    *customerPoolFilterDict({ payload }, { call }) {
      const res = yield call(customerPoolFilterDict, payload);
      return res;
    },

    // 更新crm列表默认排序规则
    *updateCrmSortType({ payload }, { call }) {
      const res = yield call(updateCrmSortType, payload);
      return res;
    }
  },

  reducers: {
    // 更新状态值数据
    setTaskListState(state, { payload }) {
      return {
        ...state,
        ...payload
      }
    },
    
    // 清空数据
    clean(state, { payload }){
      return {
        ...state,
        filterCriteria: [], // 客户列表筛选数据
        inputSearchVal: '', // input搜索
        follow: undefined, // 关注
        programme: undefined, // 方案
        beginAge: '', // 年龄开始段
        endAge: '', // 年龄结束段
        beginOffer: '', // 方案报价开始
        endOffer: '', // 方案报价结束
        lastVisitTimeStr: [null, null], // 上次就诊时间
        lastReturnVisitTimeStr: [null, null], // 上次回访时间
        potentialTreatmentInit: [], // 潜在治疗
        consultantInit: [], // 健康顾问
        customerServiceInit: [], // 客服
        responsibilityDoctorsInit: [], // 责任医生
        lastVisitDoctorsInit: [], // 上次就诊医生
        initKey: '01', // 初始化tab
        source: undefined, // 客户来源一级
        subSource: undefined, // 客户来源二级
        wayOfKnowing: [],
        wayOfChannel: []
      }
    }
  },
  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, search }) => {
        if (!(/\/customerProfile\/index/.test(pathname))) {
          dispatch({
            type: "clean",
            payload: {}
          })
        }
      })
    }
  }
};
