import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { Input, Icon, AutoComplete, Form } from 'antd';
import classNames from 'classnames';
import Debounce from 'lodash-decorators/debounce';
import Bind from 'lodash-decorators/bind';
import styles from './index.less';
import { postSeanchData, getSeanchData } from '@/services/api';
import $ from 'jquery'
import search from '@/assets/AppointmentRules/search.png'

const Item = Form.Item;
const Option = AutoComplete.Option;
const Search = Input.Search;

function searchResult(value) {
  return value.map((item, idx) => {
    return item.item;
  });
}
function renderOption(item, idx) {
  return (
    <Option key={item} value={item} text={item}>
      {item}
    </Option>
  );
}
function renderOption2(item, idx) {
  return (
    <Option key={item.id} value={item.id + ''} text={item.diagnosisName}>
      {item.diagnosisName}
    </Option>
  );
}

const validate = {
  validateStatus: 'error',
  errorMsg: 'The prime between 8 and 12 is 11!',
}

@Form.create()
export default class HeaderSearch extends PureComponent {
  static propTypes = {
    className: PropTypes.string,
    placeholder: PropTypes.string,
    value: PropTypes.string,
    onSearch: PropTypes.func,
    setvalue: PropTypes.func,
    onPressEnter: PropTypes.func,
    defaultActiveFirstOption: PropTypes.bool,
    dataSource: PropTypes.array,
    defaultOpen: PropTypes.bool,
    keywordsUrl: PropTypes.string,
    query: PropTypes.object,
    keyName: PropTypes.string,
    method: PropTypes.string,
    searchValidateStatus: PropTypes.object,
    addonBefore: PropTypes.any,
  };

  static defaultProps = {
    defaultActiveFirstOption: false,
    onPressEnter: () => { },
    onSearch: () => { },
    setvalue: () => { },
    className: '',
    placeholder: '',
    value: '',
    dataSource: [],
    defaultOpen: false,
    keywordsUrl: '',
    method: '',
    query: {},
    keyName: 'GlobalSearch',
    searchValidateStatus: {
      validateStatus: 'success',
      errorMsg: null,
    },
    addonBefore: null
  };

  constructor(props) {
    super(props);
    this.state = {
      value: this.props.value,
      dataSource: [],
    };
  }

  componentWillUnmount() {
    clearTimeout(this.timeout); // 组件销毁时释放
  }

  componentWillReceiveProps(props) {
    this.setState({
      value: props.value
    })
  }

  onSearch = (value) => {
    // 去掉首尾空格
    const ValueEnds = value.replace(/(^\s*)|(\s*$)/g, "");
    const { onSearch } = this.props;
    this.timeout = setTimeout(() => {
      if (this.selectValue) {
        onSearch(this.selectValue, this.selectName); // 异步输出input中的值
      } else {
        onSearch(ValueEnds, this.selectName); // 异步输出input中的值
      }
      this.selectValue = null;
    }, 200);
  };

  onChange = value => {
    const { onChange, query } = this.props;
    const { keywordsUrl, method } = this.props;
    // 去掉首尾空格
    const ValueEnds = value.replace(/(^\s*)|(\s*$)/g, "");
    let valuedata = {
      queryText: ValueEnds,
      ...query,
    };
    if (keywordsUrl) {
      if (method == "GET") {
        getSeanchData(`${keywordsUrl}?search=${ValueEnds}&name=${ValueEnds}`).then((re) => {
          let data = [];
          if (re && re.content) {
            data = re.content.filter(item => item);
          }
          this.setState({
            dataSource: data
          })
        })
      } else {
        postSeanchData(keywordsUrl, valuedata).then((re) => {
          let data = [];
          if (re && re.content) {
            data = re.content.filter(item => item);
          }
          this.setState({
            dataSource: data
          })
        })
      }
    }

    this.setState({ value: value });
    if (onChange) {
      onChange(value, this.selectName);
    }
  };

  onSearchChange = (e) => {
    const { onChange, query } = this.props;
    let value = e.target.value
    const ValueEnds = value.replace(/(^\s*)|(\s*$)/g, "");
    let valuedata = {
      queryText: ValueEnds,
      ...query,
    };
    console.log(ValueEnds);
    this.setState({ value: value });
    if (onChange) {
      onChange(value, this.selectName);
    }
  }

  /**
   * 关联联想数据
   */
  handleSearch = value => {
    //this.onSearch(value)
  };

  /**
   * 清空联想词和Value方法
   */
  clearValue = (notRefresh = false) => {
    let form = this.props.form
    if (!!form.getFieldValue('searchInput')) {
      form.setFieldsValue({
        [`searchInput`]: '',
      })
      this.setState({
        value: '',
        dataSource: [],
      }, () => {
        if (!notRefresh) {
          this.onSearch('')
        }
      })
    }
  }
   deleteValue=()=>{
    let form = this.props.form
      form.setFieldsValue({
        [`searchInput`]: '',
      })
   }
  setvalue = (value) => {
    console.log('来喽来喽', value)
    let form = this.props.form

    form.setFieldsValue({
      searchInput: value,
    })
    this.setState({
      value,
    })
  }


  render() {
    const { className, placeholder, keyName, source, addonBefore, ...restProps } = this.props;
    const { getFieldDecorator } = this.props.form;
    const { value, dataSource } = this.state;
    delete restProps.defaultOpen;
    const { validateStatus, errorMsg, hasFeedback } = this.props.searchValidateStatus

    return (
      <span className={classNames(className, styles.headerSearch)}>
        <Form>
          <Item
            validateStatus={validateStatus}
            help={errorMsg}
            hasFeedback={hasFeedback || false}
          >
            {/*<AutoComplete
              key={keyName}
              {...restProps}
              value={value}
              backfill={true}
              onChange={this.onChange}
              onSearch={this.handleSearch}
              onSelect={(value, option) => {
                this.selectValue = value;
                this.selectName = option.props.text;
              }}
              dataSource={dataSource.map(source === 'mengmeng' ? renderOption2 : renderOption)}
            >*/}
            {getFieldDecorator('searchInput', {
              initialValue: ''
            })(
              <Search
                id='GlobalSearch'
                className={'searchInput'}
                ref={node => {
                  this.input = node;
                }}
                //value={value}
                addonBefore={addonBefore}
                aria-label={placeholder}
                placeholder={placeholder}
                onChange={this.onSearchChange}
                onSearch={this.onSearch}
                suffix={<div><img src={search} style={{width: 16, height: 16}} alt='' /></div>}
              // setvalue={this.setvalue}
              />
            )}
            {/*</AutoComplete>*/}
          </Item>
        </Form>
      </span>
    );
  }

  componentDidMount() {
    this.props.onRef && this.props.onRef(this)
    $('#GlobalSearch').attr({ 'autocomplete': 'off' });
    $('#searchInput').attr({ 'autocomplete': 'off' });
  }
}
