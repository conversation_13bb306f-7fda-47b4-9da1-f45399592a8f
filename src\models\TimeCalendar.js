import { getscreen  , getnurse } from '@/services/api';
import moment from 'moment';
import {
  getResourceListAll,         // 获取全部预约医生
  getMonthAppintmentDefaul,   // 预约月模式
  getAppointmentDefault1,     // ①预约列表展示
  dropAppointment,            // 取消预约
  appointmentOfPatient,       // 根据选择资源及日期查询可约列表
  sameMonthMedicalResources,  // 获取当月上班资源信息 用于获取会诊医生
  appointmentAsTimeCalendar,  // 日历预约量状态查询
  appointmentAsNumberCalendar,
  searchSomeone,              // 预约列表搜索功能接口
  exportExcel,                // 导出预约
  appointmentTimeLengthAlter, // 到诊后修改时长
} from '@/services/subscribeFullscreen'
import { freeTimeOfResource } from '@/services/WaitingListService'
import { routerRedux } from "dva/router";
import { defaultDate,setEventDtoListItem } from '@/utils/CalendarUtils'
//const defaultDate = moment().subtract(1,'year').format('YYYY-MM-DD')

/**
 * 预约组件 预约制度Model
 * 包含 全屏Model 预约制度组件
 */
const typeClass = {
  '1':{ textColor:'#444444', color:'#f9c8c9',},   // 老患者预约
  '2':{ textColor:'#444444', color:'#fef3f3',},   // 新患者预约
  '3':{ textColor:'#444444', color:'#fef3f3',},   // 会诊
  '4':{ textColor:'#444444', color:'#eef1f6',},   // 个人占用时间
  '5':{ textColor:'#444444', color:'#eef1f6',},   // 个人占用时间
}

/*function setEventDtoListItem(res){
  //type 1:老患者预约   2:新患者预约  3:会诊   4:个人时间占用
  if(res.type == 1){}
  let newStartTiem = `${defaultDate}T${res.start}`
  let newEndTiem = `${defaultDate}T${res.end}`

  if(res.type == 6){
    return {
      ...res,
      ...typeClass[res.type],
      start:moment(newStartTiem).format(),
      end:moment(newEndTiem).format(),
      rendering: 'background',
      backgroundColor:'#fff9d3'
    }
  }else {
    return {
      ...res,
      ...typeClass[res.type],
      start:moment(newStartTiem).format(),
      end:moment(newEndTiem).format(),
    }
  }
}*/

export default {
  namespace: 'TimeCalendar',
  state: {
    appointmentInfoEventDtoList:[],        // ①预约列表展示 ,预约事件集合
    calendarTableDtoList:[],               // ①预约列表展示  , table列表椅位集合
    appointmentPageInfo:{},                //  预约列表分页信息
    appointmentListDtoByChange:{},         // [改约] 改约列表展示
    appointmentListDtoByChangePageInfo:{}, // [改约] 改约列表分页信息
    changeListDtoByChange:{},              // [改约] 现有预约列表展示
    consultationListDtoByChange:{},        // [改约] 会诊预约列表展示
    allEventDtoList:[],                    // [改约] 所有的预约事件
    MedicalResources:[],                   // 获取会诊医生
    appointmentAsTimeCalendar:[],          // 日历预约量状态查询
    doctorTimeCalendarPageInfo:{},         // [医生使用] 预约组件分页信息
    TimeCalendarQuery:{},                  // 获取地址栏参数
    onTimeBlankClickByFull:null,           // 当前在全屏选中的选中区域
    resourceListAll:[],                    // [月模式] 全部医生
    AgendaHeadType:'1',                    //
    appointmentCountAfterWorkTime:null,
    appointmentCountBeforeWorkTime:null,
  },
  effects: {
    /**
     * 获取根据医生id获取等待人列表医生空闲时间和等待人
     * /appointmentDataBase/freeTimeOfResource
     */
    *freeTimeOfResource({payload}, { call, put }){
      const response = yield call(freeTimeOfResource,payload);
      console.log('freeTimeOfResource :: ',response);
      //console.log(freeTimeOfResource);
      return response
    },



    // [月模式] 查询所有医生
    *getResourceListAll({payload}, { call, put }){
      console.log('*getResourceListAll');
      const response = yield call(getResourceListAll,payload);
      console.log('response :: ',response,response && response.code == 200 && Array.isArray(response.content));
      if(response && response.code == 200 && Array.isArray(response.content)){
        let content = response.content;
        console.log('content :: ',content);
        yield put({
            type: 'save',
            payload: {resourceListAll:content},
        });
      }else {
        yield put({
          type: 'save',
          payload: {resourceListAll:[]},
        });
      }
      return response
    },

    // [月模式]
    /**getMonthAppintmentDefaul({payload}, { call, put }){
      console.log('*getMonthAppintmentDefaul');
      const response = yield call(getMonthAppintmentDefaul,payload);
      console.log('response :: ',response);
    },*/

    /**
     * 单独刷新列表头
     * */
    *freeTimeOfResourceByList({payload}, { call, put }){
      //getAppointmentDefaultObj.calendarTableDtoList = calendarTableDtoList ? calendarTableDtoList : [];
      console.log(payload);
    },

    // 获取[月模式]列表展示
    *getMonthAppintmentDefaul({payload}, { call, put }){
      const response = yield call(getMonthAppintmentDefaul,payload)
      console.log('*getMonthAppintmentDefaul11122',response);
      if(response && response.code == 200 && response.content){
        let getAppointmentDefaultObj = {}
        const {
          appointmentInfoEventDtoList,
          calendarTableDtoList,
          current,              // 当前页
          pageSize,             // 每页几条
          status,               //
          total,                // 总条数
          appointmentCountAfterWorkTime,
          appointmentCountBeforeWorkTime
        } = response.content
        let eventlist = Array.isArray(appointmentInfoEventDtoList) ? appointmentInfoEventDtoList : [];
        getAppointmentDefaultObj.appointmentInfoEventDtoList = eventlist;
        getAppointmentDefaultObj.calendarTableDtoList = calendarTableDtoList ? calendarTableDtoList : [];
        //分页处理
        let pageCount = 0 //总页数
        if(total && pageSize) {pageCount = Math.ceil(total / pageSize)}
        getAppointmentDefaultObj.appointmentPageInfo = {current, pageSize, total, pageCount}
        getAppointmentDefaultObj.appointmentCountAfterWorkTime = appointmentCountAfterWorkTime
        getAppointmentDefaultObj.appointmentCountBeforeWorkTime = appointmentCountBeforeWorkTime
        console.log('getAppointmentDefaultObj :: ',getAppointmentDefaultObj);

        yield put({
          type: 'saveAppointmentInfoEventDtoList',
          payload: {...getAppointmentDefaultObj,AgendaHeadType:'2'},
        });
      }else  {
        yield put({
          type: 'saveAppointmentInfoEventDtoList',
          payload: {
            appointmentInfoEventDtoList:[],
            calendarTableDtoList:[],
            appointmentPageInfo:{},
            AgendaHeadType:'2'
          },
        });
      }
      return response
    },

    //获取①预约列表展示
    *getAppointmentDefault({payload}, { call, put }){
      const response =  yield call(getAppointmentDefault1,payload)

      if(response && response.code == 200 && response.content){
        let getAppointmentDefaultObj = {}
        let content = response.content;
        const {
          appointmentInfoEventDtoList,
          calendarTableDtoList,
          appointmentCountAfterWorkTime,
          appointmentCountBeforeWorkTime,
          current,  // 当前页
          pageSize, // 每页几条
          status,   //
          total,    // 总条数
        } = content

        let eventlist = Array.isArray(appointmentInfoEventDtoList) ? appointmentInfoEventDtoList : [];
        eventlist.map((val)=>{
          return {
            ...setEventDtoListItem(val),
          }
        })

        getAppointmentDefaultObj.appointmentInfoEventDtoList = eventlist;
        getAppointmentDefaultObj.calendarTableDtoList = calendarTableDtoList ? calendarTableDtoList : [];

        //分页处理
        let pageCount = 0 //总页数
        if(total && pageSize) {
          pageCount = Math.ceil(total / pageSize)
        }
        getAppointmentDefaultObj.appointmentPageInfo = {current, pageSize, total, pageCount}
        getAppointmentDefaultObj.appointmentCountAfterWorkTime = appointmentCountAfterWorkTime
        getAppointmentDefaultObj.appointmentCountBeforeWorkTime = appointmentCountBeforeWorkTime

        yield put({
          type: 'saveAppointmentInfoEventDtoList',
          payload: {
            ...getAppointmentDefaultObj,
            AgendaHeadType:'1'
          },
        });
      }else {
        yield put({
          type: 'saveAppointmentInfoEventDtoList',
          payload: {
            appointmentInfoEventDtoList:[],
            calendarTableDtoList:[],
            appointmentPageInfo:{},
            AgendaHeadType:'1',
            appointmentCountAfterWorkTime:null,
            appointmentCountBeforeWorkTime:null
          },
        });
      }
      return response
    },



    /**
     * 预约切换至 一人多天 全屏
     * @param payload
     * @param call
     * @param put
     * @returns {string}
     */
    *appointmentOfPatientForFull({payload},{call,put}){
      const response = yield call(appointmentOfPatient,payload)
      if(response && response.code == 200 && response.content){
        let getAppointmentDefaultObj = {}
        let content = response.content;
        const {
          appointmentInfoEventDtoList,
          calendarTableDtoList,
          current,  // 当前页
          pageSize, // 每页几条
          status,   //
          total,    // 总条数
          appointmentCountAfterWorkTime,
          appointmentCountBeforeWorkTime,
        } = content

        let eventlist = Array.isArray(appointmentInfoEventDtoList) ? appointmentInfoEventDtoList : [];
        eventlist.map((val)=>{
          return {
            ...setEventDtoListItem(val),
          }
        })

        getAppointmentDefaultObj.appointmentInfoEventDtoList = eventlist;
        getAppointmentDefaultObj.calendarTableDtoList = calendarTableDtoList ? calendarTableDtoList : [];

        //分页处理
        let pageCount = 0 //总页数
        if(total && pageSize) {
          pageCount = Math.ceil(total / pageSize)
        }
        getAppointmentDefaultObj.appointmentPageInfo = {current, pageSize, total, pageCount}
        getAppointmentDefaultObj.appointmentCountAfterWorkTime = appointmentCountAfterWorkTime
        getAppointmentDefaultObj.appointmentCountBeforeWorkTime = appointmentCountBeforeWorkTime

        yield put({
          type: 'saveAppointmentInfoEventDtoList',
          payload: {
            ...getAppointmentDefaultObj,
            AgendaHeadType:'2'
          },
        });
      }else {
        yield put({
          type: 'saveAppointmentInfoEventDtoList',
          payload: {
            appointmentInfoEventDtoList:[],
            calendarTableDtoList:[],
            appointmentPageInfo:{},
            AgendaHeadType:'2',
            appointmentCountAfterWorkTime:null,
            appointmentCountBeforeWorkTime:null
          },
        });
      }
      return response
    },

    // ② 取消预约
    *dropAppointment({payload}, { call, put }){
      const response = yield call(dropAppointment,payload)
      if(response.code == 200){
        return response.content
      }
    },



    // 根据选择资源及日期查询可约列表
    *appointmentOfPatient({payload},{call,put}){
      const response = yield call(appointmentOfPatient,payload)
      let content = response && response.content;
      if(response && response.code == 200 && content){
        console.log('appointmentOfPatient :: ',content);
        let appointmentListDto = content

        let {
          status,
          current,
          pageSize,
          timeNow,
          total,
          appointmentCountAfterWorkTime,
          appointmentCountBeforeWorkTime,
        } = appointmentListDto || {}


          current = current || 1,
          pageSize = pageSize || 4,
          status = status || 0,
          total = total || 0
        let pageCount = 0
        if(total && pageSize) {
          pageCount = Math.ceil(total / pageSize)
        }
        let doctorTimeCalendarPageInfo = {
          current,
          pageSize,
          status,
          total,
          pageCount
        }


        yield put({
          type:'saveAppointmentOfPatient1',
          payload:{
            appointmentListDtoByChange:{
              ...appointmentListDto,
              appointmentPageInfo:doctorTimeCalendarPageInfo

            } || {},   // 改约列表
            appointmentOfPatient:appointmentOfPatient || {},
          }
        })
      }else {
        yield put({
          type:'saveAppointmentOfPatient1',
          payload:{
            appointmentListDtoByChange: {},
          }
        })
      }
      return response
    },


    /**appointmentOfPatient31({payload},{call,put}){
      const response = yield call(appointmentOfPatient,payload)
    },*/

    /**
     * 获取会诊医生 信息接口
     * @returns {*}
     */
    *sameMonthMedicalResources({payload},{call,put}){
      const response = yield call(sameMonthMedicalResources,payload)
      let list = Array.isArray(response && response.content) ? response.content : [];

      let MedicalResources = [
        { resourcesName:'无', resourcesId:null},
        ...list
      ]
      if(response && response.code == 200) {
        yield put({
          type: 'save',
          payload: {'MedicalResources':MedicalResources},
        })
      }
    },
    /**
     * 日历预约量状态查询
     */
    *appointmentAsTimeCalendar({payload},{call,put}){
      console.log(payload);
      const {
        resourceId,
        year,
        month,
      } = payload || {}
      const currentPageDate = moment(`${year}-${month}`,'YYYY-MM')
      const currentNextPageDate = moment(`${year}-${month}`,'YYYY-MM').add(1,'months')


      const currentResponse = yield call(appointmentAsTimeCalendar,{
        ...payload,
        year: currentPageDate.format('YYYY'),
        month: currentPageDate.format('MM'),
      })

      const currentNextResponse = yield call(appointmentAsTimeCalendar,{
        ...payload,
        year: currentNextPageDate.format('YYYY'),
        month: currentNextPageDate.format('MM'),
      })


      console.log('currentNextPageDatecurrentNextPageDate :: ',(currentResponse && currentResponse.code == 200 && currentNextResponse && currentNextResponse.code == 200));


      if(currentResponse && currentResponse.code == 200 && currentNextResponse && currentNextResponse.code == 200) {
        yield put({
          type:'save',
          payload:{
            'appointmentAsTimeCalendar':
              Array.isArray(currentResponse.content) ?
                [...currentResponse.content,...currentNextResponse.content] :
                []
          }
        })
      }
    },

    /**
     * 挂号日历预约量状态查询
     */
    *appointmentAsNumberCalendar({payload},{call,put}){
      const response = yield call(appointmentAsNumberCalendar,payload)
      if(response && response.code == 200) {
        yield put({
          type:'save',
          payload:{'appointmentAsTimeCalendar':Array.isArray(response.content) ? response.content : []}
        })
      }
    },

    /**
      appointmentDataBase/searchSomeone
      1.3.1_预约列表关键字检索

      searchStr [string]	是	索引内容
     */
    *searchSomeone({payload},{call,put}){
      const response = yield call(searchSomeone,payload)
      if(response && response.code == 200 && response.content){
        return response.content
      }else {
        return {}
      }
    },

    /**
     * 导出预约
     */
    *exportExcel({payload},{call,put}){
      const response = yield call(exportExcel,payload)
      return response
    },


    /**
     * 调整已经到诊预约的预约时长
     */
    *appointmentTimeLengthAlter({payload},{call,put}){
      const response = yield call(appointmentTimeLengthAlter,payload)
      return response
    }
  },

  reducers:{
    /**
     * 保存全屏选中的时间区域参数
     */
    saveOnTimeBlankClickByFull(state,{payload}) {
      return {
        ...state,
        onTimeBlankClickByFull:payload,
      }
    },

    /**
     * 预约保存 列表头 和 事件集合
     * @param state
     * @param payload
     * @returns {{appointmentInfoEventDtoList}}
     * @constructor
     */
    saveAppointmentInfoEventDtoList(state,{payload}) {
      let appointmentInfoEventDtoList = payload.appointmentInfoEventDtoList.map((res,idx)=>{
        return setEventDtoListItem(res)
      })

      return {
        ...state,
        ...payload,
        appointmentInfoEventDtoList
      }
    },


    /**
     * 有患者预约半屏列表
     */
    saveAppointmentOfPatient1(state,{payload}){
      const {
        appointmentListDtoByChange,    // 改约列表
        // changeListDtoByChange,         // 现有预约列表
        // consultationListDtoByChange,   // 会诊预约列表
        // doctorTimeCalendarPageInfo
      } = payload



      // 改约列表 普通预约 和 事件
      let appointmentInfoEventDtoListByAppointment = Array.isArray(appointmentListDtoByChange.appointmentInfoEventDtoList) ? appointmentListDtoByChange.appointmentInfoEventDtoList : [];
      let calendarTableDtoListByAppointment =  Array.isArray(appointmentListDtoByChange.calendarTableDtoList) ? appointmentListDtoByChange.calendarTableDtoList : [];

      //处理 改约分页
      let {
        current,
        pageSize,
        timeNow,
        total,
        status,
        appointmentCountAfterWorkTime,
        appointmentCountBeforeWorkTime,
      } = appointmentListDtoByChange || {}

      console.log('saveAppointmentOfPatient1 :: ',appointmentCountAfterWorkTime,appointmentCountBeforeWorkTime);

      current = current || 0,
        pageSize = pageSize || 4,
        status = status || 0,
        total = total || 0

      let pageCount = 0 //总页数

      if(total && pageSize) {
        pageCount = Math.ceil(total / pageSize)
      }
      let appointmentListDtoByChangePageInfo = {
        current,
        pageSize,
        status,
        total,
        pageCount
      }

      // 处理改约列表 事件内容
      appointmentInfoEventDtoListByAppointment = appointmentInfoEventDtoListByAppointment.map((res,idx)=>{
        let getEvent = setEventDtoListItem(res);
        return {
          ...getEvent,
         /* start:`${res.start}.008`,
          end:`${res.end}.008`*/
        }
      })
      calendarTableDtoListByAppointment.map((res,idx)=>{
        res.type = 2
      })
      let appointmentListDto = { // 改约封装判空数据
        ...appointmentListDtoByChange,
        appointmentListDtoByChangePageInfo:appointmentListDtoByChangePageInfo,
        appointmentInfoEventDtoList: appointmentInfoEventDtoListByAppointment,
        calendarTableDtoList: calendarTableDtoListByAppointment
      }
      return {
        ...state,
        appointmentListDtoByChange:appointmentListDto,    // 半屏有患者显示
        appointmentPageInfo:appointmentListDtoByChangePageInfo,
        appointmentCountAfterWorkTime,
        appointmentCountBeforeWorkTime,
      }
    },

    save(state, {payload}) {
      return {
        ...state,
        ...payload
      };
    },

    cleanModel(state, {}){
      console.log('cleanModel调用了');
      return {
        appointmentInfoEventDtoList:[],        // ①预约列表展示 ,预约事件集合
        calendarTableDtoList:[],               // ①预约列表展示  , table列表椅位集合
        appointmentPageInfo:{},                //  预约列表分页信息
        appointmentListDtoByChange:{},         // [改约] 改约列表展示
        appointmentListDtoByChangePageInfo:{}, // [改约] 改约列表分页信息
        changeListDtoByChange:{},              // [改约] 现有预约列表展示
        consultationListDtoByChange:{},        // [改约] 会诊预约列表展示
        allEventDtoList:[],                    // [改约] 所有的预约事件
        MedicalResources:[],                   // 获取会诊医生
        appointmentAsTimeCalendar:[],          // 日历预约量状态查询
        doctorTimeCalendarPageInfo:{},         // [医生使用] 预约组件分页信息
        TimeCalendarQuery:{},                  // 获取地址栏参数
        onTimeBlankClickByFull:null,           // 当前在全屏选中的选中区域
        resourceListAll:[],                    // [月模式] 全部医生
        AgendaHeadType:'1',                    //
        appointmentCountAfterWorkTime:null,
        appointmentCountBeforeWorkTime:null,
      };
    }

  },

  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, query,search }) => {
        dispatch({type:'save',payload:{TimeCalendarQuery:query}});
      })
    }
  }
}
