import {
    getIsLockProblem,           // 问题是否锁定-锁定
    getProblemData,             // 获取保存数据以及字典数据
    onSaveProblem,              // 保存问题 
    onCancelEditProblem,        // 取消编辑问题，释放锁 
    getAllImageByEmrId,         // 获取患者全部影像资料   
    getFindImageClasses,        // 查询影像检查分类
    getRelationImg,             // 关联上传保存影像 
    getCancelRelationImg,       // 取消关联影像
    getProblemRecord,           // 获取操作记录口腔问题 

    getTreatSubjectDict,        // 治疗方式 获取学科字典
    getTreatModeList,           // 根据学科code，关键字获取治疗
    getTreatModeDetails,        // 获取治疗方案详情 

    getTreatPlanListByGroupId,  // 根据方案组id获取治疗方案集合
    getCheckedLikePlan,         // 更新心仪方案
    getTreatPlanDetailsById,    // 根据治疗方案id获取治疗方案详情
    getIsLookTreatPlan,         // 治疗方案添加锁 
    getCancelTreatPlan,         // 治疗方案解除锁 
    getSaveTreatPlan,           // 保存治疗方案模板
    getTreatPlanExportPreview,  // 导出方案前预览获取数据
    getTreatPlanExport,         // 导出选择方案PDF
    getTreatPlanRecord,         // 获取患者治疗方案记录


    getUsetTokenBykey,              // 其它系统进入页面获取token信息

    getHistoryListByPatientId,      // 通过用户id获取缴费历史记录
    DownSettlementListExcel,        // 缴费历史导出excel
    getSettlementDetail,            // 查询结算单详情
    DownSettlementDetailsExcel,     // 结算单详情导出excel
} from '@/services/Social';

import { setUserInfo } from '@/utils/authority';

import { message } from 'antd';
export default {
    namespace: 'CustomerPlan',

    state: {

    },

    effects: {
        // 问题是否锁定-锁定
        *getIsLockProblem({ payload }, { call }) {
            const res = yield call(getIsLockProblem, payload);
            return res;
        },
        // 获取保存数据以及字典数据
        *getProblemData({ payload }, { call }) {
            const res = yield call(getProblemData, payload);
            return res;
        },
        // 保存问题 
        *onSaveProblem({ payload }, { call }) {
            const res = yield call(onSaveProblem, payload);
            return res;
        },
        // 取消编辑问题，释放锁 
        *onCancelEditProblem({ payload }, { call }) {
            const res = yield call(onCancelEditProblem, payload);
            return res;
        },
        // 获取患者全部影像资料
        *getAllImageByEmrId({ payload }, { call }) {
            const res = yield call(getAllImageByEmrId, payload);
            return res;
        },
        // 查询影像检查分类
        *getFindImageClasses({ payload }, { call }) {
            const res = yield call(getFindImageClasses, payload);
            return res;
        },
        // 关联上传保存影像
        *getRelationImg({ payload }, { call }) {
            const res = yield call(getRelationImg, payload);
            return res;
        },
        // 取消关联影像
        *getCancelRelationImg({ payload }, { call }) {
            const res = yield call(getCancelRelationImg, payload);
            return res;
        },
        // 获取操作记录口腔问题 
        *getProblemRecord({ payload }, { call }) {
            const res = yield call(getProblemRecord, payload);
            return res;
        },


        // 治疗方式 获取学科字典
        *getTreatSubjectDict({ payload }, { call }) {
            const res = yield call(getTreatSubjectDict, payload);
            return res;
        },
        // 根据学科code，关键字获取治疗
        *getTreatModeList({ payload }, { call }) {
            const res = yield call(getTreatModeList, payload);
            return res;
        },
        // 获取治疗方案详情
        *getTreatModeDetails({ payload }, { call }) {
            const res = yield call(getTreatModeDetails, payload);
            return res;
        },
        // 根据方案组id获取治疗方案集合
        *getTreatPlanListByGroupId({ payload }, { call }) {
            const res = yield call(getTreatPlanListByGroupId, payload);
            return res;
        },
        // 更新心仪方案
        *getCheckedLikePlan({ payload }, { call }) {
            const res = yield call(getCheckedLikePlan, payload);
            return res;
        },
        // 根据治疗方案id获取治疗方案详情
        *getTreatPlanDetailsById({ payload }, { call }) {
            const res = yield call(getTreatPlanDetailsById, payload);
            return res;
        },
        // 治疗方案添加锁  
        *getIsLookTreatPlan({ payload }, { call }) {
            const res = yield call(getIsLookTreatPlan, payload);
            return res;
        },
        // 治疗方案解除锁
        *getCancelTreatPlan({ payload }, { call }) {
            const res = yield call(getCancelTreatPlan, payload);
            return res;
        },
        // 保存治疗方案模板
        *getSaveTreatPlan({ payload }, { call }) {
            const res = yield call(getSaveTreatPlan, payload);
            return res;
        },
        // 导出方案前预览获取数据
        *getTreatPlanExportPreview({ payload }, { call }) {
            const res = yield call(getTreatPlanExportPreview, payload);
            return res;
        },
        // 导出选择方案PDF
        *getTreatPlanExport({ payload }, { call }) {
            const res = yield call(getTreatPlanExport, payload);
            return res;
        },
        // 获取患者治疗方案记录
        *getTreatPlanRecord({ payload }, { call }) {
            const res = yield call(getTreatPlanRecord, payload);
            return res;
        },
        // 其它系统进入页面获取token信息
        *getUsetTokenBykey({ payload }, { call }) {
            const res = yield call(getUsetTokenBykey, payload);
            localStorage.setItem('tenantId', res.content.tenantId);  //  存租户ID
            localStorage.setItem('access_token', res.content.token);  //  存access_token
            localStorage.setItem('token_username', res.content.phone)  // 存用户名

            localStorage.setItem('UidKey', '滥竽充数');               // 三方登录必须拥有，

            setUserInfo(res.content)
            return true
        },

       
        // 通过用户id获取缴费历史记录
        *getHistoryListByPatientId({ payload }, { call }) {
            const res = yield call(getHistoryListByPatientId, payload);
            return res;
        },
        // 缴费历史导出excel
        *DownSettlementListExcel({ payload }, { call }) {
            const res = yield call(DownSettlementListExcel, payload);
            return res;
        },
        // 查询结算单详情
        *getSettlementDetail({ payload }, { call }) {
            const res = yield call(getSettlementDetail, payload);
            return res;
        },
        // 结算单详情导出excel
        *DownSettlementDetailsExcel({ payload }, { call }) {
            const res = yield call(DownSettlementDetailsExcel, payload);
            return res;
        },
    },

    reducers: {
        save(state, { payload }) {
            return {
                ...state,
                ...payload
            };
        },
    }
};
