version: "3.5"

services:
  ant-design-pro_build:
    build: ../
    container_name: "ant-design-pro_build"
    volumes:
      - dist:/usr/src/app/dist

  ant-design-pro_web:
    image: nginx
    ports:
      - 80:80
    container_name: "ant-design-pro_web"
    restart: unless-stopped
    volumes:
      - dist:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/conf.d/default.conf

volumes:
  dist:
