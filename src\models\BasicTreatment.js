import {findTreatClass,saveContentClass,findTreatsByClass,deleteTreat,saveTreat,getTreatClasses,findTreatClassSearch,deleteContentClass,updateContentClass} from '@/services/BasicTreatment';
import {notification} from "antd";
const BasicTreatmentModel = {
  namespace: 'BasicTreatments',
  state: {
    BasicTreatmentData:{},//获取基础治疗分组列表
    saveContentClassData:{},//新建文件夹
    findTreatsByClassData:{},//获取基础治疗列表
    deleteTreatData:{},//删除基础治疗
    saveTreatData:{},//新增/编辑基础治疗
    getTreatClassesData:{},//获取当前分类和上级分类
    findTreatClassSearchData:{},//基础治疗分组列表搜索
    deleteContentClassData:{},//删除文件夹
    updateContentClassData:{},//编辑文件夹
    loading:false,
    loadTip:"加载中",
  },
  //异步
  effects: {
    //获取基础治疗分组列表
    *findTreatClassService({payload, callback} , { call, put }) {
      const response = yield call(findTreatClass ,payload);
      yield put({
        type: 'findTreatInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    ////新建文件夹
    *saveContentClassService({payload, callback} , { call, put }) {
      const response = yield call(saveContentClass ,payload);
      yield put({
        type: 'saveContentClassInfo',
        payload: response,
      });
      if (response.code === 200 ||response.code === 500) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //获取基础治疗列表
    *findTreatsByClassService({payload, callback} , { call, put }) {
      const response = yield call(findTreatsByClass ,payload);
      yield put({
        type: 'findTreatsByClassInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //删除基础治疗
    *deleteTreatService({payload, callback} , { call, put }) {
      const response = yield call(deleteTreat ,payload);
      yield put({
        type: 'deleteTreatInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //新增/编辑基础治疗
    *saveTreatService({payload, callback} , { call, put }) {
      const response = yield call(saveTreat ,payload);
      yield put({
        type: 'saveTreatInfo',
        payload: response,
      });
      if (response.code === 200 || response.code === 500) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //获取当前分类和上级分类
    *getTreatClassesService({payload, callback} , { call, put }) {
      const response = yield call(getTreatClasses ,payload);
      yield put({
        type: 'getTreatClassesInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //基础治疗分组列表搜索
    *findTreatClassSearchService({payload, callback} , { call, put }) {
      const response = yield call(findTreatClassSearch ,payload);
      yield put({
        type: 'findTreatClassSearchInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //删除文件夹
    *deleteContentClassService({payload, callback} , { call, put }) {
      const response = yield call(deleteContentClass ,payload);
      yield put({
        type: 'deleteContentClassInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //编辑文件夹
    *updateContentClassService({payload, callback} , { call, put }) {
      const response = yield call(updateContentClass ,payload);
      yield put({
        type: 'updateContentClassClassInfo',
        payload: response,
      });
      if (response.code === 200 ||response.code === 500) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
  },

  //同步
  reducers: {
    updateContentClassClassInfo(state, action) {
      return {
        ...state,
        updateContentClassData: action.payload || {},
      };
    },
    deleteContentClassInfo(state, action) {
      return {
        ...state,
        deleteContentClassData: action.payload || {},
      };
    },
    findTreatClassSearchInfo(state, action) {
      return {
        ...state,
        findTreatClassSearchData: action.payload || {},
      };
    },
    getTreatClassesInfo(state, action) {
      return {
        ...state,
        getTreatClassesData: action.payload || {},
      };
    },
    saveTreatInfo(state, action) {
      return {
        ...state,
        saveTreatData: action.payload || {},
      };
    },
    deleteTreatInfo(state, action) {
      return {
        ...state,
        deleteTreatData: action.payload || {},
      };
    },
    findTreatsByClassInfo(state, action) {
      return {
        ...state,
        findTreatsByClassData: action.payload || {},
      };
    },
    saveContentClassInfo(state, action) {
      return {
        ...state,
        saveContentClassData: action.payload || {},
      };
    },
    findTreatInfo(state, action) {
      return {
        ...state,
        BasicTreatmentData: action.payload || {},
      };
    },
  },
};
export default BasicTreatmentModel;
