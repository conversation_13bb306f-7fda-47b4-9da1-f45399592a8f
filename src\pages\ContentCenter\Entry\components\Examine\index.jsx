import {Button, Form, Input, message, Modal, Pagination, Popconfirm, Space, Table} from 'antd';
import React, { Component } from 'react';
import { GridContent } from '@ant-design/pro-layout';
import styles from './style.less';//引入样式
import {connect} from "dva";
import {StringUtils} from "@/utils/StringUtils";//公共验证
const { TextArea } = Input;


class MainSuit extends Component {
  constructor(props) {
    super(props);
    this.state = {
      mainSuitStatus:false,//打开新增编辑弹窗状态
      params:{
        tenantId: localStorage.getItem('tenantId'),//平台标识
        wordType:5,//字典表枚举 5检查
        pageNum:1,//分页
        pageSize:10,//分页大小
      },
      EntryList:[],//词条列表
      loading:false,//表格改变数据loading
      EntryMsg:{},//单条数据详情
      totals:1,//列表数量
      editorLoading:false,//新增/编辑确定loading
      saveBtnLoading:false//删除气泡确定loading
    };
  }
  //初始化
  componentDidMount() {
    this.getEntryList()//获取词条列表
  }
  //获取词条列表
  getEntryList=()=>{
    const {params}=this.state;
    this.setState({
      loading:true
    })
    const {dispatch} = this.props
    if (dispatch) {
      dispatch({
        type: 'entryModel/EntryListService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              totals:res.total,
              EntryList:res.rows,
              loading:false
            })
          }else{
            this.setState({
              loading:false
            })
          }
        }
      });
    }
  }
  // 打开新增编辑弹窗
  openAdd=(key,record)=>{
    if(key==1){
      this.setState({
        mainSuitStatus:true,
        EntryMsg:{}
      })
    }
    if(key==2){
      this.setState({
        mainSuitStatus:true,
        EntryMsg:record
      })
    }

  }
  // 新增编辑保存
  handlemainSuit=()=>{
    const {EntryMsg}=this.state;
    let param;
    if(EntryMsg.id){
      param={
        tenantId: localStorage.getItem('tenantId'),//平台标识
        wordContent:EntryMsg.wordContent?StringUtils.trimToEmpty(EntryMsg.wordContent):"",//词条内容
        wordType: 5,//检查
        id: EntryMsg.id?EntryMsg.id:"",//词条标识,
        updateName:localStorage.getItem('userName'),
        updateId:localStorage.getItem('userId')
      }
    }else{
      param={
        tenantId: localStorage.getItem('tenantId'),//平台标识
        wordContent:EntryMsg.wordContent?StringUtils.trimToEmpty(EntryMsg.wordContent):"",//词条内容
        wordType: 5,//检查
        id: EntryMsg.id?EntryMsg.id:"",//词条标识,
        createName:localStorage.getItem('userName'),
        createId:localStorage.getItem('userId'),
      }
    }
    if(!param.wordContent){
      message.warning({
        content: '请填写检查',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if(param.wordContent.length>100){
      message.warning({
        content: '检查最大长度为100',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    this.setState({
      editorLoading:true
    })
    const {dispatch} = this.props
    if (dispatch) {
      dispatch({
        type: 'entryModel/saveWordsService',
        payload: param,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              mainSuitStatus:false,
              editorLoading:false,
              EntryMsg:{}
            })
            this.getEntryList();
          }else{
            message.error({
              content: '操作失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              editorLoading:false
            })
          }
        }
      });
    }
  }
  // 新增编辑取消
  handlemainSuitCancel=()=>{
    this.setState({
      editorLoading:false,
      mainSuitStatus:false,
      EntryMsg:{}
    })
    this.getEntryList()
  }
  // 是否删除气泡
  sureDelete=(record)=>{
    const {dispatch} = this.props
    let param={
      id:record.id,
      tenantId: localStorage.getItem('tenantId'),//平台标识
    }
    this.setState({
      saveBtnLoading:true
    })
    if (dispatch) {
      dispatch({
        type: 'entryModel/deleteWordService',
        payload: param,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '删除成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              saveBtnLoading:false
            })
            this.getEntryList();
          }else{
            message.error({
              content: '删除失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              saveBtnLoading:false
            })
          }
        }
      });
    }
  }
  // 是否删除气泡取消按钮
  noDelete=()=>{
    this.setState({
      saveBtnLoading:false
    })
  }
  //分页数据
  pageNumberOnChange = (pageNum,pageSize) => {
    this.state.params.pageNum=pageNum;
    this.state.params.pageSize=pageSize;
    this.getEntryList()
  };
  render() {
    const {mainSuitStatus,EntryList,loading,EntryMsg,params,totals,editorLoading,saveBtnLoading}=this.state;
    //表格标题
    const columns = [
      {
        title: '检查',
        width:'85%',
        dataIndex: 'wordContent',
        key: 'wordContent',
        render: (text) => <span>{text}</span>,
      },
      {
        title: '操作',
        width:'15%',
        key: 'action',
        render: (text, record) => (
          <Space size="middle">
            <a onClick={()=>{this.openAdd(2,record)}}>编辑</a>
            <Popconfirm
              title="确定删除?"
              okButtonProps={{ loading: saveBtnLoading }}
              onConfirm={()=>{this.sureDelete(record)}}
              onCancel={()=>{this.noDelete}}
              okText="是"
              cancelText="否"
            >
              <a>删除</a>
            </Popconfirm>
          </Space>
        ),
      },
    ];

    return (
      <GridContent>
        <Button type="primary"  onClick={()=>{this.openAdd(1)}} className={styles.btnstyle}>新增</Button>
        <Table
          rowKey={EntryList => EntryList.id}
          columns={columns}
          dataSource={EntryList}
          loading={loading}
          pagination={false}
        />
        <Pagination
          style={{ float: 'right',marginTop:16 }}
          total={totals}
          showTotal={(totals) => `共 ${totals} 条记录`}
          defaultPageSize={params.pageSize}
          defaultCurrent={params.pageNum}
          onChange={(pageNum,pageSize) => this.pageNumberOnChange(pageNum,pageSize)}
        />
        <Modal
          title="新增/编辑检查"
          visible={mainSuitStatus}
          destroyOnClose={true}
          onOk={this.handlemainSuit}
          onCancel={this.handlemainSuitCancel}
          okText="保存"
          cancelText="取消"
          width={600}
          maskClosable={false}
          confirmLoading={editorLoading}
        >
          <Form
            name="basic"
            labelCol={{
              span: 3,
            }}
            wrapperCol={{
              span: 21,
            }}
            initialValues={{
              remember: true,
              wordContent:EntryMsg.wordContent
            }}
            onFinish={this.onFinish}
            autoComplete="off"
          >
            <Form.Item
              label="检查"
              name="wordContent"
              rules={[
                {
                  required: true,
                  message: '请输入检查内容',
                },{
                  max:100,
                  message: '最多可输入100个文字'
                }
              ]}
            >
              <TextArea
                // showCount
                // maxLength={100}
                autoSize={{ minRows: 1,maxRows:10}}
                onChange={e=>{EntryMsg.wordContent=e.target.value}}
                placeholder="请输入检查内容"/>
            </Form.Item>
          </Form>
        </Modal>
      </GridContent>
    );
  }
}

export default connect(({ EntryData, }) => ({
  EntryData
}))(MainSuit);
