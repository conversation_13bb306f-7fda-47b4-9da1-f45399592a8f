@import '~antd/lib/style/themes/default.less';

@pro-header-hover-bg: rgba(0, 0, 0, 0.025);

.header {
  height: 50px;
  line-height: 48px;
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: relative;
}
.scrollDrop{
  :global{
    .ant-dropdown{
      top: 48px !important;
    }
  }
}
.logo {
  height: 50px;
  line-height: 42px;
  vertical-align: top;
  display: inline-block;
  padding: 0 0 0 24px;
  cursor: pointer;
  font-size: 20px;
  img {
    display: inline-block;
    vertical-align: middle;
  }
}

.menu {
  position:relative;
  :global(.anticon) {
    margin-right: 8px;
  }
  :global(.ant-dropdown-menu-item) {
    width: 168px;
    cursor: pointer;
  }
  .AccountArrow{
    position: absolute;
    width:0;
    height:0;
    right:20px;
    top:-10px;
    border-width:0 10px 10px;
    border-style:solid;
    border-color:transparent transparent #fff;
  }
}
.switchclinic{
  cursor: pointer;
  font-size: 16px;
  color: #17233d;
}
i.trigger {
  font-size: 20px;
  height: 64px;
  cursor: pointer;
  transition: all 0.3s, padding 0s;
  //padding: 22px px;
  padding:0 10px 0 20px;
  &:hover {
    background: @pro-header-hover-bg;
  }
}

.right {
  display: flex;
  float: right;
  height: 100%;
  overflow: hidden;
  .action {
    cursor: pointer;
    padding: 0px 0px 0px 8px;
    display: inline-block;
    transition: all 0.3s;
    height: 100%;
    > i {
      vertical-align: middle;
      color: @text-color;
    }
    &:hover {
      background: #D8E7F5;
    }
    :global(&.ant-popover-open) {
      background: @pro-header-hover-bg;
    }
  }
  .search {
    padding: 0 12px;
    &:hover {
      background: transparent;
    }
  }
  .account {
    .avatar {
      margin: 12px 8px 20px 0;
      color: @primary-color;
      background: rgba(255, 255, 255, 0.85);
      vertical-align: top;
      border:1px solid #444;
    }
  }
}

.dark {
  height: 50px;
  .action {
    color: rgba(255, 255, 255, 0.85);
    > i {
      color: rgba(255, 255, 255, 0.85);
    }
    &:hover,
    &:global(.ant-popover-open) {
      background: @primary-color;
    }
    :global(.ant-badge) {
      color: rgba(255, 255, 255, 0.85);
    }
  }
}

.cityList{
  width: 160px;
  background: #E2E1E6;
  max-height: 674px;
  min-height: 216px;
  &>div>div{
    padding: 10px 0px;
    text-align: center;
    cursor: pointer;
  }
  &>div>div:hover{
    color: #176CE0;
  }
  &>h4{
    margin: 0px 20px 5px 20px;
    cursor: pointer;
    padding: 10px 0px 10px 25px;
    background: url('@/assets/user/exchange.png') no-repeat left center;
    border-bottom: 1px solid #BBBBBB;
  }
  &>h4:hover{
    background: url('@/assets/user/changex.png') no-repeat left center;
  }
}
.organNameList{
  width: 250px;
  background: #FFFFFF;
  max-height: 674px;
  min-height: 216px;
  &>div>div{
    padding: 10px 20px;
    text-align: left;
    cursor: pointer;
  }
  &>div>div:hover{
    color: #176CE0
  }
  &>h4{
    margin: 0px 20px 5px 20px;
    padding: 10px 0px;
    border-bottom: 1px solid #BBBBBB;
  }
}

@media only screen and (max-width: @screen-md) {
  .header {
    :global(.ant-divider-vertical) {
      vertical-align: unset;
    }
    .name {
      display: none;
    }
    i.trigger {
      padding: 22px 12px;
    }
    .logo {
      padding-left: 12px;
      padding-right: 12px;
      position: relative;
    }
    .right {
      position: absolute;
      right: 12px;
      top: 0;
      background: #fff;
      .account {
        .avatar {
          margin-right: 0;
          border:1px solid #444;
        }
      }
    }
  }
}

.tooltipClass{
  :global{
    .ant-tooltip-inner{
      background: #FFFFFF;
      color: #333333;
    }
    .ant-tooltip-arrow::before{
      background: #FFF2E5;
    }
  }
}
