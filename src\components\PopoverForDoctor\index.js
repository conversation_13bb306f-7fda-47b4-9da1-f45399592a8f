import React, {Component} from 'react';
import {Card,Radio,Calendar,Popover,Spin,message} from 'antd';
import $ from 'jquery';
import moment from 'moment'
import styles from './PopoverForCalendar.less'
import classNames from 'classnames';
import PropTypes from 'prop-types';
import zhCN from 'antd/lib/calendar/locale/zh_CN';
import { connect } from 'dva'
import { getOrganizationInfo } from '@/utils/utils';
import TagSelect from '@/components/TagSelect';
import 'moment/locale/zh-cn'
moment.locale('zh-cn')



@connect(({ TimeCalendar, loading }) => ({
  TimeCalendar,
  loading
}))
export default class PopoverForDoctor extends Component {

  static propTypes = {
    onSelect:PropTypes.func,
  };

  static defaultProps = {
    onSelect:()=>{},
  };

  constructor(props) {
    super(props);
    this.state = {
      visible:false,
      values:[]
    }
  }

  visibleTrigger=()=>{
    this.setState({
      visible:!this.state.visible
    })
  }

  /**
   * 打开悬窗回调
   */
  onVisibleChange=(visible)=>{
    this.setState({
      visible:visible
    },()=>{
      if(visible){
        this.refreshDoctorList && this.refreshDoctorList();
      }
    })
  }

  refreshDoctorList=()=>{
    const { dispatch } = this.props
    dispatch({
      type:'TimeCalendar/getResourceListAll',
      payload:{}
    }).then((response)=>{
      if(response && response.code == 200 && Array.isArray(response.content) && response.content.length != 0){
        if(this.state.values.length == 0) {
          this.setState({
            values:[response.content[0].resourceId]
          },()=>{
            this.props.onSelect(response.content[0])
          })
        }
      }
    })
  }

  onChangeTagSelect=(value)=>{
    let differenceAddSet = [...value].filter(x => !new Set([...this.state.values]).has(x));
    const { TimeCalendar } = this.props;
    const { resourceListAll } = TimeCalendar;

    /**
     adeptSubjectDtoList: null
     chairNumList: null
     coreDoctorMark: null
     flag: null
     photoUrl: null
     rejectClassifyIdList: null
     rejectContractIdList: null
     rejectCureIdList: null
     resourceId: 21
     resourceName: "岳丽丽医生测试"
     status: null
     usableType: null
     workStatus: null
     */

    if(differenceAddSet.length != 0) {
      let resfindValue = resourceListAll.find((res)=>{
        return res.resourceId == differenceAddSet[0]
      })
      this.setState({
        values: [...differenceAddSet]
      }, () => {
        this.props.onSelect(resfindValue)
      })
    }else {
      //this.props.onSelect(this.state.values)
    }
  }

  render() {
    const {
      children,     //传入的子组件
      TimeCalendar,
    } = this.props;
    const { resourceListAll } = TimeCalendar;

    /**
     adeptSubjectDtoList: null
     chairNumList: null
     coreDoctorMark: null
     flag: null
     photoUrl: null
     rejectClassifyIdList: null
     rejectContractIdList: null
     rejectCureIdList: null
     resourceId: 21
     resourceName: "岳丽丽医生测试"
     status: null
     usableType: null
     workStatus: null
     */

    const content = (
      <div id="doctorContent" className={styles.PopoverContentWrap}>
        <TagSelect
          hideCheckAll={true}
          value={this.state.values}
          onChange={this.onChangeTagSelect}>
          {resourceListAll.map((res,idx)=>{
            return (<TagSelect.Option key={idx} type={'1'} value={res.resourceId}>{res.resourceName}</TagSelect.Option>)
          })}
        </TagSelect>
      </div>
    )

    return (
      <Popover
        // placement="bottom"
        // arrowPointAtCenter={true}
        placement="bottomLeft"
        trigger="click"
        overlayClassName={classNames({
          [styles.PopoverCard]:true,
          'PopoverCard':true,
        })}
        content={content}
        getPopupContainer={()=>{return document.getElementById('docterSelect')}}
        autoAdjustOverflow={false}
        visible={this.state.visible}
        onVisibleChange={this.onVisibleChange}
      >
        {children}
      </Popover>
    )
  }

  setSelectDoctor=(value)=>{
    this.setState({
      values:[value]
    })

  }

  componentDidMount() {
    this.props.onRef && this.props.onRef(this)
    this.refreshDoctorList()
  }

}
