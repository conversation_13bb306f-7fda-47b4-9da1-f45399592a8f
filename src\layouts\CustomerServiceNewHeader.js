import React, { PureComponent } from 'react';
import { formatMessage } from 'umi/locale';
import { Layout, message } from 'antd';
import Animate from 'rc-animate';
import { connect } from 'dva';
import router from 'umi/router';
import GlobalHeader from '@/components/GlobalHeader/CustomerNewService/index.js';
import TopNavHeader from '@/components/TopNavHeader';
import styles from './Header.less';
import Authorized from '@/utils/Authorized';
import { routerRedux, Link } from "dva/router";
const { Header } = Layout;

class HeaderView extends PureComponent {
  state = {
    visible: true,
    visibleType: false
  };

  static getDerivedStateFromProps(props, state) {
    if (!props.autoHideHeader && !state.visible) {
      return {
        visible: true,
      };
    }
    return null;
  }

  componentDidMount() {
    document.addEventListener('scroll', this.handScroll, { passive: true });
  }

  componentWillUnmount() {
    document.removeEventListener('scroll', this.handScroll);
  }

  getHeadWidth = () => {
    const { isMobile, collapsed, setting } = this.props;
    const { fixedHeader, layout } = setting;
    if (isMobile || !fixedHeader || layout === 'topmenu') {
      return '100%';
    }
    return collapsed ? '100%' : '100%';
  };

  handleMenuClick = ({ key }) => {
    const { dispatch } = this.props;
    //console.log('userCenter :: ',key);

    if (key === 'SavePassword') {
      router.push('/customerService/SavePassword');
      return;
    }
    if (key === 'logout') {
      dispatch({
        type: 'login/logout',
      });
    }
  };


  handScroll = () => {
    const { autoHideHeader } = this.props;
    const { visible } = this.state;
    if (!autoHideHeader) {
      return;
    }
    const scrollTop = document.body.scrollTop + document.documentElement.scrollTop;
    if (!this.ticking) {
      this.ticking = true;
      requestAnimationFrame(() => {
        if (this.oldScrollTop > scrollTop) {
          this.setState({
            visible: true,
          });
        }
        if (scrollTop > 300 && visible) {
          this.setState({
            visible: false,
          });
        }
        if (scrollTop < 300 && !visible) {
          this.setState({
            visible: true,
          });
        }
        this.oldScrollTop = scrollTop;
        this.ticking = false;
      });
    }
  };

  render() {
    const { setting } = this.props;
    const { fixedHeader } = setting;
    const { visible, visibleType } = this.state;
    const width = this.getHeadWidth();

    const HeaderDom = visible ? (
      <Header style={{ padding: 0, width, height: 50, lineHeight: '50px' }} className={fixedHeader ? styles.fixedHeader : ''}>
        <GlobalHeader
          onMenuClick={this.handleMenuClick}
          visibleType={visibleType}
          {...this.props}
        />
      </Header>
    ) : null;
    return (
      <Animate component="" transitionName="fade">
        {HeaderDom}
      </Animate>
    );
  }
}

export default connect(({ user, global, setting, loading, homeIndex }) => ({
  organizationList: user.OrganizationList,
  currentUser: user.currentUser,
  currentUserList: user.currentUserList,
  collapsed: global.collapsed,
  fetchingNotices: loading.effects['user/getMessageList'],
  notices: global.notices,
  setting,
  homeIndex
}))(HeaderView);
