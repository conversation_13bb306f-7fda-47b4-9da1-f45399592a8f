
import {
    getSystemUpdateRecord,  // 获取更新记录列表
    getUpdateIsRead,        // 告知更新系统后用户第一次登陆记录
    getSystemPDFlist,       // 获取系统更新手册
    getViewOperatingPDF,    // 获取pdf路径
    getDownOperatingPDF,    // 下载pdf
} from '@/services/user';
export default {
    namespace: 'updateInformation',

    state: {
        obj: {}
    },

    effects: {
        // 获取更新记录列表
        *getSystemUpdateRecord({ payload }, { call }) {
            const res = yield call(getSystemUpdateRecord, payload);
            return res;
        },
        // 告知更新系统后用户第一次登陆记录
        *getUpdateIsRead({ payload }, { call }) {
            const res = yield call(getUpdateIsRead, payload);
            return res;
        },
        // 获取系统更新手册
        *getSystemPDFlist({ payload }, { call }) {
            const res = yield call(getSystemPDFlist, payload);
            return res;
        },
        // 获取pdf路径
        *getViewOperatingPDF({ payload }, { call }) {
            const res = yield call(getViewOperatingPDF, payload);
            return res;
        },
        // 下载pdf
        *getDownOperatingPDF({ payload }, { call }) {
            const res = yield call(getDownOperatingPDF, payload);
            return res;
        }
    },

    reducers: {
        save(state) {
            return {
                ...state,
            };
        },
    }
};
