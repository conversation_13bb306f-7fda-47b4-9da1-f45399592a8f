import {
  getCurrentTaskSummary,
  taskPermissionList,
  majorAppointmentCustomer,
  getComplainCount,
  complainBaseList,
  getCustomerConfigByStaffJustAuth,
  taskHomeStatistics,
  getOftenTelFromCustomerEs,
  sendMsg
} from '@/services/newHomeIndex';


export default {
  namespace: 'newHomeIndex',

  state: {
    getMyTaskLists: false, // web推送调用任务列表 true 刷新 false 不刷新
    newHomeTabKey: 1, // 跳转详情页返回后的页面
    newHomeTaskType: null, // 任务类型选中的值
    showComplaintModal: false, // 是否打开投诉弹框
  },

  effects: {
    // 任务列表
    *getCurrentTaskSummary({ payload }, { call }) {
      const res = yield call(getCurrentTaskSummary, payload);
      return res;
    },

    // 根据用户ID 查询本机构的权限
    *taskPermissionList({ payload }, { call }) {
      const res = yield call(taskPermissionList, payload);
      return res;
    },

    // 重点预约客户
    *majorAppointmentCustomer({ payload }, { call }) {
      const res = yield call(majorAppointmentCustomer, payload);
      return res;
    },

    // 获取投诉任务条数
    *getComplainCount({ payload }, { call }) {
      const res = yield call(getComplainCount, payload);
      return res;
    },

    // 获取投诉任务条数
    *complainBaseList({ payload }, { call }) {
      const res = yield call(complainBaseList, payload);
      return res;
    },

    // 查询用户在该诊所的任务操作权限
    *getCustomerConfigByStaffJustAuth({ payload }, { call }) {
      const res = yield call(getCustomerConfigByStaffJustAuth, payload);
      return res;
    },

    // 获取流水
    *taskHomeStatistics({ payload }, { call }) {
      const res = yield call(taskHomeStatistics, payload);
      return res;
    },

    // 根据租户ID以及用户ID查询客户手机号并锁定操作
    *getOftenTelFromCustomerEs({ payload }, { call }) {
      const res = yield call(getOftenTelFromCustomerEs, payload);
      return res;
    },

    // 生日祝福任务发送短信
    *sendMsg({ payload }, { call }) {
      const res = yield call(sendMsg, payload);
      return res;
    },
  },

  reducers: {
    // 更新状态值数据
    setTaskListState(state, { payload }) {
      return {
        ...state,
        ...payload
      }
    },

    // 清空数据
    clean(state, { payload }){
      return {
        ...state,
        newHomeTabKey: 1, // 跳转详情页返回后的页面
        newHomeTaskType: null, // 任务类型选中的值
        showComplaintModal: false, // 是否打开投诉弹框
      }
    }
  },
  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, search }) => {
        if (!((/\/consult/.test(pathname)) || (/\/subscribe/.test(pathname)) || (/\/doctorworkbenchsimplify/.test(pathname)) || (/\/newHomeIndex/.test(pathname)) || (/\/settlement/.test(pathname)) || (/\/customerfollow/.test(pathname)))) {
          dispatch({
            type: "clean",
            payload: {}
          })
        }
      })
    }
  }
};
