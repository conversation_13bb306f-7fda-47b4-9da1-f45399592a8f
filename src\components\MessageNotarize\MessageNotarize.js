
import React , { Component  } from  'react'
import PropTypes from 'prop-types';
import { Modal } from 'antd';
import {connect} from "dva";
@connect(({ loading, cancels }) => ({
  cancels,
  loading
}))

class MessageNotarize extends Component{


  static defaultProps = {
    MessageNotarizeType:false,
    MessageNotarizeFunc:()=>{},
    MessageArr:[],
  }

  static propTypes={
    MessageNotarizeType:PropTypes.bool,
    MessageArr:PropTypes.array,
  }

  constructor(props) {
    super(props);
    this.state = {

    }
  }

  handleOk = () => {
    this.props.MessageNotarizeFunc&& this.props.MessageNotarizeFunc(1)
  };

  handleCancel = () => {
    this.props.MessageNotarizeFunc&& this.props.MessageNotarizeFunc(0)
  };


  render() {
    const { MessageNotarizeType ,MessageArr} = this.props;
    const appointmentShortMsgSend = this.props.loading.effects['cancels/appointmentShortMsgSend'];
    return (
      <>
        <Modal
          title="提醒"
          visible={MessageNotarizeType}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          okText='是'
          cancelText='否'
          confirmLoading={!!appointmentShortMsgSend}
        >
          {
            Array.isArray(MessageArr) && MessageArr.map((item,index)=>{
              return <p key={index}>{index!=0?"；":""}{item.appointmentDate}</p>
            })
          }
          预约确认信息已经发送过，是否再次发送？
        </Modal>
      </>
    );
  }

}
export default MessageNotarize
