# 十字牙位组件 

## 参数

 * ToothInfo : 牙位格式信息 区分左上,右上,左下,右下 包含牙位`index`和牙面信息`side`
 * ToothBefore: 展示在牙位图前面的治疗名称 类型String
 * ToothAfter: 展示在牙位图后面的信息 类型String
 
## 方法

* onClickToothBit: 支持整个组件点击事件 并携带牙位信息 

## 实例

```
 <ToothBit
              ToothBefore="根管治疗"
              ToothInfo={{
                topLeft:[
                  {index:1, side:['M','O','C']},
                ],
                topRight:[],
                tottomLeft:[],
                tottomRight:[
                  {index:3, side:['M','C']},
                  {index:5, side:['M']}
                ],
              }}
              onClickToothBit={()=>{}}
            />

```

