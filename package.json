{"name": "ant-design-pro", "version": "4.1.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "umi build", "deploy": "npm run site && npm run gh-pages", "dev": "npm run start:dev", "fetch:blocks": "pro fetch-blocks && npm run prettier", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "umi g tmp", "lint": "umi g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check \"**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "prettier": "prettier -c --write \"**/*\"", "start": "umi dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none umi dev", "start:no-mock": "cross-env MOCK=none umi dev", "start:no-ui": "cross-env UMI_UI=none umi dev", "start:pre": "cross-env REACT_APP_ENV=pre umi dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none umi dev", "pretest": "node ./tests/beforeTest", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components", "tsc": "tsc"}, "homepage": "", "husky": {"hooks": {"pre-commit": "npm run lint-staged"}}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.0.0", "@ant-design/pro-form": "2.2.2", "@ant-design/pro-layout": "^6.0.0", "@ant-design/pro-table": "^2.4.0", "@antv/data-set": "^0.11.0", "@antv/l7": "^2.2.38", "@antv/l7-maps": "^2.2.38", "@antv/l7-react": "^2.1.9", "@types/lodash.debounce": "^4.0.6", "@types/lodash.isequal": "^4.5.5", "@types/numeral": "^0.0.28", "antd": "4.21.4", "base-64": "^1.0.0", "bizcharts": "^3.5.3-beta.0", "bizcharts-plugin-slider": "^2.1.1-beta.1", "classnames": "^2.2.6", "compression-webpack-plugin": "^5.0.1", "crypto-js": "^3.3.0", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "dva": "^2.4.0", "gg-editor": "^2.0.2", "html-to-draftjs": "^1.5.0", "lodash": "^4.17.11", "lodash-decorators": "^6.0.0", "lodash.debounce": "^4.0.8", "lodash.isequal": "^4.5.0", "mockjs": "^1.0.1-beta3", "moment": "^2.25.3", "numeral": "^2.0.6", "nzh": "^1.0.3", "omit.js": "^1.0.2", "path-to-regexp": "2.4.0", "prop-types": "^15.5.10", "qs": "^6.9.0", "react": "^17.0.0", "react-beautiful-dnd": "^13.0.0", "react-dom": "^16.8.6", "react-draft-wysiwyg": "^1.14.7", "react-fittext": "^1.0.0", "react-helmet-async": "^1.0.4", "react-loadable": "^5.5.0", "react-router": "^4.3.1", "react-router-dom": "^5.2.0", "react-umeditor": "^1.0.15", "react-webworker": "^2.1.0", "reqwest": "^2.0.5", "umi": "^3.2.0", "umi-request": "^1.0.8", "umi-webpack-bundle-analyzer": "^4.4.2", "url-loader": "^4.1.1", "use-merge-value": "^1.0.1", "webpack-dev-server": "^4.7.4", "ws": "8.9.0", "@sentry/react": "^7.57.0", "@sentry/tracing": "^7.57.0"}, "devDependencies": {"@ant-design/pro-cli": "^1.0.18", "@babel/core": "^7.17.5", "@babel/preset-env": "^7.16.11", "@babel/preset-react": "^7.16.7", "@types/classnames": "^2.2.7", "@types/express": "^4.17.0", "@types/history": "^4.7.2", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.144", "@types/qs": "^6.5.3", "@types/react": "^16.9.17", "@types/react-dom": "^16.8.4", "@types/react-helmet": "^5.0.13", "@umijs/fabric": "^2.2.0", "@umijs/plugin-blocks": "^2.0.5", "@umijs/preset-ant-design-pro": "^1.2.0", "@umijs/preset-react": "^1.4.8", "@umijs/preset-ui": "^2.0.9", "babel-loader": "^8.2.3", "carlo": "^0.9.46", "chalk": "^4.0.0", "cross-env": "^7.0.3", "cross-port-killer": "^1.1.1", "detect-installer": "^1.0.1", "enzyme": "^3.11.0", "eslint": "^7.1.0", "express": "^4.17.1", "gh-pages": "^3.0.0", "html-webpack-plugin": "^5.5.0", "husky": "^4.0.7", "jsdom-global": "^3.0.2", "lint-staged": "^10.0.0", "mockjs": "^1.0.1-beta3", "page-lifecycle": "^0.1.2", "prettier": "^2.0.1", "pro-download": "1.0.1", "puppeteer-core": "^4.0.1", "stylelint": "^13.0.0", "webpack": "^5.70.0", "webpack-cli": "^4.9.2"}, "engines": {"node": ">=10.0.0"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"], "__npminstall_done": false, "main": ".eslintrc.js", "directories": {"test": "tests"}, "repository": {"type": "git", "url": "https://git.navi-tek.com/liujh/arrail_mv_web.git"}, "keywords": [], "author": "", "license": "ISC"}