import {
  additionPersonalPatient,
  appointmentColumn,
  appointmentIconInfo,
  appointmentInfoCard,
  appointmentReschedule,
  dropAppointment,
  getAddRecommend,
  getappointmentBuild,
  getappointmentCacheShow,
  getappointmentTag,
  getcomplaintDtoCacheSave,
  getComplaintTable,
  getDoctorFilter,
  getInquireRecommend,
  getinsuranceDtoCacheSave,
  getpatientInfoOfAppointment,
  getPatients,
  getPatientScree,
  getReferenceCacheSave,
  getWelfare,
  getworkingAsTime,
  modifyAppointmentIcon,
  otherOrganizationInfoLists,
  postChairOccupyTime,
  postOccupyTimeAllocation,
  postOccupyTimeRemove,
  remarkReplace,
  updateAppointmentAffirmt,
  visitRevision,
  queryPatientByNameAndTel,
  appointmentMarketColumn,
} from '@/services/appointmentApi';
import { openTelAppointmentOrgList } from '@/services/CustomerService'; // 老400 弃
import { routerRedux } from 'dva/router';

export default {
  namespace: 'Getscreen',
  state: {
    list: [],
    welfarelist:{},  // 福利list
    nurselist : {},
    PatientsSearchData:{}, //  半屏  添加患者搜索
    Complaintlist :{},    // 主诉
    PatientScreeData :{}, // // 左側 患者 詳細信息
    AddRecommendData:{} ,// 推荐人信息
    InquireRecommendData:{},// 查询  推荐人
    appointmentLableData:{} ,// 右侧  患者标签
    appointmentCacheShowData:{},
    getDoctorFilterData:[] ,// 根据  主诉 福利 筛选出的可以看诊的医生
    getpatientInfoOfAppointmentDta:{}, // 改约 详情展示
    TPatientScreeData:{},// 推荐人
    storageWelfareName:'', //  福利名称
    storageWelfareId:'', // 福利id
    patientBstatus:'', // //录入患者 新增患者
    resourceName:'',// 新建预约 医生名字
    orderForm:'1', // 使用福利卡的  激活单号
    condition:1,// 默认1 是预约新建
    ConsultantDoctor:"", // 会诊医生
    DoctorList:[], // 可看诊医生
    DoctorWhoDetail:{},   // 客服 可看诊医生是否可新建预约
    doctorIdOfAppointment:null,// // 改约可看诊医生 存储
    InsuranceData:0 ,// 商报  从新加载  状态   0 的时候  不更新
    doctorIndex:0, // 定位 可看诊 医生
    usableTypeDoctorList:[], // 再次筛选医生的时候不可看诊的医生
    openTelAppointmentOrgList:[],// 400使用全部机构
    storePatientId:null, // 地址栏上  最新的患者 id
    listOrganizations:{}, // 时间占用  获取机构诊所
    appointmentIconInfoDatas:{}, // 手工标签 查询  数据
    appointmentColumnLists:[] //  预约列表 数据
  },

  // 半屏页面的demo  modifyAppointmentIcon
  effects: {

    //  预约列表 数据
    * appointmentColumn({ payload }, { put, call }) {
      const response = yield call(appointmentColumn, payload);
      yield put({
        type: 'appointmentColumnDatas',
        payload : response&&response.content
      });
      return response
    },

    // 预约400列表 数据
    * appointmentMarketColumn({ payload }, { put, call }) {
      const response = yield call(appointmentMarketColumn, payload);
      return response
    },

    //  修改手工标签
    * modifyAppointmentIcon({ payload }, { put, call }) {
      const response = yield call(modifyAppointmentIcon, payload);
      return response
    },


    //  手工标签 查询  数据
    * appointmentIconInfo({ payload }, { put, call }) {
      const response = yield call(appointmentIconInfo, payload);
      if (response&&response.content === null){
        yield put({
          type: 'appointmentIconInfoData',
          payload : {
            appointmentId: 0,
            basicTreatment: 0,
            bigCustomer: 0,
            emergency: 0,
            implantSurgery: 0,
            implantTwice: 0,
            lab: 0,
            majorTreatment: 0,
            orthodontic: 0,
            platinumCard: 0,
            policyHolder: 0,
            repairCrown: 0,
            rootCanal: 0,
            teethWhitening: 0,
            vipClient: 0,
            voucher: 0,
            toothExtraction:0,          // 拔牙
            firstOrthodontics:0,        // 正畸初戴
            appInsurance:0,             // 保险
            hitSupport:0,               // 打支抗
            bankPlatinumCard:0,         // 银行白金卡
            microscopicRootCanal:0,
            childrenBound:0,
            toothWhitening:0,
            regularCustomerVisit:0,
            comfortTreatment:0,       // 舒适治疗
            crown:0,                  // 戴冠
            regularInspection:0,      // 三个月定检
          }
        });
      }else {
        yield put({
          type: 'appointmentIconInfoData',
          payload : response&&response.content
        });
      }
      return response
    },

    // 根据预约id获取预约详情
    * appointmentInfoCard({ payload }, { put, call }){
      const response = yield call(appointmentInfoCard,payload)
      return response
    },

    // 预约详情 卡片 修改备注
    * remarkReplace({ payload }, { put, call }) {
      const response = yield call(remarkReplace, payload);
      return response
    },

    // 获取机构信息
    * otherOrganizationInfoLists({ payload }, { put, call }) {
      const response = yield call(otherOrganizationInfoLists, payload);
      yield put({
        type: 'listOrganizations',
        payload : response
      });
      return response
    },

    // 删除时间占用

    *postOccupyTimeRemove({ payload }, { call }) {
      const res = yield call(postOccupyTimeRemove, payload);
      return res;
    },

    // 预约全屏 个人占用时间 保存
    *postOccupyTimeAllocation({ payload }, { call }) {
      const res = yield call(postOccupyTimeAllocation, payload);
      return res;
    },

    // 预约全屏 个人占用时间 检验
    *postChairOccupyTime({ payload }, { call }) {
      const res = yield call(postChairOccupyTime, payload);
      return res;
    },

    //到诊变更
      *visitRevision({ payload }, { call }) {
      // console.log(payload);
      const res = yield call(visitRevision, payload);
      return res;
    },



    // 福利弹窗
    *getwelfares({payload} , {put , call}){
      const welfare = yield call(getWelfare ,payload);
      yield put({
        type: 'welfarelist',
        payload : welfare
      });
    },


    // 保存状态值 到 地址栏
    *querydoctorlogin({payload}, { call, put,select }) {
      let systemType = JSON.parse(localStorage.getItem('organizationInfoJson')).systems;
      yield put(routerRedux.replace(
        {
          pathname:`/${systemType == '1' ?'subscribe' : 'registerCalendar'}/therearepatient/${payload.makeid}`,
          query: {
            activeKey:payload.activeKey?payload.activeKey:null,
            issue:payload.issue?payload.issue:null,
            ReferralId:payload.ReferralId?payload.ReferralId:null,
            GroupType:payload.GroupType?payload.GroupType:null
          }
        }));
    },

    // 弃用
    *querydoctorloginCS({payload}, { call, put,select }) {
      let organizationInfoJson = localStorage.getItem('organizationInfoJsonCS') ? JSON.parse(localStorage.getItem('organizationInfoJsonCS')) : {}
      let systemType = organizationInfoJson.systems;
      yield put(routerRedux.replace(
        {
          pathname:`/customerservice/${systemType == '1' ?'subscribe' : 'registerCalendar'}/appointment/${payload.makeid}/${organizationInfoJson.id}`,
          query: {
            activeKey:payload.activeKey?payload.activeKey:null,
            issue:payload.issue?payload.issue:null,
            ReferralId:payload.ReferralId?payload.ReferralId:null,
            GroupType:payload.GroupType?payload.GroupType:null
          }
        }));
    },
    // 半屏  添加患者搜索
    *getPatientsSearch({ payload },{ put, call }){
      const PatientsSearchText = yield call(getPatients , payload);
      yield put({
        type: 'PatientsSearchlist',
        payload : PatientsSearchText
      });
      return  PatientsSearchText
    },

    // 根据手机号和姓名查询患者
    *queryPatientByNameAndTel({ payload },{put,call}) {
      let queryPatientByNameAndTelRes = yield call(queryPatientByNameAndTel,payload)
      return queryPatientByNameAndTelRes
    },
    // 主诉弹窗
    *getComplaintTable({ payload },{ put, call }){
      const ComplaintText = yield call(getComplaintTable , payload);
      yield put({
        type: 'Complaintlist',
        payload : ComplaintText
      });
      if(ComplaintText&&ComplaintText.code === 200 && ComplaintText&&ComplaintText.content){
        return ComplaintText.content;
      }else {
        return null
      }
    },


    // 左側 患者詳細信息
    *getPatientScree({ payload },{ put, call }){
      const PatientScreeText = yield call(getPatientScree , payload);
      if (PatientScreeText && PatientScreeText.code == 200 && PatientScreeText.content) {
        yield put({
          type: 'PatientScreelist',
          payload : PatientScreeText
        });
      }
      return PatientScreeText
    },

    // 左側 患者詳細信息
    *getPatientScreeByWaitingList({ payload },{ put, call }){
      const PatientScreeText = yield call(getPatientScree , payload);
      return PatientScreeText
    },

    // 推荐人 患者詳細信息
    *getPatientScreeT({ payload },{ put, call }){
      const getPatientScreeTText = yield call(getPatientScree , payload);
      if (getPatientScreeTText && getPatientScreeTText.code == 200 && getPatientScreeTText.content) {
        yield put({
          type: 'getPatientScreeTlist',
          payload : getPatientScreeTText
        });
      }
      return getPatientScreeTText
    },

    // 添加  推荐人
    *getAddRecommend({ payload },{ put, call }){
      const AddRecommendText = yield call(getAddRecommend , payload);
      yield put({
        type: 'AddRecommendlist',
        payload : AddRecommendText
      });
      return AddRecommendText
    },

    // 根据患者id  查询是否有推荐人
    *getInquireRecommend({ payload },{ put, call }){
      const InquireRecommendText = yield call(getInquireRecommend , payload);
      if(InquireRecommendText.code == 200){
        yield put({
          type: 'InquireRecommendText',
          payload : InquireRecommendText
        })
      }
      return InquireRecommendText
    },

    // 右侧 获取患者标签
    *getappointmentTag({ payload },{ put, call }) {
      const appointmentLableText = yield call(getappointmentTag, payload);
      if(appointmentLableText && appointmentLableText.code == 200 && appointmentLableText.content) {
        yield put({
          type: 'appointmentLableText',
          payload: appointmentLableText
        })
      }
      return appointmentLableText
    },

    // 缓存  主诉显示的回显
    *getappointmentCacheShow({ payload },{ put, call }){
      const appointmentCacheShowText = yield call(getappointmentCacheShow , payload);
      // console.log(appointmentCacheShowText.content,'qweqeqeqweq');
      if(appointmentCacheShowText && appointmentCacheShowText.code === 200 ) {
        yield put({
          type: 'appointmentCacheShowText',
          payload: appointmentCacheShowText.content
        });
        const {welfareList }= appointmentCacheShowText.content
        localStorage.setItem('welfareList', JSON.stringify(welfareList) );
        return appointmentCacheShowText.content;
      }
      return {}
    },

    //  主诉弹窗确定存储
    *getcomplaintDtoCacheSave({ payload },{ put, call }){
      const complaintDtoCacheSaveText = yield call(getcomplaintDtoCacheSave , payload);
      /*yield put({
        type: 'getappointmentCacheShow',
        payload:payload2
      });*/
      // if(complaintDtoCacheSaveText && complaintDtoCacheSaveText.code === 200 && complaintDtoCacheSaveText.content){
      //   return complaintDtoCacheSaveText.content;
      // }else {
      //   return {}
      // }
      return complaintDtoCacheSaveText
    },

    // 福利弹窗确认存储
    *getinsuranceDtoCacheSave({ payload },{ put, call }){
      const complaintDtoCacheSaveText = yield call(getinsuranceDtoCacheSave , payload);
      if(complaintDtoCacheSaveText && complaintDtoCacheSaveText.code === 200 && complaintDtoCacheSaveText.content){
        return complaintDtoCacheSaveText.content;
      }else if(complaintDtoCacheSaveText.code == 422 ) {
        return 422
      }
    },

    // 推荐人确认存储
    *getReferenceCacheSave({ payload },{ put, call }){
      const getReferenceCacheSaveText = yield call(getReferenceCacheSave , payload);
      if(getReferenceCacheSaveText.code === 200){
        return getReferenceCacheSaveText;
      }else {
        return {}
      }
    },

    // 可看诊医生 getpatientInfoOfAppointment
    *getDoctorFilter({ payload },{ put, call }){
      const getDoctorFilterText = yield call(getDoctorFilter , payload);
      if(getDoctorFilterText && getDoctorFilterText.code == 200  || getDoctorFilterText && getDoctorFilterText.code == 507 ) {
        yield put({
          type: 'getDoctorFilterlist',
          payload: Array.isArray(getDoctorFilterText.content) ? getDoctorFilterText.content : []
        });
      }
      return getDoctorFilterText
    },

    //  非医生 点击 右侧 历史预约标签 进行改约操作，获取  用户的详情
    *getpatientInfoOfAppointment({ payload },{ put, call }){
      const getpatientInfoOfAppointmentText = yield call(getpatientInfoOfAppointment , payload);
     // console.log(getpatientInfoOfAppointmentText)
      if (getpatientInfoOfAppointmentText && getpatientInfoOfAppointmentText.code ===200 && getpatientInfoOfAppointmentText.content) {
        yield put({
          type: 'getpatientInfoOfAppointmentText',
          payload: getpatientInfoOfAppointmentText.content
        });
        return getpatientInfoOfAppointmentText
      }
      return {}
    },

    // 改约  确认弹窗
    *getworkingAsTime({ payload },{ put, call }){
      const complaintDtoCacheSaveText = yield call(getworkingAsTime , payload);
     // console.log(complaintDtoCacheSaveText)
      return complaintDtoCacheSaveText
    },

    // 时间制  新建预约
    *getappointmentBuild({ payload },{ put, call }){
      const appointmentBuildText = yield call(getappointmentBuild , payload);
      //console.log( '时间制  新建预约状态值',appointmentBuildText)
     // console.log('appointmentBuildText ::: ',appointmentBuildText);
      return appointmentBuildText
    },

    // // 时间制  有会诊新建预约
    // *consultationBuild({ payload },{ put, call }){
    //   const consultationBuildText = yield call(consultationBuild , payload);
    //   //console.log(consultationBuildText)
    //   return consultationBuildText
    // },

    // // 有会诊改预约
    // *getconsultationRevision({ payload },{ put, call }){
    //   const getconsultationRevisionText = yield call(getconsultationRevision , payload);
    //   console.log(getconsultationRevisionText)
    //   return getconsultationRevisionText
    // },
    // 有无会诊改约
    *appointmentReschedule({ payload },{ put, call }){
      const response = yield call(appointmentReschedule , payload);
      return response;
    },


    //录入患者 新增患者
    *additionPersonalPatient({payload}, { call, put,select }) {
      const response = yield call(additionPersonalPatient,payload);
      return response
    },


    // 取消预约
    *dropAppointment({payload}, { call, put }){
      const response = yield call(dropAppointment,payload)
      if(response.code == 200){
        return response
      }
    },
    // 全屏预约确认
    *updateAppointmentAffirmt({payload}, { call, put,select }) {
      const response = yield call(updateAppointmentAffirmt,payload);
      return response
    },


    /*
    *
    * 挂号制预约
    *
    * */
    // 全部机构id数据
    *openTelAppointmentOrgList({ payload }, { put,call }) {
      const res = yield call(openTelAppointmentOrgList, payload);
     // console.log('res123123 :: ',res);
      if(res && res.code == 200 && res.content){
        // console.log('res.content :: ',res.content);
        yield put({
          type:'openTelAppointmentOrgListSave',
          payload:res.content.resultList
        })
      }
      return res;
    },

  },

  reducers : {  // appointmentIconInfoData


    // 手工标签 查询
    appointmentIconInfoData(state,{ payload }){
      return {
        ...state,
        appointmentIconInfoDatas: payload
      };
    },

// 时间占用  获取机构诊所

    listOrganizations(state,{ payload }){
      return {
        ...state,
        listOrganizations: payload
      };
    },
    //录入患者 新增患者
    patientB(state, {payload}) {
      return {
        ...state,
        patientBstatus:payload.content,
      };
    },

    //非医生 点击 右侧 历史预约标签 进行改约操作，获取  用户的详情
    getpatientInfoOfAppointmentText(state,action){

      return {
        ...state,
        getpatientInfoOfAppointmentDta : action.payload
      }
    },

    // 可看诊医生
    getDoctorFilterlist(state,{payload}){
      // console.log(payload);
      return {
        ...state,
        getDoctorFilterData : Array.isArray(payload) ? payload : []
      }
    },

    // 主诉右侧回显
    appointmentCacheShowText(state,{payload}){
      return {
        ...state,
        appointmentCacheShowData :payload
      }
    },
    // 点击  确认预约成功后 清除主诉右侧回显
    clearAppointmentCacheShowText(state){
      return {
        ...state,
        appointmentCacheShowData : {
          complaintListDto: null,
          insuranceDtoList: null,
          referenceDto: null,
          status: null,
        }
      }
    },

    // 现有预约
    appointmentLableText(state,{payload}){
      return {
        ...state,
        appointmentLableData : payload
      }
    },

    // 查询id
    InquireRecommendText(state , {payload}){
      // console.log('InquireRecommendText : ',payload);
      return {
        ...state,
        InquireRecommendData : payload
      }
    },

    // 添加  推荐人
    AddRecommendlist(state , action){
      return {
        ...state,
        AddRecommendData : action.payload
      }
    },

    // 左側 患者 詳細信息
    PatientScreelist(state , action){
      return {
        ...state,
        PatientScreeData : action.payload
      }
    },
    // 推荐人 患者 詳細信息
    getPatientScreeTlist(state , action){
      return {
        ...state,
        TPatientScreeData : action.payload
      }
    },

    // 半屏  添加患者搜索
    PatientsSearchlist(state , action){
      return {
        ...state,
        PatientsSearchData : action.payload
      }
    },

    listdata(state , action){
      return {
        ...state,
        list : action.payload
      }
    },

    welfarelist(state , action){

      return {
        ...state,
        welfarelist :action.payload
      }
    },

    Complaintlist(state , action){
      return {
        ...state,
        Complaintlist :action.payload
      }
    },

    nurselist(state , action){
      return {
        ...state,
        nurselist : action.payload
      }
    },

    // 存值得方法 resourceName
    storageWelfare(state , {storageWelfareName,storageWelfareId}){

      return {
        ...state,
        storageWelfareName:storageWelfareName,
        storageWelfareId:storageWelfareId

      }
    },
    // 清除  搜索数据
    clearPatientsSearchData(state, { PatientsSearchData }) {
      return {
        ...state,
        PatientsSearchData: PatientsSearchData
      };
    },

    // 弃用  // 新建预约 医生名字
    resourceNames(state , resourceName){

      return {
        ...state,
        resourceName:resourceName.payload.resourceName
      }
    },

    // 使用福利卡的  激活单号

    orderForms(state , orderForm){

      return {
        ...state,
        orderForm:orderForm.payload.orderForm
      }
    },
   //  默认1 是预约新建
    conditions(state , condition){
      return {
        ...state,
        condition:condition.payload.condition
      }
    },
    // 预约会诊醫生保存
    saveDoctor(state , {payload}){
      return {
        ...state,
        ConsultantDoctor:payload
      }
    },

    // 改约可看诊医生 存储
    DoctorList(state , {payload}){

      return {
        ...state,
        DoctorList:payload.DoctorList,
        doctorIdOfAppointment:payload.doctorIdOfAppointment,
        doctorIndex:payload.doctorIndex
      }
    },
    // 可看诊医生   新建预约弹窗的二次弹窗确认  可看诊主诉  doctorComplained
    DoctorWho(state , {payload}){

      return {
        ...state,
        DoctorWhoDetail:payload
      }
    },

    // 商业保险 从新加载

    Insurance(state , {payload}){
      return {
        ...state,
        InsuranceData:payload
      }
    },

    // 保存数据
    openTelAppointmentOrgListSave(state,{payload}){
      return {
        ...state,
        openTelAppointmentOrgList:payload
      }
    },

    // 在次调用筛选的 医生的 时候  吧不可预约的  医生存上

    usableTypeDoctorLists(state , {payload}){

      return {
        ...state,
        usableTypeDoctorList:payload
      }
    },

    // 存储  最新的  患者id

    storePatientIds(state , {payload}){

      return {
        ...state,
        storePatientId:payload
      }
    },



    // 半屏有预约 患者 Vip 跟新
    PatientScreeDataUpdata(state , {payload}){
      // console.log(payload);
      return {
        ...state,
        PatientScreeData:payload
      }
    },
    //  预约列表 数据
    appointmentColumnDatas(state , {payload}){
      // console.log(payload);
      return {
        ...state,
        appointmentColumnLists:payload
      }
    },
  }

}
