.ClinicMornMeetWarp {
  min-width: 1200px;
  height: 100vh;
  position: relative;
}

.children {
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 10px;
}

.DetailsBox {
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 10px;
  display: flex;
  height: calc(100vh - 260px);

  .DetailsBox_left {
    width: 60%;
    height: calc(100% - 15px);
    // height: 30px;
    overflow: auto;
  }

  .DetailsBox_Right {
    width: 40%;
    height: calc(100% - 15px);
  }
}


.ClinicMornMeetFrom {
  width:100%;
  height: 150px;
  // border-top: #9a9a9a solid 1px;
  box-shadow: 0 -2px 6px -2px rgba(0, 0, 0, 0.25);
  position: absolute;
  bottom: 0px;
  background: #fff;
  padding-left: 30px;
  padding-right: 30px;
  z-index: 100;
}

.ClinicMornMeetBox {
  display: flex;

  .ClinicMornMeetBoxTitle {
    width: calc(100% - 180px);
    height: 150px;
    font-size: 18px;
    display: flex;
    justify-content: space-around;
    // align-items: center;
    padding-top: 30px;
    padding-right: 15px;

    .title {
      margin-right: 20px;
      font-weight: 700;
    }

    .ClinicMornMeetBoxTextArea {
      flex: 1;
    }

  }
  .ClinicMornMeetBoxContent {
    width: 180px;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .Btn_ClinicMornMeetBoxContent {
      width: 100px;
      height: 40px;
      // margin-top: 40px;
    }
  }
}
