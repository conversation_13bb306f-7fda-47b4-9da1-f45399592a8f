import React, { PureComponent } from 'react';
import { FormattedMessage, formatMessage } from 'umi/locale';
import { Tag, Menu, Icon, Dropdown, Avatar, Card } from 'antd';
import moment from 'moment';
import groupBy from 'lodash/groupBy';
import PictUrl from '@/assets/user/user.png';
import Link from 'umi/link';
import styles from '../index.less';

export default class GlobalHeaderRight extends PureComponent {
  getNoticeData() {
    const { notices = [] } = this.props;
    if (notices.length === 0) {
      return {};
    }
    const newNotices = notices.map(notice => {
      const newNotice = { ...notice };
      if (newNotice.datetime) {
        newNotice.datetime = moment(notice.datetime).fromNow();
      }
      if (newNotice.id) {
        newNotice.key = newNotice.id;
      }
      if (newNotice.extra && newNotice.status) {
        const color = {
          todo: '',
          processing: 'blue',
          urgent: 'red',
          doing: 'gold',
        }[newNotice.status];
        newNotice.extra = (
          <Tag color={color} style={{ marginRight: 0 }}>
            {newNotice.extra}
          </Tag>
        );
      }
      return newNotice;
    });
    return groupBy(newNotices, 'type');
  }

  getUnreadData = noticeData => {
    const unreadMsg = {};
    Object.entries(noticeData).forEach(([key, value]) => {
      if (!unreadMsg[key]) {
        unreadMsg[key] = 0;
      }
      if (Array.isArray(value)) {
        unreadMsg[key] = value.filter(item => !item.read).length;
      }
    });
    return unreadMsg;
  };
  // 跳转通知列表页面
  handleNoticeClick = () => {
    // router.push("/notification?activeKey=0")

    const { dispatch } = this.props;
    dispatch({
      type: "user/Jump"
    })
  };
  changeReadState = clickedItem => {
    const { dispatch } = this.props;
    const userName = localStorage.getItem("userName");
    console.log("01111111111")
    // 点击未读
    dispatch({
      type: 'user/updateMessage',
      payload: {
        id: clickedItem.id,
        messageType: clickedItem.messageType,
        userName: userName
      }
    });
    // 铃铛展开通知列表list
    dispatch({
      type: 'user/getMessageList',
      payload:{
        pageSize:5,
          current:1
      }
    });
    // 数量
    dispatch({
      type: 'user/getMessageCount',
    });
  };
  render() {
    const {
      currentUser,
      fetchingNotices,
      onNoticeVisibleChange,
      onMenuClick,
      onNoticeClear,
      theme,
      visibleType,
    } = this.props;
    const isResources = localStorage.getItem('isResources');  // 是否是资源
    const menu = (
      <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick}>
        <Menu.Item key="logout">
          <Icon type="logout" />
          <FormattedMessage id="menu.account.logout" defaultMessage="退出登录" />
        </Menu.Item>

      </Menu>
    );
    const noticeData = this.getNoticeData();
    const unreadMsg = this.getUnreadData(noticeData);
    let className = styles.right;
    if (theme === 'dark') {
      className = `${styles.right}  ${styles.dark}`;
    }
    const userName = localStorage.getItem('userName'); //用户名
    const role = JSON.parse(localStorage.getItem('role')); // 角色
    const useAvatar = localStorage.getItem("avatar"); //用户头像
    return (
      <div className={className}>
        <span style={{ marginRight: 14, fontSize: 18, color: '#333' }}>{userName}</span>
        {role && role.map(item => (
          <span key={item.roleName} style={{ marginRight: 10, fontSize: 14, color: '#999' }}>{item.roleName}</span>
        ))}

          <Dropdown overlay={menu}>
            <span className={`${styles.action} ${styles.account}`}>
              <Avatar
                size="small"
                className={styles.avatar}
                src={useAvatar ? `${useAvatar}` : PictUrl}
                alt="avatar"
              />
            </span>
          </Dropdown>
      </div>
    );
  }
}
