import { message } from 'antd';
import { LockTwoTone, UserOutlined, BankOutlined } from '@ant-design/icons';
import { Alert, Tabs } from 'antd';
import React, { useState } from 'react';
import ProForm, { ProFormText } from '@ant-design/pro-form';
import {
  getTenantId,
  getTokenC,
  UploadInfoC,
  getLoginInfo,
  checkInfoAllReady,
  fakeAccountLogin,
} from '@/services/login';
import { v4 as uuidv4 } from 'uuid';
import { useIntl, connect, FormattedMessage, history } from 'umi';
import styles from './index.less';
import passport from '@/utils/passport';
import Base64 from 'base-64'; //Base64图片转换
const LoginMessage = ({ content }) => (
  <Alert
    style={{
      marginBottom: 24,
    }}
    message={content}
    type="error"
    showIcon
  />
);

const Login = (props) => {
  const { userLogin = {}, submitting } = props;
  const { status, type: loginType } = userLogin;
  const [type, setType] = useState('account');
  const [loginText, setLoginText] = useState(null);
  const intl = useIntl();

  /*
  TenantCode: "11000"
  accountNumber: "rx2008190005"
  password: "*********"
  * */

  const handleSubmit = async (values) => {
    const UidKey = uuidv4();
    const { TenantCode, accountNumber } = values || {};

    setLoginText(null);

    //----① 根据输入的机构号换取TenantId
    const dataByGetTenantId = await getTenantId({
      TenantCode: TenantCode,
    });
    const { code, content: TenantId } = dataByGetTenantId || {};
    if (code != 200 || !TenantId) {
      message.error('根据机构号获取TenantId失败');
      setLoginText(null);
      return;
    }
    setLoginText(`① 根据机构号获取TenantId成功! 机构号: ${TenantId}`);

    //----② 通过TenantId 输入的用户名和密码 获取Token
    const AgetES = passport.getAES(values.password, 'arrail-dentail&2', 'arrail-dentail&3');
    let paramsByGetTokenC = {
      grant_type: 'password',
      username: `${accountNumber}/${TenantId}`,
      password: AgetES,
      accountNumber: accountNumber,
    };
    let formDataByGetTokenC = new FormData();
    Object.keys(paramsByGetTokenC).map((v) => {
      formDataByGetTokenC.append(v, paramsByGetTokenC[v]);
    });
    let dataByGetTokenC = await getTokenC(formDataByGetTokenC);
    /*access_token: "fjx0NKOBLfgAGIPp53_5ZHtQY1k"
    expires_in: 35948
    refresh_token: "IEOJUX8MHAKDsaqxfWgAP-p_u7k"
    scope: "all"
    token_type: "bearer"*/
    const { access_token, expires_in, refresh_token, scope, token_type } = dataByGetTokenC || {};
    if (!dataByGetTokenC || !access_token) {
      message.error('token获取失败');
      setLoginText(null);
      return;
    }
    setLoginText(`② token获取成功! access_token: ${access_token}`);
    // 保存token
    localStorage.setItem('access_token', access_token);

    //-③ 保存save-login-info 保存登录信息
    let paramsByUploadInfoC = {
      FormData: {
        tenantId: TenantId,
        username: paramsByGetTokenC.username,
        accessToken: access_token,
      },
      UidKey,
    };
    let dataByUploadInfoC = await UploadInfoC(paramsByUploadInfoC);
    const {
      code: codeByUploadInfoC, // 登录信息code
      content: contentByUploadInfoC, // 登录信息content
    } = dataByUploadInfoC || {};
    if (!dataByUploadInfoC || codeByUploadInfoC != 200) {
      message.error('③ 保存save-login-info 保存登录信息失败');
      setLoginText(null);
      return;
    }
    setLoginText(`③ 保存save-login-info 保存登录信息`);

    //-④ get-login-info-获取登录存储的个人信息
    let dataByGetLoginInfo = await getLoginInfo(UidKey);
    const { code: codeByGetLoginInfo, content: contentByGetLoginInfo } = dataByGetLoginInfo || {};
    if (code != 200 || !contentByGetLoginInfo) {
      message.error('④ 保存get-login-info 获取登录信息失败');
      setLoginText(null);
      return;
    }
    setLoginText('④ 保存get-login-info 获取登录信息成功');

    const {
      accessToken: accessTokenByloginInfo,
      tenantId: tenantIdByloginInfo,
      username: usernameByloginInfo,
    } = contentByGetLoginInfo || {};

    //-⑤ 验证是否需要完善信息
    localStorage.setItem('username', usernameByloginInfo);
    localStorage.setItem('token_userName', accountNumber);

    let dataByCheckInfoAllReady = await checkInfoAllReady();
    setLoginText('⑤ 验证是否需要完善信息');
    const { code: codeByCheckInfoAllReady, content: contentByCheckInfoAllReady } =
      dataByCheckInfoAllReady || {};
    const { flag: flagByContentByCheckInfoAllReady } = contentByCheckInfoAllReady || {};

    if (dataByCheckInfoAllReady == 'error' || codeByCheckInfoAllReady != 200) {
      message.error('⑤ 验证是否需要完善信息,获取状态失败');
      setLoginText(null);
      return;
    }
    if (!flagByContentByCheckInfoAllReady) {
      message.warning('请前往诊所端完善信息');
      setLoginText(null);
      return;
    }
    //-⑥ 登录  获取账户信息
    let dataByfakeAccountLogin = await fakeAccountLogin({ id: '123123' });
    // 添加接口报错提示～！
    if (dataByfakeAccountLogin == undefined) {
      message.error('获取账户信息失败！');
      setLoginText(null);
      return;
    }
    //  判断有没有开通业务端权限
    if (dataByfakeAccountLogin.code == 401) {
      message.error('获取账户信息失败！Code 401');
      setLoginText(null);
      return;
    }
    // 账号被禁用
    if (dataByfakeAccountLogin.content && dataByfakeAccountLogin.content.status == 1) {
      message.error('您的账号已被禁用，如需启用请联系管理员!');
      setLoginText(null);
      return;
    }
    // 获取账户信息失败
    if (dataByfakeAccountLogin.code != 200) {
      message.error('获取账户信息失败！');
      setLoginText(null);
      return;
    }
    setLoginText('⑥ 登录获取账户信息 ');
    const { code: codeBydataByfakeAccountLogin, content: contentBydataByfakeAccountLogin } =
      dataByfakeAccountLogin || {};

    const {
      accountNumber: accountNumberByLogin, //: "rx2008190005"
      doctorIdentification, //: 2
      email, //: "<EMAIL>"
      emrQueryRole, //: 0
      fourCustomer, //: 0
      fourOnState, //: 0
      headUrl, //: ""
      headUrlView, //: ""
      id, // : 3877
      isOldUser, //: "true"
      isRead, //: 1
      isResources, // : 2
      menuInfoList, //: [{id: "0aed577f4c2dfce5f65426baf6489124", menuName: "回访模板维护", upId: "0", menuType: "0",…},…]
      noticeSize, //: 0
      organizationInfo, //: {id: "f0b3c01bf2934d79b20c71149a0a79b1", organizationName: "市场化测试第一诊所", enterDate: "2022-06-22",…}
      organizationSize, //: 7
      phone, //: "131****8901"
      position, //: []
      procurementAuth, //: 0
      status, //: "0"
      tenantId, //: "3a9d9c680c364df9a84bfd5de91e9324"
      tenantName, //: "瑞诚齿科"
      userName, //: "岳丽丽测试客服"
      userRoleList, // :
      wjUrl,
    } = contentBydataByfakeAccountLogin || {};

    // localStorage.setItem('username', 'rc2008190009/3a9d9c680c364df9a84bfd5de91e9324');
    // localStorage.setItem('tenantId', '3a9d9c680c364df9a84bfd5de91e9324');
    // localStorage.setItem('organizationId', 'e88824a05f7a48029233821cf231b88c');
    // localStorage.setItem('organizationName', '刘医生演示诊所');
    // localStorage.setItem('userName', '岳丽丽');
    // localStorage.setItem('userId', 3883);
    // localStorage.setItem('access_token', 'gk69sapgNyNlOD41cgpUs3AqEgM');

    localStorage.setItem('id', id);
    localStorage.setItem('userName', userName);
    localStorage.setItem('tenantId', tenantId);
    localStorage.setItem('organizationId', organizationInfo.id);
    localStorage.setItem('organizationInfoId', organizationInfo.id);
    localStorage.setItem('organizationName', organizationInfo.organizationName);
    localStorage.setItem('pool', organizationInfo.pool);
    localStorage.setItem('organizationlnfoJson', JSON.stringify(organizationInfo));
    localStorage.setItem('organizationlnfo', JSON.stringify(organizationInfo));
    // localStorage.setItem('roleCodes', JSON.stringify(roleCodes));
    localStorage.setItem('loginInfo', JSON.stringify(contentBydataByfakeAccountLogin));

    message.success('🎉 🎉 🎉  登录成功！');
    setLoginText(null);
    history.push('/emr/ArriveToday');
  };

  return (
    <div className={styles.main}>
      <ProForm
        initialValues={{
          autoLogin: true,
        }}
        submitter={{
          render: (_, dom) => dom.pop(),
          submitButtonProps: {
            loading: submitting,
            size: 'large',
            style: {
              width: '100%',
            },
          },
        }}
        onFinish={async (values) => {
          handleSubmit(values);
          return Promise.resolve();
        }}
      >
        <Tabs activeKey={type} onChange={setType}>
          <Tabs.TabPane
            key="account"
            tab={intl.formatMessage({
              id: 'pages.login.accountLogin.tab',
              defaultMessage: '账户密码登录',
            })}
          />
        </Tabs>

        {status === 'error' && loginType === 'account' && !submitting && (
          <LoginMessage
            content={intl.formatMessage({
              id: 'pages.login.accountLogin.errorMessage',
              defaultMessage: '账户或密码错误',
            })}
          />
        )}
        {type === 'account' && (
          <>
            <ProFormText
              name="TenantCode"
              fieldProps={{
                size: 'large',
                prefix: <BankOutlined className={styles.prefixIcon} />,
              }}
              placeholder={intl.formatMessage({
                id: 'pages.login.username.placeholder',
                defaultMessage: '请输入五位机构代码',
              })}
              rules={[
                {
                  required: true,
                  message: (
                    <FormattedMessage
                      id="pages.login.username.required"
                      defaultMessage="请输入五位机构代码!"
                    />
                  ),
                },
              ]}
            />

            <ProFormText
              name="accountNumber"
              fieldProps={{
                size: 'large',
                prefix: <UserOutlined className={styles.prefixIcon} />,
              }}
              placeholder={intl.formatMessage({
                id: 'pages.login.username.placeholder',
                defaultMessage: '请输入医生账户',
              })}
              rules={[
                {
                  required: true,
                  message: (
                    <FormattedMessage
                      id="pages.login.username.required"
                      defaultMessage="请输入医生账户!"
                    />
                  ),
                },
              ]}
            />

            <ProFormText.Password
              name="password"
              fieldProps={{
                size: 'large',
                prefix: <LockTwoTone className={styles.prefixIcon} />,
              }}
              placeholder={intl.formatMessage({
                id: 'pages.login.password.placeholder',
                defaultMessage: '请输入密码',
              })}
              rules={[
                {
                  required: true,
                  message: (
                    <FormattedMessage
                      id="pages.login.password.required"
                      defaultMessage="请输入密码！"
                    />
                  ),
                },
              ]}
            />
          </>
        )}
      </ProForm>
      {!!loginText && <div className={styles.loadingBox}>{loginText}</div>}
    </div>
  );
};

export default connect(({ login, loading, tokenInfo }) => ({
  userLogin: login,
  submitting: loading.effects['login/login'],
  tokenInfo: tokenInfo,
}))(Login);
