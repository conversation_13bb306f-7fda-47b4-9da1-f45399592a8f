import React, { Component } from 'react';
import { Popover,Select } from 'antd';
import PropTypes from 'prop-types';
import moment from 'moment';
import styles from './EventElement.less';
import classNames from 'classnames';
import LinesEllipsis from 'react-lines-ellipsis';
import consultation from '@/assets/RegisterImg/huizhen.png';
import Consultant from '@/assets/RegisterImg/huizhenyisheng.png';
import firstVisit from '@/assets/registerAndArrival/legend_icon2.png';
import Fu from '@/assets/registerAndArrival/fu.png';
import Insurance from '@/assets/registerAndArrival/legend_icon1.png';
import zhuazhanIcon from '@/assets/registerAndArrival/legend_icon5.png';
import jieya from '../../../assets/W-img/jieya.png';
import padiDui from '../../../assets/W-img/padiDui.png';
import SmallerAppointment from '@/assets/registerAndArrival/smaller.png';
import saveuser from '../../../assets/registerAndArrival/SaveUser.png'; // 更换患者图标
import jizhen from '@/assets/AppointmentRules/jizhen.png'
import Manualicon from './Manualicon';
import {
  getFirstAppointmentIdForShowPrompt,
  setFirstAppointmentIdForShowPrompt,
  setIsNotFirstShowPrompt,
  formatTime,
} from  '@/utils/CalendarUtils.js'
import { getOrganizationInfo } from '@/utils/utils';


// 格式化后的时间


/**
 * @type
 * 1:预约
 * 2:会诊
 * 3:资源排班上班时间
 * 4:个人占用时间
 * 5:已过时间
 * 6:休息 只有状态无具体时间
 * 7:走诊
 */
export default class EventElement extends Component {

  static propTypes = {
    eventObj:PropTypes.object,
    eventPersonalTimeClose:PropTypes.func,
    showChangePatient:PropTypes.bool,  // 是否展示更改患者按钮 是否在到诊页面中使用
    useDraggable:PropTypes.bool,       // 是否用于改约页面中
  }

  static defaultProps = {
    eventObj:{},
    eventPersonalTimeClose:null,
    showChangePatient:false,      // 是否展示更改患者按钮 是否在到诊页面中使用
    useDraggable:false,           // 是否用于改约页面中
  }

  constructor(props) {
    super(props);
    const { event } = this.props.eventObj;
    const {
      extendedProps,
    } = event || {}
    const {
      appointmentInfoOfTableDto,
    } = extendedProps || {}

    let {
      appointmentId,
    } = appointmentInfoOfTableDto || {}
    let firstAppointmentId = getFirstAppointmentIdForShowPrompt();

    this.state = {
      isShowFirstAppointment: !!(appointmentId == firstAppointmentId),
      WaitFormatTime:null,        // 格式化后的等待时间
      isWaitStute: null,          // 是否是等待状态 0:不等待 1:等待 2:等待超时
      isHiddenElement:null,       // 是否需要隐藏元素 null:不隐藏 1:隐藏姓名和等待标签
    }
  }


  onChangePatient=()=>{
    this.props.onChangePatient && this.props.onChangePatient(this.props.eventObj.event)
  }

  getEventRemarkStyle=(diffMinutes,complaintListText,isIcon)=>{
    // 计算占用行数
    let occupyCull = 2; // 名称行
    if (Array.isArray(isIcon) && isIcon.length != 0) { occupyCull++ }
    if(complaintListText){ occupyCull++ }

    let num = 1;
    if(diffMinutes && !this.props.useDraggable){
      num = Math.floor(diffMinutes / 20)
      if(num > occupyCull){
        num = num - occupyCull
      }
    }
    return {
      display : '-webkit-box',
      WebkitBoxOrient: 'vertical',
      WebkitLineClamp : num + '',
      overflow: 'hidden',
      wordBreak :'break-all'
    }
  }

  onClickShutDownByTemporaryStorage=(e)=>{ e.stopPropagation() }

  componentDidMount(){
    const { event,isfullScreen } = this.props.eventObj;
    const {
      extendedProps,
    } = event || {}
    const {
      appointmentInfoOfTableDto,
    } = extendedProps || {}
    const {
      isComeVisit,               // 是否已到诊
      isConsultationVisit,       // 0:未接诊 1:已接诊
      waitingTimes,              // 等待时长(秒)
      isWaitingOver,             // 等待超时(1是，0否)
    } = appointmentInfoOfTableDto || {}
    if(isComeVisit == 1 && isConsultationVisit == 0) {
      // 开始等待时长的计时
      this.IntervalByformatTime()
    }
  }

  // 开启等待时长的正计时
  IntervalByformatTime=()=>{
    const { event,isfullScreen } = this.props.eventObj;
    const {
      extendedProps,
    } = event || {}
    const {
      appointmentInfoOfTableDto,
    } = extendedProps || {}
    const {
      waitingTimes,              // 等待时长(秒)
      isWaitingOver,             // 等待超时(1是，0否)
    } = appointmentInfoOfTableDto || {}


    let {
      appointmentId,
      isComeVisit,  // 0未到诊 1已到诊
      isLate,       // 过预约时间未到诊 0没有迟到  1迟到
      isSettlement, // 结算状态 0:未结算 1:已结算
      isConsultationVisit,
    } = appointmentInfoOfTableDto || {}
    const OrganizationInfo = getOrganizationInfo()// 收费模式(1规范化，2简洁化，3无医生)
    if(
      isComeVisit == 1 &&
      isConsultationVisit == 0 &&
      OrganizationInfo.chargeModel == 1 &&
      waitingTimes != null &&
      !!isfullScreen  // 预约全屏下才展示等待时长
    ){ // 已到诊
      this.setState({
        isWaitStute: isWaitingOver ? 2 : 1,  // 是否是等待状态 0:不等待 1:等待 2:等待超时
      },()=>{
        this.computerBoxWaitWarp()
        if (this.IntervalId) { clearInterval(this.IntervalId) }
        let seconds = waitingTimes ? waitingTimes : 0; // 开始的秒数
        this.IntervalId = setInterval(() => {
          this.setState({
            WaitFormatTime: formatTime(seconds) // 每秒输出一次格式化后的时间
          })
          seconds++;
        }, 1000);
      })
    }else {
      this.setState({
        isWaitStute: null,  // 是否是等待状态 0:不等待 1:等待 2:等待超时
        WaitFormatTime:null, // 格式化后的等待时间
        isHiddenElement:null,
      })
    }
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    let waitingTimesByrevProps = prevProps?.eventObj?.event?.extendedProps?.appointmentInfoOfTableDto?.waitingTimes;
    let waitingTimesBythisProps = this.props?.eventObj?.event?.extendedProps?.appointmentInfoOfTableDto?.waitingTimes;
    if(waitingTimesByrevProps != waitingTimesBythisProps){
      this.IntervalByformatTime()
    }
  }

  componentWillUnmount(){
    if (this.IntervalId) { clearInterval(this.IntervalId) }
  }

  // 根据预约卡片的宽高动态计算展示内容
  // ① 获取当前元素的clientWidth,clientHeight
  // ② 根据元素大小来判断是否需要展示名称或标签
  computerBoxWaitWarp=()=>{
    const { el, event, view, eventPropsChange,isfullScreen } = this.props.eventObj;
    console.log('this.props.eventObj; ',this.props.eventObj);
    /*
    clientHeight: 29
    clientLeft: 1
    clientTop: 1
    clientWidth: 103
    * */
    const {
      clientHeight,
      clientWidth,
    } = el || {}

    if(clientHeight < 30){
      this.setState({
        isHiddenElement:1,
      })
    }

  }

  render() {
    const { el, event, view, eventPropsChange,isfullScreen } = this.props.eventObj;

    const {
      id,                   // 预约id
      start,                // 预约开始时间
      end,
      extendedProps,
    } = event || {}
    const {
      type,
      titleInfo,
      appointmentInfoOfTableDto,
      consultationInfoOfTableDto, // 会诊信息
      operationType,

    } = extendedProps || {}

    let isDoctor = localStorage.getItem('doctorIdentification') == 1
    let isDisOperationType = operationType == 0;

    // 计算当前预约时长
    let diffMinutes = 0;
    let startByFormat = moment(start).format('HH:mm');
    let endByFormat = moment(end).format('HH:mm');

    if (start && end) {
      let startMoment = moment(start);
      let endMoment = moment(end);
      diffMinutes = endMoment.diff(startMoment, 'minutes');
    }

    if (eventPropsChange) {
      diffMinutes = 100
    }

    let {
      appointmentId,
      patientInfoDto,            // 患者信息
      isInsurance,               // 是否使用保险 0不使用 1使用
      complaintList,             // 主诉与治疗
      remark,                    // 预约备注
      doctorNameOfReferral,      // 转诊医生
      doctorNameOfAppointment,   // 当前预约医生
      doctorNameOfConsultation,    // 当前预约的会诊医生
      presentIllness,            // 患者标签
      isConsultation,            // 是否携带会诊 1有 0没有
      isReferral,                // 是否是转诊
      isFirstVisit,              // 是否初复诊 1: 为初诊 2:复诊
      appointmentIconDto,        // 治疗项目页标签
      isComeVisit,               // 是否已到诊
      isClean,                   // 是否是洁牙
      isWaitingList,             // 是否在排队列表
      taskType,                  // 预约确认状态 已完成预约确认1/未完成预约确认0/已联系未确认2/撤销预约确认3
      appointmentSource,         // 预约来源 3代表小程序
      toDirector,                // 咨询师到诊状态 0未到咨询师 1已到咨询师 2咨询师已分配 3接诊中
      appointmentStatus,         // 预约状态 预约状态 1正常 2已改约 3已取消 10预约占用
      appointmentIconNewDtoList, // 治疗标签
      timeEnd,                   // "11:15"
      timeLength,                // 60
      timeStart,                 // "10:15"
      emergency:emergencyByApp,  // 是否是急诊 1是急诊  0是不急诊
      waitingTimes,              // 等待时长(秒)
      isWaitingOver,             // 等待超时(1是，0否)
    } = appointmentInfoOfTableDto || {}

    let {
      basicTreatment,    //  指定客服大客户
      bigCustomer,       //  指定客服大客户
      emergency,         //  急诊
      implantSurgery,    //  种植手术
      implantTwice,      //  种植二期
      lab,               //  lab
      majorTreatment,       //  重大治疗
      orthodontic,          //  正畸
      platinumCard,         //  招商银行白金卡
      bankPlatinumCard,     //  银行白金卡
      policyHolder,         //  MSH保险客户
      repairCrown,          //  修复戴冠
      rootCanal,            //  根管
      teethWhitening,       //  冷光美白
      vipClient,            //  VIP客户
      hitSupport,           //  打支抗
      toothExtraction,      //  拔牙
      firstOrthodontics,    //  正畸初戴
      voucher,              //
      microscopicRootCanal, // 显微镜根管
      childrenBound,        // 儿童束缚
      toothWhitening,        // 牙齿美白
      regularCustomerVisit,   // 老客户回访
      regularInspection,      // 三个月定检
      comfortTreatment,       // 舒适治疗
      crown,                  // 戴冠
    } = appointmentIconDto || {}

    // 主诉与治疗信息
    let complaintListTextList = []
    if (Array.isArray(complaintList)) {
      complaintList.map((res) => {
        if (res.treatType == 1 || res.treatType == 2) {
          complaintListTextList.push(res.complaintMergeName ? res.complaintMergeName : res.complaintName)
        } else if (res.treatType == 3 || res.treatType == 4 || res.treatType == 5) {
          complaintListTextList.push(res.medicalDictionaryName)
        }
      })
    }
    let complaintListText = complaintListTextList.join(',')
    complaintListText = complaintListText && complaintListText.replace(/"-"/g,'-').replace(/\s+/g, "")

    let {
      age,
      sex,
      sexDescribe,
      name,
      patientName,            // 患者名称
      appointmentDate,
      appointmentDateList,
      doctor,
      doctorName,
      // isFirstVisit,        // 是否初复诊 1: 为初诊 2:复诊
      vipGrade,

    } = patientInfoDto || {}

    let sexText = sex == 1 ? '男' : '女'


    /*{ isFirstVisit == 1 && <span className={styles.patientEventicon}><img width={22} height={22} src={firstVisit}/></span>}
    { isFirstVisit == 2 && <span className={styles.patientEventicon}><img width={22} height={22}  src={Fu}/></span>}
    { type == 2 && <span className={styles.patientEventicon}><img width={22} height={22}  src={consultation}/></span>}
    { isInsurance == 1 && <span className={styles.patientEventicon}><img width={22} height={22}  src={Insurance}/></span>}
    {/!*<span className={classNames(styles.patientEventicon,styles.Diamonds)}><img src={Diamonds}/></span>*!/}
    { isReferral == 1 && <span className={styles.patientEventicon}><div className={styles.dingwei5}></div></span>}*/
    let typeIconList = []
    if (emergencyByApp == 1) {
      typeIconList.push('emergency')
    }
    if (isFirstVisit == 1) {
      typeIconList.push('firstVisit')
    }
    if (isFirstVisit == 2) {
      typeIconList.push('fuVisit')
    }
    if (isInsurance == 1) {
      typeIconList.push('Insurance')
    }
    if (isReferral == 1) {
      typeIconList.push('isReferral')
    }
    if (vipGrade == 1) {
      typeIconList.push('vipGrade')
    }
    if (appointmentSource == 3 && !this.props.showChangePatient) {
      typeIconList.push('appointmentSource')
    }
    if (isWaitingList == 1 && !this.props.showChangePatient) {
      typeIconList.push('isWaitingList')
    }
    if (toDirector == 1 || toDirector == 3) {
      typeIconList.push('toDirector')
      typeIconList.push('toDirector')
    }


    //{appointmentInfoOfTableDto && appointmentInfoOfTableDto.isClean == 1 ? <img src={jieya} alt="" /> : null}
    if (this.props.showChangePatient) {
      typeIconList.push('showChangePatient')
    }

    // 是否有手工标签
    let isIcon = Array.isArray(appointmentIconNewDtoList) ? appointmentIconNewDtoList : [];
    let firstAppointmentId = getFirstAppointmentIdForShowPrompt()

    /*预约*/
    let appointmentDom = (
      /* <Popover
        content={<a>Close</a>}
        title="Title"
        trigger="click"
        visible={true}
        // onVisibleChange={this.handleVisibleChange}
      > */
      <Popover
        className={styles.PopoverByEventElent}
        visible={(!!this.state.isShowFirstAppointment && !!isfullScreen)}
        title={null}
        overlayClassName={styles.PopoverByEventElent}
        content={
          <div onClick={()=>{
            this.setState({
              isShowFirstAppointment:false,
            },()=>{
              setIsNotFirstShowPrompt();
            })
          }} className={styles.PopoverByEventElentContent}>
            <div className={styles.PopoverByEventElentText}> 双击卡片还可以放大哦~ </div>
            <div className={styles.PopoverByEventElentTextBtn}> 我知道了 </div>
          </div>
        }
        placement="bottom"
        okText="Yes"
        cancelText="No"
        getPopupContainer={(triggerNode) => { return triggerNode.parentNode.parentNode }}
      >
      <div id={`event_${id}`} className={classNames({
        [styles.eventWarp]:true,
      })}>
        {!!this.state.isWaitStute &&
          <div className={classNames({
            [styles.boxWaitWarp]:true,
            [styles.timeoutBorder]:this.state.isWaitStute == 2,
            [styles.timeoutBackgroundTransparency]:this.state.isWaitStute == 2,
          })}>
            <div className={classNames({
              [styles.stateByWait]:true,
              [styles.timeoutBorder]:this.state.isWaitStute == 2,
              [styles.timeoutBackground]:this.state.isWaitStute == 2,
              // [styles.isHiddenElement]:!!this.state.isHiddenElement,
            })}>
              <i className={styles.stateByWaitIcon}/>
              <div>已等待</div>
            </div>
            <div className={classNames({
              [styles.TimeByText]:true,
              [styles.timeoutColor]:this.state.isWaitStute == 2,
              [styles.isHiddenElement]:!!this.state.isHiddenElement,
            })}>
              {this.state.WaitFormatTime}
            </div>
          </div>
        }

        {/* 基础 重大
                basicTreatment,   //  基础治疗
                bigCustomer,      //  指定客服大客户
                emergency,        //  急诊
                lab,              //  lab
                majorTreatment,   //  重大治疗
                vipClient,        // 	VIP客户
        */}
        {/* <i className={styles.jichu}></i> */}
        {diffMinutes >= 10 && basicTreatment == 1 && majorTreatment != 1 && <i className={styles.jichu}></i>}
        {diffMinutes >= 10 && majorTreatment == 1 && <i className={styles.zhongda}></i>}
        {!!(taskType && taskType == 1) && <i className={styles.appmentEnter}></i>} { /* 预约确认 */}

        <div className={classNames('eventElent', styles.eventElent)}>
          {/* 5分钟一行会诊显示样例：显示“患者姓名+客户身份标识+初/复诊标识+会诊标识+转诊标识+保险客户标识+待接诊标识 */}
          {diffMinutes <= 5 &&
          <div>
            {/* 姓名行 */}
            {/* <span className={styles.patientName} >{name}</span> */}
            <div className={styles.patienBox}>
              {!this.state.isWaitStute &&
                <div>
                  {
                    vipGrade == 1 &&
                    <i className={styles.vipicon}></i>
                  }
                  <div className={styles.patientName}>{name}<span className={styles.TimeSpan}>{timeStart}-{timeEnd}</span></div>
                  {/* <LinesEllipsis className={styles.patientName} text={name} maxLine={1} ellipsis='...' >{name}</LinesEllipsis> */}
                </div>
              }

              {this.props.showChangePatient && isComeVisit == 0 &&
              <span className={styles.PatienticonRigth}>
                  <span onClick={(e) => {
                    e.stopPropagation()
                    this.onChangePatient(this.props.eventObj)
                  }} className={classNames(styles.patientEventicon, 'changePatientIcon')}><img width={22} height={22}
                                                                                               src={saveuser} /></span>
                </span>
              }
              {/* <span className={styles.PatienticonRigth}>
                    { isFirstVisit == 1 && <span className={styles.patientEventicon}><img width={21} height={21} src={firstVisit}/></span>}
                  { isFirstVisit == 2 && <span className={styles.patientEventicon}><img width={21} height={21}  src={Fu}/></span>}
                  { type == 2 && <span className={styles.patientEventicon}><img width={21} height={21}  src={consultation}/></span>}
                  { isInsurance == 1 && <span className={styles.patientEventicon}><img width={21} height={12}  src={Insurance}/></span>}
                  <span className={classNames(styles.patientEventicon,styles.Diamonds)}><img src={Diamonds}/></span>
                  { isReferral == 1 && <span className={styles.patientEventicon}><div className={styles.dingwei5}></div></span>}
                  { isConsultation == 1 && <span className={styles.patientEventicon}><img width={18} height={18}  src={consultation}/></span>}
                    </span> */}
            </div>
          </div>
          }

          {/* 5分钟一行会诊显示样例：显示“患者姓名+客户身份标识+初/复诊标识+会诊标识+转诊标识+保险客户标识+待接诊标识 */}
          {diffMinutes > 5 &&
          <div>
            {/*姓名行*/}
            <div className={classNames({
              [styles.patienBox]: true,
              [styles.patienLineHeigth]: true,
              [styles.patienLinePadding]: diffMinutes > 10 && (basicTreatment == 1 || majorTreatment == 1),
              // [styles.isHiddenElement]:!!this.state.isHiddenElement
            })}>

              <div
                className={classNames({
                  [styles.patientName]: true,
                    /* [styles.patientNameWidth1]: typeIconList.length == 1,
                    [styles.patientNameWidth2]: typeIconList.length == 2,
                    [styles.patientNameWidth3]: typeIconList.length == 3,
                    [styles.patientNameWidth4]: typeIconList.length >= 4, */

                })}
                style={{
                  maxWidth:`calc(100% - ${typeIconList.length * 20}px)`
                }}
              >
                {name}
                <span className={classNames({
                  [styles.TimeSpan]:true,
                  [styles.warp_element_notName_none]:!!this.state.isWaitStute,
                })}>{timeStart}-{timeEnd}</span>
              </div>

              <span className={classNames({
                [styles.PatienticonRigth]:true,
                [styles.warp_element_notName]:!!this.state.isWaitStute,
              })}>
                {vipGrade == 1 && <i className={styles.vipicon}></i>}
                {this.props.showChangePatient && isComeVisit == 0 && <span onClick={(e) => {
                  e.stopPropagation()
                  this.onChangePatient(this.props.eventObj)
                }} className={classNames(styles.patientEventicon, 'changePatientIcon')}><img width={20} height={20}
                                                                                             src={saveuser} /></span>}
                {/* 市场化要求顺序 复急会 */}
                {/* 初 */}
                {isFirstVisit == 1 &&
                <span className={styles.patientEventicon}><img width={16} height={16} src={firstVisit} /></span>}
                {/* 复 */}
                {isFirstVisit == 2 &&
                <span className={styles.patientEventicon}><img width={16} height={16} src={Fu} /></span>}
                {/* 排 */}
                {!this.props.showChangePatient && isWaitingList == 1 &&
                <span className={styles.patientEventicon}> <img width={16} height={16} src={padiDui} /> </span>}
                {/* 急 */}
                {emergencyByApp == 1 &&
                  <span className={styles.patientEventicon}><img width={16} height={16} src={jizhen} /></span>
                }
                {/* 关联小程序 */}
                {this.props.showChangePatient && appointmentSource == 3 &&
                <span className={styles.patientEventicon}> <img width={16} height={16}
                                                                src={SmallerAppointment} /> </span>}
                {/* 洁 */}
                {isClean == 1 &&
                <span className={styles.patientEventicon}> <img width={16} height={16} src={jieya} /> </span>}
                {/* 转 */}
                {isReferral == 1 &&
                <span className={styles.patientEventicon}><img width={16} height={16} src={zhuazhanIcon} /></span>}
                {/* 保 */}
                {isInsurance == 1 &&
                <span className={styles.patientEventicon}><img width={16} height={16} src={Insurance} /></span>}
                {/* 会 */}
                {type == 2 &&
                <span className={styles.patientEventicon}><img width={16} height={16} src={consultation} /></span>}


                {/* toDirector：咨询师到诊状态 0未到咨询师1已到咨询师2咨询师已分配3接诊中 */}
                {toDirector == 1 &&
                <span className={styles.Eventicon}> <i className={styles.stayToDirectorWarp}>待接待</i> </span>}
                {toDirector == 3 &&
                <span className={styles.Eventicon}> <i className={styles.alreadyToDirectorWarp}>接待中</i> </span>}


                {/*{ isConsultation == 1 && <span className={styles.patientEventicon}><img width={18} height={18}  src={consultation}/></span>}*/}
              </span>
            </div>
          </div>
          }

          {/* 10分钟两行显示样例 第二行显示本次患者预约主诉，显示不全...代替 */}
          {/*主诉行*/}
          {diffMinutes >= 20 && diffMinutes < 40 &&
          <div className={classNames({
            [styles.warp_element_notName]:!!this.state.isWaitStute})}>
            <div>
              {isIcon.length != 0 &&
              <div className={styles.cureProjectIcon}>
                <Manualicon appointmentIconNewDtoList={isIcon} />
              </div>
              }
            </div>
          </div>
          }

          {/*15分钟三行显示样例：第三行显示手工标签，，当本次无手工标签时显示预约备注信息，当既没有预约备注也没有手工标签时不显示*/}
          {diffMinutes >= 40 && diffMinutes < 60 &&
          <div className={classNames({
            [styles.warp_element_notName]:!!this.state.isWaitStute
          })}>
            {/*备注行 或者 手工标签行*/}
            <div>
              <div className={styles.cureProjectIcon}>
                <Manualicon appointmentIconNewDtoList={isIcon} />
              </div>
              {/*主诉行*/}
              {complaintListText &&
              <div className={styles.complaintList}>{complaintListText}</div>
              }
            </div>
          </div>
          }


          {/*
          大于三行显示样例：第一行显示“患者姓名+客户身份标识+初/复诊标识+会诊标识+转诊标识+保险客户标识”
                         第行显示手工标签，无手工标签时不显示
                         第二行显示患者本次预约主诉，显示不全...代替
                         第三行显示预约备注信息，当既没有预约备注时显示手工标签，两者都没有时不显示

          */}
          {diffMinutes >= 60 &&
          <div className={classNames({
            [styles.warp_element_notName]:!!this.state.isWaitStute
          })}>
            <div className={styles.cureProjectIcon}>
              <Manualicon appointmentIconNewDtoList={isIcon}/>
            </div>
            {/*主诉行*/}
            {complaintListText &&
            <div className={styles.complaintList}>{complaintListText}</div>
            }
            {/*备注行*/}
            {remark &&
              <div className={classNames({
                [styles.patienLineHeigth]: true,
              })}>
                {/*<Ellipsis lines={1} className={""}>备注: {remark}</Ellipsis>*/}
                {/* <LinesEllipsis text={remark} maxLine={1} ellipsis='...' />*/}
                <div style={this.getEventRemarkStyle(diffMinutes, complaintListText, appointmentIconNewDtoList)}
                     className={styles.remark}>{remark}</div>
              </div>
            }
          </div>
          }
        </div>


      </div>
      </Popover>
    )

    let otherHours = (<div></div>)

    /*
     * @type
     * 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
     * appointmentStatus 10  是否是预约暂存
     */
    if (type == 1 && appointmentStatus != 10) {
      return appointmentDom
    } else if (type == 1 && appointmentStatus == 10) {
      let meetHours = (
        <div className={classNames('eventBytemporaryStorage', styles.eventBytemporaryStorage)}>
          {diffMinutes >= 5 &&
          <div style={{ marginTop: '3px', display: 'flex' }}>
            {/* 姓名行 */}
            <LinesEllipsis className={styles.eventBytemporaryStoragePatientName} text={name} maxLine={1}
                           ellipsis='...'>{name}</LinesEllipsis>
            <div className={styles.TimeBox}>{startByFormat} - {endByFormat}</div>
          </div>
          }
          <div onClick={this.onClickShutDownByTemporaryStorage} className={classNames(styles.ShutDownBtn,'ShutDownBtn')}></div>
        </div>
      )
      return meetHours
    } else if (type == 2) {
      let {
        patientInfoDto,          // 患者信息
        isInsurance,             // 是否使用保险 0不使用 1使用
        complaintList,           // 主诉与治疗
        remark,                  // 预约备注
        doctorNameOfReferral,    // 转诊医生
        doctorNameOfAppointment, // 当前预约医生
        doctorIdOfConsultation,  // 当前预约的会诊医生
        presentIllness,          // 患者标签
        isConsultation,          // 是否携带会诊 1有 0没有
      } = consultationInfoOfTableDto || {}

      let {
        age,
        sex,
        sexDescribe,
        name,
        patientName,            // 患者名称
        appointmentDate,
        appointmentDateList,
        doctor,
        doctorName,
        isFirstVisit,     // 是否初复诊 1: 为初诊 2:复诊
      } = patientInfoDto || {}

      let sexText = sex == 1 ? '男' : '女'

      let meetHours = (
        <div className={classNames('eventElent', styles.eventElent)}>
          {diffMinutes >= 5 &&
          <div>
            {/*姓名行*/}
            <LinesEllipsis className={styles.patientName} text={name} maxLine={1} ellipsis='...'>{name}</LinesEllipsis>
            {type == 2 && <span className={styles.huizhenText}>会诊</span>}
          </div>
          }
          {diffMinutes < 5 &&
          <div>
            {type == 2 && <span className={styles.huizhenText}>会诊</span>}
          </div>
          }
        </div>
      )
      return meetHours
    } else if (type == 4) {
      // 个人占用时间
      let otherHoursDiv = (
        <div id={`event_${id}`}>
          {this.props.eventPersonalTimeClose && !(isDoctor && isDisOperationType) &&
          <div className={styles.closeWarp} onClick={(e) => {
            e.stopPropagation()
            this.props.eventPersonalTimeClose && this.props.eventPersonalTimeClose(event)
          }}><i className={styles.closeIcon}></i>
          </div>
          }
          <div className={classNames('eventElent', styles.eventElentOther)}>
            <LinesEllipsis text={titleInfo} maxLine={1} ellipsis='...' />
          </div>
        </div>)
      return otherHoursDiv
    } else if (type == 7) {
      // 走诊
      let otherHoursDiv = (
        <div id={`event_${id}`} className={classNames('eventElent', styles.eventElentOther, styles.evnetOffTextColor)}>
          <LinesEllipsis text={titleInfo} maxLine={1} ellipsis='...' />
        </div>)
      return otherHoursDiv
    } else {
      return otherHours
    }
  }

}


