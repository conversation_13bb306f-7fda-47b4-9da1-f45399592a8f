@import '~@/utils/utils.less';

.scrollBox{
  height: ~"calc(61vh + 5px)";
  min-height: 258px;
  overflow: hidden;
  border-bottom: 1px solid #ccc;
}

.scroll {
  overflow-x: auto;
  height: 100%;
  :global {
    html {
      /*隐藏滚动条，当IE下溢出，仍然可以滚动*/
      -ms-overflow-style:none;
      /*火狐下隐藏滚动条*/
      overflow:-moz-scrollbars-none;
    }

    /*Chrome下隐藏滚动条，溢出可以透明滚动*/
    html::-webkit-scrollbar{width:0px}
  }
}

.calendarBy60min {
  :global {
    .fc-time-grid .fc-slats td {
      height: 2.5em!important;
    }
  }
}

.calendarBy30min {
  :global {
    .fc-time-grid .fc-slats td {
      height: 3em!important;
    }
  }
}

.calendarBy15min {
  :global {
    .fc-time-grid .fc-slats td {
      height: 4.2em!important;
    }
  }
}


.calendar {
  :global {
    font-size: 14px;
    background: #fff;
    margin-top: -1px;
    .fc-widget-content:not(.fc-axis .fc-time) {
      cursor:pointer;
    }
    .fc-widget-content {
      border-bottom: none;
    }
    .fc-head {
      display:none;
    }
    .fc-ltr .fc-axis {
      vertical-align: middle;
      padding: 0 12px;
      white-space: nowrap;
      width: 41px!important;
    }

    /*.fc th, .fc td {
      border-style: solid;
      border-width: 1px;
      padding: 0;
      vertical-align: top;
    }*/

    .fc-time-grid .fc-slats td {
      height: 4.2em;
      //height: 2em;
      //border-color: #dddddd;
    }
    .fc-unthemed tbody {
      //background: #fff;
    }
    .fc-v-event{
      border: 1px solid #ccd;
      //min-height: 16px;
      overflow: unset;
    }

    .fc-view .fc-head-container tr {
      background: #E0E2E7;
      height: 60px;
      line-height: 60px;
      font-family: MicrosoftYaHei;
      font-weight: normal;
      border-bottom: 1px solid #000000;
      font-size: 14px;
      color: #444444;
    }

    .fc-body .fc-axis {
      background: #E0E2E7;
      padding: 0 13px;
      border-color: #ccc;
      box-sizing: content-box;
    }


    .fc-head .fc-widget-content {
      //background: #fff;
      //border-color: #ddd;
    }

    .fc-unthemed th,
    .fc-unthemed td,
    .fc-unthemed thead,
    .fc-unthemed tbody,
    .fc-unthemed .fc-divider,
    .fc-unthemed .fc-row,
    .fc-unthemed .fc-content,
    .fc-unthemed .fc-popover,
    .fc-unthemed .fc-list-view,
    .fc-unthemed .fc-list-heading td{
      border-color: #ccc;
    }

    .fc-unthemed  .fc-resource-cell {
      background: #ECECF1;
      font-weight: normal;
      font-size: 14px;
    }





    .doctorName {
      margin-left: 10px;
    }

    .onterEventTile {
      text-align: center;
    }

    .DoctorNameTr th {
      text-align: left;
      padding-left: 16px;
      font-weight: normal;
      font-size: 16px;
      font-family: "Microsoft YaHei";
    }

    .DoctorNameTr th.DoctorIconTr {
      padding: 0 12px;
      text-align: center;
    }

    .removeIcon {
      position: absolute;
      z-index: 99;
      right: -5px;
      top: -6px;
      width: 14px;
      height: 14px;
      display: inline-block;
      background-image: url('../../../../src/assets/calendar/guanbi.png');
      background-size: 14px 14px;
    }
    .manIcon {
      width: 12px;
      height: 12px;
      display: inline-block;

      background: url('../../../../src/assets/calendar/man.png');
    }
    .womanIcon {
      width: 8px;
      height: 14px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/woman.png');
    }
    .doctorIcon {
      width: 14px;
      height: 18px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/doctorIcon.png')
    }
    .bigDoctorIcon {
      width: 22px;
      height: 28px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/bigDoctorIcon.png')
    }
    .insuranceIcon {
      width: 18px;
      height: 18px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/InsuranceIcon.png');
    }
    .inIcon {
      width: 18px;
      height: 18px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/IN.png');
    }
    .goNextIcon {
      width: 22px;
      height: 22px;
      display: inline-block;
      position:relative;
      left:10px;
      vertical-align: text-bottom;
      background: url('../../../../src/assets/calendar/next.png');
      cursor: pointer;
    }

    .goDisableNextIcon {
      width: 22px;
      height: 22px;
      display: inline-block;
      position:relative;
      left:10px;
      vertical-align: text-bottom;
      background: url('../../../../src/assets/calendar/disableNext.png');
      cursor: no-drop;
    }

    .goLastIcon {
      width: 22px;
      height: 22px;
      position:relative;
      right: 10px;
      display: inline-block;
      vertical-align: text-bottom;
      transform:scaleX(-1);
      background: url('../../../../src/assets/calendar/next.png');
      cursor: pointer;
    }

    .goDisableLastIcon {
      width: 22px;
      height: 22px;
      position:relative;
      right: 10px;
      display: inline-block;
      vertical-align: text-bottom;
      transform:scaleX(-1);
      background: url('../../../../src/assets/calendar/disableNext.png');
      cursor: no-drop;
    }
    .labelTdsTitle{
      line-height: 0px;
      margin-top: 20px;
      font-weight: 400;
      color: #444444;
    }

    .titleLable {
      line-height:0px;
      margin-top: 20px;
      color: #444444;
      font-weight: 600;
    }

    .surplusBackgroundEvent {
      opacity: 1!important;
    }
    .inverseWorkbackground {
      opacity: 0.6!important;
    }

    .fc-event-container .fc-event{
      border-radius:10px;
      overflow: hidden;

    }

    .expirationTime.fc-bgevent{
      opacity:0.5;
      border-bottom: 2px solid #F43D3D;
    }

    .fc-event-container .fc-event.unset {
      overflow: unset;
    }

    .fc-event-container .fc-event.unset .antd-pro-components-agenda-caleandar-elements-event-element-patientEventicon{
      margin-top: -7px;
    }

    /*已结算*/
    .fc-event-container .fc-event.finishType:not(.unset):before {
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #9E9E9E;
    }
    /*已经迟到*/
    .fc-event-container .fc-event.lateType:not(.unset):before {
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #F28A19;
    }
    /*未结算 未迟到 未到诊*/
    .fc-event-container .fc-event.notClinicType:not(.unset):before {
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #27AE6B;
    }
    /*未结算 未迟到 已经到诊*/
    .fc-event-container .fc-event.comeClinicType:not(.unset):before {
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #2796F3;
    }
    /*已接诊*/
    .fc-event-container .fc-event.consultationVisitType:not(.unset):before {
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #909E33;
    }

    /*已爽约*/
    .fc-event-container .fc-event.shuangAboutType:before{
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #C5497D;
    }

    /*搜索到的event*/
    .fc-event-container .fc-event.checkedEvent{
      border:2px solid blue!important;
    }

    .eventOtherEvent{
      border-radius: 10px;
      border: 1px solid #5B6AFF;
      margin: 1px;
      text-align: center;
      opacity: 1;
    }

    .otherWalkingTime.fc-bgevent{
      border-radius: 10px;
      border: 1px solid #5B6AFF;
      margin: 1px;
      text-align: center;
      opacity: 1;
    }

    .checkedBackEvent.fc-bgevent {
      border:2px solid blue!important;
      opacity: 1;
    }

    /*6: 工作时间*/
    /*.fc-event-container .fc-event.officeHoursClass:before{
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: rgba(255,0,0,.3);
    }*/

    /*.fc-event-container .fc-v-event.eventTypeClass:hover {
      border-color: #22ff22!important;
    }*/
  }
}

/*.popperContentArrows:after {
  content:'';
  position:absolute;
  //height:20px;
  //width:20px;
  //background:yellow;
  left: 300px;
  top: 1px;
  //border:10px solid gray;
  border:10px solid transparent;
  //border-top-color:gray,
  border-left-color:gray
}*/




.popperContent{
  z-index: 99;
  position: relative;
  background: #ffc107;
  color: black;
  width: 300px;
  border-radius: 3px;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
  text-align: center;

  :global {
    [x-arrow] {
      position: absolute;
      /* left: -8px;
       border-right: 8px solid blue;
       border-top: 8px solid transparent;
       border-bottom: 8px solid transparent;
       */

      border:10px solid transparent;
      border-left-color:gray
    }
  }
}

.scrollPhoneBox {
  height: calc(100vh - 211px);
  min-height: 419px;
  overflow: hidden;
  border-bottom: 1px solid #ccc;
}
