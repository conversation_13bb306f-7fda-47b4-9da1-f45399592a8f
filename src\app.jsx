import * as Sentry from "@sentry/react";
import { Integrations } from '@sentry/tracing';

/* ---------------设置sentry---------------- */
if (process.env.NODE_ENV === 'production') {
  Sentry.init({
    dsn: 'https://<EMAIL>/24',
    environment: process.env.NODE_ENV,
    integrations: [new Integrations.BrowserTracing(),new Sentry.Replay()],
    tracesSampleRate: 1.0,
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
    tracePropagationTargets:['https://emr.friday.tech']
  });
  const userId = localStorage.getItem("userId")?localStorage.getItem("userId"):""; // 用户Id
  const userNameId = localStorage.getItem("token_username")?localStorage.getItem("token_username"):""; // 帐户Id
  const userName = localStorage.getItem("userName")?localStorage.getItem("userName"):""; // 用户名称
  const tenantId = localStorage.getItem("tenantId")?localStorage.getItem("tenantId"):""; // 租户Id
  const organizationId = localStorage.getItem('organizationId') ? localStorage.getItem('organizationId') : ''; // 机构Id
  Sentry.configureScope((scope) => {
    scope.setUser({
      id: userId,
      userNameId,
      userName,
      tenantId,
      organizationId
    });
  });
}
