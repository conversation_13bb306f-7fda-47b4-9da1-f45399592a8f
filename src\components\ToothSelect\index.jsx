import React, {Component} from 'react';
import {GridContent} from '@ant-design/pro-layout';
import styles from './style.less';//样式
import {Button, Modal, Tag} from "antd";
import {toothUtils} from "@/utils/toothUtils";//牙面选择工具类
import {StringUtils} from "@/utils/StringUtils";

//牙位选择快捷按钮
const arrTag = [
  {id: '1', name: '乳牙'},
  {id: '2', name: '全口'},
  {id: '3', name: '左上'},
  {id: '4', name: '左下'},
  {id: '5', name: '右上'},
  {id: '6', name: '右下'},
  {id: '7', name: '上半口'},
  {id: '8', name: '下半口'},
  {id: '9', name: '清除'},
];
//恒牙面
const arrCircle = [
  {index: '8', num: '8', states: 'false'},
  {index: '7', num: '7', states: 'false'},
  {index: '6', num: '6', states: 'false'},
  {index: '5', num: '5', states: 'false'},
  {index: '4', num: '4', states: 'false'},
  {index: '3', num: '3', states: 'false'},
  {index: '2', num: '2', states: 'false'},
  {index: '1', num: '1', states: 'false'},
];
//牙面
const toothSurface = [
  {index: 8,  mesial: 'M', bistal: 'D', occlusalf: 'O', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 7,  mesial: 'M', bistal: 'D', occlusalf: 'O', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 6,  mesial: 'M', bistal: 'D', occlusalf: 'O', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 5, mesial: 'M', bistal: 'D', occlusalf: 'O', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 4, mesial: 'M', bistal: 'D', occlusalf: 'O', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 3, mesial: 'M', bistal: 'D', occlusalf: 'I', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 2, mesial: 'M', bistal: 'D', occlusalf: 'I', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 1, mesial: 'M', bistal: 'D', occlusalf: 'I', buccal: 'B', lingual: 'P',lingualer:'L'},
];
//牙面
const toothSurfaceReverse = [
  {index: 1,  mesial: 'M', bistal: 'D', occlusalf: 'I', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 2,  mesial: 'M', bistal: 'D', occlusalf: 'I', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 3,  mesial: 'M', bistal: 'D', occlusalf: 'I', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 4, mesial: 'M', bistal: 'D', occlusalf: 'O', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 5, mesial: 'M', bistal: 'D', occlusalf: 'O', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 6, mesial: 'M', bistal: 'D', occlusalf: 'O', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 7, mesial: 'M', bistal: 'D', occlusalf: 'O', buccal: 'B', lingual: 'P',lingualer:'L'},
  {index: 8, mesial: 'M', bistal: 'D', occlusalf: 'O', buccal: 'B', lingual: 'P',lingualer:'L'},
];
//恒牙面
const arrCircleNum = [
  {index: '1', num: '1', states: 'false'},
  {index: '2', num: '2', states: 'false'},
  {index: '3', num: '3', states: 'false'},
  {index: '4', num: '4', states: 'false'},
  {index: '5', num: '5', states: 'false'},
  {index: '6', num: '6', states: 'false'},
  {index: '7', num: '7', states: 'false'},
  {index: '8', num: '8', states: 'false'},
];
//乳牙面
const arrEnglist = [
  {index: '5', letter: 'E'},
  {index: '4', letter: 'D'},
  {index: '3', letter: 'C'},
  {index: '2', letter: 'B'},
  {index: '1', letter: 'A'}
];
//乳牙面
const arrEnglistOrder = [
  {index: '1', letter: 'A'},
  {index: '2', letter: 'B'},
  {index: '3', letter: 'C'},
  {index: '4', letter: 'D'},
  {index: '5', letter: 'E'}
];
//牙位取值下标对应
const quzhi = {
  A: 1,
  B: 2,
  C: 3,
  D: 4,
  E: 5,
  8: 8,
  7: 7,
  6: 6,
  5: 5,
  4: 4,
  3: 3,
  2: 2,
  1: 1,
}
//牙位牙面页面
class ToothSelect extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tooth: {//牙位
        englishVal: [],//按钮
        englishValTopRight: [],//右上
        englishValBotLeft: [],//左下
        englishValBotRight: [],//左下
      },
      toothSelect: {//牙位选择
        englishVal1: [],//左上
        englishValTopRight1: [],//右上
        englishValBotLeft1: [],//左下
        englishValBotRight1: [],//右下
        englishVal2: [],//左上
        englishValTopRight2: [],//右上
        englishValBotLeft2: [],//左下
        englishValBotRight2: [],//右下
        englishVal3: [{},{M:false,D:false,O:false,B:false,P:false},//牙面
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false}],//左上牙面是否选中
        englishValTopRight3: [{},{M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false}],//右上牙面是否选中
        englishValBotLeft3: [{},{M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false}],//左下牙面是否选中
        englishValBotRight3: [{},{M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false},
          {M:false,D:false,O:false,B:false,P:false}]//右下牙面是否选中
      },
      toothDesc:{
        ToothPosition:null,//牙位
        BSelected:false,//颊侧
        LSelected:false,//舌侧
        MSelected:false,//近中
        DSelected:false,//远中
        OSelected:false,//颌面
      },
    }
  }
  //初始化
  componentDidMount() {
    this.props.onRef(this);

    let toothPosition = this.props.toothPosition;

    var p = /[a-z]/i;
    let check = {
      englishVal:[],
      englishValTopRight:[],
      englishValBotLeft:[],
      englishValBotRight:[],
    }
    let select = {
      englishVal1:[],
      englishValTopRight1:[],
      englishValBotLeft1:[],
      englishValBotRight1:[],
      englishVal2:[],
      englishValTopRight2:[],
      englishValBotLeft2:[],
      englishValBotRight2:[],
      englishVal3: [{},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false}],

      englishValTopRight3:[{},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false}],
      englishValBotLeft3: [{},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false}],
      englishValBotRight3: [{},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false},
        {'M':false,'D':false,'O':false,'B':false,'P':false}]
    };
    if(toothPosition) {
      let toothArr = toothPosition.trim().split(";");
      for(let t of toothArr){

        let toothArr1 = t.trim().split(" ");
        let str =  t[0];
        let surface;
        if(toothArr1.length===2){//此牙位拥有牙面
          surface = toothArr1[1];
        }

        if(str==="1"){

          if(p.test(t[1])){
            select.englishVal2[quzhi[t[1]]] = true;
            check.englishVal[quzhi[t[1]]] = t.substring(1,t.length);
          }else{
            select.englishVal1[quzhi[t[1]]] = true;
            check.englishVal[t[1]] = t.substring(1,t.length);
          }

          if(surface){
            for(let s of surface){
              select.englishVal3[quzhi[t[1]]][s] = true;
            }
          }


        }else if(str==="2"){

          if(p.test(t[1])){
            select.englishValTopRight2[quzhi[t[1]]] = true;
            check.englishValTopRight[quzhi[t[1]]]= t.substring(1,t.length);
          }else{
            select.englishValTopRight1[quzhi[t[1]]] = true;
            check.englishValTopRight[t[1]] =t.substring(1,t.length);
          }
          if(surface){
            for(let s of surface){
              select.englishValTopRight3[quzhi[t[1]]][s] = true;
            }
          }
        }else if(str==="4"){

          if(p.test(t[1])){
            select.englishValBotLeft2[quzhi[t[1]]] = true;
            check.englishValBotLeft[quzhi[t[1]]] = t.substring(1,t.length);
          }else{
            select.englishValBotLeft1[quzhi[t[1]]] = true;
            check.englishValBotLeft[t[1]] = t.substring(1,t.length);
          }
          if(surface){
            for(let s of surface){
              select.englishValBotLeft3[quzhi[t[1]]][s] = true;
            }
          }
        }else if(str==="3"){

          if(p.test(t[1])){
            check.englishValBotRight[quzhi[t[1]]] = t.substring(1,t.length);
            select.englishValBotRight2[quzhi[t[1]]] = true;
          }else{
            select.englishValBotRight1[quzhi[t[1]]] = true;
            check.englishValBotRight[t[1]] = t.substring(1,t.length);
          }
          if(surface){
            for(let s of surface){
              select.englishValBotRight3[quzhi[t[1]]][s] = true;
            }
          }
        }
      }
    }

    this.state.tooth = check;
    this.setState({
      toothSelect:select
    })
  }
  //获取牙位数据
  getTooth(){
    let e = this.state.tooth;
    let tl=1+e.englishVal.filter(i=>i&&i.trim()).join(";1");
    let tr=2+e.englishValTopRight.filter(i=>i&&i.trim()).join(";2");
    let bl=4+e.englishValBotLeft.filter(i=>i&&i.trim()).join(";4");
    let br=3+e.englishValBotRight.filter(i=>i&&i.trim()).join(";3");
    return (e.englishVal.length>0?tl.substr(0,tl.length)+";":"")+
      (e.englishValTopRight.length>0?tr.substr(0,tr.length)+";":"")+
      (e.englishValBotLeft.length>0?bl.substr(0,bl.length)+";":"")+
      (e.englishValBotRight.length>0?br.substr(0,br.length):"");
  }


  //恒牙点击事件
  btnClickNum = (e, uid, item, o,) => {
    let result = {};
    if(o!=true){
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishVal",uid,item.num,1);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishVal",uid,item.num,1);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });

  };
  //乳牙点击事件
  btnClickLetter = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishVal",index,item.letter,2);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishVal",index,item.letter,2);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });

  };
  //右侧恒牙牙位点击事件
  btnClickRightNum = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishValTopRight",index,item.num,1);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishValTopRight",index,item.num,1);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });

  };
  //左侧乳牙点击事件
  btnClickRightLetter = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishValTopRight",index,item.letter,2);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishValTopRight",index,item.letter,2);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });


  };
  //右侧乳牙点击事件
  btnClickLeftLetter = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishValBotLeft",index,item.letter,2);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishValBotLeft",index,item.letter,2);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });

  };
  //左侧恒牙点击事件
  btnClickLeftNum = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishValBotLeft",index,item.num,1);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishValBotLeft",index,item.num,1);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });
  };
  //右下乳牙点击事件
  btnClickRightBotLetter = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishValBotRight",index,item.letter,2);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishValBotRight",index,item.letter,2);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });

  };
  //右下恒牙点击事件
  btnClickRightBotNum = (e, index, item,o) => {
    let result = {};
    if(o!=true) {
      result = toothUtils.selectTooth(this.state.tooth,this.state.toothSelect,"englishValBotRight",index,item.num,1);
    }else{
      result = toothUtils.noSelectTooth(this.state.tooth,this.state.toothSelect,"englishValBotRight",index,item.num,1);
    }
    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });
  }
  //重置牙面选择按钮左上，右上，左下，右下，全口等等
  tagClickWmDisp = (e, id, item) => {
    let key = this.state.key;
    let index = this.state.index;
    this.setState({
      hoverIndex: id,
    });

    if (id == 1) {
      this.state.tooth.englishVal = [];//右上
      this.state.tooth.englishValTopRight = [];//右上
      this.state.tooth.englishValBotLeft = [];//左下
      this.state.tooth.englishValBotRight = [];//右下
      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal[arrEnglist[i].index] = arrEnglist[i].letter;
        this.state.tooth.englishValBotLeft[arrEnglist[i].index] = arrEnglist[i].letter;
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=true;//左上
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=true;//左下
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight[arrEnglistOrder[i].index] = arrEnglistOrder[i].letter;
        this.state.tooth.englishValBotRight[arrEnglistOrder[i].index] = arrEnglistOrder[i].letter
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=true;//右上
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=true;//右下
      }

      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishVal.push();//左上8-1
        this.state.tooth.englishValBotLeft.push();//左下8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=false;//左上
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=false;//左下
      }
      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValTopRight.push();//右上
        this.state.tooth.englishValBotRight.push()//右下
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=false;//右上
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=false;//右下
      }

      for(let i=0;i<toothSurface.length;i++){
        this.state.toothSelect.englishVal3[toothSurface[i].index]={};//左上
        this.state.toothSelect.englishValBotLeft3[toothSurface[i].index]={};//左下
      }
      for(let i=0;i<toothSurfaceReverse.length;i++){
        this.state.toothSelect.englishValTopRight3[toothSurfaceReverse[i].index]={};//右上
        this.state.toothSelect.englishValBotRight3[toothSurfaceReverse[i].index]={};//右下
      }
      this.setState({
        tooth: this.state.tooth,//牙位
        toothSelect: this.state.toothSelect//牙位选择
      })
    } else if (id == 2) {
      this.state.tooth.englishVal = [];//左上
      this.state.tooth.englishValTopRight = [];//右上
      this.state.tooth.englishValBotLeft = []//左下
      this.state.tooth.englishValBotRight = []//右下

      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishVal[arrCircle[i].index] = arrCircle[i].num;//左上8-1
        this.state.tooth.englishValBotLeft[arrCircle[i].index] = arrCircle[i].num;//左下8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=true;//左上
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=true;//左下
      }
      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValTopRight[arrCircleNum[i].index] = arrCircleNum[i].num;//右上
        this.state.tooth.englishValBotRight[arrCircleNum[i].index] = arrCircleNum[i].num;//右下
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=true;//右上
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=true;//右下
      }


      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;//左上
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;//左下
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push();
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;//右上
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;//右下
      }

      for(let i=0;i<toothSurface.length;i++){
        this.state.toothSelect.englishVal3[toothSurface[i].index]={};
        this.state.toothSelect.englishValBotLeft3[toothSurface[i].index]={};
      }
      for(let i=0;i<toothSurfaceReverse.length;i++){
        this.state.toothSelect.englishValTopRight3[toothSurfaceReverse[i].index]={};
        this.state.toothSelect.englishValBotRight3[toothSurfaceReverse[i].index]={};
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })
    } else if (id == 3) {
      this.state.tooth.englishVal = [];
      this.state.tooth.englishValTopRight = [];
      this.state.tooth.englishValBotLeft = []
      this.state.tooth.englishValBotRight = []

      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValTopRight[arrCircleNum[i].index] = arrCircleNum[i].num;//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=true;//右上
        this.state.tooth.englishValBotRight.push()//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=false;//右下
      }
      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishVal.push();//左上8-1
        this.state.tooth.englishValBotLeft.push();//左下8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=false;//左上
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=false;//左下
      }


      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }


      for(let i=0;i<toothSurface.length;i++){
        this.state.toothSelect.englishVal3[toothSurface[i].index]={};
        this.state.toothSelect.englishValBotLeft3[toothSurface[i].index]={};
      }
      for(let i=0;i<toothSurfaceReverse.length;i++){
        this.state.toothSelect.englishValTopRight3[toothSurfaceReverse[i].index]={};
        this.state.toothSelect.englishValBotRight3[toothSurfaceReverse[i].index]={};
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })

    } else if (id == 4) {
      this.state.tooth.englishVal = [];
      this.state.tooth.englishValTopRight = [];
      this.state.tooth.englishValBotLeft = []
      this.state.tooth.englishValBotRight = []

      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValBotRight[arrCircleNum[i].index] = arrCircleNum[i].num;//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=true;//右下
        this.state.tooth.englishValTopRight.push();//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=false;//右上
      }
      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }

      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishVal.push();//左上8-1
        this.state.tooth.englishValBotLeft.push();//左下8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=false;//左上
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=false;//左下
      }


      for(let i=0;i<toothSurface.length;i++){
        this.state.toothSelect.englishVal3[toothSurface[i].index]={};
        this.state.toothSelect.englishValBotLeft3[toothSurface[i].index]={};
      }
      for(let i=0;i<toothSurfaceReverse.length;i++){
        this.state.toothSelect.englishValTopRight3[toothSurfaceReverse[i].index]={};
        this.state.toothSelect.englishValBotRight3[toothSurfaceReverse[i].index]={};
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })

    } else if (id == 5) {
      this.state.tooth.englishVal = [];
      this.state.tooth.englishValTopRight = [];
      this.state.tooth.englishValBotLeft = []
      this.state.tooth.englishValBotRight = []
      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishVal[arrCircle[i].index] = arrCircle[i].num;//左上8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=true;//左上
        this.state.tooth.englishValBotLeft.push();//左下8-1
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=false;//左下
      }

      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValBotRight.push()//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=false;//右下
        this.state.tooth.englishValTopRight.push();//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=false;//右上
      }
      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }


      for(let i=0;i<toothSurface.length;i++){
        this.state.toothSelect.englishVal3[toothSurface[i].index]={};
        this.state.toothSelect.englishValBotLeft3[toothSurface[i].index]={};
      }
      for(let i=0;i<toothSurfaceReverse.length;i++){
        this.state.toothSelect.englishValTopRight3[toothSurfaceReverse[i].index]={};
        this.state.toothSelect.englishValBotRight3[toothSurfaceReverse[i].index]={};
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })

    } else if (id == 6) {
      this.state.tooth.englishVal = [];
      this.state.tooth.englishValTopRight = [];
      this.state.tooth.englishValBotLeft = []
      this.state.tooth.englishValBotRight = []
      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishValBotLeft[arrCircle[i].index] = arrCircle[i].num;//左下8-1
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=true;//左下
        this.state.tooth.englishVal.push();//左上8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=false;//左上
      }

      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValBotRight.push()//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=false;//右下
        this.state.tooth.englishValTopRight.push();//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=false;//右上
      }
      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }


      for(let i=0;i<toothSurface.length;i++){
        this.state.toothSelect.englishVal3[toothSurface[i].index]={};
        this.state.toothSelect.englishValBotLeft3[toothSurface[i].index]={};
      }
      for(let i=0;i<toothSurfaceReverse.length;i++){
        this.state.toothSelect.englishValTopRight3[toothSurfaceReverse[i].index]={};
        this.state.toothSelect.englishValBotRight3[toothSurfaceReverse[i].index]={};
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })

    } else if (id == 7) {
      this.state.tooth.englishVal = [];
      this.state.tooth.englishValTopRight = [];
      this.state.tooth.englishValBotLeft = []
      this.state.tooth.englishValBotRight = []

      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishVal[arrCircle[i].index] = arrCircle[i].num;//左上8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=true;//左上
        this.state.tooth.englishValBotLeft.push();//左下8-1
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=false;//左下
      }
      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValTopRight[arrCircleNum[i].index] = arrCircleNum[i].num;//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=true;//右上
        this.state.tooth.englishValBotRight.push()//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=false;//右下
      }

      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }


      for(let i=0;i<toothSurface.length;i++){
        this.state.toothSelect.englishVal3[toothSurface[i].index]={};
        this.state.toothSelect.englishValBotLeft3[toothSurface[i].index]={};
      }
      for(let i=0;i<toothSurfaceReverse.length;i++){
        this.state.toothSelect.englishValTopRight3[toothSurfaceReverse[i].index]={};
        this.state.toothSelect.englishValBotRight3[toothSurfaceReverse[i].index]={};
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })

    } else if (id == 8) {
      this.state.tooth.englishVal = [];
      this.state.tooth.englishValTopRight = [];
      this.state.tooth.englishValBotLeft = [];
      this.state.tooth.englishValBotRight = [];
      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishValBotLeft[arrCircle[i].index] = arrCircle[i].num;//左下8-1
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=true;//左下
        this.state.tooth.englishVal.push();//左上8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=false;//左上
      }
      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValBotRight[arrCircleNum[i].index] = arrCircleNum[i].num;//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=true;//右下
        this.state.tooth.englishValTopRight.push();//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=false;//右上
      }

      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }


      for(let i=0;i<toothSurface.length;i++){
        this.state.toothSelect.englishVal3[toothSurface[i].index]={};
        this.state.toothSelect.englishValBotLeft3[toothSurface[i].index]={};
      }
      for(let i=0;i<toothSurfaceReverse.length;i++){
        this.state.toothSelect.englishValTopRight3[toothSurfaceReverse[i].index]={};
        this.state.toothSelect.englishValBotRight3[toothSurfaceReverse[i].index]={};
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })

    } else if (id == 9) {
      this.state.tooth.englishVal = [];
      this.state.tooth.englishValTopRight = [];
      this.state.tooth.englishValBotLeft = [];
      this.state.tooth.englishValBotRight = [];
      this.state.toothSelect.englishValTopRight3=[];
      for(let i=0;i<arrCircle.length;i++){
        this.state.tooth.englishValBotLeft.push();//左下8-1
        this.state.toothSelect.englishValBotLeft1[arrCircle[i].index]=false;//左下
        this.state.tooth.englishVal.push();//左上8-1
        this.state.toothSelect.englishVal1[arrCircle[i].index]=false;//左上
      }
      for(let i=0;i<arrCircleNum.length;i++){
        this.state.tooth.englishValBotRight.push()//右下
        this.state.toothSelect.englishValBotRight1[arrCircleNum[i].index]=false;//右下
        this.state.tooth.englishValTopRight.push();//右上
        this.state.toothSelect.englishValTopRight1[arrCircleNum[i].index]=false;//右上
      }

      for (let i = 0; i < arrEnglist.length; i++) {
        this.state.tooth.englishVal.push();
        this.state.tooth.englishValBotLeft.push();
        this.state.toothSelect.englishVal2[arrEnglist[i].index]=false;
        this.state.toothSelect.englishValBotLeft2[arrEnglist[i].index]=false;
      }
      for (let i = 0; i < arrEnglistOrder.length; i++) {
        this.state.tooth.englishValTopRight.push();
        this.state.tooth.englishValBotRight.push()
        this.state.toothSelect.englishValTopRight2[arrEnglistOrder[i].index]=false;
        this.state.toothSelect.englishValBotRight2[arrEnglistOrder[i].index]=false;
      }

      for(let i=0;i<toothSurface.length;i++){
        this.state.toothSelect.englishVal3[toothSurface[i].index]={};
        this.state.toothSelect.englishValBotLeft3[toothSurface[i].index]={};
      }
      for(let i=0;i<toothSurfaceReverse.length;i++){
        this.state.toothSelect.englishValTopRight3[toothSurfaceReverse[i].index]={};
        this.state.toothSelect.englishValBotRight3[toothSurfaceReverse[i].index]={};
      }
      this.setState({
        tooth: this.state.tooth,
        toothSelect: this.state.toothSelect
      })
    }

  };
  //左上牙面点击事件
  surfaceClickTopLeft(name,index,o){
    let result = {};
    if(o!=true) {
      result = toothUtils.selectToothSurface(this.state.tooth,this.state.toothSelect,"englishVal",index,name);
    }else{
      result = toothUtils.noSelectToothSurface(this.state.tooth,this.state.toothSelect,"englishVal",index,name);
    }

    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });

  }
  //左下牙面点击事件
  surfaceClickBotLeft(name,index,o){
    let result = {};
    if(o!=true) {
      result = toothUtils.selectToothSurface(this.state.tooth,this.state.toothSelect,"englishValBotLeft",index,name);
    }else{
      result = toothUtils.noSelectToothSurface(this.state.tooth,this.state.toothSelect,"englishValBotLeft",index,name);
    }

    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });


  }
  //右上牙面点击事件
  surfaceClickTopRight(name,index,o){
    let result = {};
    if(o!=true) {
      result = toothUtils.selectToothSurface(this.state.tooth,this.state.toothSelect,"englishValTopRight",index,name);
    }else{
      result = toothUtils.noSelectToothSurface(this.state.tooth,this.state.toothSelect,"englishValTopRight",index,name);
    }

    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });
  }
  //右下牙面点击事件
  surfaceClickBotRight(name,index,o){
    let result = {};
    if(o!=true) {
      result = toothUtils.selectToothSurface(this.state.tooth,this.state.toothSelect,"englishValBotRight",index,name);
    }else{
      result = toothUtils.noSelectToothSurface(this.state.tooth,this.state.toothSelect,"englishValBotRight",index,name);
    }

    this.state.tooth = result.tooth;
    return this.setState({
      toothSelect: result.toothSelect,
    });
  }

  render() {
    const {toothSelect} = this.state;
    return (
      <GridContent>
        <div className={styles.tooth_top}>
          {
            arrTag.map((item, index) => {
              return <Tag
                className={[this.state.hoverIndex === item.id ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                onClick={(e) => this.tagClickWmDisp(e, item.id, item)} id="tagwm"
                key={index}>{item.name}</Tag>
            })
          }
        </div>
        <div className={styles.tableAll}>
          <table className={styles.table}>
            <tbody>
            <tr>
              <td className={styles.td}
              >
                <div >
                  <div className={styles.btn_tooth} id="tagColor2" style={{paddingLeft:6}}>
                    {
                      arrCircle.map((item, index) => {
                        return <Button
                          className={[this.state.toothSelect.englishVal1[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                          type="" shape="circle" key={index}
                          onClick={(e) => this.btnClickNum(e, item.index, item,this.state.toothSelect.englishVal1[item.index])}
                        >{item.num}</Button>
                      })
                    }
                  </div>
                  <div className={styles.btn_tooth_Eng} id="babyTeethE2" style={{paddingRight:3}}>
                    {
                      arrEnglist.map((item, index) => {
                        return <Button
                          className={[this.state.toothSelect.englishVal2[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                          type="" shape="circle" key={index}
                          onClick={(e) => this.btnClickLetter(e, item.index, item,this.state.toothSelect.englishVal2[item.index])}
                        >{item.letter}</Button>
                      })
                    }
                  </div>
                  <div className={styles.td_box}>
                    {
                      toothSurface.map((item, i) => {
                        return <div className={styles.btn_box} key={i}>
                          {/*B*/}
                          <div className={[toothSelect.englishVal3[item.index][item.buccal]?(styles.tooth_box1_hover):(styles.tooth_box1)].join(' ')}
                               onClick={()=>this.surfaceClickTopLeft(item.buccal,item.index,toothSelect.englishVal3[item.index][item.buccal])}><span>{item.buccal}</span></div>
                          <div className={styles.tooth_box1_s}></div>
                          {/*D*/}
                          <div className={[toothSelect.englishVal3[item.index][item.bistal]?(styles.tooth_box2_hover):(styles.tooth_box2)].join(' ')}
                               onClick={()=>this.surfaceClickTopLeft(item.bistal,item.index,toothSelect.englishVal3[item.index][item.bistal])}><span>{item.bistal}</span></div>
                          <div className={styles.tooth_box2_s}></div>
                          {/*O*/}
                          <div className={[toothSelect.englishVal3[item.index][item.occlusalf]?(styles.tooth_box3_hover):(styles.tooth_box3)].join(' ')}
                               onClick={()=>this.surfaceClickTopLeft(item.occlusalf,item.index,toothSelect.englishVal3[item.index][item.occlusalf])}><span>{item.occlusalf}</span></div>
                          {/*P*/}
                          <div className={[toothSelect.englishVal3[item.index][item.lingual]?(styles.tooth_box4_hover):(styles.tooth_box4)].join(' ')}
                               onClick={()=>this.surfaceClickTopLeft(item.lingual,item.index,toothSelect.englishVal3[item.index][item.lingual])}><span>{item.lingual}</span></div>
                          <div className={styles.tooth_box4_s}></div>
                          {/*M*/}
                          <div className={[toothSelect.englishVal3[item.index][item.mesial]?(styles.tooth_box5_hover):(styles.tooth_box5)].join(' ')}
                               onClick={()=>this.surfaceClickTopLeft(item.mesial,item.index,toothSelect.englishVal3[item.index][item.mesial])}><span>{item.mesial}</span></div>
                          <div className={styles.tooth_box5_s}></div>
                        </div>
                      })
                    }
                  </div>
                </div>
              </td>
              <td>
                <div className={styles.btn_tooth} id="tagColor22"  style={{paddingLeft:6}}>
                  {
                    arrCircleNum.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishValTopRight1[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickRightNum(e, item.index, item,this.state.toothSelect.englishValTopRight1[item.index] )}
                      >{item.num}</Button>
                    })
                  }
                </div>
                <div className={styles.btn_tooth_Eng_left} id="babyTeethOrder2" style={{paddingLeft:7}}>
                  {
                    arrEnglistOrder.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishValTopRight2[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickRightLetter(e, item.index, item,this.state.toothSelect.englishValTopRight2[item.index])}
                      >{item.letter}</Button>
                    })
                  }
                </div>
                <div className={styles.td_box} style={{marginLeft: 1.5}}>
                  {
                    toothSurfaceReverse.map((item, i) => {
                      return <div className={styles.btn_box} key={i}>
                        {/*B*/}
                        <div className={[toothSelect.englishValTopRight3[item.index][item.buccal]?(styles.tooth_box1_hover):(styles.tooth_box1)].join(' ')}
                             onClick={()=>this.surfaceClickTopRight(item.buccal,item.index,this.state.toothSelect.englishValTopRight3[item.index][item.buccal])}
                        ><span>{item.buccal}</span></div>

                        <div className={styles.tooth_box1_s}></div>
                        {/*P*/}
                        <div className={[toothSelect.englishValTopRight3[item.index][item.mesial]?(styles.tooth_box2_hover):(styles.tooth_box2)].join(' ')}
                             onClick={()=>this.surfaceClickTopRight(item.mesial,item.index,toothSelect.englishValTopRight3[item.index][item.mesial])}><span>{item.mesial}</span></div>
                        <div className={styles.tooth_box2_s}></div>
                        {/*O*/}
                        <div className={[toothSelect.englishValTopRight3[item.index][item.occlusalf]?(styles.tooth_box3_hover):(styles.tooth_box3)].join(' ')}
                             onClick={()=>this.surfaceClickTopRight(item.occlusalf,item.index,toothSelect.englishValTopRight3[item.index][item.occlusalf])}><span>{item.occlusalf}</span></div>
                        {/*M*/}
                        <div className={[toothSelect.englishValTopRight3[item.index][item.lingual]?(styles.tooth_box4_hover):(styles.tooth_box4)].join(' ')}
                             onClick={()=>this.surfaceClickTopRight(item.lingual,item.index,toothSelect.englishValTopRight3[item.index][item.lingual])}><span>{item.lingual}</span></div>
                        <div className={styles.tooth_box4_s}></div>
                        {/*D*/}
                        <div className={[toothSelect.englishValTopRight3[item.index][item.bistal]?(styles.tooth_box5_hover):(styles.tooth_box5)].join(' ')}
                             onClick={()=>this.surfaceClickTopRight(item.bistal,item.index,toothSelect.englishValTopRight3[item.index][item.bistal])}><span>{item.bistal}</span></div>
                        <div className={styles.tooth_box5_s}></div>
                      </div>
                    })
                  }

                </div>
              </td>
            </tr>
            </tbody>
            <tbody>
            <tr>
              <td>
                <div className={styles.td_box}>
                  {
                    toothSurface.map((item, i) => {
                      return <div className={styles.btn_box} key={i}>
                        {/*L*/}
                        <div className={[toothSelect.englishValBotLeft3[item.index][item.lingualer]?(styles.tooth_box1_hover):(styles.tooth_box1)].join(' ')}
                             onClick={()=>this.surfaceClickBotLeft(item.lingualer,item.index,toothSelect.englishValBotLeft3[item.index][item.lingualer])}
                        ><span>{item.lingualer}</span></div>
                        <div className={styles.tooth_box1_s}></div>
                        {/*D*/}
                        <div className={[toothSelect.englishValBotLeft3[item.index][item.bistal]?(styles.tooth_box2_hover):(styles.tooth_box2)].join(' ')}
                             onClick={()=>this.surfaceClickBotLeft(item.bistal,item.index,toothSelect.englishValBotLeft3[item.index][item.bistal])}><span>{item.bistal}</span></div>
                        <div className={styles.tooth_box2_s}></div>
                        {/*O*/}
                        <div className={[toothSelect.englishValBotLeft3[item.index][item.occlusalf]?(styles.tooth_box3_hover):(styles.tooth_box3)].join(' ')}
                             onClick={()=> this.surfaceClickBotLeft(item.occlusalf,item.index,toothSelect.englishValBotLeft3[item.index][item.occlusalf])}><span>{item.occlusalf}</span></div>
                        {/*B*/}
                        <div className={[toothSelect.englishValBotLeft3[item.index][item.buccal]?(styles.tooth_box4_hover):(styles.tooth_box4)].join(' ')}
                             onClick={() => this.surfaceClickBotLeft(item.buccal,item.index,toothSelect.englishValBotLeft3[item.index][item.buccal])}><span>{item.buccal}</span></div>
                        <div className={styles.tooth_box4_s}></div>
                        {/*M*/}
                        <div className={[toothSelect.englishValBotLeft3[item.index][item.mesial]?(styles.tooth_box5_hover):(styles.tooth_box5)].join(' ')}
                             onClick={() => this.surfaceClickBotLeft(item.mesial,item.index,toothSelect.englishValBotLeft3[item.index][item.mesial])}><span>{item.mesial}</span></div>
                        <div className={styles.tooth_box5_s}></div>
                      </div>
                    })
                  }

                </div>
                <div className={styles.btn_tooth_Eng} id="babyTeethBot2" style={{paddingRight:4}}>
                  {
                    arrEnglist.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishValBotLeft2[item.index]? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickLeftLetter(e, item.index, item,this.state.toothSelect.englishValBotLeft2[item.index])}
                      >{item.letter}</Button>
                    })
                  }
                </div>
                <div className={styles.btn_tooth} id="tagColor32" style={{paddingLeft:5}}>
                  {
                    arrCircle.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishValBotLeft1[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickLeftNum(e, item.index, item,this.state.toothSelect.englishValBotLeft1[item.index])}
                      >{item.num}</Button>
                    })
                  }
                </div>

              </td>
              <td className={styles.bottom_td}>
                <div className={styles.td_box} style={{marginLeft: 1.5}}>
                  {
                    toothSurfaceReverse.map((item, i) => {
                      return <div className={styles.btn_box} key={i}>
                        {/*L*/}
                        <div className={[toothSelect.englishValBotRight3[item.index][item.lingualer] ?(styles.tooth_box1_hover):(styles.tooth_box1)].join(' ')}
                             onClick={()=>this.surfaceClickBotRight(item.lingualer,item.index,toothSelect.englishValBotRight3[item.index][item.lingualer])}
                        ><span>{item.lingualer}</span></div>

                        <div className={styles.tooth_box1_s}></div>
                        {/*M*/}
                        <div className={[toothSelect.englishValBotRight3[item.index][item.mesial]?(styles.tooth_box2_hover):(styles.tooth_box2)].join(' ')}
                             onClick={()=>this.surfaceClickBotRight(item.mesial,item.index,toothSelect.englishValBotRight3[item.index][item.mesial])}><span>{item.mesial}</span></div>
                        <div className={styles.tooth_box2_s}></div>
                        {/*O*/}
                        <div className={[toothSelect.englishValBotRight3[item.index][item.occlusalf]?(styles.tooth_box3_hover):(styles.tooth_box3)].join(' ')}
                             onClick={()=>this.surfaceClickBotRight(item.occlusalf,item.index,toothSelect.englishValBotRight3[item.index][item.occlusalf])}><span>{item.occlusalf}</span></div>
                        {/*D*/}
                        <div className={[toothSelect.englishValBotRight3[item.index][item.buccal]?(styles.tooth_box4_hover):(styles.tooth_box4)].join(' ')}
                             onClick={()=>this.surfaceClickBotRight(item.buccal,item.index,toothSelect.englishValBotRight3[item.index][item.buccal])}><span>{item.buccal}</span></div>
                        <div className={styles.tooth_box4_s}></div>
                        {/*P*/}
                        <div className={[toothSelect.englishValBotRight3[item.index][item.bistal]?(styles.tooth_box5_hover):(styles.tooth_box5)].join(' ')}
                             onClick={()=>this.surfaceClickBotRight(item.bistal,item.index,toothSelect.englishValBotRight3[item.index][item.bistal])}><span>{item.bistal}</span></div>
                        <div className={styles.tooth_box5_s}></div>
                      </div>
                    })
                  }
                </div>
                <div className={styles.btn_tooth_Eng_left} id="babyTeethBotRig2" style={{paddingLeft:7}}>
                  {
                    arrEnglistOrder.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishValBotRight2[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickRightBotLetter(e, item.index, item,this.state.toothSelect.englishValBotRight2[item.index])}
                      >{item.letter}</Button>
                    })
                  }
                </div>
                <div className={styles.btn_tooth} id="tagColor42" style={{paddingLeft:8}}>
                  {
                    arrCircleNum.map((item, index) => {
                      return <Button
                        className={[this.state.toothSelect.englishValBotRight1[item.index] ? (styles.tableSearch) : (styles.tableSearchBtns)].join(' ')}
                        type="" shape="circle" key={index}
                        onClick={(e) => this.btnClickRightBotNum(e, item.index, item,this.state.toothSelect.englishValBotRight1[item.index])}
                      >{item.num}</Button>
                    })
                  }
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </GridContent>
    );
  }
}

export default ToothSelect
