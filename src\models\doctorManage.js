import {doctorList, doctorInfoById, findHsmDoctors, editDoctorInfo} from '@/services/doctorManage'; // 医生管理列表 单个医生详情
//引入接诊，医生列表，今日预约，专业，等级，保存转诊，昨日待写病历，病历详情，更新病历，用户面板，更新面板，设置面板，今日预约排序等相关接口
import { notification } from 'antd';

const Model = {
  namespace: 'doctorManage',
  state: {
    doctorList: [], // 医生列表
    doctorInfo: {}, //医生详情
    hsmDoctorList:[], //上级医生列表
    // panelInfo: {},//医生首页看板
    // PendingInfo: {},//今日就诊
    // rejectedInfo:{},//已驳回
    // getUserCard:{},//获得当前用户面板设置
    // returnMsg:{},//接诊
    // majorInfo:{},//发起转诊 -- 专业
    // saveInfo:{},//发起转诊确定提交保存 -- 医生列表
    // emrDate:{},//获取指定病历的通用病历详情
    // majorGradeInfo:{},//发起转诊 -- 医生等级
    // emrPostponeList:{},//顺延病历
  },
  effects: {
    //注意：  所有接口统一添加参数  品牌id，机构id，用户名，用户id
    /**医生列表--查询病历权限
     *  参数：平台标识 分页
     * **/
    *fetchDoctorList({ payload, callback }, { call, put }) {
      const response = yield call(doctorList, payload);
      yield put({
        type: 'getDoctorList',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**查询单个医生详情
     *  参数：用户名  ,机构id,平台id等
     **/
    *fetchDoctorInfo({ payload, callback }, { call, put }) {
      const response = yield call(doctorInfoById, payload);
      yield put({
        type: 'getDoctorInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },

    /** 获取上级医生列表  参数未知，待开发
     * findHsmDoctors
     */
     *fetchHsmDoctors({ payload, callback }, { call, put }) {
      const response = yield call(findHsmDoctors, payload);
      yield put({
        type: 'getHsmDoctorInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /** 获取上级医生列表  参数未知，待开发
     * findHsmDoctors
     */
     *fetchEditDoctorInfo({ payload, callback }, { call, put }) {
      const response = yield call(editDoctorInfo, payload);
      yield put({
        type: 'editDoctorInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
  },
  reducers: {
    editDoctorInfo(state, action) {
      return {
        ...state,
        doctorInfo: action.payload || [],
      };
    },
    getDoctorList(state, action) {
      return {
        ...state,
        doctorList: action.payload || [],
      };
    },
    getDoctorInfo(state, action) {
      return {
        ...state,
        doctorInfo: action.payload || {},
      };
    },
    getHsmDoctorInfo(state, action) {
      return {
        ...state,
        hsmDoctorList: action.payload || {},
      };
    },
  },
};

export default Model;
