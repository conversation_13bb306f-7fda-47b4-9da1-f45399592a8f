/**
 * [页面组件][悬窗] 预约头部图例组件
 * 预约->预约头部组件->点击[例]->[悬窗]预约图例组件
 */
import React , { Component } from "react";
import { Popover} from 'antd';
import styles from './index.less'
import A24  from '@/assets/tuli/A24.png';
import A27  from '@/assets/tuli/A27.png';
import A28  from '@/assets/tuli/A28.png';
import A6  from '@/assets/tuli/A6.png';
import A31  from '@/assets/tuli/A31.png';
import A4 from '@/assets/tuli/A4.png';
import A8  from '@/assets/tuli/A8.png';
import A99  from '@/assets/tuli/A99.png';
import padiDui from "../../assets/W-img/padiDui.png";
import SmallerAppointment from  '@/assets/registerAndArrival/smaller.png'
import A23 from '@/assets/AppointmentRules/chu.png';
import A11 from '@/assets/AppointmentRules/bao.png';
import ATE from '@/assets/AppointmentRules/appmentConfirm.png';
import A7 from '@/assets/AppointmentRules/shortMessage.png';
import vip from '@/assets/AppointmentRules/vip.png';

class LegendWhole extends Component{


  constructor(props) {
    super(props);
    this.state={
      onVisibleChangeType:false
    }
  }

  onVisibleChange=(e)=>{
    this.setState({
      onVisibleChangeType:e
    })
  };

  render() {
    const { href } = window.location;
    const fullscreen = href.indexOf('subscribe/fullscreen') != -1; // 判断当前是否在预约页面
    const isNeedSignRecord=localStorage.getItem('isNeedSignRecord')
    return (
      <div>
        <Popover
          trigger='click'
          placement="bottomLeft"
          overlayClassName={styles.Popover_box}
          content={
            <>
              <ul className={styles.Popover_ul}>
                <li>
                  <h3>色块</h3>
                </li>
                <li className={styles.Popover_ul_block}>
                  <span className={styles.Popover_ul_block_span} />
                  <span>未到诊</span>
                  <span className={styles.Popover_ul_block_span1} />
                  <span>已到诊</span>
                  <span className={styles.Popover_ul_block_span2} />
                  <span>已接诊</span>
                  <span className={styles.Popover_ul_block_span3} />
                  <span>过预约时间未到诊</span>
                  <span className={styles.Popover_ul_block_span6} />
                  <span>爽约</span>
                  <span className={styles.Popover_ul_block_span4} />
                  <span>时间占用</span>
                  <span className={styles.Popover_ul_block_span5} />
                  <span>已结算</span>
                </li>
                <li style={{ marginTop:35 }}>
                  <h3>系统图标</h3>
                </li>
                <li className={styles.Popover_ul_block2}>
                  <span className={styles.Popover_ul_block_span}>
                    <img src={A23} alt="" />
                  </span>
                  <span>患者初诊</span>
                  <span className={styles.Popover_ul_block_span1}>
                    <img src={A24} alt="" />
                  </span>
                  <span>患者复诊</span>
                  {/*<span className={styles.Popover_ul_block_span2}>*/}
                  {/*  <img src={A12} alt="" />*/}
                  {/*</span>*/}
                  {/*<span>主诉，未完成治疗</span>*/}
                  {/*<span className={styles.Popover_ul_block_span3}>*/}
                  {/*  <img src={A13} alt="" />*/}
                  {/*</span>*/}
                  {/*<span>新主诉</span>*/}
                  <span className={styles.Popover_ul_block_span4}>
                    <img src={A27} alt="" />
                  </span>
                  <span>会诊</span>
                  <span className={styles.Popover_ul_block_span5}>
                    <img src={A6} alt="" />
                  </span>
                  <span>转诊</span>
                </li>
                <li className={styles.Popover_ul_block3}>
                  <span className={styles.Popover_ul_block_span6}>
                    <img src={A31} alt="" />
                  </span>
                  <span>Walk in 患者</span>
                  <span className={styles.Popover_ul_block_span7}>
                    <img src={A11} alt="" />
                  </span>
                  <span>保险客户</span>
                  <span className={styles.Popover_ul_block_span8}>
                    {/*<img src={A4} alt="" />*/}
                    <img src={ATE} alt="" />
                  </span>
                  <span>预约已确认</span>
                  <span className={styles.Popover_ul_block_span9}>
                    <img src={A8} alt="" />
                  </span>
                  <span>已联系未确认</span>
                </li>
                <li className={styles.Popover_ul_block4}>
                  <span className={styles.Popover_ul_block_span6}>
                    <img src={A7} alt="" />
                  </span>
                  <span>信息已发送</span>
                  <span className={styles.Popover_ul_block_span7}>
                    <img src={A4} alt="" />
                  </span>
                  <span>患者已到诊</span>
                  {
                    fullscreen
                      ?
                        <>
                          <span className={styles.Popover_ul_block_span7}>
                            <img src={padiDui} alt="" />
                          </span>
                          <span>排队</span>
                        </>
                      :null

                  }
                  {
                    fullscreen || isNeedSignRecord!='0'
                      ?
                      null
                      : <>
                          <span className={styles.Popover_ul_block_span6}>
                            <img src={SmallerAppointment} alt="" />
                          </span>
                        <span>小程序预约患者</span>
                      </>
                  }
                  <span style={{fontSize: '12px'}}>
                    <img style={{ width:17,height: 15 }} src={vip} alt="" />  VIP客户
                  </span>
                </li>
                <li style={{ marginTop:35 }}>
                  <h3>手工图标</h3>
                </li>

                <li className={styles.li_box}>
                  <span

                    className={styles.implantSurgery}
                  >
                    舒适治疗
                  </span>
                  <span
                    className={styles.basicTreatment1}
                  >
                    拔牙
                  </span>
                  <span

                    className={styles.basicTreatment1}
                  >
                    种植手术
                  </span>
                  <span

                    className={styles.basicTreatment1}
                  >
                    种植二期修复
                  </span>
                  <span

                    className={styles.basicTreatment1}
                  >
                    正畸
                  </span>
                  <span

                    className={styles.basicTreatment1}
                  >
                    修复
                  </span>
                  <span

                    className={styles.basicTreatment1}
                  >
                    戴冠
                  </span>
                  <span

                    className={styles.basicTreatment1}
                  >
                    根管
                  </span>

                </li>
                <li className={styles.li_box2}>
                   <span
                     className={styles.basicTreatment1}
                   >
                    冷光美白
                  </span>
                  <span

                    className={styles.lab}
                  >
                    LAB
                  </span>
                  <span

                    className={styles.implantSurgery}
                  >
                    急诊
                  </span>
                  <span

                    className={styles.basicTreatment1}
                  >
                    打支抗
                  </span>
                  <span

                    className={styles.basicTreatment1}
                  >
                    正畸初戴
                  </span>
                  <span

                    className={styles.platinumCard}
                  >
                    招白金
                  </span>
                  <span

                    className={styles.platinumCard}
                  >
                    银行白金卡
                  </span>
                  <span

                    className={styles.lab}
                  >
                    保险
                  </span>
                </li>

                <li className={styles.li_box2}>
                  <span

                    className={styles.implantSurgery}
                  >
                    基础治疗
                  </span>
                  <span

                    className={styles.implantSurgery}
                  >
                    重大治疗
                  </span>
                  <span
                    className={styles.basicTreatment1}
                  >
                    显微镜根管
                  </span>

                  <span
                    className={styles.implantSurgery}
                  >
                    儿童束缚
                  </span>

                  <span
                    className={styles.basicTreatment1}
                  >
                    牙齿美白
                  </span>

                  <span
                    className={styles.lab}
                  >
                    老客户回访
                  </span>

                  {/*<span*/}
                  {/*  className={styles.implantTwice}*/}
                  {/*>*/}
                  {/*  三个月定检*/}
                  {/*</span>*/}



                </li>

                <li className={styles.Popover_ul_block5}>
                  {/* <span className={styles.Popover_ul_block_span1}> */}
                  {/* <img src={A22} alt=""/> */}
                  {/* </span> */}
                  {/* <span>种植手术</span> */}
                  {/* <span className={styles.Popover_ul_block_span2}> */}
                  {/* <img src={A9} alt=""/> */}
                  {/* </span> */}
                  {/* <span>种植二期修复</span> */}
                  {/* <span className={styles.Popover_ul_block_span3}> */}
                  {/* <img src={A5} alt=""/> */}
                  {/* </span> */}
                  {/* <span>正畸</span> */}
                  {/* <span className={styles.Popover_ul_block_span4}> */}
                  {/* <img src={A26} alt=""/> */}
                  {/* </span> */}
                  {/* <span>修复戴冠</span> */}
                  {/* <span className={styles.Popover_ul_block_span5}> */}
                  {/* <img src={A32} alt=""/> */}
                  {/* </span> */}
                  {/* <span>根管</span> */}
                  {/* <span className={styles.Popover_ul_block_span6}> */}
                  {/* <img src={A29} alt=""/> */}
                  {/* </span> */}
                  {/* <span>冷光美白</span> */}
                  {/* /!*<span className={styles.Popover_ul_block_span7}>*!/ */}
                  {/* /!*<img src={A88} alt=""/>*!/ */}
                  {/* /!*</span>*!/ */}
                  {/* /!*<span>指定客户</span>*!/ */}
                  <span className={styles.Popover_ul_block_span7}>
                    <img src={A28} alt="" />
                  </span>
                  <span style={{fontSize: '12px'}}>基础治疗</span>
                  <span className={styles.Popover_ul_block_span7}>
                    <img src={A99} alt="" />
                  </span>
                  <span style={{fontSize: '12px'}}>重大治疗 </span>
                </li>
                <li className={styles.Popover_ul_block6}>
                  {/* <span className={styles.Popover_ul_block_span1}> */}
                  {/* <img src={A21} alt=""/> */}
                  {/* </span> */}
                  {/* <span>招商银行白金卡</span> */}
                  {/* <span className={styles.Popover_ul_block_span2}> */}
                  {/* <img src={A30} alt=""/> */}
                  {/* </span> */}
                  {/* <span>MSH保险客户</span> */}
                  {/* <span className={styles.Popover_ul_block_span2}> */}
                  {/* <img src={b2} alt=""/> */}
                  {/* </span> */}
                  {/* <span>LAB</span> */}
                  {/* <span className={styles.Popover_ul_block_span2}> */}
                  {/* <img src={b1} alt=""/> */}
                  {/* </span> */}
                  {/* <span>急诊</span> */}
                  {/* <span className={styles.Popover_ul_block_span5}> */}
                  {/* <img src={A25} alt=""/> */}
                  {/* </span> */}
                  {/* <span>VIP客户</span> */}

                </li>
              </ul>
            </>
          }
          onVisibleChange={this.onVisibleChange}
        >
          <div className={styles.div_box}>
            <i className={styles.div_Icon}>例</i>
            {/* {
              !this.state.onVisibleChangeType
              ?'图例'
              :'收回'
            } */}
          </div>

        </Popover>

      </div>
    );
  }

}
export default LegendWhole
