import {
  getComponentsList,
  getComponentsScreeningItems,
  getPatientInfoOption,
  getMedicalRecordsOption,
  creatComplainInfo,
  compileComplainInfo,
  compileComplainInfoToSave,
  exportExcel,
} from '@/services/complaint.js';

// 数据格式化TreeData 平铺数据结构
function formatTreeDataToList (nodeToTertiaryStructureDtoList) {
  let levelSecondNode  = [] // 拼接全部二级节点
  let organizationInfoList = [] // 拼接全部诊所数据
  if (Array.isArray(nodeToTertiaryStructureDtoList)) {
    nodeToTertiaryStructureDtoList.forEach((level1res,level1idx)=>{
      if (Array.isArray(level1res.subordinateNodeList)) {
        // 遍历全部二级节点
        level1res.subordinateNodeList.forEach((level2res,level2idx)=>{
          levelSecondNode.push(level2res)
          if (Array.isArray(level2res.subordinateNodeList)) {
            // 遍历全部诊所数据
            level2res.subordinateNodeList.forEach((level3res,level3idx)=>{
              if (level3res && level3res.managedNodeName) {
                organizationInfoList.push(level3res)
              }
            })
          }
        })
      }
    })
  }
  return {levelSecondNode,organizationInfoList}
}

/**
 * ComponentsCreate
 * 新建/编辑/投诉
 */
export default {
  namespace: 'ComponentsCreate',
  state: {
    PatientId: null,               // 患者id
    PatientIdObj: null,            // 患者详细信息
    MedicalRecordItem:null,        // 选中的治疗项目

    /*-----------获取的信息-----------*/
    PatientInfoOption:[],          // 根据手机号获取患者信息
    selectMedicalRecordsOption:[],       // 获取治疗记录信息
    selectMedicalRecordsOptionState:'暂无数据',
  },
  effects: {
    // 获取患者信息
    *getPatientInfoOption({ payload: params }, { call }) {
      let res = yield call(getPatientInfoOption,params)
      return res;
    },
    // 选择患者id后请求缴费记录信息
    *getMedicalRecordsOption({ payload: params }, { call ,put}) {
      const res = yield call(getMedicalRecordsOption,{
        ...params,
      })

      if (res && res.code == 200 && res.data) {
        let resList = Array.isArray(res.data) ? res.data: []
        yield put({
          type: 'save',
          payload: {
            selectMedicalRecordsOption: resList,
            selectMedicalRecordsOptionState: resList.length == 0 ? '暂无数据' : null,
          }
        })
      }else {
        yield put({
          type: 'save',
          payload: {
            selectMedicalRecordsOption: [],
            selectMedicalRecordsOptionState:res && res.message ? res.message : '数据加载失败',
          }
        })
      }
      return res;
    },

    // 手机号查询患者
    *getPatientInfo({ payload: params }, { call }) {
      const res = yield call(getPatientInfo,params)
      return res;
    },

    // 获取投诉基础信息Option
    *getComponentsScreeningItems({ payload: params }, { call }){
      const res = yield call(getComponentsScreeningItems,{
        ...params,
        isUseList: params.isUseList ? true : null
      })
      return res;
    },
    // 创建一条录入单
    *creatComplainInfo({ payload: params }, { call }){
      const res = yield call(creatComplainInfo,params)
      return res;
    },

    // 草稿编辑后提交/保存
    *compileComplainInfoToSave({ payload: params }, { call }){
      const res = yield call(compileComplainInfoToSave,params)
      return res;
    },

    // 获取编辑投诉原录入单信息
    *compileComplainInfo({ payload: params }, { call }){
      const res = yield call(compileComplainInfo,params)
      return res;
    },
  },
  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },

    clearPatientInfoByFileNumber( state, { payload }) {
      const { PatientIdObj } = state
      let obj = PatientIdObj;
      if(PatientIdObj && PatientIdObj.fileNumber) {
        obj = {
          ...PatientIdObj,
          fileNumber: null,
        }
      }
      return {
        ...state,
        PatientIdObj:obj
      }
    },

    clearPatientInfo(state, { payload }) {
      return {
        ...state,
        PatientId: null,               // 患者id
        PatientIdObj: null,            // 患者详细信息
        MedicalRecordItem:null,        // 选中的治疗项目
        PatientInfoOption:[],          // 根据手机号获取患者信息
        MedicalRecordsOption:[],       // 获取治疗记录信息
      };
    },

    clear(state,{payload}) {
      return {
        ...state,
        PatientId: null,               // 患者id
        PatientIdObj: null,            // 患者详细信息
        MedicalRecordItem:null,        // 选中的治疗项目

        PatientInfoOption:[],          // 根据手机号获取患者信息
        selectMedicalRecordsOption:[],       // 获取治疗记录信息
        selectMedicalRecordsOptionState:'暂无数据',
      }
    },
  },
}
