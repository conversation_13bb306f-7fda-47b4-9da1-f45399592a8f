import React, { Component } from 'react';
import PropTypes from 'prop-types';
import styles from './index.less';
import DataFailIco from '@/assets/DataFailure.png';
import Nodataimg from '@/assets/nodata.png';
import consultDocIcon from '@/assets/consultDocIcon.png';
import CustomerInfoNoData from '@/assets/CustomerManagement/customerInfoNoData.png'
import NoPersonnel from '@/assets/CustomerManagement/noPersonnel.png'
import homeNoData from '@/assets/home/<USER>'; // 首页列表暂无数据
import homeDataFail from '@/assets/home/<USER>'; // 首页列表数据加载失败
import noDataIcon from '@/assets/home/<USER>'; // 暂无数据


export default class DataFailure extends Component{
  static contextTypes = {
    CodeType:PropTypes.number, // 0 数据加载失败  1 暂无数据 2 搜索不到会诊医生
    width: PropTypes.string,
    height: PropTypes.string,
    ImgWidth:PropTypes.number,
    ImgHeight:PropTypes.number,
    margin: PropTypes.string,
    blockType: PropTypes.bool,
    DataFailMsg:PropTypes.string,
    textMargin:PropTypes.string,
    textColor:PropTypes.string,
  };

  static defaultProps = {
    CodeType:0,
    width:"96px",
    height:"auto",
    ImgWidth:96,
    ImgHeight:96,
    margin: "0 0 20px 0",
    blockType:false,
    DataFailMsg:'数据加载失败！',
    textMargin: '20px 0 0',
    textColor: '#666',
  };

  constructor(props){
    super(props);
    this.state = {
      CodeType:this.props.CodeType,
      width:this.props.width,
      height:this.props.height,
      imgWidth:this.props.ImgWidth,
      imgHeight:this.props.ImgHeight,
      margin:this.props.margin,
      blockType:this.props.blockType,
      DataFailmsg:this.props.DataFailMsg,
      textMargin:this.props.textMargin,
      textColor:this.props.textColor,
    };
  }

  render() {
    const {CodeType,width,height,imgWidth,imgHeight,margin,blockType,DataFailmsg, textMargin, textColor } = this.state;
    return (
      <div className={blockType ? styles.DataFail : styles.DataFailinline} style={{width: width, height:height,margin:margin}}>
        {(()=>{
          switch (CodeType){
            case 0 :
              return <img className={styles.DataFailIcon}  width={imgWidth} height={imgHeight}  src={DataFailIco} alt={DataFailmsg} />
            case 1 :
              return  <img width={imgWidth} height={imgHeight} className={styles.DataFailIcon} src={Nodataimg} alt={DataFailmsg} />
            case 2 :
              return <img width={81} height={81} className={styles.DataFailIcon} src={consultDocIcon} alt={DataFailmsg} />
            case 3 :
              return  <img width={imgWidth} height={imgHeight} className={styles.DataFailIcon} src={CustomerInfoNoData} alt={DataFailmsg} />
            case 4 :
              return  <img width={imgWidth} height={imgHeight} className={styles.DataFailIcon} src={NoPersonnel} alt={DataFailmsg} />
            case 5 :
              return  <img width={imgWidth} height={imgHeight} className={styles.DataFailIcon} src={homeNoData} alt={DataFailmsg} />
            case 6 :
              return  <img width={imgWidth} height={imgHeight} className={styles.DataFailIcon} src={homeDataFail} alt={DataFailmsg} />
            case 7 :
              return  <img width={imgWidth} height={imgHeight} className={styles.DataFailIcon} src={noDataIcon} alt={DataFailmsg} />
          };
        })()}
        <p className={styles.DataFailMsg} style={{margin: textMargin, color: textColor}}>{this.props.DataFailMsg ? this.props.DataFailMsg : '数据加载失败！'}</p>
      </div>
    )
  }
}
