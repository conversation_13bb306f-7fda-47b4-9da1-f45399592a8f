import {
  findTherapyType,                   // 查询树结构的学科、分类、治疗数据
  findSpecByServiceProjectIdAndLikeName,             // 选择治疗，查询收费项
  financeNodeInfoBySaas,          // 查询财务节点
  findNodeInfoTherapyType,                   // 查询树结构的学科、分类、治疗数据，400
  financeNodeInfoAndLikeName,           // 选择治疗，查询收费项，400
} from '@/services/searchChargeItem';
export default {
  namespace: 'searchChargeItem',
  state: {

  },
  effects: {
    // 查询树结构的学科、分类、治疗数据
    *findTherapyType({ payload: params }, { call }){
      const res = yield call(findTherapyType, params);
      return res;
    },
    // 选择治疗，查询收费项
    *findSpecByServiceProjectIdAndLikeName({ payload: params }, { call }){
      const res = yield call(findSpecByServiceProjectIdAndLikeName, params);
      return res;
    },
    // 查询财务节点
    *financeNodeInfoBySaas({ payload: params }, { call }){
      const res = yield call(financeNodeInfoBySaas, params);
      return res;
    },
    // 查询树结构的学科、分类、治疗数据，400
    *findNodeInfoTherapyType({ payload: params }, { call }){
      const res = yield call(findNodeInfoTherapyType, params);
      return res;
    },
    // 选择治疗，查询收费项，400
    *financeNodeInfoAndLikeName({ payload: params }, { call }){
      const res = yield call(financeNodeInfoAndLikeName, params);
      return res;
    },
  },
  reducers: {

  }
};
