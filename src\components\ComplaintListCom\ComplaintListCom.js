import React, { Component } from 'react';
import { connect } from 'dva';
import { Checkbox, Col, message, Row, Spin, Tag } from 'antd';
import styles from './ComplaintListCom.less';
import classNames from 'classnames';
import PropTypes from 'prop-types';

const {CheckableTag} = Tag;
/**
 * 主诉公共组件
 */
@connect(({Getscreen, loading}) => ({
  loading,
  Getscreen,
  ComplaintData: Getscreen.Complaintlist,// 主诉数据
  appointmentCacheShowData: Getscreen.appointmentCacheShowData
}))
class ComplaintListCom extends Component {

  static propTypes = {
    onRef:PropTypes.func,                        // 主诉组件对象
    onChange:PropTypes.func,                     // 更改主诉项目
    makeid:PropTypes.any,                        // 患者id
    doctorId:PropTypes.any,                      // 医生id
    selectedTagContentListByProp:PropTypes.any,  // 已选中的主诉项目(用于到诊医生工作台)
    appointmentId:PropTypes.any,                 // 预约id(用于到诊医生工作台)
    isTelephoneCustomerService:PropTypes.bool,   // 是否是400客服系统
    isDisable:PropTypes.bool,                    // 是否禁用
    isDisbaleMessage:PropTypes.any,              // 禁用文案
    isEmptyMessage:PropTypes.any,                // 是否为空提示文案
  }

  static defaultProps = {
    onRef:()=>{},
    onChange:()=>{},
    selectedTagContentListByProp:null,
    appointmentId:null,
    makeid:null,
    doctorId:null,
    isTelephoneCustomerService:false,
    isDisable:false,
    isDisbaleMessage:null,
    isEmptyMessage:'请选择预约主诉!',
  }


  constructor(props) {
    super(props);
    this.state = {
      makeid: null,                       // 患者id
      appointmentVMList: [],              // 主诉类型数据
      selectedTags: [],                   // 主诉分类选中提交
      selectedTagContentList:[],          // 主诉选中
      complaintListVerifyisError:false,   // 是否已选主诉
      isComplaintListDataState:null,      //
      appointmentVMListState:null,        // 标记暂(empty)无数据或(error)加载失败
    }
  }

  componentWillUpdate(nextProps, nextState, nextContext) {
    if (this.props.makeid !== nextProps.makeid) {
      this.setState({
        makeid: nextProps.makeid
      });
      this.refreshComplaint(nextProps.makeid,()=>{
        this.getappointmentCacheShow(nextProps.makeid)
      });  // 组件加载后 请求数据
    }
  }

  componentDidMount() {

    this.props.onRef && this.props.onRef(this); // 向外抛出组件对象
    this.hidnComplaintListVerifyisError()
    console.log('makeidmakeidmakeid ::',this.props.makeid);
    this.refreshComplaint(this.props.makeid,()=>{
      this.setState({
        makeid: this.props.makeid
      },()=>{
        this.getappointmentCacheShow(this.props.makeid)
      });
    });  // 组件加载后 请求数据
  }

  /**
   * 更新并清理缓存数据 (用于手动更新数据)
   */
  updataAndCleanData(callBack) {
    let makeidState = this.props.makeid;
    this.setState({
      appointmentVMList: [],       // 主诉类型数据
      selectedTags: [],            // 主诉分类选中提交
      selectedTagContentList:[],   // 主诉选中
      appointmentVMListState:null,
    },()=>{
      this.refreshComplaint(makeidState,()=>{
        this.setState({
          makeid: makeidState
        },()=>{
          this.getappointmentCacheShow(makeidState,callBack)
        });
      });  // 组件加载后 请求数据 // 美就
    })
  }

  // 获取临时存储的主诉数据
  getappointmentCacheShow = (makeid,callBack) => {
    const {dispatch} = this.props;
    console.log('getappointmentCacheShow',makeid);
    if ((this.props.selectedTagContentListByProp && Array.isArray(this.props.selectedTagContentListByProp) && this.props.appointmentId) || !makeid) {

      const appointmentVMList = this.props.selectedTagContentListByProp

      /**
       * complaintName
       * complaintId:2,
       * medicalType:2
       */
      let selectedTagsFilter = [];    // 一级主诉分类
      const selectedTagsSecond = [];    // 二级主诉项目
      const selectedTagsFiterIdList = [];

      if (appointmentVMList) {
        appointmentVMList.forEach((res) => {
          if (selectedTagsFiterIdList.indexOf(res.complaintCategoryId) == -1) {
            selectedTagsFiterIdList.push(res.complaintCategoryId)
          }
          if (res.treatType != 2) {
            selectedTagsSecond.push(res)
          }
        })
      }

      if (Array.isArray(this.state.appointmentVMList) && this.state.appointmentVMList.length != 0) {
        let stateVMListCategoryId = this.state.appointmentVMList[0] && this.state.appointmentVMList[0].categoryId ? this.state.appointmentVMList[0].categoryId : null
        if (selectedTagsFiterIdList.indexOf(stateVMListCategoryId) == -1) {
          selectedTagsFiterIdList.push(stateVMListCategoryId)
        }

        selectedTagsFilter = this.state.appointmentVMList.filter((listRes) => {
          if (listRes && selectedTagsFiterIdList.indexOf(listRes.categoryId) != -1) {
            return listRes
          }
        })
      }

      this.setState({
        selectedTags: selectedTagsFilter,             // 主诉
        selectedTagContentList: selectedTagsSecond
      }, () => {
        callBack && callBack(selectedTagsSecond)
      })

    }else {
      dispatch({
        type: 'Getscreen/getappointmentCacheShow',
        payload: {
          patientId: makeid, // this.props.makeid  患者的id
          operatorType: localStorage.getItem('doctorIdentification'), // localStorage.getItem('doctorIdentification'); 1醫生 2客服
          userId: localStorage.getItem('id'),   // Y用戶id
          userName: localStorage.getItem('userName'),
          organizationId: localStorage.getItem('organizationInfoId'),  // tenantId
          tenantId: localStorage.getItem('tenantId'),
          taskId: this.props.ReferralId ? this.props.ReferralId : null
        },
      }).then((res) => {

        if (res) {
          const appointmentVMList = res.complaintListDto || []


          /**
           * complaintName
           * complaintId:2,
           * medicalType:2
           */
          let selectedTagsFilter = [];    // 一级主诉分类
          const selectedTagsSecond = [];    // 二级主诉项目
          const selectedTagsFiterIdList = [];


          if (appointmentVMList) {
            //appointmentVMList
            appointmentVMList.forEach((res) => {
              if (selectedTagsFiterIdList.indexOf(res.complaintCategoryId) == -1) {
                selectedTagsFiterIdList.push(res.complaintCategoryId)
              }
              if (res.treatType != 2) {
                selectedTagsSecond.push(res)
              }
            })
          }


          if (Array.isArray(this.state.appointmentVMList) && this.state.appointmentVMList.length != 0) {
            let stateVMListCategoryId = this.state.appointmentVMList[0] && this.state.appointmentVMList[0].categoryId ? this.state.appointmentVMList[0].categoryId : null
            if (selectedTagsFiterIdList.indexOf(stateVMListCategoryId) == -1) {
              selectedTagsFiterIdList.push(stateVMListCategoryId)
            }

            selectedTagsFilter = this.state.appointmentVMList.filter((listRes) => {
              if (listRes && selectedTagsFiterIdList.indexOf(listRes.categoryId) != -1) {
                return listRes
              }
            })
          }

          this.setState({
            selectedTags: selectedTagsFilter,             // 主诉
            selectedTagContentList: selectedTagsSecond
          }, () => {
            callBack && callBack(selectedTagsSecond)
          })

        } else {
          message.error('数据加载失败！')
          callBack && callBack([])
        }
      }).catch((error) => {
        console.log('错误信息', error)
        callBack && callBack([])
      })
    }
  }; // 回显口

  /**
   * 刷新主诉弹窗数据方法
   */
  refreshComplaint = (makeid,callBack) => {
    const {dispatch} = this.props;
    dispatch({
      type: 'Getscreen/getComplaintTable',
      payload: {
        organizationId: localStorage.getItem('organizationInfoId'), // tenantId
        tenantId: localStorage.getItem('tenantId'),
        patientId: makeid,                                              // this.props.makeid  患者的id
        operatorType: localStorage.getItem('doctorIdentification'), // localStorage.getItem('doctorIdentification'); 1醫生 2客服
        userId: localStorage.getItem('id'),                         // Y用戶id
        userName: localStorage.getItem('userName'),
        taskId: this.props.ReferralId || '',                            // 转诊id
      },
    }).then((res) => {
      if (!res) {
        const error = new Error('无数据');
        error.name = response.status;
        error.response = response;
        throw error;
      }

      if (Array.isArray(res) && res.length != 0) {
        let appointmentVMList = [...res]

        this.setState({
          appointmentVMList: appointmentVMList || [],
          appointmentVMListState: null,
        }, () => {
          callBack && callBack()
        });
      }else {
        this.setState({
          appointmentVMList: appointmentVMList || [],
          appointmentVMListState: 'empty',   // 空数据
        }, () => {
          callBack && callBack()
        });
      }
    }).catch(() => {
      console.log('暂无数据')
      this.setState({
        appointmentVMList: [],
        appointmentVMListState:'error'      // 接口调用失败
      },()=>{
        // callBack && callBack()
      });
    })
  };

  // 主诉回显事件
  handleChange(tag, checked) {
    const {selectedTags,selectedTagContentList} = this.state;
    const nextSelectedTags = !checked
      ? [...selectedTags, tag]
      : selectedTags.filter(t => t.categoryId !== tag.categoryId);

    const nextSelectedTagsContent = !checked
      ? [...selectedTagContentList]
      : selectedTagContentList.filter(t => t.complaintCategoryId !== tag.categoryId);

    this.setState({
      selectedTags: nextSelectedTags,
      selectedTagContentList: nextSelectedTagsContent
    }, () => {});
  }

  onClickContentBox=(tag,checked)=>{
    if(this.props.isDisable){
      if(this.props.isDisbaleMessage){
        message.warning(this.props.isDisbaleMessage)
      }
      return
    }
    const { selectedTagContentList } = this.state;
    const nextSelectedTags = !checked
      ? [...selectedTagContentList, tag]
      : selectedTagContentList.filter(t => t.complaintId !== tag.complaintId);
    this.setState({
      selectedTagContentList: nextSelectedTags,
      complaintListVerifyisError: false
      // complaintListVerifyisError: nextSelectedTags.length == 0
    }, () => {});
  }

  getCheckedState=(tag)=>{
    const checkedTags = this.state.selectedTags.filter((value) => {
      // complaintId
      if (tag.categoryId == value.categoryId) {
        return value
      }
    });
    return checkedTags.length != 0
  }

  getCheckStateByContentBox=(tag)=>{
    const checkedTags = this.state.selectedTagContentList.filter((value)=>{
      if(tag.complaintId == value.complaintId){
        return value
      }
    })
    return checkedTags.length != 0
  }

  /**
   * 保存选择的主诉数据
   */
  saveComplaintContent=()=>{
    return this.state.selectedTagContentList
  }

  /**
   * 设置主诉必填提示是否展示
   * @param value
   */
  showComplaintListVerifyisError=()=>{
    this.setState({
      complaintListVerifyisError:true
    })
  }

  hidnComplaintListVerifyisError=()=>{
    this.setState({
      complaintListVerifyisError:false
    })
  }

  render() {
    const {loading, ComplaintData,appointmentCacheShowData,isDisable,isTelephoneCustomerService} = this.props;       // 结构 主诉数据
    const { complaintListVerifyisError } = this.state
    const {content} = ComplaintData || {};
    const {visitReferralDto} = content || {};
    const listLoading = !!loading.effects['Getscreen/getComplaintTable'] || !!loading.effects['Getscreen/getappointmentCacheShow'];   // 请求


    return (
      <div>
        <div className={styles.ModalContentWarp}>
          <Spin spinning={listLoading}>
            <div className={classNames({
              [styles.ModalContent]:true,
              [styles.ModalContentDisable]:isDisable
            })}>
              { /* 按钮添加主诉事件 */
                <div className={styles.zhusuInput}>
                  <Row className={styles.ReferralIdBottomBox}>
                    <Col className={styles.zhusuIBox} span={24}>
                      <div className={classNames({
                        [styles.zhusuInputbtn]:true,
                        [styles.grayborder]:!complaintListVerifyisError,
                        [styles.redborder]:complaintListVerifyisError,
                      })}>
                        {this.state.appointmentVMListState ?
                          <div className={styles.appointmentVMListStateText}>
                            {this.state.appointmentVMListState && '无主诉数据'}
                          </div>
                          :
                          <>
                            {/*{*/}
                            {/*  this.state.appointmentVMList.map((tag, index) => {*/}
                            {/*    let checkState = this.getCheckedState(tag);*/}
                            {/*    return (*/}
                            {/*      <>*/}
                            {/*        <div className={styles.complaintBox}>*/}
                            {/*          <div*/}
                            {/*            key={index}*/}
                            {/*            className={classNames({*/}
                            {/*              [styles.complaintNameBox]: !isTelephoneCustomerService && !checkState,*/}
                            {/*              [styles.complaintNameBoxSelected]: !isTelephoneCustomerService && checkState,*/}
                            {/*              [styles.complaintNameBoxTelephoneCustomerService]: isTelephoneCustomerService && !checkState,*/}
                            {/*              [styles.complaintNameBoxSelectedTelephoneCustomerService]: isTelephoneCustomerService && checkState,*/}
                            {/*            })}*/}
                            {/*            onClick={() => {*/}
                            {/*              this.handleChange(tag, checkState)*/}
                            {/*            }}*/}
                            {/*          >{tag.categoryName}</div>*/}
                            {/*        </div>*/}
                            {/*        {((Array.isArray(tag.appointmentVMList) && tag.appointmentVMList.length > 0) && checkState) &&*/}
                            {/*        tag.appointmentVMList.map((itemtag, itemindex) => {*/}
                            {/*          let checkStateByContentBox = this.getCheckStateByContentBox(itemtag);*/}
                            {/*          return (*/}
                            {/*            <div*/}
                            {/*              className={classNames({*/}
                            {/*                [styles.complaintContentBox]: true,*/}
                            {/*                [styles.complaintContentBoxSelectFirst]: itemindex == 0,*/}
                            {/*                [styles.complaintContentBoxDefault]: !isTelephoneCustomerService && !checkStateByContentBox,*/}
                            {/*                [styles.complaintContentBoxSelect]: !isTelephoneCustomerService && checkStateByContentBox,*/}
                            {/*                [styles.complaintContentBoxDefaultTelephoneCustomerService]: isTelephoneCustomerService && !checkStateByContentBox,*/}
                            {/*                [styles.complaintContentBoxSelectTelephoneCustomerService]: isTelephoneCustomerService && checkStateByContentBox,*/}
                            {/*              })}*/}
                            {/*              onClick={() => {*/}
                            {/*                this.onClickContentBox(itemtag, checkStateByContentBox)*/}
                            {/*              }}*/}
                            {/*            >*/}
                            {/*              {itemtag.complaintName}*/}
                            {/*            </div>*/}
                            {/*          )*/}
                            {/*        })*/}
                            {/*        }*/}
                            {/*      </>*/}
                            {/*    )*/}
                            {/*  })*/}
                            {/*}*/}
                            {
                              this.state.appointmentVMList.map((tag, index) => {
                                let checkState = this.getCheckedState(tag);
                                return (
                                  <>
                                    <div className={styles.contentItem} key={index}>
                                      <div className={styles.contentItemTitle}>{tag.categoryName}：</div>
                                      <div className={styles.contentItemContent}>
                                        {
                                          tag.appointmentVMList && tag.appointmentVMList.map((itemtag,itemindex)=>{
                                            let checkStateByContentBox = this.getCheckStateByContentBox(itemtag);
                                            return (
                                              <div
                                                key={itemindex}
                                                className={classNames({
                                                  [styles.complaintContentBox]: true,
                                                  [styles.complaintContentBoxDefault]: !isTelephoneCustomerService && !checkStateByContentBox,
                                                  [styles.complaintContentBoxSelect]: !isTelephoneCustomerService && checkStateByContentBox,
                                                  [styles.complaintContentBoxDefaultTelephoneCustomerService]: isTelephoneCustomerService && !checkStateByContentBox,
                                                  [styles.complaintContentBoxSelectTelephoneCustomerService]: isTelephoneCustomerService && checkStateByContentBox,
                                                })}
                                                onClick={() => {
                                                  this.onClickContentBox(itemtag, checkStateByContentBox)
                                                }}
                                              >{itemtag.complaintName}</div>
                                            )
                                          })
                                        }
                                      </div>
                                    </div>
                                  </>
                                )
                              })
                            }
                          </>
                        }
                      </div>
                    </Col>
                  </Row>
                </div>
              }
              {complaintListVerifyisError && <p className={classNames({
                [styles.redTextComlaintList]: true,
              })}>{this.props.isEmptyMessage}</p>}
            </div>
          </Spin>
        </div>
      </div>
    )
  }

}

export default ComplaintListCom


