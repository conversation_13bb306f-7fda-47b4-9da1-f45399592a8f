@import '~antd/es/style/themes/default.less';

.allcontentStyle {
  //min-width:1024px;
  padding: 0 16px 0 0;
  overflow: hidden;
  //margin: 24px;
  border-top: 1px solid #e5e6eb;
  border-bottom: 1px solid #e5e6eb;
}
:global {
  .ant-tabs-top > .ant-tabs-nav::before,
  .ant-tabs-bottom > .ant-tabs-nav::before,
  .ant-tabs-top > div > .ant-tabs-nav::before,
  .ant-tabs-bottom > div > .ant-tabs-nav::before {
    border-bottom: 1px solid #e5e6eb;
  }
  .ant-tabs > .ant-tabs-nav .ant-tabs-nav-wrap,
  .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-wrap {
    margin-left: 10px;
  }
}
.topborder {
  position: relative;
}
.AllbtnStyle {
  position: absolute;
  top: 25%;
  right: 20px;
  .btnStyle {
    margin-left: 14px;
    font-size: 14px;
  }
}
.fontStyle {
  font-family: 'RoboData';
}
//加载中
.loading{
  position: absolute;
  top: 45%;
  left: 47%;
  z-index: 888;
}
.AssociatedStyle{
  :global{
    .ant-table-row{
      cursor: pointer;
    }
  }
}
