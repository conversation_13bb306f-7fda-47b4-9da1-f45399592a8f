.HeaderTableWeap {
  position: relative;
  .goToLast {
    float:right;
    width: 22px;
    height: 22px;
    display: inline-block;
    position:absolute;
    left: 59px;
    top: 50px;
    vertical-align: text-bottom;
    background: url('../../../../src/assets/calendar/next.png');
    cursor: pointer;
    transform:scaleX(-1);
    z-index: 1;
  }

  .goToNext {
    float:left;
    width: 22px;
    height: 22px;
    position:absolute;
    right: -10px;
    top: 50px;
    display: inline-block;
    vertical-align: text-bottom;
    background: url('../../../../src/assets/calendar/next.png');
    cursor: pointer;
    z-index:1;
  }

  :global {
    table {
      width:100%;
      table-layout:fixed;
      border-color: #ccc;
    }

    .fc-axis {
      background: #E0E2E7;
    }

    .DoctorNameTr th {
      text-align: center;
      //padding-left: 16px;
      font-weight: normal;
      border-color: #ccc;
      font-size: 16px;
      font-family: "Microsoft YaHei";
      background: #E0E2E7;
      cursor: pointer;
    }

    .fc-resource-cell {
      background: #ECECF1;
      font-weight: normal;
      font-size: 14px;
    }

    th, td {
      border-style: solid;
      border-width: 1px;
      padding: 0;
      vertical-align: top;
      border-color: #ccc;
    }

    th {
      height: 45px;
      line-height: 45px;
      text-align: center;
      border-color: #ccc;
    }
  }
}

.lableTitleHead {
  margin-top: 12px;
}

.labelTdsTitle {
  line-height: 8px;
}

.doctorIcon {
  width: 18px;
  height: 22px;
}

.chairIcon {
  width: 24px;
  height: 20px;
}

.doctorIconTh {
  padding-left: 0px;
}

.AgendaHeadName {
  font-family:'MicrosoftYaHei';
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-weight: 700;
  font-size: 16px;
}


.suplusHeader {
  background: #E0E2E7!important;
  //border:#E0E2E7;
}


