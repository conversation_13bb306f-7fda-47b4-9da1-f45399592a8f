// 今日就诊左侧列表

import {connect} from "dva";

const { GridContent } = require('@ant-design/pro-layout');
import { ExclamationCircleFilled } from '@ant-design/icons';
import React, { Component, useState } from 'react';
import { Row, Col, Tag, Collapse, Tooltip, Form, Button, Image, Modal, Spin, message } from 'antd';
import styles from './style.less';//引入样式
import img1 from '@/assets/img1.png';
import MedicalRecord from '@/components/MedicalRecord';  // 全部病历的内容组件
import { visitIndicatorType, status } from '@/utils/common.js';
import { useIntl, FormattedMessage, history } from 'umi';
// import { getArrailUrl } from "@/utils/arrailUrl";//跳转路径
import commonStyle from "@/pages/common.less";//公共样式
import noData from "@/assets/<EMAIL>";//引入图标
import Screenage from "@/components/Screenage";  //关联影像组件
import InforConsent from "@/components/InforConsent";//关联知情同意书
import {StringUtils} from "@/utils/StringUtils"; //公共验证

const { Panel } = Collapse;
const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};
/*
  AllPatientRecord:
  clickPatientInfo:
  dispatch:
  getTodayWaitInfo:
  isPrint:
  onRef:
  patientData: {
    fileNumber:
    name:
    patientId:
  }
* */
class AllPatientRecord extends Component {
  constructor(props) {
    super(props);
    this.state = {
      patientData:props.patientData,//患者信息，上个页面传过来
      fileModal: false, // 归档弹窗
      emrLoading: false, //分页加载数据
      emrNextStatus: true, //分页加载数据
      pagination: {
        pageNum: 0, //必填 当前页
        pageSize: 10, //必填 限制页
        // emrId: props.emrId //病历号 必填
      },
      allPanelCount: 0, //全部病历总数量
      allPanelParams: {
        //全部病历入参
        tenantId: localStorage.getItem('tenantId'),//品牌id
        pageNum: 1, //当前页
        pageSize: props.isPrint?null:10, //限制页
        patientId: props.patientData?props.patientData.patientId:null, //患者标识
        emrId:  props.patientData?props.patientData.fileNumber:null, //大病历号
        beginTime: '', //就诊时间筛选开始时间
        endTime: '', //就诊时间筛选结束时间
        sort: 'DESC', //排序：正序是ASC 倒叙是DESC
        status: props.isPrint ? 4 : null, //就诊状态 状态：：2编辑中；3已完成；4归档
      },
      fileRes: {}, // 归档的结果
      medicalDetail: {
        //病历详情入参
        tenantId: 'ba67e6cf30dc4f9c9c46adef188bbd04', //平台标识
        emrId: '大病历号',
        emrSubId: '小病历号   //保存病历时电子病历系统自动生成的标识',
      },
      medicalDetailInfo: {  //病例详情数据
        "_id": "9cc9aad15eb245fe97298bcc91b4702e",
        "patientName": "测试奇", //患者姓名
        "createdGmtAt": "2022-05-12 23:59:59", //创建时间
        "userId": 3310, //医生标识
        "userName": "曹盟盟", //医生姓名
        "tenantId": "ba67e6cf30dc4f9c9c46adef188bbd04", //平台标识
        "organizationId": "5d244295bddb425fb4481895e2b1c326", //机构标识
        "organizationName": "瑞尔口腔运营测试诊所", //机构名称
        "patientId": 1398622, //患者标识
        "emrId": "TS11000495", //患者大病历号
        "emrSubId": "92721d6e2f1145b8bfbe3e4e429b049f", //患者小病历号
        "appointmentId": 2336963, //预约标识
        "illnessDesc": "复查,检查,洁治", //主诉
        "visitIndicator": 0, //初复诊标识 0初诊，1复诊
        "createName": "曹盟盟", //创建人姓名
        "createId": 3310, //创建人标识
        "status": 3, //病历状态 2编辑中；3已完成；4已归档
        "emrStatus": 1, //病历状态 代表病历是否暂存过 0否1是
        "auxiExam": "sr源文件卡死人我", //辅助检查
        "diag": [//诊断列表
          {
            "toothPosition": "", //牙位
            "value": "义齿修复,充填", //诊断内容
          }
        ],
        "dispose": [//处置
          {
            "toothPosition": "", //牙位
            "value": "签署根管治疗同意书。阿替卡因肾上腺素注射液局部麻醉，去净腐质，开髓，出血中，接髓顶，拔髓成型，5.25%次氯酸钠冲洗，Protaper根管预备至F3，工作长度mm，5.25%次氯酸钠，2%氯已定，17%EDTA，生理盐水交替冲洗润滑，95%乙醇终末冲洗干燥，暂封氢氧化钙糊剂，嘱一周复诊，嘱根管预备后注意事项。"//处置内容
          }
        ],
        "docOrder": "挨个不让他我认为他认为", //医嘱
        "examine": [//检查
          {
            "toothPosition": "14", //牙位
            "value": "1/3萌出 e"//检查项目
          }
        ],
        "genCond": "按个w奥尔格", //全身状况
        "pastHist": "rha 年前曾在外院洗牙，做过牙周治疗", //既往史
        "preIllness": "ag 词条添加测试", //现病史
        "treat": [//本次治疗
          {
            "toothPosition": "16 P;48 M;37 B", //牙位及牙面
            "value": "涂氟", //治疗项名称
            "linkCode": "k4.949"
          }
        ],
        "updateId": 3310, //更新人标识
        "updateName": "曹盟盟", //更新人姓名
        "updatedGmtAt": "2022-05-12 09:34:53", //更新时间
      }, // 从接口获取的病例详情数据
      allPanelList:[],// 请求全部病历数据
      photoStatus:false,//关联影像弹窗状态
      linkImgs:[],//辅助检查
      chooseImgs:[],//选择的影像
      choosePeopleInfo:{
      },//选择的病历
      inforStatus:false,//关联知情同意书弹窗状态
      sureLoading:false,//关联影像确定按钮loading
      sureMsgLoading:false,//关联知情同意书确定按钮loading
      uploadingStatus:true,//判断影像文件是否正在上传
      MrcuploadingStatus:true//判断同意书文件是否正在上传
    };
  }
 //初始化
  componentDidMount() {
    if(this.props.onRef){
      this.props.onRef(this);
    }
    if(this.props.patientData||this.props.emrId){//this.props.emrId是写完病历后返回全部病历带过来的参数
      this.getAllMedicalRecords();
    }

  }

  // eslint-disable-next-line react/no-deprecated
   // 监听全部病历数据变化
  componentWillReceiveProps(nextProps) {
    if(nextProps.patientData){
      if(!this.state.patientData||nextProps.patientData.patientId != this.state.patientData.patientId){
        // 页面中的操作都初始化一下
        this.setState({
          patientData:nextProps.patientData,
          fileModal: false, // 归档弹窗
          emrLoading: false, //分页加载数据
          emrNextStatus: true, //分页加载数据
          pagination: {
            pageNum: 0, //必填 当前页
            pageSize: 10, //必填 限制页
            // emrId: props.emrId //病历号 必填
          },
          allPanelCount: 0, //全部病历总数量
          allPanelParams: {
            //全部病历入参
            tenantId: localStorage.getItem('tenantId'),//品牌id
            pageNum: 1, //当前页
            pageSize: nextProps.isPrint?null:10, //限制页
            patientId: nextProps.patientData?nextProps.patientData.patientId:null, //患者标识
            emrId: nextProps.patientData?nextProps.patientData.fileNumber:null, //大病历号
            beginTime: '', //就诊时间筛选开始时间
            endTime: '', //就诊时间筛选结束时间
            sort: 'DESC', //排序：正序是ASC 倒叙是DESC
            status: nextProps.isPrint ? 4 : null, //就诊状态 状态：：2编辑中；3已完成；4归档
          },
          fileRes: {}, // 归档的结果
          medicalDetail: {
            //病历详情入参
            tenantId: localStorage.getItem("tenantId"), //平台标识
            emrId: null,//'大病历号',
            emrSubId: null  //保存病历时电子病历系统自动生成的标识',
          },
          medicalDetailInfo: null, // 从接口获取的病例详情数据
          allPanelList:[],// 请求全部病历数据
          photoStatus:false,
          linkImgs:[],//辅助检查
          chooseImgs:[],//选择的影像
          choosePeopleInfo:{
          },//选择的病历
          inforStatus:false,//关联知情同意书弹窗状态
        },()=> this.getAllMedicalRecords());

      }
    }

  }


  // 请求全部病历接口
  getAllMedicalRecords = (value) => {
    const { dispatch } = this.props;
    const { allPanelParams } = this.state;

    if(value){
      this.state.allPanelParams = {...value};
    }
    if(this.state.patientData.patientId){
      allPanelParams.patientId = this.state.patientData.patientId;
      allPanelParams.emrId = this.state.patientData.fileNumber;
    }
    if(this.props.wrpatientId && this.props.wremrId){
      allPanelParams.patientId = this.props.wrpatientId;
      allPanelParams.emrId = this.props.wremrId;
    }

    if(allPanelParams.pageNum===1){
      this.setState({
        allPanelList:[]
      })
    }
    allPanelParams.patientId = this.state.patientData.patientId;
    allPanelParams.emrId = this.state.patientData.fileNumber;

    //如果病历号为null,阻断
    if(StringUtils.isBlank(allPanelParams.emrId)){
      this.setState({
        allPanelList: [],
        allPanelCount:0
      });
      return false;
    }
    this.setState({
      emrLoading: true,
    });
    if (dispatch) {
      dispatch({
        type: 'homePage/allMedicalRecords',
        payload: this.state.allPanelParams,
        callback: (res) => {
          if (res.code == 200) {
            if(!res.rows||res.rows.length===0){
              this.setState({
                emrNextStatus: false,
                emrLoading: false,
              });
              return;
            }

            let arr=[];
            if (this.state.allPanelParams.pageNum === 1) {
              arr = res.rows;
            } else {
              arr = [...this.state.allPanelList, ...res.rows];
            }
            this.setState({
              allPanelList: arr,
              emrLoading: false,
              allPanelCount:arr.length
            });
          }
        },
      });
    }
  };



  // 鼠标移入移出影像图片
  onMouseIn = (mouseIn) => {
    this.setState({
      ['mouseIn']: mouseIn,
    });
  };
  // 编辑
  editMedical = (item) => {
    // console.log("itemitemitem===",item);
    // this.setState({
    //   medicalDetail: {
    //     tenantId: '',
    //     emrSubId: emrSubId,
    //     emrId: emrId,
    //   }
    // }, () => { this.getMedicalRecordsDetail(this.state.medicalDetail); })
    history.push({
      pathname: `/emr/MedicalCenter/CaseReport`,
      state: {
        emrSubId: item.emrSubId,
        patientId:item.patientId,
        appointmentId:this.state.patientData.id,
        // visitStatus: this.props.todyWaite.visitStatus,
      }
    })

  }
  // // 请求病例详情接口
  getMedicalRecordsDetail = (param) => {
    const { dispatch } = this.props;
    let params = param || this.state.medicalDetail;
    // console.log(params, this.props, this.state.medicalDetailInfo, '例详情接口入参参数999---')

    history.push({
      pathname: `/emr/MedicalCenter/CaseReport`,
      state: {
        'medicalDetailInfo': this.state.medicalDetailInfo,
        "clickPatientInfo": this.props.clickPatientInfo
      }
    })

    if (dispatch) {
      dispatch({
        type: 'homePage/medicalRecordDetail',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              medicalDetailInfo: res.content
            },)
          }
        }
      })
    }
  }
  // 归档
  openFileModal = (e, item) => {
    const { fileModal } = this.state;
    Modal.confirm({
      width: '492px',
      visible: {
        fileModal,
      },
      // centered:true, //弹窗居中
      title: (
        <div style={{ fontSize: '14px' }}>
          病历归档后，此份病历只允许查看，将不允许修改，您是否继续对此份病历进行【归档】操作？
        </div>
      ),
      icon: <ExclamationCircleFilled />,
      content: (
        <div style={{ color: 'rgba(0,0,0,0.45)', fontSize: '12px' }}>
          注：与此份病历相关联的影像与知情同意书不会受到此影响
        </div>
      ),
      okText: '是，归档',
      cancelText: '否',
      onOk: () => this.saveFileModal(item),// 归档确定
      onCancel: () => this.hideFileModal(),// 归档取消
    });
  };

  // 归档接口
  toFile = (item) => {
    // console.log("item====",JSON.stringify(item))
    const { dispatch } = this.props;
    let params = {
      emrSubId:item.emrSubId,
      userId:localStorage.getItem('userId'),
      userName:localStorage.getItem('userName'),
    }
    if (dispatch) {
      dispatch({
        type: 'homePage/BeFile',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '归档成功'
            })

            this.getAllMedicalRecords();// 重新调用全部病历列表
            this.props.getTodayWaitInfo();   //重新调用左侧列表
            this.hideFileModal(); // 归档取消
          }
        },
      });
    }
  };
  // 归档确认
  saveFileModal = (item) => {
    this.toFile(item);
    // this.hideFileModal();
  };
  // 归档取消
  hideFileModal = () => {
    const { fileModal } = this.state;
    this.setState({
      fileModal: false,
    });
  };


  //加载更多
  emrNext = () => {
    let {allPanelCount,allPanelParams} = this.state;
    this.setState({
      emrLoading: true,
    });
    // if(allPanelParams.length<10){}
    allPanelParams.pageNum++;
    this.getAllMedicalRecords(allPanelParams);
  };
  //关联影像
  relativeImage = (item) =>{
    // console.log("item====",JSON.stringify(item))
    this.setState({
      choosePeopleInfo:item,
      photoStatus:true,
    });
  }
  //关联影像确定
  AssisPhoto=()=>{
    const {choosePeopleInfo}=this.state;
    let ImageLibrarys=this.ImageListMsg.state.ImageLibrary;
    let arr=this.ImageListMsg.state.fileList;
    let otherCheck=this.ImageListMsg.state.uploadParams;
    let linkImgss= [];
    //对右侧未上传的判断
    if(arr.length>0 || otherCheck.uploadDate ||otherCheck.uploadType.length>0 || otherCheck.fileDesc){
      if(otherCheck.uploadType.length==0){
        message.warning({
          content: '请选择类型',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        return;
      }
      if(arr.length==0){
        message.warning({
          content: '请上传文件',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        return;
      }
      arr.forEach((text,index)=>{
        if(text.status=="uploading"){
          message.warning({
            content: '图片正在上传',
            className: 'custom-class',
            style: {
              marginTop: '20vh',
            },
          });
          this.state.uploadingStatus=false;
          return;
        }else{
          this.state.uploadingStatus=true;
        }
      })
      if(!otherCheck.uploadDate){
        message.warning({
          content: '请选择日期',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        return;
      }
    }


    if(this.state.uploadingStatus){
      arr.forEach((text,index)=>{
        if(text.response&&text.response.content){
          linkImgss.push({
            checkId:"",
            className:otherCheck.uploadType[0].key.className,
            classCode:otherCheck.uploadType[0].key.classCode,
            url:text.response.content.fileUrl,
            fileUrlView:text.response.content.fileUrlView,
            fileDesc:otherCheck.fileDesc,
            shootingTime:otherCheck.uploadDate,//拍摄时间
            createdGmtAt:""
          })
        }
      })
      if(linkImgss.length==0 && ImageLibrarys.length==0){
        message.warning({
          content: '请选择影像',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        return;
      }
      let linkImgs=[...ImageLibrarys,...linkImgss];
      const {dispatch} = this.props;
      let params = {
        tenantId: localStorage.getItem('tenantId'),//平台标识
        patientName: choosePeopleInfo.patientName, //患者姓名
        userId: localStorage.getItem('userId'), //医生标识
        userName: localStorage.getItem('userName'), //医生姓名
        organizationId: localStorage.getItem('organizationId'), //机构标识
        organizationName: localStorage.getItem('organizationName'), //机构名称
        patientId: choosePeopleInfo.patientId, //患者标识
        emrId: choosePeopleInfo.emrId, //患者大病历号
        emrSubId: choosePeopleInfo.emrSubId, //患者小病历号 必填
        linkImgs: linkImgs
      };
      this.setState({
        sureLoading:true
      })
      if (dispatch) {
        dispatch({
          type: 'WriteMedical/saveLinkImgsDataService',
          payload: params,
          callback: (res) => {
            if (res.code == 200) {
              message.success({
                content: '关联成功',
                className: 'custom-class',
                style: {
                  marginTop: '20vh',
                },
              });
              this.setState({
                photoStatus:false,
                sureLoading:false,
                uploadingStatus:true,

              });
              this.getAllMedicalRecords();
            } else {
              this.setState({
                sureLoading:false,
                uploadingStatus:true,
              });
              // console.log("关联失败")
            }
          }
        });
      }
    }
  }
  //关联影像弹窗关闭
  CancelPhoto=()=>{
    this.setState({
      photoStatus:false,
      sureLoading:false
    });
  }
  //关联知情同意书
  relativeagree = (item) =>{
    this.setState({
      choosePeopleInfo:item,
      inforStatus:true,
    });
  }
  // 关联知情同意书弹窗
  relevance=()=>{
    this.setState({
      inforStatus:true,
    });
  }
  //关联知情同意书确定
  AssisInfo=()=>{
    let arr=this.mrcsListMsg.state.fileList;
    let otherCheck=this.mrcsListMsg.state.MrcMessage;
    if(arr.length>0 || otherCheck){
      if(arr.length==0){
        message.warning({
          content: '请上传文件',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        return;
      }
      if(!otherCheck.tmptName && !otherCheck.tmptCode){
        message.warning({
          content: '请选择类型',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        return;
      }
    }
    arr.forEach((text,index)=>{
      if(text.status=="uploading"){
        message.warning({
          content: '图片正在上传',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        this.state.MrcuploadingStatus=false;
        // this.setState({
        //   MrcuploadingStatus:false
        // })
        return;
      }else{
        this.state.MrcuploadingStatus=true;
        // this.setState({
        //   MrcuploadingStatus:true
        // })
      }
    })

    // let mrcs=this.state.choosePeopleInfo.mrcs;
    if(this.state.MrcuploadingStatus){
      let mrcs=[];
      arr.forEach((text,index)=>{
        if(text.response&&text.response.content){
          mrcs.push({
            id:"",
            tmptName:otherCheck.tmptName,
            tmptCode:otherCheck.tmptCode,
            url:text.response.content.fileUrl,
            fileUrlView:text.response.content.fileUrlView,
            createdGmtAt:"",
          })
        }
      })
      this.setState({
        sureMsgLoading:true
      })
      const {choosePeopleInfo}=this.state;
      const {dispatch} = this.props;
      let params = {
        tenantId: localStorage.getItem('tenantId'),//平台标识
        patientName: choosePeopleInfo.patientName, //患者姓名
        userId: localStorage.getItem('userId'), //医生标识
        userName: localStorage.getItem('userName'), //医生姓名
        organizationId: localStorage.getItem('organizationId'), //机构标识
        organizationName: localStorage.getItem('organizationName'), //机构名称
        patientId: choosePeopleInfo.patientId, //患者标识
        emrId: choosePeopleInfo.emrId, //患者大病历号
        emrSubId: choosePeopleInfo.emrSubId, //患者小病历号 必填
        mrcs: mrcs
      };
      if (dispatch) {
        dispatch({
          type: 'WriteMedical/saveEmrMrcService',
          payload: params,
          callback: (res) => {
            if (res.code == 200) {
              message.success({
                content: '关联成功',
                className: 'custom-class',
                style: {
                  marginTop: '20vh',
                },
              });
              this.setState({
                inforStatus:false,
                MrcuploadingStatus:true,
                sureMsgLoading:false
              });
              this.getAllMedicalRecords();
            } else {
              this.setState({
                MrcuploadingStatus:true,
                sureMsgLoading:false
              });
              // console.log("关联失败")
            }
          }
        });
      }
    }
  }
  // 关闭关联知情同意书弹窗
  CancelAssisInfo=()=>{
    this.setState({
      inforStatus:false,
      sureMsgLoading:false
    });
  }
  render() {
    const {
      allPanelList,
      emrLoading,
      sureLoading,
      sureMsgLoading
    } = this.state;
    const {
      notOptions,
      isPrint
    } = this.props;
    return (
      <GridContent>
        {notOptions != false && isPrint!=true ? (
          <div>
            <div className={styles.AllContent}>
              {!allPanelList || allPanelList.length===0?
                <div>
                  <Spin spinning={emrLoading} tip="加载中...">
                    <div className={commonStyle.nodataContent} style={{marginTop: '20%'}}>
                      <img src={noData} className={commonStyle.imgStyle} alt=""/>
                      <div className={commonStyle.fontStyle}>暂无数据</div>
                    </div>
                  </Spin>
                </div>
                :
                <>
                  <Collapse
                    defaultActiveKey={this.props.isPrint?(allPanelList || []).map((item, index) => (index+1)+''):['1']}
                    // expandIconPosition="end"
                    bordered={false}
                    // onChange={this.onChange}
                    style={{width: '100%', marginBottom: '10px'}}
                    ghost
                  >
                    {(allPanelList || []).map((item, index) => (
                      <Panel
                        showArrow={false}
                        key={index+1}
                        style={{marginBottom: '10px'}}
                        header={
                          <div style={{color: 'rgba(0,0,0,0.45)', width: '100%'}}>
                            <Tooltip
                              placement="top"
                            >
                              <Row className={`${styles.main9}`}>
                                <Col span={18}>
                                  {notOptions != false ? <>
                                    <Tag
                                      color="success"
                                      className={
                                        item.status == 4//已归档
                                          ? styles.tagSuccess
                                          : item.status == 3//已完成（未归档）
                                          ? styles.tagWarn
                                          :item.status == 2//编辑中（未完成）
                                            ? styles.tagRed:""
                                      }
                                    >
                                      {item.status == 2?'未完成':item.status == 3?'未归档':item.status == 4?'已归档':''}
                                    </Tag>
                                  </> : <></>
                                  }
                                  <span style={{color: '#212121'}}>
                                {item.clinicTime}

                              </span>
                                  <span style={{marginLeft: 30}}>
                                    {visitIndicatorType[item.visitIndicator]}
                              </span>
                                  <span style={{marginLeft: 30}}>
                                医生：{item.userName}
                              </span>
                                </Col>
                                {notOptions != false ?
                                  <>
                                    {item.status == 4 && item.userId == localStorage.getItem("userId") && item.organizationId == localStorage.getItem("organizationId")?
                                      <>
                                        <Col style={{float: 'right'}}>
                                <span className={styles.txt11}
                                      hidden={this.props.isPrint}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        this.relativeImage(item);
                                      }}>
                                  关联影像
                                </span>
                                          <span
                                            className={styles.txt11}
                                            hidden={this.props.isPrint}
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              this.relativeagree(item);
                                            }}
                                          >
                                  关联同意书
                                </span>
                                        </Col>
                                      </>:
                                      <>
                                        {item.userId == localStorage.getItem("userId") && item.organizationId == localStorage.getItem("organizationId")?
                                          <Col style={{float: 'right'}}>
                                            {item.status == 2 || item.status==3?
                                              <span className={styles.txt11}
                                                    hidden={this.props.isPrint}
                                                    onClick={(e) => {
                                                      e.stopPropagation();
                                                      this.editMedical(item);
                                                    }}>编辑</span>:''
                                            }

                                            {item.status==3?
                                              <span
                                                className={styles.txt11}
                                                hidden={this.props.isPrint}
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  this.openFileModal(e, item);
                                                }}
                                              >
                                  归档
                                </span>:''
                                            }

                                          </Col>:""
                                        }
                                      </>

                                    }
                                  </>

                                  : (
                                    <></>
                                  )}
                              </Row>
                            </Tooltip>
                          </div>
                        }
                      >
                        <MedicalRecord
                          data={item}
                          notOptions={false}
                        />
                      </Panel>
                    ))}
                  </Collapse>
                  <Spin spinning={emrLoading} tip="加载中...">

                    {!this.state.emrNextStatus?
                      <div
                        style={{color:'rgba(0,0,0,0.4500)'}}
                        className={styles.loading_more}
                        hidden={this.props.isPrint||emrLoading}
                      >
                        没有更多啦！
                      </div>
                      : <div
                        className={styles.loading_more}
                        hidden={this.props.isPrint||emrLoading}
                        onClick={this.emrNext}
                      >
                        加载更多
                      </div>}

                  </Spin>
                </>
              }
            </div>
          </div>
        ) : (
          <div style={{ marginTop: '20px' }}>
            {allPanelList &&
            allPanelList.map((item, index) => (
              <div key={index}>
                <div className={styles.peopleInfo_border}>
                  <div className={styles.peopleInfo}>
                    <span className={styles.fontgray} style={{marginLeft:0}}>
                      {item.clinicTime}
                    </span>
                    <span className={styles.fontgray}>
                      {visitIndicatorType[item.visitIndicator]}
                    </span>
                    <span className={styles.fontgray}>医生：{item.userName}</span>
                  </div>
                </div>
                <MedicalRecord
                  notOptions={notOptions}
                  data={item}
                />
              </div>
            ))}
          </div>
        )}

        <Modal
          title="关联影像"
          visible={this.state.photoStatus}
          destroyOnClose={true}
          // onOk={this.AssisPhoto}
          onCancel={this.CancelPhoto}
          width={900}
          confirmLoading={sureLoading}
          footer={[
            <Button key="back" onClick={this.CancelPhoto}>
              取消
            </Button>,
            <Button type="primary" onClick={this.AssisPhoto}>
              确认上传
            </Button>,
          ]}
        >
          <Screenage
            emrId={this.state.choosePeopleInfo.emrId}
            chooseImgs={this.state.choosePeopleInfo.linkImgs}
            linkImgs={[]}
            onRef={(ref)=>this.ImageListMsg=ref}
          />
        </Modal>
        <Modal
          title="关联知情同意书"
          visible={this.state.inforStatus}
          destroyOnClose={true}
          onOk={this.AssisInfo}
          onCancel={this.CancelAssisInfo}
          okText="确认上传"
          cancelText="取消"
          width={800}
          confirmLoading={sureMsgLoading}
        >
          <InforConsent
            onRef={(ref)=>this.mrcsListMsg=ref}
          />
        </Modal>
      </GridContent>
    );
  }
}

export default connect(
  ({AllPatientRecord,}) => ({
    AllPatientRecord,
  }),
)(AllPatientRecord);
