/**
 * [页面中组件][悬窗] 预约详情组件 父组件为[appointmentParticulars]
 * 预约->预约日历面板->点击其中一条预约->普通预约详情悬窗
 */
import React,{ Component } from 'react';
import { Icon, Divider, Tag, Button, Popover, Input, Spin,message } from 'antd';
import QRCode from 'qrcode';
import styles from './AppointmentInfoSmallWindow.less';
import fuzhenfang from '@/assets/AppointmentRules/fuzhenfang.png';
import male from '@/assets/AppointmentRules/male.png';
import women from '@/assets/AppointmentRules/women.png';
import orangeSigh from '@/assets/AppointmentRules/orangeSigh.png'; // 预约未确认，去确认
import greenYes from '@/assets/AppointmentRules/greenYes.png'; // 预约已确认,取消
import redDifference from '@/assets/AppointmentRules/redDifference.png'; // 预约确认已取消,去确认
import ashAsk from '@/assets/AppointmentRules/ashAsk.png'; // 已联系未确认,去确认
import shortMessage from '@/assets/AppointmentRules/shortMessage.png'; // 信息
import Maximize from '@/assets/AppointmentRules/Maximize.png'; // 放大图标
import { getOrganizationInfo, noEmoji, randomLabel, screenData } from '@/utils/utils';
import chuzhenfang from '@/assets/AppointmentRules/chuzhenfang.png';
import jizhenfang from '@/assets/AppointmentRules/jizhenfang.png';
import iphonHui from '@/assets/AppointmentRules/iphonHui.png';
import xin from '@/assets/AppointmentRules/xin.png';
import contentsTextImg1 from '@/assets/AppointmentRules/fuzhenfang.png';
import firstVisit from '@/assets/registerAndArrival/legend_icon2.png';
import Fu from '@/assets/registerAndArrival/fu.png';
import Ellipsis from '@/components/Ellipsis';
import { connect } from 'dva';
import moment from 'moment';
import classNames from 'classnames';
import {
  CLOSE_AppointDetailsHungWindow,
  CHANGE_RemarkByAppointmentInfoSmallWindow,
  eventBus,
  formatTime,
} from '@/utils/CalendarUtils';
import $ from 'jquery';
import _ from 'lodash';
import redWarning from "@/assets/redWarning.png";
import Manualicon from "@/components/AgendaCaleandar/Elements/Manualicon";
import timeoutIcon from '@/assets/home/<USER>'; // 超时标签
import timeIcon from '@/assets/home/<USER>'; // 等待标签
const { TextArea } = Input;
@connect(({ Getscreen, appointmentNew, loading }) => ({
  Getscreen,
  appointmentNew,
  loading
}))
export default class AppointmentInfoSmallWindow extends Component {
  constructor(props) {
    super(props);
    const { appointmentInfoOfTableDto } = this.props
    let { remark } = appointmentInfoOfTableDto || {};
    this.state = {
      arr: [],
      remarksValue: '',
      visiblePopover: false,
      tabHeightType: false,
      labelArr: [],
      contentsDetailType:false, // 是否更改备注
      remarksByAppointmentInfoSmallWindow:remark, // 备注

      allergyList: [], // 患者标签过敏史
      medicalHistoryList: [], // 患者标签既往史
      inDrugList: [], // 患者标签在服药物
      WaitFormatTime:null,    // 格式化后的等待时间
      isWaitStute: null,      // 是否是等待状态 0:不等待 1:等待 2:等待超时
      materialQRImgUrl:null,  // 进销存获取患者信息的二维码
    }
    this.onHide = _.debounce(this.onHide,100);
    this.contentsInputOnBlur = _.debounce(this.contentsInputOnBlur,300);
  }
  componentDidMount() {
    this.props.onRef && this.props.onRef(this)
    const { appointmentInfoOfTableDto } = this.props
    let { remark, materialQRKey } = appointmentInfoOfTableDto || {};
    this.setState({
      arr: randomLabel(this.state.arr),
      remarksByAppointmentInfoSmallWindow:remark, // 备注
      contentsDetailType:false, // 是否更改备注
    })
    // 获取患者信息的二维码
    this.getMaterialQRCode(materialQRKey)
    // 广播: 关闭预约详情的广播
    eventBus.on(CLOSE_AppointDetailsHungWindow,()=>{
      this.onHide()
    })
    // 开启等待时长的正计时
    // this.IntervalByformatTime()
  }

  componentWillUnmount(){
    if (this.IntervalId) { clearInterval(this.IntervalId) }
  }

  // 开启等待时长的正计时
  IntervalByformatTime=(appointmentInfoOfTableDto)=>{
    // const { appointmentInfoOfTableDto } = this.props
    let {
      isComeVisit,               // 是否已到诊
      isConsultationVisit,       // 0:未接诊 1:已接诊
      waitingTimes,              // 等待时长(秒)
      isWaitingOver,             // 等待超时(1是，0否)
    } = appointmentInfoOfTableDto || {}

    const OrganizationInfo = getOrganizationInfo()// 收费模式(1规范化，2简洁化，3无医生)


    console.log('appointmentInfoOfTableDto 哈哈哈哈 :: ',appointmentInfoOfTableDto);

    if(
      isComeVisit == 1 &&
      isConsultationVisit == 0 &&
      OrganizationInfo.chargeModel == 1 &&
      waitingTimes != null
    ) {
      // 开始等待时长的计时
      this.setState({
        isWaitStute: isWaitingOver ? 2 : 1,  // 是否是等待状态 0:不等待 1:等待 2:等待超时
      },()=>{
        if (this.IntervalId) { clearInterval(this.IntervalId) }
        let seconds = waitingTimes ? waitingTimes : 0; // 开始的秒数
        this.IntervalId = setInterval(() => {
          this.setState({
            WaitFormatTime: formatTime(seconds) // 每秒输出一次格式化后的时间
          })
          seconds++;
        }, 1000);
      })
    }else  {
      this.setState({
        isWaitStute: null,   // 补充如果是已接诊状态则清除等待时长的计时
        WaitFormatTime:null,
      })
    }
  }

 /* componentWillUnmount() {
    eventBus.removeListener(CLOSE_AppointDetailsHungWindow)
  }*/

  componentDidUpdate(prevProps, prevState, snapshot) {
    // 打开弹窗 根据患者id更新患者信息 更新获取字典项 主诉 预约类型 结算方式 和 手工标签
    if (this.props.appointmentInfoOfTableDto && prevProps.appointmentInfoOfTableDto) {
      const { appointmentId:thisByAppointmentId,remark:thisByRemark } = this.props.appointmentInfoOfTableDto;
      const { appointmentId:prevPropsByAppointmentId,remark:prevPropsByRemark} = prevProps.appointmentInfoOfTableDto;
      if (thisByAppointmentId != prevPropsByAppointmentId || (thisByRemark != prevPropsByRemark)){
        this.setState({
          arr: [],
          remarksValue: '',
          visiblePopover: false,
          tabHeightType: false,
          labelArr: [],
          contentsDetailType:false, // 是否更改备注
          remarksByAppointmentInfoSmallWindow:thisByRemark, // 备注
        })
      }
    }
  }
  // 放大
  maximize = () => {
    const { maximizeAppointmentDetails, appointmentInfoOfTableDto, operationType, staffStatus } = this.props
    maximizeAppointmentDetails && maximizeAppointmentDetails(appointmentInfoOfTableDto, operationType, staffStatus)
    this.setState({
      visiblePopover: false,
      remarksValue: ''
    })
  }

  // 到诊按钮 调转页面
  arrival = (appointmentInfoOfTableDto) => {
    const { arrival } = this.props
    arrival && arrival(appointmentInfoOfTableDto)
    this.setState({
      visiblePopover: false,
      remarksValue: ''
    })
  };
  // 加入排队
  lineUpList = () => {
    const { foinQueueList } = this.props
    foinQueueList && foinQueueList(1)
    this.setState({
      visiblePopover: false,
      remarksValue: ''
    })
  }
  // 移除排队
  onConfirmIsWaitingList = () => {
    const { foinQueueList } = this.props
    foinQueueList && foinQueueList(2)
    this.setState({
      visiblePopover: false,
      remarksValue: ''
    })
  }
  // 改约
  appointments = (appointmentInfoOfTableDto) => {
    const { changeContract } = this.props
    changeContract && changeContract(appointmentInfoOfTableDto)
    this.setState({
      visiblePopover: false,
      remarksValue: ''
    })
  }
  // 取消预约
  cancelSubscribe = () => {
    const { cancelReservation } = this.props
    cancelReservation && cancelReservation()
    this.setState({
      visiblePopover: false,
      remarksValue: ''
    })
  }
  // 一键到诊
  oneKeyArrival = (appointmentInfoOfTableDto) => {
    const { oneKeyArrival } = this.props
    oneKeyArrival && oneKeyArrival(appointmentInfoOfTableDto)
    this.setState({
      visiblePopover: false,
      remarksValue: ''
    })
  }
  // 关闭点击
  closeTooltip = () => {
   this.props.updateStatus && this.props.updateStatus()
    this.setState({
      visiblePopover: false,
      remarksValue: ''
    })
  }
  // 预约备注改变
  remarksChange = (e) => {
    console.log(e.target.value);
    this.setState({
      remarksValue: e.target.value
    })
  }
  // 去确认
  onOpen = () => {
    this.setState({
      visiblePopover: true,
      remarksValue: null,
      WaitFormatTime:null,    // 格式化后的等待时间
      isWaitStute: null,      // 是否是等待状态 0:不等待 1:等待 2:等待超时
    })
  }
  // 关闭浮窗
  onHide = () => {
    if (this.IntervalId) { clearInterval(this.IntervalId) }
    this.setState({
      visiblePopover: false,
      remarksValue: null,
      WaitFormatTime:null,    // 格式化后的等待时间
      isWaitStute: null,      // 是否是等待状态 0:不等待 1:等待 2:等待超时
    })
  }
  getWeek (date) { // 参数时间戳
    let week = moment(date).day()
    switch (week) {
      case 1:
        return '周一'
      case 2:
        return '周二'
      case 3:
        return '周三'
      case 4:
        return '周四'
      case 5:
        return '周五'
      case 6:
        return '周六'
      case 0:
        return '周日'
    }
  }
  // 获取客户标签盒子高度
  getHeight = () => {
    const { appointmentInfoOfTableDto } = this.props
    const { patientInfoDto } = appointmentInfoOfTableDto || {}
    const { labelList, preferenceList, customerMedicalLabelDto } = patientInfoDto || {}
    const { allergyList, medicalHistoryList, inDrugList } = customerMedicalLabelDto || {}
    let arr = labelList || []
    let arr2 = preferenceList || []
    let arr3 = arr2.concat(arr)
    let labelArr = []
    arr.map((item)=>{
      if (item != 'VIP客户') {
        labelArr.push({ name: item })
      }
    })
    this.setState({
      labelArr: randomLabel(labelArr),
      allergyList,
      medicalHistoryList,
      inDrugList
    },()=>{
      let tabHeight = document.getElementById('tabHeight') && document.getElementById('tabHeight').offsetHeight
      console.log(tabHeight,'tabHeighttabHeight');
      this.setState({
        tabHeightType: tabHeight > 33,

      })
    })
  }

  // 刷新等待时长
 /* setWaitingTime = () => {
    this.IntervalByformatTime()
  }*/

  // 客户标签展开
  downClick = () => {
    this.setState({
      tabHeightType: false
    })
  }
  // 修改备注失去焦点
  contentsInputOnBlur = (appointmentId,remark) => {
    console.log('contentsInputOnBlur12 :: ',appointmentId,remark);
    const { dispatch } = this.props;
    if (remark && remark.length > 200) {
      message.warning('不能大于200字符！');
      $('#remarksByAppointmentInfoSmallWindowInput') && $('#remarksByAppointmentInfoSmallWindowInput').focus()
      return;
    }
    if (remark && (remark.match(noEmoji))) {
      message.warning('输入格式错误！');
      $('#remarksByAppointmentInfoSmallWindowInput') && $('#remarksByAppointmentInfoSmallWindowInput').focus()
      return;
    }
    const { appointmentInfoOfTableDto } = this.props
    const {
      appointmentId:appointmentIdByOld,
    } = appointmentInfoOfTableDto || {};

    dispatch({
      type: 'Getscreen/remarkReplace',
      payload: {
        appointmentId, // 预约id
        remark, // 填写的 备注
      },
    }).then((res) => {
      message.success('预约备注修改成功');
      if(appointmentIdByOld != appointmentId) {
        this.setState({
          contentsDetailType: false,
        })
      }else {
        this.setState({
          contentsDetailType: false,
          remarksByAppointmentInfoSmallWindow: remark,
        })
      }
      eventBus.emit(CHANGE_RemarkByAppointmentInfoSmallWindow, {
        appointmentId, // 预约id
        remark,        // 填写的 备注
      })
    }).catch((err) => {

    });
  };

  // 生成进销存获取用户信息的二维码
  getMaterialQRCode = (text) => {
    QRCode.toDataURL(JSON.stringify({
      materialQRKey:text
    })).then(url => {
      this.setState({
        materialQRImgUrl: url,
      });
    }).catch(err => {
      message.error('生成二维码失败～!');
    })
  }

  render() {
    const { appointmentInfoOfTableDto, operationType, staffStatus, loading } = this.props
    const {tabHeightType, labelArr,remarksByAppointmentInfoSmallWindow, allergyList, medicalHistoryList, inDrugList, materialQRImgUrl} = this.state
    let {
      appointmentDate,
      timeStart,
      timeEnd,
      appointmentConfirmStatus,
      appointmentTaskNotesList,
      patientInfoDto,
      isToday,
      isShuangAbout,
      isWaitingList,
      isComeVisit,
      waitingListBaseDto,
      appointmentDateAndStartTimeStr,
      visitButtonStatus,   // 是否有权限到诊 1有权限  0无权限
      appointmentDatetimeStr,
      timeLength,
      complaintList,
      createdGmtAt,
      createUserName,
      remark,
      taskType,
      appointmentIconNewDtoList, // 治疗标签
      typeColor,
      taskId,
      appointmentId,
      isFirstVisit,
      emergency,
      inTime, // 到诊时间
      waitingTimes,              // 等待时长(秒)
      isWaitingOver,             // 等待超时(1是，0否)
    } = appointmentInfoOfTableDto || {};
    const DoctorType = localStorage.getItem('doctorIdentification');
    const { claimTime, claimRemark, resourceDtoList } = waitingListBaseDto || {};
    const operationTypeState = operationType == 0;  // 当前event是否可操作 1为可操作 0为不可操作
    const visitButtonStatusValue = visitButtonStatus != 1 // 当前用户是否权限到诊 1有权限  0无权限
    const { name, sex, fileNumber, age, oftenTel, labelList, preferenceList,patientRecommendName, refereeRelationship, recommendId } = patientInfoDto || {}
    let appointmentInfoCard = loading.effects['Getscreen/appointmentInfoCard']
    let remarkReplace = loading.effects['Getscreen/remarkReplace']
    return (
      <div onClick={
        (e)=>{
          e.stopPropagation()
          console.log('eeee123123 :: ',e.target, $(e.target).attr("id"));
          if(e.target && $(e.target).attr("id") == "remarksByAppointmentInfoSmallWindowInput") {
            // $('#remarksByAppointmentInfoSmallWindowInput') && $('#remarksByAppointmentInfoSmallWindowInput').focus()
          }else {
            console.log('$(\'#remarksByAppointmentInfoSmallWindowInput\') :: ', $('#remarksByAppointmentInfoSmallWindowInput')[0]);
            $('#remarksByAppointmentInfoSmallWindowInput') && $('#remarksByAppointmentInfoSmallWindowInput').blur()
          }
          }} id={"patientAppointmentById"}>
        {
          this.props.tooltipType &&
            <Spin spinning={!!appointmentInfoCard || !!remarkReplace || !!loading.effects['appointmentNew/AppointmenDiagnosis']}>
              <div className={styles.bigBox}>
                <div style={typeColor ? { background: `${typeColor}` } : {}} className={styles.bgLeft}></div>
                <div className={styles.topInfo}>
                  <div className={styles.top}>
                    <div className={styles.topLeft} style={{lineHeight: '20px', display: 'flex'}}>
                      <Popover placement="topLeft" content={<span className={styles.name}>{name}</span>} trigger="hover">
                    <span style={{maxWidth: 130, display: 'inline-block'}}>
                      <Ellipsis lines={1}><span className={styles.name}>{name}</span></Ellipsis>
                    </span>
                      </Popover>
                      {
                        sex == 1 ? <img src={male} alt='' /> : sex == 2 ? <img src={women} alt='' /> : null
                      }
                      {
                        isFirstVisit == 2 ?
                          <img src={Fu} alt='' />: <img src={firstVisit} alt='' />
                      }
                      {
                        emergency == 1 ?
                          <img src={jizhenfang} alt='' />: null
                      }
                      {
                        taskType == 3 ? null:
                          taskType == 1
                            ?
                            <>
                              <div className={styles.confirmStatus} style={{color: '#52C41A'}}>
                                <img src={greenYes} alt='' />预约已确认
                                {
                                  isShuangAbout == 1 || operationTypeState || appointmentConfirmStatus == 2 ?
                                    null :
                                    <span onClick={(e)=>{e.stopPropagation();this.props.ReservationBtnNo(appointmentDateAndStartTimeStr)}}>，<span style={{textDecoration: 'underline'}}>取消</span></span>
                                }
                              </div>
                            </>
                            :
                            taskType == 2 ?
                              <div className={styles.confirmStatus} style={{color: '#666666'}}>
                                <img src={ashAsk} alt='' />已联系未确认
                                {
                                  isShuangAbout == 1 || operationTypeState || appointmentConfirmStatus == 2?
                                    null:
                                    <span>，
                                  <Popover
                                    trigger={'click'}
                                    id={'PopoverByConfirmation'}
                                    visible={this.state.visiblePopover}
                                    content={
                                      <div>
                                        <div style={{display: 'flex',justifyContent: 'space-between',borderBottom: '1px solid rgba(0, 0, 0, 0.06)',padding: 16}}>
                                          <div><img src={iphonHui} alt='' style={{width: 18,height: 18}} />{oftenTel}</div>
                                          <div><Icon type="close" onClick={this.onHide} /></div>
                                        </div>
                                        <div className={styles.remarks}>
                                          <div>预约确认备注</div>
                                          <div>
                                            <TextArea rows={4} placeholder={'请填写备注信息'} value={this.state.remarksValue} onChange={this.remarksChange} />
                                          </div>
                                        </div>
                                        <div className={styles.button}>
                                          <Button type={'primary'} onClick={(e) => {e.stopPropagation();this.props.RadioonChange(1, appointmentDateAndStartTimeStr,this.state.remarksValue);}}>已确认</Button>
                                        </div>
                                      </div>
                                    }
                                    overlayClassName={styles.appointmentConfirm}
                                  >
                                    <span style={{textDecoration: 'underline', cursor: 'pointer'}} onClick={this.onOpen}>去确认</span>
                                  </Popover>
                                </span>
                                }
                              </div>:
                              taskType == 4 ?
                                <div className={styles.confirmStatus} style={{color: '#FF2E2E'}}>
                                  <img src={redDifference} alt='' />预约确认已取消
                                  {
                                    isShuangAbout == 1 || operationTypeState || appointmentConfirmStatus == 2?
                                      null:
                                      <span>
                                  <Popover
                                    trigger={'click'}
                                    id={'PopoverByConfirmation'}
                                    visible={this.state.visiblePopover}
                                    content={
                                      <div>
                                        <div style={{display: 'flex',justifyContent: 'space-between',borderBottom: '1px solid rgba(0, 0, 0, 0.06)',padding: 16}}>
                                          <div><img src={iphonHui} alt='' style={{width: 18,height: 18}} />{oftenTel}</div>
                                          <div><Icon type="close" onClick={this.onHide} /></div>
                                        </div>
                                        <div className={styles.remarks}>
                                          <div>预约确认备注</div>
                                          <div>
                                            <TextArea rows={4} placeholder={'请填写备注信息'} value={this.state.remarksValue} onChange={this.remarksChange} />
                                          </div>
                                        </div>
                                        <div className={styles.button}>
                                          <Button type={'primary'} onClick={(e) => {e.stopPropagation();this.props.RadioonChange(1, appointmentDateAndStartTimeStr,this.state.remarksValue);}}>已确认</Button>
                                          <Button onClick={(e) => {e.stopPropagation();this.props.RadioonChange(2, appointmentDateAndStartTimeStr,this.state.remarksValue);}}>已联系未确认</Button>
                                        </div>
                                      </div>
                                    }
                                    overlayClassName={styles.appointmentConfirm}
                                  >
                                    <span style={{textDecoration: 'underline', cursor: 'pointer'}} onClick={this.onOpen}>去确认</span>
                                  </Popover>
                                </span>
                                  }
                                </div>:
                                <div className={styles.confirmStatus} style={{color: '#ED6A09'}}>
                                  <img src={orangeSigh} alt='' />预约未确认
                                  {
                                    isShuangAbout == 1 || operationTypeState || appointmentConfirmStatus == 2?
                                      null:
                                      <span>，
                                  <Popover
                                    trigger={'click'}
                                    id={'PopoverByConfirmation'}
                                    visible={this.state.visiblePopover}
                                    content={
                                      <div>
                                        <div style={{display: 'flex',justifyContent: 'space-between',borderBottom: '1px solid rgba(0, 0, 0, 0.06)',padding: 16}}>
                                          <div><img src={iphonHui} alt='' style={{width: 18,height: 18}} />{oftenTel}</div>
                                          <div><Icon type="close" onClick={this.onHide} /></div>
                                        </div>
                                        <div className={styles.remarks}>
                                          <div>预约确认备注</div>
                                          <div>
                                            <TextArea rows={4} placeholder={'请填写备注信息'} value={this.state.remarksValue} onChange={this.remarksChange} />
                                          </div>
                                        </div>
                                        <div className={styles.button}>
                                          <Button type={'primary'} onClick={(e) => {e.stopPropagation();this.props.RadioonChange(1, appointmentDateAndStartTimeStr,this.state.remarksValue);}}>已确认</Button>
                                          <Button onClick={(e) => {e.stopPropagation();this.props.RadioonChange(2, appointmentDateAndStartTimeStr,this.state.remarksValue);}}>已联系未确认</Button>
                                        </div>
                                      </div>
                                    }
                                    overlayClassName={styles.appointmentConfirm}
                                  >
                                    <span style={{textDecoration: 'underline', cursor: 'pointer'}} onClick={this.onOpen}>去确认</span>
                                  </Popover>
                                </span>
                                  }
                                </div>
                      }
                      {
                        appointmentInfoOfTableDto && appointmentInfoOfTableDto.isSendShortMessage == 1 ?
                          <img src={shortMessage} style={{marginLeft: 0, marginTop: 1}} alt='' />:
                          null
                      }
                    </div>
                    <div className={styles.topRight}>
                      <img src={Maximize} alt='' onClick={(e) => {
                        e.stopPropagation();
                        this.maximize();
                      }} />
                      <Icon id={'closeByAppointmentInfoSmallWindow'} type="close" style={{marginTop: 3}} onClick={this.closeTooltip} />
                    </div>
                  </div>
                  <div className={styles.info}>
                    <span>{fileNumber}</span><Divider type="vertical" /><span>{screenData(age)}</span><Divider type="vertical" /><span>{oftenTel}</span>
                  </div>
                  {
                    tabHeightType ?
                      <div className={styles.tab} id='tabHeight' style={{height: 23, overflow: 'hidden', marginTop: 9}}>
                        <div>
                          {
                            allergyList && allergyList.length ?
                              <Popover
                                overlayClassName={styles.complaintPopover}
                                getPopupContainer={() => document.getElementById('tabHeight')}
                                content={
                                  <div style={{color: 'rgba(255, 255, 255, 0.8500)', maxWidth: '250px'}}>
                                    过敏史：
                                    {
                                      allergyList.map((item,index)=>{
                                        return (
                                          <span key={index}>{item}{allergyList && allergyList.length - 1 == index ? null : '、'}</span>
                                        )
                                      })
                                    }
                                  </div>
                                }
                                placement={'topLeft'}
                              >
                                <Tag style={{color: '#FF1313', background: '#FFEEEE', border: '1px solid #FF3F3F'}}>
                                  <img src={redWarning} style={{width: 16, height: 16, verticalAlign: 'sub', marginRight: 2}} alt=""/>
                                  有过敏史
                                </Tag>
                              </Popover>
                              : null
                          }
                          {
                            medicalHistoryList && medicalHistoryList.length ?
                              <Popover
                                overlayClassName={styles.complaintPopover}
                                getPopupContainer={() => document.getElementById('tabHeight')}
                                content={
                                  <div style={{color: 'rgba(255, 255, 255, 0.8500)', maxWidth: '250px'}}>
                                    既往病史：
                                    {
                                      medicalHistoryList.map((item,index)=>{
                                        return (
                                          <span key={index}>{item}{medicalHistoryList && medicalHistoryList.length - 1 == index ? null : '、'}</span>
                                        )
                                      })
                                    }
                                  </div>
                                }
                                placement={'top'}
                              >
                                <Tag style={{color: '#FF1313', background: '#FFEEEE', border: '1px solid #FF3F3F'}}>
                                  <img src={redWarning} style={{width: 16, height: 16, verticalAlign: 'sub', marginRight: 2}} alt=""/>
                                  有既往病史
                                </Tag>
                              </Popover>
                              : null
                          }
                          {
                            inDrugList && inDrugList.length ?
                              <Popover
                                overlayClassName={styles.complaintPopover}
                                getPopupContainer={() => document.getElementById('tabHeight')}
                                content={
                                  <div style={{color: 'rgba(255, 255, 255, 0.8500)', maxWidth: '250px'}}>
                                    在服药物：
                                    {
                                      inDrugList.map((item,index)=>{
                                        return (
                                          <span key={index}>{item}{inDrugList && inDrugList.length - 1 == index ? null : '、'}</span>
                                        )
                                      })
                                    }
                                  </div>
                                }
                                placement={'topLeft'}
                              >
                                <Tag style={{color: '#FF1313', background: '#FFEEEE', border: '1px solid #FF3F3F'}}>
                                  <img src={redWarning} style={{width: 16, height: 16, verticalAlign: 'sub', marginRight: 2}} alt=""/>
                                  有在服药物
                                </Tag>
                              </Popover>
                              : null
                          }
                          {
                            labelArr.map((item,index)=>{
                              return (
                                <span key={index}>
                                <Tag
                                  color={item.style.background}
                                  // style={item.check ? {background: item.style.borderColor,color: '#ffffff'} : null}
                                >
                                  {item.name}
                                </Tag>
                              </span>
                              )
                            })
                          }
                        </div>
                        <div style={{cursor: 'pointer'}} onClick={this.downClick}><Icon type="down" style={{fontSize: 12}} /></div>
                      </div>:
                      <div className={styles.tab} id='tabHeight'>
                        <div>
                          {
                            allergyList && allergyList.length ?
                              <Popover
                                overlayClassName={styles.complaintPopover}
                                getPopupContainer={() => document.getElementById('tabHeight')}
                                content={
                                  <div style={{color: 'rgba(255, 255, 255, 0.8500)', maxWidth: '250px'}}>
                                    过敏史：
                                    {
                                      allergyList.map((item,index)=>{
                                        return (
                                          <span key={index}>{item}{allergyList && allergyList.length - 1 == index ? null : '、'}</span>
                                        )
                                      })
                                    }
                                  </div>
                                }
                                placement={'topLeft'}
                              >
                                <Tag style={{color: '#FF1313', background: '#FFEEEE', border: '1px solid #FF3F3F'}}>
                                  <img src={redWarning} style={{width: 16, height: 16, verticalAlign: 'sub', marginRight: 2}} alt=""/>
                                  有过敏史
                                </Tag>
                              </Popover>
                              : null
                          }
                          {
                            medicalHistoryList && medicalHistoryList.length ?
                              <Popover
                                overlayClassName={styles.complaintPopover}
                                getPopupContainer={() => document.getElementById('tabHeight')}
                                content={
                                  <div style={{color: 'rgba(255, 255, 255, 0.8500)', maxWidth: '250px'}}>
                                    既往病史：
                                    {
                                      medicalHistoryList.map((item,index)=>{
                                        return (
                                          <span key={index}>{item}{medicalHistoryList && medicalHistoryList.length - 1 == index ? null : '、'}</span>
                                        )
                                      })
                                    }
                                  </div>
                                }
                                placement={'top'}
                              >
                                <Tag style={{color: '#FF1313', background: '#FFEEEE', border: '1px solid #FF3F3F'}}>
                                  <img src={redWarning} style={{width: 16, height: 16, verticalAlign: 'sub', marginRight: 2}} alt=""/>
                                  有既往病史
                                </Tag>
                              </Popover>
                              : null
                          }
                          {
                            inDrugList && inDrugList.length ?
                              <Popover
                                overlayClassName={styles.complaintPopover}
                                getPopupContainer={() => document.getElementById('tabHeight')}
                                content={
                                  <div style={{color: 'rgba(255, 255, 255, 0.8500)', maxWidth: '250px'}}>
                                    在服药物：
                                    {
                                      inDrugList.map((item,index)=>{
                                        return (
                                          <span key={index}>{item}{inDrugList && inDrugList.length - 1 == index ? null : '、'}</span>
                                        )
                                      })
                                    }
                                  </div>
                                }
                                placement={'topLeft'}
                              >
                                <Tag style={{color: '#FF1313', background: '#FFEEEE', border: '1px solid #FF3F3F'}}>
                                  <img src={redWarning} style={{width: 16, height: 16, verticalAlign: 'sub', marginRight: 2}} alt=""/>
                                  有在服药物
                                </Tag>
                              </Popover>
                              : null
                          }
                          {
                            labelArr.map((item,index)=>{
                              return (
                                <span key={index} style={{display: 'inline-block',marginBottom: 8}}>
                                  <Tag
                                    color={item.style.background}
                                    // style={item.check ? {background: item.style.borderColor,color: '#ffffff'} : null}
                                  >
                                    {item.name}
                                  </Tag>
                                </span>
                              )
                            })
                          }
                        </div>
                      </div>
                  }
                </div>
                <div className={styles.middleInfo}>
                  <div className={styles.infoItem}>
                    <div>预约时间：</div>
                    <div>{appointmentDate ? <span>{moment(appointmentDate).format('YYYY/MM/DD')} &nbsp;({this.getWeek(appointmentDate)}) &nbsp;{timeStart}-{timeEnd}</span> : null}</div>
                    {/*<div>{appointmentDatetimeStr}</div>*/}
                    {
                      materialQRImgUrl&&materialQRImgUrl!=null&&<Popover placement="bottomRight"  overlayClassName={'materialQRBody'} getPopupContainer={triggerNode => triggerNode.parentNode.parentNode} content={<div className={styles.materialQRBody}><img src={materialQRImgUrl} /><div><h6>扫码获取患者信息</h6><p>请使用佳沃思APP内的进销存模块扫码。</p></div></div>} trigger={'hover'}>
                        <div className={styles.materialQRBodyIcon}></div>
                      </Popover>
                    }
                  </div>
                  <div className={styles.infoItem}>
                    <div>预约时长：</div>
                    <div>{timeLength}分钟</div>
                  </div>
                  <div className={styles.infoItem}>
                    <div>预约医生：</div>
                    <div>{appointmentInfoOfTableDto && appointmentInfoOfTableDto.doctorNameOfAppointment}</div>
                  </div>
                  <div className={styles.infoItem}>
                    <div>预约主诉：</div>
                    <div>
                      {
                        complaintList && complaintList.map((k, index) => {
                          if (k.treatType == 1 || k.treatType == 2) {
                            return (<span className={styles.contentsTextSpan} key={index}>
                                        {k.complaintMergeName ? k.complaintMergeName : k.complaintName}
                              <img className={styles.contentsTextImg1} style={{width: 14,height: 14, marginTop: '-5px', marginLeft: 4 }} src={xin} alt="" />&nbsp;&nbsp;
                                              </span>)
                          }
                          return (<span className={styles.contentsTextSpan} key={index}>
                                        {k.medicalDictionaryName}
                            <img className={styles.contentsTextImg1} style={{width: 14,height: 14, marginTop: '-5px', marginLeft: 4 }} src={contentsTextImg1} alt="" />&nbsp;&nbsp;
                                              </span>)
                        })
                      }
                    </div>
                  </div>
                  <div className={styles.infoItem}>
                    <div>操作人：</div>
                    <div>{createUserName}</div>
                  </div>
                  <div className={styles.infoItem}>
                    <div>操作时间：</div>
                    <div>{createdGmtAt}</div>
                  </div>
                  <div className={styles.infoItem}>
                    <div>到诊时间：</div>
                    <div className={styles.infoItemContent}>
                      <div className={styles.inTime}>
                        {isComeVisit == 1 && inTime ? inTime : '-'}
                      </div>

                      {!!this.state.isWaitStute  &&
                        <div className={classNames({
                          [styles.redContent]: true,
                          [styles.timeoutContent]: this.state.isWaitStute == 2,
                        })}>
                          <img src={this.state.isWaitStute == 2 ?  timeoutIcon : timeIcon} />
                          <span>已等待: {this.state.WaitFormatTime}</span>
                        </div>
                      }

                    </div>
                  </div>
                  <div className={styles.infoItem1}>
                    <div className={styles.left}>备注：</div>
                    {this.state.contentsDetailType ?
                      <div className={classNames({
                        [styles.right]:true,
                        [styles.rightInput]:true,
                      })}>
                        <TextArea 
                          rows={3} 
                          id={'remarksByAppointmentInfoSmallWindowInput'}
                          /* ref={function(input) {
                            if(!!input){
                              input.focus() }
                          }} */

                          onClick={(e)=>{
                            e.stopPropagation()
                            // $('#remarksByAppointmentInfoSmallWindowInput') && $('#remarksByAppointmentInfoSmallWindowInput').focus()
                          }}
                          onFocus={(e)=>{
                            // e && e.focus()
                            // console.log("onFocusonFocus :: 哈哈",e);
                            // $('#remarksByAppointmentInfoSmallWindowInput') && $('#remarksByAppointmentInfoSmallWindowInput').focus()
                          }}
                          onBlur={(e)=>{
                            console.log('2222222remarksByAppointmentInfoSmallWindowInput :: ',e.target);
                            let value = e.target.value
                            const { appointmentInfoOfTableDto } = this.props
                            const {
                              appointmentId,
                            } = appointmentInfoOfTableDto || {};
                            this.contentsInputOnBlur(appointmentId,value)
                          }}
                          defaultValue={remarksByAppointmentInfoSmallWindow ? remarksByAppointmentInfoSmallWindow : null}
                          style={{width: 305}}
                          placeholder="请输入备注"
                        />
                      </div>
                      :
                      <div
                        className={
                          classNames({
                            [styles.right]:true,
                            [styles.rightInputBox]:true,
                          })}
                        onClick={()=>{
                          if (operationType == 0) {
                            // message.warning('您没有权限！');
                            return;
                          }

                          if (isShuangAbout == 1) {
                            // message.warning('此预约单已经过期，不可变更预约备注！');
                            return;
                          }

                          if (isComeVisit == 1) {
                            // message.warning('此预约单已经到诊，不可变更预约备注！');
                            return;
                          }

                          this.setState({
                            contentsDetailType:true
                          },()=>{
                            $('#remarksByAppointmentInfoSmallWindowInput') && $('#remarksByAppointmentInfoSmallWindowInput').focus()
                          })
                        }}
                      >
                        <Popover
                          placement="topLeft"
                          content={<div style={{maxWidth: 400, wordBreak: 'break-all'}}>{remarksByAppointmentInfoSmallWindow}</div>} trigger="hover">
                          <Ellipsis lines={1} className={styles.ellipsis_text}><span>{remarksByAppointmentInfoSmallWindow}</span></Ellipsis>
                        </Popover>
                        {(operationType != 0 && isShuangAbout != 1 && isComeVisit != 1) &&
                          <span className={styles.rightIcon}>
                            编辑
                          </span>
                        }
                      </div>
                    }
                  </div>
                  <div className={styles.infoItem1}>
                    <div className={styles.left}>治疗标签：</div>
                    <div className={styles.right}>
                      <Manualicon
                        appointmentIconNewDtoList={appointmentIconNewDtoList}
                        lineHeight={'20px'}
                        height={'22px'}
                        padding={'0 8px'}
                        marginBottom={'6px'}
                      />
                    </div>
                  </div>
                </div>
                <div className={styles.bottomInfo}>
                  <div>
                    {
                      isWaitingList === 0 ?
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            this.lineUpList(1);
                          }}
                          disabled={operationTypeState || isComeVisit == 1}
                        >加入排队</Button>:
                        <Button onClick={(e) => {
                          e.stopPropagation();
                          this.onConfirmIsWaitingList();
                        }} disabled={operationTypeState || isComeVisit == 1}> {/*  判断 是否在 等待列表里面   移除排队列表  */}移除排队</Button>
                    }
                    <Button style={{marginLeft: 8}} onClick={(e) => {
                      e.stopPropagation();
                      this.cancelSubscribe();
                    }}
                            disabled={isShuangAbout == 1 || operationTypeState || !!(appointmentInfoOfTableDto && appointmentInfoOfTableDto.isComeVisit == 1)}
                    >取消预约</Button>
                    <Button
                      style={{marginLeft: 8}}
                      disabled={isShuangAbout == 1 || operationTypeState || !!(appointmentInfoOfTableDto && appointmentInfoOfTableDto.isComeVisit == 1)}
                      onClick={(e) => {
                        e.stopPropagation();
                        this.appointments(appointmentInfoOfTableDto);
                      }}>改约</Button>
                  </div>
                  <div>
                    {
                      (DoctorType == 1) || (isToday == 0) ? null :
                        <Button type="primary" disabled={visitButtonStatusValue || isShuangAbout == 1 || operationTypeState || !!(appointmentInfoOfTableDto && appointmentInfoOfTableDto.isComeVisit == 1) || staffStatus == 1 || isFirstVisit != 2} onClick={(e)=>{e.stopPropagation(); this.oneKeyArrival(appointmentInfoOfTableDto)}}>一键到诊</Button>
                    }
                    {(DoctorType == 1) || (isToday == 0) ? null : <Button
                      style={{marginLeft: 8}}
                      disabled={visitButtonStatusValue || isShuangAbout == 1 || operationTypeState || !!(appointmentInfoOfTableDto && appointmentInfoOfTableDto.isComeVisit == 1) || staffStatus == 1}
                      onClick={(e) => {
                        e.stopPropagation();
                        this.arrival(appointmentInfoOfTableDto);
                      }} type="primary">到诊</Button>}
                  </div>
                </div>
              </div>
            </Spin>
        }
      </div>
    )
  }
}
