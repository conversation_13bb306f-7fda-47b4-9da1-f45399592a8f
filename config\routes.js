export default [
  {
    path: '/',
    component: '../layouts/BlankLayout',
    routes: [
      {
        //登录相关
        path: '/emr/user',
        component: '../layouts/UserLayout',
        routes: [
          {
            name: 'login',
            path: '/emr/user/login',
            component: './User/login',
          },
        ],
      },
      {
        //中间页
        path: '/emr/loading',
        icon: 'smile',
        name: 'loading',
        routes: [
          {
            name: 'loading',
            icon: 'smile',
            path: '/emr/loading',
            component: './loading',
          },
        ],
      },
      {
        //项目执行后的第一个页面
        path: '/',
        redirect: '/emr/loading',
      },
      {
        name: 'exception',
        icon: 'warning',
        path: '/emr/exception',
        routes: [
          {
            //无权访问该页面
            name: '403',
            icon: 'smile',
            path: '/emr/exception/403',
            component: './exception/403',
          },
          {
            //访问的页面不存在
            name: '404',
            icon: 'smile',
            path: '/emr/exception/404',
            component: './exception/404',
          },
          {
            //服务器出错
            name: '500',
            icon: 'smile',
            path: '/emr/exception/500',
            component: './exception/500',
          },
        ],
      },
      {
        path: '/emr/MedicalCenter',
        name: 'MedicalCenter',
        icon: 'smile',
        routes: [
          {
            //全部面板
            name: 'AllPanel',
            icon: 'smile',
            path: '/emr/MedicalCenter/AllPanel',
            component: './MedicalCenter/AllPanel',
          },
          {
            //医疗中心
            name: 'CaseReport',
            icon: 'smile',
            path: '/emr/MedicalCenter/CaseReport',
            component: './MedicalCenter/CaseReport',
          },
        ],
      },
      {
        // 诊所-晨会
        path: '/emr/ClinicMornMeet',
        name: 'ClinicMornMeet',
        // component: '../layouts/UserLayout',
        routes: [
          {
            path: '/emr/ClinicMornMeet',
            redirect: '/emr/ClinicMornMeet/List',
          },
          // 晨会-列表
          {
            name: 'ClinicMornMeet',
            icon: 'smile',
            path: '/emr/ClinicMornMeet/List',
            component: './ClinicMornMeet/List/index',
          },
          {
            // 晨会-详情
            name: 'ClinicMornMeet',
            icon: 'smile',
            path: '/emr/ClinicMornMeet/Details/:appointmentId/:patientId',
            component: './ClinicMornMeet/Details/index',
          },
        ]
      },
      {
        path: '/emr/SystemSetup',
        name: '系统设置',
        component: './SystemSetup',
        icon: 'smile',
        routes: [
          {
            //影像类型管理
            name: '影像类型管理',
            path: '/emr/SystemSetup/ImageManage',
            component: './SystemSetup/ImageManage',
          },
          {
            //专业字典维护
            name: '专业字典维护',
            path: '/emr/SystemSetup/MajorManage',
            component: './SystemSetup/MajorManage',
          },
        ],
      },

      {
        path: '/emr/DoctorManage',
        name: 'DoctorManage',
        icon: 'smile',
        component: './DoctorManage',
        routes: [
          {
            //医生管理
            name: 'MedicalRecordAuthority',
            icon: 'smile',
            path: '/emr/DoctorManage/MedicalRecordAuthority',
            component: './DoctorManage/MedicalRecordAuthority',
          },
        ],
      },

      {
        //内容中心
        path: '/emr/ContentCenter',
        icon: 'smile',
        name: 'ContentCenter',
        component: './ContentCenter',
        routes: [
          {
            name: '内容下载中心',
            path: '/emr/ContentCenter/DownLoad',
            component: './ContentCenter/DownLoad',
          },
          {
            name: '病历模板管理',
            path: '/emr/ContentCenter/MedicalModel',
            component: './ContentCenter/MedicalModel',
          },
          {
            name: '病历词条管理',
            path: '/emr/ContentCenter/Entry',
            component: './ContentCenter/Entry',
          },
          {
            name: '标准诊断管理',
            path: '/emr/ContentCenter/Standard',
            component: './ContentCenter/Standard',
          },
          {
            name: '基础治疗词典',
            path: '/emr/ContentCenter/BasicTreatment',
            component: './ContentCenter/BasicTreatment',
          },
          {
            name: '知情同意书模板',
            path: '/emr/ContentCenter/InforConsent',
            component: './ContentCenter/InforConsent',
          }
        ],
      },
      {
        //目录
        path: '/emr/Menu',
        icon: 'smile',
        name: 'Menu',
        routes: [
          {
            name: 'Menu',
            icon: 'smile',
            path: '/emr/Menu',
            component: './Menu',
          },
        ],
      },
      {
        //今日就诊
        path: '/emr/ArriveToday',
        icon: 'smile',
        name: 'ArriveToday',
        routes: [
          {
            name: 'ArriveToday',
            icon: 'smile',
            path: '/emr/ArriveToday',
            component: './ArriveToday',
          },
        ],
      },
      {
        component: '404',
      },
    ],
  },
];
