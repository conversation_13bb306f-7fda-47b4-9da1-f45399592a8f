import {
  getComponentsList,
  getComponentsScreeningItems,
  getNodeData,
  exportExcel,
  complainRestart,
} from '@/services/complaint.js';
import moment from 'moment';

function formatOptionData (value) {
  if(Array.isArray(value)) {
    return value.map((res)=>{
      return {
        label:res.value,
        value:res.code,
      }
    })
  }else {
    return []
  }
}

// 判断数组长度是否相同 去除
function isArraylengthSame (arr1, arr2) {
  return Array.isArray(arr1) && Array.isArray(arr2) && arr1.length != 0 && arr2.length != 0 && arr1.length == arr2.length;
}


// 数据格式化TreeData 平铺数据结构
function formatTreeDataToList (nodeToTertiaryStructureDtoList) {
  let levelSecondNode  = [] // 拼接全部二级节点
  let organizationInfoList = [] // 拼接全部诊所数据
  if (Array.isArray(nodeToTertiaryStructureDtoList)) {
    nodeToTertiaryStructureDtoList.forEach((level1res,level1idx)=>{
      if (Array.isArray(level1res.subordinateNodeList)) {
        // 遍历全部二级节点
        level1res.subordinateNodeList.forEach((level2res,level2idx)=>{
          levelSecondNode.push(level2res)
          if (Array.isArray(level2res.subordinateNodeList)) {
            // 遍历全部诊所数据
            level2res.subordinateNodeList.forEach((level3res,level3idx)=>{
              organizationInfoList.push(level3res)
            })
          }
        })
      }
    })
  }
  return {levelSecondNode,organizationInfoList}
}


function formatTreeDataToAntTree (nodeToTertiaryStructureDtoList) {
  let level1 = []
  if (Array.isArray(nodeToTertiaryStructureDtoList)) {
    nodeToTertiaryStructureDtoList.forEach((level1res,level1idx)=>{
      // 遍历全部一级节点
      let level1children = []
      if (Array.isArray(level1res.subordinateNodeList)) {
        level1res.subordinateNodeList.forEach((level2res,level2idx)=>{
          // 遍历全部二级节点
          let level2children = []
          if (Array.isArray(level2res.subordinateNodeList)){
            level2res.subordinateNodeList.forEach((organization,organizationidx)=>{
              if(organization && organization.managedNodeName) {
                let organizationRes = {
                  level:'3',
                  title: organization.managedNodeName,
                  value: organization.nodeId,
                  clinicId: organization.nodeId,
                  parentNodeId: organization.parentNodeId,
                  key: `${organization.parentNodeId}-${organization.nodeId}`,
                }
                level2children.push(organizationRes)
              }
            })
          }
          if(level2res && level2res.managedNodeName) {
            let level2 = {
              level:'2',
              title: level2res.managedNodeName,
              value: level2res.nodeId,
              parentNodeId: level2res.parentNodeId,
              key: `${level2res.nodeId}`,
              children: level2children
            }
            level1.push(level2)
          }
        })
      }
      //level1.push({ title: level1res.managedNodeName,level:'1', value: level1res.nodeId, key: `${level1res.nodeId}`, children: level1children })
    })
  }
  return level1;
}

export default {
  namespace: 'ComponentsList',

  state: {
    // 获取筛选数据关键字
    key:['patientIdentityOptions','complaintObject','complaintsProject','complaintsFocus','classified','complaintChannels','complaintType'],
    /*存储筛选项目数据*/
    patientIdentityOptionsValues:[],        // 患者身份
    complaintObjectValues:[],               // 投诉对象
    complaintsProjectValues:[],             // 投诉项目
    complaintsFocusValues:[],               // 归类
    complaintTypeValues:[],                    // 状态
    complaintChannelsValues:[],             // 投诉渠道
    complaintDateLimitValues:false,         // 是否限制投诉时间
    complaintDateLimitStart:null,           // 投诉开始时间
    complaintDateLimitEnd:null,             // 投诉结束时间
    complaintAreaValues:[],                 // 区域value项目
    complaintAreaValuesSelect:[],
    /*存储筛选框选项*/
    dataState: null,                        // 是否已获取数据
    patientIdentityOptions: [],
    complaintObject: [],
    complaintsProject: [],
    complaintsFocus: [],
    complaintChannels : [],
    complaintType: [],
    /*区域选择项*/
    complaintArea:[],
    /*投诉列表数据*/
    componentsList:[],                      // 投诉列表数据
    componentsDataState:null,               // 投诉
    componentsDataMsg:null,                 // 加载失败原因
    /*投诉列表pageNum*/
    pageNum:'1',                            // 页数
    pageSize:'10',                          // 单页条数
    total: '0',                             // 总条数
    /*NodeData*/
    nodeData:null,                            // 投诉节点数据
    nodeDataMsg:null,                         // 投诉节点数据
    IsCarryScreeningCondition:false,          // 是否携带参数
  },

  effects: {
    // 获取列表数据
    *getComponentsList({ payload }, { call, put,select }) {
      const status =  yield select(status => status.ComponentsList)
      let {
        patientIdentityOptionsValues,        // 患者身份
        complaintObjectValues,               // 投诉对象
        complaintsProjectValues,             // 投诉项目
        complaintsFocusValues,               // 归类
        complaintTypeValues,                    // 状态
        complaintChannelsValues,             // 投诉渠道
        complaintDateLimitValues,         // 是否限制投诉时间
        complaintDateLimitStart,           // 投诉开始时间
        complaintDateLimitEnd,             // 投诉结束时间
        pageNum,                            // 页数
        pageSize,                          // 条数
        complaintAreaValuesSelect,

        patientIdentityOptions,      // 患者身份筛选
        complaintObject,             // 投诉对象
        complaintsProject,           // 投诉项目
        complaintsFocus,             // 归类
        complaintType,                  // 状态
        complaintChannels,           // 投诉渠道
      } = status

      let clinicId = []
      let involvedAreaId = []
      complaintAreaValuesSelect.forEach((res)=>{
        if (res.level == 3) {
          clinicId.push(res.value) // 添加筛选节点
        }else {
          involvedAreaId.push(res.value) // 添加筛选机构
        }
      })

      // 判断当前请求是否携带筛选条件
      let IsCarryScreeningCondition = false;
      if(Array.isArray(patientIdentityOptionsValues) && patientIdentityOptionsValues.length != 0){ IsCarryScreeningCondition = true } // 患者身份
      if(Array.isArray(complaintObjectValues) && complaintObjectValues.length != 0){ IsCarryScreeningCondition = true }     // 投诉对象
      if(Array.isArray(complaintsProjectValues) && complaintsProjectValues.length != 0){ IsCarryScreeningCondition = true } // 投诉项目
      if(Array.isArray(complaintsFocusValues) && complaintsFocusValues.length != 0){ IsCarryScreeningCondition = true }     // 归类
      if(Array.isArray(complaintTypeValues) && complaintTypeValues.length != 0){ IsCarryScreeningCondition = true }               // 状态
      if(Array.isArray(complaintChannelsValues) && complaintChannelsValues.length != 0){ IsCarryScreeningCondition = true }   // 投诉渠道
      if(Array.isArray(clinicId) && clinicId.length != 0){ IsCarryScreeningCondition = true }                     // 投诉机构
      if(Array.isArray(involvedAreaId) && involvedAreaId.length != 0){ IsCarryScreeningCondition = true }                     // 投诉节点
      if(complaintDateLimitValues){ IsCarryScreeningCondition = true }                                                      // 是否限制投诉时间

      // 当前所选筛选项与筛选一致时参数传入空数组
      let patientIdentityOptionsValuesEnter = isArraylengthSame(patientIdentityOptions,patientIdentityOptionsValues); // 身份
      let complaintsProjectValuesEnter = isArraylengthSame(complaintsProject,complaintsProjectValues);                // 项目
      let complaintObjectValuesEnter = isArraylengthSame(complaintObject,complaintObjectValues);                      // 对象
      let complaintsFocusValuesEnter = isArraylengthSame(complaintsFocus,complaintsFocusValues);                      // 项目
      let complaintTypeValuesEnter = isArraylengthSame(complaintType,complaintTypeValues);                            // 状态
      let complaintChannelsValuesEnter = isArraylengthSame(complaintChannels,complaintChannelsValues); // 渠道

      const response = yield call(getComponentsList, {
        userId:localStorage.getItem('id'),
        userName:localStorage.getItem("userName"),          // 用户名称
        patientIdentity:patientIdentityOptionsValuesEnter ? [] : patientIdentityOptionsValues,    // 患者身份
        clinicId:clinicId,
        involvedAreaId:involvedAreaId,
        complainTargetObject:complaintObjectValuesEnter ? [] : complaintObjectValues,             // 投诉对象
        complainTreatment:complaintsProjectValuesEnter ? [] :  complaintsProjectValues,           // 投诉项目
        complainConcerned:complaintsFocusValuesEnter ? [] : complaintsFocusValues,                // 归类
        complainType:complaintTypeValuesEnter ? [] : complaintTypeValues,                     // 状态
        complainChannel:complaintChannelsValuesEnter ? [] : complaintChannelsValues,              // 投诉渠道
        complaintDateLimitValues,                                                                 // 是否限制投诉时间
        startTime:complaintDateLimitStart ? moment(complaintDateLimitStart,'YYYY-MM-DD').format('YYYY-MM-DD') : null,             // 投诉开始时间
        endTime:complaintDateLimitEnd ? moment(complaintDateLimitEnd,'YYYY-MM-DD').format('YYYY-MM-DD') : null,               // 投诉结束时间

        pageNum,
        pageSize,
        ...payload,
      });

      if (response && response.code && response.code == 200 && response.content) {
        const { current,pageSize,resultList,total } = response.content
        yield put({
          type: 'save',
          payload: {
            // 投诉列表数据
            componentsList:Array.isArray(resultList) ? resultList : [],
            IsCarryScreeningCondition:IsCarryScreeningCondition,
            componentsDataState:true,                    // 投诉
            componentsDataMsg:null,                  // 加载失败原因
            pageNum:current,                            // 页数
            pageSize:pageSize,                          // 单页条数
            total: total,                             // 总条数
          }
        })
      }else {
        yield put({
          type: 'save',
          payload: {
            // 投诉列表数据
            componentsList:[],
            IsCarryScreeningCondition:IsCarryScreeningCondition,
            componentsDataState:false,                    // 投诉
            componentsDataMsg:(response && response.code && response.msg) ? response.msg : '数据加载失败',
            pageNum:'1',                            // 页数
            pageSize:"10",                          // 单页条数
            total: "0",                             // 总条数
          }
        })
      }
      return response;
    },

    // 获取筛选项数据
    *getComponentsScreeningItems({ payload }, { call, put }) {
      const response = yield call(getComponentsScreeningItems, {
        ...payload,
        isUseList: payload.isUseList ? true : null
      });
        if (response && response.code && response.code == 200 && response.content) {
          const {
            treatment,      // 投诉项目
            targetObject,   // 投诉对象
            concerned,      // 归类
            channel,        // 投诉渠道
            nodeInfo,       // 节点信息
          } = response.content || {}

          let { levelSecondNode,organizationInfoList } = formatTreeDataToList(nodeInfo && nodeInfo.nodeToTertiaryStructureDtoList)


          yield put({
            type: 'save',
            payload: {
              dataState:true,
              complaintArea:formatTreeDataToAntTree(nodeInfo && nodeInfo.nodeToTertiaryStructureDtoList),
              // patientIdentity 患者身份 1:初诊、2:复诊、3:未就诊
              patientIdentityOptions: formatOptionData([{ value: '初诊', code: '1' }, { value: '复诊', code: '2' }, { value: '未就诊',code: '3'},]),
              // complainTargetObject 投诉对象 1:医生、2:护士、3:客服、4:其他
              complaintObject:formatOptionData(targetObject),
              // complainTreatment 	投诉项目 1:正畸、2:种植、3:儿科、4:洁牙、5:治疗、6:其他
              complaintsProject:formatOptionData(treatment),
              // complainConcerned 归类 1:服务流程、2:服务态度、3:医疗质量、4:价格、5:等候时间、6就诊环境、7其他
              complaintsFocus: formatOptionData(concerned),
              // complainChannel 投诉渠道 1:400热线、2:诊所现场、3:第三方平台、4:满意度短信、5:小程序自助
              complaintChannels:formatOptionData(channel),
              // 状态
              complaintType:formatOptionData([
                { code: 1, value: '跟进中' },
                { code: 2, value: '未回访' },
                { code: 3, value: '重开启' },
                { code: 4, value: '已跟进' },
                { code: 5, value: '草稿' },
              ]),
            },
          });
        }
      return response;
    },

    // 获取节点数据和机构数据
    *getNodeData({ payload }, { call, put }) {
      const response = yield call(getNodeData, payload);
      if (response && response.content && response.code && response.code == 200) {
        yield put({
          type: 'save',
          payload: {
            nodeData:response.content
          }
        })
      }else {
        yield put({
          type: 'save',
          payload: { nodeData:null }
        })
      }
      return response
    },
    // 导出投诉
    *exportExcel({ payload: params }, { call, put,select }){
      const status =  yield select(status => status.ComponentsList)
      let {
        patientIdentityOptionsValues,        // 患者身份
        complaintObjectValues,               // 投诉对象
        complaintsProjectValues,             // 投诉项目
        complaintsFocusValues,               // 归类
        complaintTypeValues,                    // 状态
        complaintChannelsValues,             // 投诉渠道
        complaintDateLimitValues,         // 是否限制投诉时间
        complaintDateLimitStart,           // 投诉开始时间
        complaintDateLimitEnd,             // 投诉结束时间
        pageNum,                            // 页数
        pageSize,                          // 条数
        complaintAreaValuesSelect,         // 筛选的机构节点
      } = status

      let clinicId = []
      let involvedAreaId = []
      complaintAreaValuesSelect.forEach((res)=>{
        if (res.level == 3) {
          clinicId.push(res.value) // 添加筛选节点
        }else {
          involvedAreaId.push(res.value) // 添加筛选机构
        }
      })

      const response = yield call(exportExcel, {
        userId:localStorage.getItem('id'),
        userName:localStorage.getItem("userName"),  // 用户名称
        patientIdentity:patientIdentityOptionsValues,        // 患者身份
        clinicId:clinicId,
        involvedAreaId:involvedAreaId,
        complainTargetObject:complaintObjectValues,               // 投诉对象
        complainTreatment:complaintsProjectValues,             // 投诉项目
        complainConcerned:complaintsFocusValues,               // 归类
        complainType:complaintTypeValues,                    // 状态
        complainChannel:complaintChannelsValues,             // 投诉渠道
        complaintDateLimitValues,            // 是否限制投诉时间
        startTime:complaintDateLimitStart ? moment(complaintDateLimitStart,'YYYY-MM-DD').format('YYYY-MM-DD') : null,             // 投诉开始时间
        endTime:complaintDateLimitEnd ? moment(complaintDateLimitEnd,'YYYY-MM-DD').format('YYYY-MM-DD') : null,               // 投诉结束时间
        pageNum,
        pageSize,
        ...params,
      });

      return response;
    },
    // 点击确认重开
    *complainRestart({ payload: params }, { call, put,select }){
      const response = yield call(complainRestart, params);
      return response
    }
  },

  reducers: {
      save(state, { payload }) {
        return {
          ...state,
          ...payload,
        };
      },

      // 清空models数据
      clean(state, { payload }){
        return {
          ...state,
          patientIdentityOptionsValues:[],        // 患者身份
          complaintObjectValues:[],               // 投诉对象
          complaintsProjectValues:[],             // 投诉项目
          complaintsFocusValues:[],               // 归类
          complaintTypeValues:[],                    // 状态
          complaintChannelsValues:[],             // 投诉渠道
          complaintDateLimitValues:false,         // 是否限制投诉时间
          complaintDateLimitStart:null,           // 投诉开始时间
          complaintDateLimitEnd:null,             // 投诉结束时间
          complaintAreaValues:[],                 // 区域value项目
          complaintAreaValuesSelect:[],
          /*存储筛选框选项*/
          dataState: null,                        // 是否已获取数据
          patientIdentityOptions: [],
          complaintObject: [],
          complaintsProject: [],
          complaintsFocus: [],
          complaintType: [],
          complaintChannels : [],
          /*区域选择项*/
          complaintArea:[],
          /*投诉列表数据*/
          componentsList:[],                      // 投诉列表数据
          componentsDataState:null,               // 投诉
          componentsDataMsg:null,                 // 加载失败原因
          /*投诉列表pageNum*/
          pageNum:'1',                            // 页数
          pageSize:'10',                          // 单页条数
          total: '0',                             // 总条数
          /*NodeData*/
          nodeData:null,                            // 投诉节点数据
          nodeDataMsg:null,                         // 投诉节点数据
          IsCarryScreeningCondition:false,          // 是否携带参数
        }
      }
  },

  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, search }) => {
        if (!((/\/complaint/.test(pathname)) || (/\/homeIndex/.test(pathname)))) {
          dispatch({
            type: "clean",
            payload: {}
          })
        }
      })
    }
  }

}

