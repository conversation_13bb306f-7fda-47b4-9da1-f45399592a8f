import {
  findCheckClasses,
  findMrcTmptNamesByClass,
  saveMrcTmpt,
  getMrcTempInfoById,
  editMrcTmpt,
  deleteMrcTmpt,
  findMrcTempClassSearch,
  downMrcByCode,
  findMrcTmpts
} from '@/services/InforConsent';//接口
import { notification } from 'antd';
const InforConsentsModel = {
  namespace: 'InforConsentModel',
  state: {
    InforConsentData: {}, //知情同意书模板分组列表
    findMrcTmptNamesData: {}, //根据知情同意书模板分类获取模板名
    saveMrcTmptData: {}, //新增同意书模板
    getMrcTempInfoData: {}, //获取知情同意书模板详情
    editMrcTmptData: {}, //编辑知情同意书
    deleteMrcTmptData: {}, //删除知情同意书
    findMrcTempClassSearchData: {}, //知情同意书模板分组列表搜索
    downMrcByCodeData: {}, //下载指定知情同意书
    findMrcTmptsData:{},// 获取同意书模板分组列表
    loading: false,
    loadTip: '加载中',
  },
  //异步
  effects: {
    // 获取同意书模板分组列表
    *findMrcTmptsService({ payload, callback }, { call, put }) {
      const response = yield call(findMrcTmpts, payload);
      yield put({
        type: 'findMrcTmptsInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    // 知情同意书模板分组列表
    *findCheckService({ payload, callback }, { call, put }) {
      const response = yield call(findCheckClasses, payload);
      yield put({
        type: 'findCheckInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //根据知情同意书模板分类获取模板名
    *findMrcTmptNamesService({ payload, callback }, { call, put }) {
      const response = yield call(findMrcTmptNamesByClass, payload);
      yield put({
        type: 'findMrcTmptNamesInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //新增同意书模板
    *saveMrcTmptService({ payload, callback }, { call, put }) {
      const response = yield call(saveMrcTmpt, payload);
      yield put({
        type: 'saveMrcTmptInfo',
        payload: response,
      });
      if (response.code === 200 ||response.code === 500) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //获取知情同意书模板详情
    *getMrcTempInfoService({ payload, callback }, { call, put }) {
      const response = yield call(getMrcTempInfoById, payload);
      yield put({
        type: 'getMrcTempInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //编辑知情同意书
    *editMrcTmptService({ payload, callback }, { call, put }) {
      const response = yield call(editMrcTmpt, payload);
      yield put({
        type: 'editMrcTmptInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //删除知情同意书
    *deleteMrcTmptService({ payload, callback }, { call, put }) {
      const response = yield call(deleteMrcTmpt, payload);
      yield put({
        type: 'deleteMrcTmptInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //知情同意书模板分组列表搜索
    *findMrcTempClassSearchService({ payload, callback }, { call, put }) {
      const response = yield call(findMrcTempClassSearch, payload);
      yield put({
        type: 'findMrcTempClassSearchInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    // 下载指定知情同意书
    *downMrcByCodeService({ payload, callback }, { call, put }) {
      const response = yield call(downMrcByCode, payload);
      yield put({
        type: 'downMrcByCodeInfo',
        payload: response,
      });
      if (response instanceof Blob) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        message.warning('Some error messages...', 5);
      }
    },
  },

  //同步
  reducers: {
    findMrcTmptsInfo(state, action) {
      return {
        ...state,
        findMrcTmptsData: action.payload || {},
      };
    },
    downMrcByCodeInfo(state, action) {
      return {
        ...state,
        downMrcByCodeData: action.payload || {},
      };
    },
    findMrcTempClassSearchInfo(state, action) {
      return {
        ...state,
        findMrcTempClassSearchData: action.payload || {},
      };
    },
    deleteMrcTmptInfo(state, action) {
      return {
        ...state,
        deleteMrcTmptData: action.payload || {},
      };
    },
    editMrcTmptInfo(state, action) {
      return {
        ...state,
        editMrcTmptData: action.payload || {},
      };
    },
    findCheckInfo(state, action) {
      return {
        ...state,
        InforConsentData: action.payload || {},
      };
    },
    findMrcTmptNamesInfo(state, action) {
      return {
        ...state,
        findMrcTmptNamesData: action.payload || {},
      };
    },
    saveMrcTmptInfo(state, action) {
      return {
        ...state,
        saveMrcTmptData: action.payload || {},
      };
    },
    getMrcTempInfo(state, action) {
      return {
        ...state,
        getMrcTempInfoData: action.payload || {},
      };
    },
  },
};
export default InforConsentsModel;
