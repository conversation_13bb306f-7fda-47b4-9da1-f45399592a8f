import {
  Input,
  Row,
  Col,
  Select,
  message,
  Table,
  Form,
  Space,
  Modal,
  Pagination, Radio, Button, Spin
} from 'antd';
import React, { Component } from 'react';
import { GridContent } from '@ant-design/pro-layout';
//引入样式
import styles from './style.less';
import commonStyles from '@/components/common.less';
// import commonStyle from '@/pages/common.less';
import ModalContent from '../components/ModalContent'; //编辑、详情中的上半部分
import noData from '@/assets/<EMAIL>';
import { connect } from 'dva';
import {StringUtils} from "@/utils/StringUtils";

const { Search } = Input;
const { Option } = Select;
/**form表单控制布局**/
const modalLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 14 },
};

const marjorSelect = [{ value: 'Zhejiang' }, { value: 'Jiangsu' }];
class MedicalRecordAuthority extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isEditVisible: false, //是否打开编辑弹窗
      isEdit: false, // 是否为编辑
      AddAuthorityStatus: false, //是否打开新增弹窗
      major: [], //编辑中的专业
      doctorList: [], //医生列表
      doctorTotal: 9, //总条数
      organizationId: localStorage.getItem("organizationId"), //机构id
      tenantId: localStorage.getItem("tenantId"),  //平台标识
      mRAuthority: {
        pageNum: 1,
        pageSize: 10,
        userName: '', //医生姓名
      },
      majorList: [ //专业
        {
          majorName: null,//专业名称
          majorCode: null,//专业代码
          id: null,
          tenantId: localStorage.getItem("tenantId"),
          userId: localStorage.getItem("userId"),
        }
      ],
      doctorInfo: {}, //医生详情
      loading: false,//表格改变数据loading
      params: { //专业字典列表
        tenantId: localStorage.getItem("tenantId"),//平台标识
        pageNum: 1,
        pageSize: 1000
      },
      majorSelect: [], //专业列表-返回数据
      majorTotal: '0',//专业字典维护列表数量
      hsmDoctorList: [], //上级医生列表
    };
  }
  //初始化
  componentDidMount() {
    this.getDoctorList(); //获取医生列表数据
    this.getMangageList() //专业字典查询
    this.getHsmDoctorList(); //获取上级医生列表

  }
  // 页面的搜索按钮
  onSearch = (value) => {
    let art=StringUtils.trim(value);
    this.state.mRAuthority.userName = art;
    this.state.mRAuthority.pageNum = 1;
    this.getDoctorList()
  };

  //专业字典维护列表
  getMangageList = () => {
    const { params } = this.state;
    const { dispatch } = this.props
    if (dispatch) {
      dispatch({
        type: 'findMajoresModel/findMajoresService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              majorTotal: res.total,
              majorSelect: res.rows,
            })
          }
        }
      });
    }
  }

  // 新增时搜索
  onSearchName = (name) => {
    this.setState({
        mRAuthority: {
          ...this.state.mRAuthority,
          userName: name,
        },
      },
      () => this.getDoctorList(this.state.mRAuthority),
    );
  };

  //编辑、详情弹窗
  openEditDtModal = (record, type) => {
    this.getDoctorInfo(record); //请求医生详情接口
    if (type == 'edit') {
      this.setState({
        isEdit: true,
        isEditVisible: true,
        newsKey: new Date() + Math.random(),
      });
    } else {
      this.setState({
        isEdit: false,
        isEditVisible: true,
      });
    }
  };

  // 编辑医生详情
  handleEditOk = (isEdit) => {
    if (isEdit == true) {
      const { dispatch } = this.props;
      const { doctorInfo } = this.state;
      doctorInfo.tenantId = localStorage.getItem("tenantId");
      doctorInfo.sex = doctorInfo.sex == '女' ? 1 : doctorInfo.sex == '男' ? 0 : null;
      doctorInfo.createName = localStorage.getItem("userName");
      doctorInfo.createId = localStorage.getItem("userId");
      doctorInfo.updateName = localStorage.getItem("userName");
      doctorInfo.updateId = localStorage.getItem("userId");
      // doctorInfo.userId = // localStorage.getItem("userId");
      if (doctorInfo.majorList && doctorInfo.majorList.length > 0) {
        let arr=[];
        doctorInfo.majorList.forEach((key,index)=>{
          if(key.id && key.majorCode){
            arr.push(key)
          }
        })
        const res = new Map();
        // 过滤掉重复的
        let new2 = arr.filter(item => !res.has(item.majorCode) && res.set(item.majorCode, 1));
        // console.log("new2===",new2)
        doctorInfo.majorList = new2;
      }

      let params = {
        ...this.state.doctorInfo
      }
      if (dispatch) {
        dispatch({
          type: 'doctorManage/fetchEditDoctorInfo',
          payload: params,
          callback: (res) => {
            if (res.code == 200) {
              message.success({
                content: '提交成功',
                className: 'custom-class',
                style: {
                  marginTop: '20vh',
                },
              });
              this.getDoctorList();
            }
          }
        })
      }
    }
    this.handleEditCancel();
  };
  //取消按钮点击事件
  handleEditCancel = () => {
    this.setState({
      doctorInfo: {},
      isEditVisible: false,
    });
  };
  onFinish = (values) => {

  };

  /**添加新的一项**/
  addRowSelect = (key) => {
    let arr = this.state[key];
    arr.push({
      majorCode: null,
      majorName: null,
      id: null,
    });
    this.state[key] = arr;
    this.setState({
      [key]: arr,
    });
  };
  /**删除新一项**/
  delRowSelect = (key, index, arrkey) => {
    this.state[key].splice(index, 1);
    this.setState({
      [key]: this.state[key],
      newsKey: new Date() + Math.random(),
    });
  };


  //获取当前诊所的所有医生
  getDoctorList = () => {
    const { dispatch } = this.props;
    this.setState({
      loading: true
    })
    let params = {
      ...this.state.mRAuthority,
      tenantId: localStorage.getItem("tenantId"),
      organizationId: this.state.organizationId,
    };
    if (dispatch) {
      dispatch({
        type: 'doctorManage/fetchDoctorList',
        payload: params,
        callback: (res) => {
          if (res.code === 200) {
            this.setState({
              doctorList: res.content.resultList,
              doctorTotal: res.content.total,
              loading: false
            });
          } else {
            this.setState({
              loading: false
            })
          }
        },
      });
    }
  };

  // 获取上级医生列表
  getHsmDoctorList = () => {
    const { dispatch } = this.props;
    let params = {
      organizationId: this.state.organizationId,//机构id
      tenantId: localStorage.getItem('tenantId')//平台标识
    };
    if (dispatch) {
      dispatch({
        type: 'doctorManage/fetchHsmDoctors',
        payload: params,
        callback: (res) => {
          if (res.code === 200) {
            this.setState({
              hsmDoctorList: res.content
            })
          }
        }
      })
    }
  }

  // 医生详情
  getDoctorInfo = (info) => {
    const { dispatch } = this.props;
    let params = {
      userId: info.userId,//用户ID
      organizationId: info.organizationId,//机构id
      tenantId: localStorage.getItem('tenantId'),//平台标识
    }

    this.setState({
      infoLoading: true
    });

    if (dispatch) {
      dispatch({
        type: 'doctorManage/fetchDoctorInfo',
        payload: params,
        callback: (res) => {
          if (res.code === 200) {
            this.setState({
              doctorInfo: res.content,
              majorList: res.content.majorList?res.content.majorList:[{
                  majorName: null,
                  majorCode: null,
                  id: null,
                  tenantId: localStorage.getItem("tenantId"),
                  userId: localStorage.getItem("userId"),
                }],
              newsKey: new Date() + Math.random(),
              infoLoading: false
            })
          }
        }
      })
    }
  };

  // 打开为非医生用户添加病历权限 弹窗
  openAdd = (record) => {
    this.setState({
      AddAuthorityStatus: true,
    });
  };
  // 新增医生权限确定事件
  handleAddAuthorityOk = () => {
    // 调用新增接口 后调用查询列表

    this.getDoctorList();
    this.handleAddAuthorityCancel();
    // this.setState({
    //   AddAuthorityStatus: true,
    // });
  };
  // 关闭为非医生用户添加病历权限 弹窗 事件
  handleAddAuthorityCancel = () => {
    this.setState({
      AddAuthorityStatus: false,
    });
  };
  // 点击搜索到的医生按钮
  putInput = (value) => {

    this.setState({
      mRAuthority: {
        ...this.state.mRAuthority,
        userName: value,
      },
    });
  };

  //分页数据
  pageNumberOnChange = (pageNum, pageSize) => {
    this.setState(
      {
        mRAuthority: {
          ...this.state.mRAuthority,
          pageNum: pageNum,
          pageSize: pageSize
        },
      },
      () => this.getDoctorList(this.state.mRAuthority),
    );
  };
  // 选择上级医生事件
  hsmUserAll = (value, option) => {
    this.state.doctorInfo.hsmUserId = value;
    this.state.doctorInfo.hsmUserName = option.children;
  }
  render() {
    const { isEditVisible, isEdit, majorList, doctorList, doctorInfo, infoLoading, loading,  doctorTotal, mRAuthority, newsKey,
      majorSelect,
      hsmDoctorList
    } = this.state;

    const columns = [
      {
        width: '15%',
        title: '姓名',
        dataIndex: 'userName',
        key: 'userName',
      },
      {
        width: '7%',
        title: '性别',
        dataIndex: 'sex',
        key: 'sex',
      },
      {
        width: '8%',
        title: '角色',
        dataIndex: 'roleStrList',
        key: 'roleStrList',
      },
      {
        width: '15%',
        title: '执业地点',
        dataIndex: 'organizationName',
        key: 'organizationName',
      },
      {
        width: '10%',
        title: '独立写病历',
        dataIndex: 'isWriteEmr',
        key: 'isWriteEmr',
        render: (text, record) => (
          <Space size="middle">
            <span>{record.isWriteEmr === 1 ? "否" : record.isWriteEmr === 0 ? "是" : ""}</span>
          </Space>
        )
      },
      {
        width: '10%',
        title: '创建人',
        dataIndex: 'createName',
        key: 'createName',
      },
      {
        width: '13%',

        title: '创建时间',
        dataIndex: 'createdGmtAt',
        key: 'createdGmtAt',
      },
      {
        width: '12%',
        title: '操作',
        key: 'action',
        render: (text, record) => (
          <Space size="middle">
            <a
              onClick={() => {
                this.openEditDtModal(record, 'detail');
              }}
            >
              查看详情
            </a>
            <a
              onClick={() => {
                this.openEditDtModal(record, 'edit');
              }}
            >
              编辑
            </a>
          </Space>
        ),
      },
    ];

    // const data = [
    //   {
    //     key: '1',
    //     name: 'John Brown',
    //     id: '1',
    //     age: 32,
    //     address: 'New York No. 1 Lake Park',
    //     tags: ['nice', 'developer'],
    //   },
    //   {
    //     key: '2',
    //     id: '2',
    //     name: 'Jim Green',
    //     age: 42,
    //     address: 'London No. 1 Lake Park',
    //     tags: ['loser'],
    //   },
    //   {
    //     key: '3',
    //     id: '3',
    //     name: 'Joe Black',
    //     age: 32,
    //     address: 'Sidney No. 1 Lake Park',
    //     tags: ['cool', 'teacher'],
    //   },
    // ];

    return (
      <GridContent>
        <div className={styles.imageMagecontent}>
          <div className={styles.searchBtn}>
            <Search placeholder="搜索" onSearch={this.onSearch} style={{ width: 200 }} />
            {/*<Button type="primary" onClick={this.openAdd}>*/}
            {/*  增加非医生用户病历权限*/}
            {/*</Button>*/}
          </div>
          <Table
            rowKey={render => render.userId}
            columns={columns}
            dataSource={doctorList}
            style={{ marginTop: 16, marginBottom: 16 }}
            pagination={false}
            loading={loading}
          />
          <Pagination
            showSizeChanger
            style={{ float: 'right' }}
            total={doctorTotal}
            showTotal={(total) => `共 ${total} 条记录`}
            defaultPageSize={mRAuthority.pageSize}
            defaultCurrent={mRAuthority.pageNum}
            onChange={(pageNum, pageSize) => this.pageNumberOnChange(pageNum, pageSize)}
          />
        </div>
        {/* <Modal
          title="为非医生用户添加病历权限"
          visible={this.state.AddAuthorityStatus}
          destroyOnClose={true}
          onOk={this.handleAddAuthorityOk}
          onCancel={this.handleAddAuthorityCancel}
          okText="确定"
          cancelText="取消"
          width={600}
        >
          <Form
            name="add"
            labelCol={{
              span: 3,
            }}
            wrapperCol={{
              span: 21,
            }}
            initialValues={{
              remember: true,
            }}
            onFinish={this.onFinish}
            autoComplete="off"
          >
            <Form.Item
              label=""
              wrapperCol={24}
            >
              <Search
                placeholder="搜索"
                onSearch={() => this.onSearchName}
                style={{ width: '100%' }}
              />
              <div>
                {data ? (
                  <div style={{ padding: '10px 0' }}>
                    <Row style={{ display: 'flex' }}>
                      <div style={{ marginRight: '5px' }}>
                        <div className={styles.searchName} onClick={() => this.putInput('jflajf')}>
                          医生(涉及付款)
                        </div>
                      </div>
                      <div className={styles.searchNameDiv}>
                        <div className={styles.searchName}>医生(涉及付款)</div>
                      </div>
                      <div className={styles.searchNameDiv}>
                        <div className={styles.searchName}>医生(涉及付款)</div>
                      </div>
                      <div className={styles.searchNameDiv}>
                        <div className={styles.searchName}>医生(涉及付款)</div>
                      </div>
                      <div className={styles.searchNameDiv}>
                        <div className={styles.searchName}>医生(涉及付款)</div>
                      </div>
                      <div className={styles.searchNameDiv}>
                        <div className={styles.searchName}>医生(涉及付款)</div>
                      </div>
                    </Row>
                  </div>
                ) : (
                  // 暂无数据
                  <div
                    className={commonStyle.nodataContent}
                    style={{ marginTop: '10%', marginBottom: '10%' }}
                  >
                    <img src={noData} className={commonStyle.imgStyle} alt="" />
                    <div className={commonStyle.fontStyle}>暂无数据</div>
                  </div>
                )}
              </div>
            </Form.Item>
          </Form>
        </Modal> */}
        <Modal
          title={isEdit ? '编辑' : '查看详情'}
          width={960}
          visible={isEditVisible}
          destroyOnClose={true}
          maskClosable={isEdit ? false :true}
          // onOk={this.handleEditOk.bind(this, isEdit)}
          onCancel={this.handleEditCancel}
          footer={[
            <Button type="primary" onClick={this.handleEditOk.bind(this, isEdit)} hidden={isEdit == false}>
              确定
            </Button>,
            <Button key="back" onClick={() => this.handleEditCancel()}>
              取消
            </Button>,
          ]}
        >
          <Spin spinning={infoLoading}>
            <ModalContent doctorInfo={doctorInfo} />
            <Row
              style={{ marginTop: '10px' }}
              className={`${commonStyles.width100} ${styles.padding20}`}
              key={newsKey}
            >
              {isEdit ? (
                <Form
                  name="edit"
                  {...modalLayout}
                  initialValues={{
                    remember: true,
                  }}
                  style={{ width: '100%' }}
                  onFinish={this.onFinish}
                  autoComplete="off"
                >
                  <Row className={styles.displayFlex}>
                    <Col span={12} className={styles.col8}>
                      <Form.Item label="是否独立写病历" >
                        <Radio.Group
                          defaultValue={doctorInfo.isWriteEmr}
                          onChange={(e) => this.state.doctorInfo.isWriteEmr = e.target.value}
                        >
                          <Radio value={0}>是</Radio>
                          <Radio value={1}>否</Radio>
                        </Radio.Group>
                      </Form.Item>
                    </Col>
                    <Col span={12} className={styles.col8}>
                      <Form.Item
                        label="带教医生"
                        name="editType"
                      >
                        <Select
                          placeholder="请选择上级医生"
                          defaultValue={doctorInfo.hsmUserName}
                          onChange={this.hsmUserAll}>
                          <Select.Option key={-1} value={null}>无带教医生</Select.Option>
                          {(hsmDoctorList || []).map((data, index) =>
                            <Select.Option key={index} value={data.userId}>{data.userName}</Select.Option>
                          )}

                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row className={styles.displayFlex}>
                    <Col span={12} className={styles.col8}>
                      <Form.Item
                        label="专业"
                        name="majorList"
                      >
                        {/*{doctorInfo && doctorInfo.majorList == null*/}
                        {/*|| doctorInfo.majorList.length == 1*/}
                        {/*  ?*/}
                        {/*  <div className={styles.AllchooseLine}>*/}
                        {/*    <Select*/}
                        {/*      placeholder="请选择专业"*/}
                        {/*      defaultValue={doctorInfo.majorList && doctorInfo.majorList[0].majorCode || ''}*/}
                        {/*      onChange={(value, option) => {*/}
                        {/*        majorList[0].id = option.data.id;*/}
                        {/*        majorList[0].majorCode = value;*/}
                        {/*        majorList[0].majorName = option.children;*/}
                        {/*        majorList[0].tenantId = localStorage.getItem('tenantId');*/}
                        {/*        doctorInfo.majorList = majorList*/}
                        {/*      }}>*/}
                        {/*      {majorSelect.map((item) => (*/}
                        {/*        <Option key={item.majorCode} value={item.majorCode} data={item}>*/}
                        {/*          {item.majorName}*/}
                        {/*        </Option>*/}
                        {/*      ))}*/}
                        {/*    </Select>*/}
                        {/*    <div className={styles.operation}>*/}
                        {/*      {doctorInfo.majorList && doctorInfo.majorList.length >= 2 ? (*/}
                        {/*        <span*/}
                        {/*          className={styles.txt11}*/}
                        {/*          style={{ right: '-52px' }}*/}
                        {/*          onClick={() => this.delRowSelect('majorList', 0, 'checkArr1')}*/}
                        {/*        >*/}
                        {/*          删除*/}
                        {/*        </span>*/}
                        {/*      ) : (*/}
                        {/*        <span*/}
                        {/*          className={styles.txt11}*/}
                        {/*          onClick={() => this.addRowSelect('majorList')}*/}
                        {/*        >*/}
                        {/*          添加*/}
                        {/*        </span>*/}
                        {/*      )}*/}
                        {/*    </div>*/}
                        {/*  </div> : null}*/}

                        {(majorList||[]).map((item, index) =>
                            <div className={styles.AllchooseLine} key={index}>
                              <Select
                                placeholder="请选择专业"
                                defaultValue={doctorInfo && doctorInfo.majorList != null ? doctorInfo.majorList[index].majorCode : null}
                                onChange={(value, option) => {
                                  majorList[index].id = option.data.id;
                                  majorList[index].majorCode = value;
                                  majorList[index].majorName = option.children;
                                  majorList[index].tenantId = localStorage.getItem('tenantId');
                                  doctorInfo.majorList = majorList
                                }}>
                                {majorSelect.map((item1) => (
                                  <Option key={item1.majorCode} value={item1.majorCode} data={item1}>
                                    {item1.majorName}
                                  </Option>
                                ))}
                              </Select>
                              <div className={styles.operation}>
                                {majorList && majorList.length == 1?
                                  (
                                    <span
                                      className={styles.txt11}
                                      onClick={() => this.addRowSelect('majorList')}
                                    >
                                      添加
                                  </span>
                                  )
                                  :majorList && majorList.length - 1 == index  ? (
                                  <span style={{ marginRight: '-37px' }}>
                                    <span
                                      className={styles.txt11}
                                      onClick={() => this.addRowSelect('majorList')}
                                    >
                                      添加
                                    </span>
                                    <span
                                      className={styles.txt11}
                                      onClick={() => this.delRowSelect('majorList', index, 'checkArr1')}
                                    >
                                      删除
                                    </span>{' '}
                                  </span>
                                ) : (
                                    <span
                                    className={styles.txt11}
                                    onClick={() => this.delRowSelect('majorList', index, 'checkArr1')}
                                  >
                                    删除
                                  </span>
                                )}
                              </div>
                            </div>
                        )}
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row className={styles.displayFlex}>
                    <Col span={12} className={styles.col8}>
                      <Form.Item
                        label="职业资格证类型"
                        name="profCardType">
                        <Select placeholder="请选择证件类型"
                                defaultValue={doctorInfo.profCardType}
                                onChange={e => {
                                  this.state.doctorInfo.profCardType = e
                                }}>
                          <Option value="YS">医师资格证</Option>
                          <Option value="YJ">药剂师资格证</Option>
                          <Option value="YY">营养师资格证</Option>
                          <Option value="ZC">助产师证</Option>
                          <Option value="XL">心理咨询师证</Option>
                          <Option value="YL">医疗技师证</Option>
                          <Option value="HS">护士资格证</Option>
                          <Option value="QT">其它</Option>
                        </Select>
                      </Form.Item>
                    </Col>

                    <Col span={12} className={styles.col8}>
                      <Form.Item
                        label="职业资格证件编码"
                        name="profCard"
                      >
                        <Input
                          maxLength={30}
                          placeholder="请输入证件编码"
                          defaultValue={doctorInfo.profCard}
                          onChange={(e) => this.state.doctorInfo.profCard = e.target.value}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              ) : (
                <Form
                  name="detail"
                  {...modalLayout}
                  initialValues={{
                    remember: true,
                  }}
                  style={{ width: '100%' }}
                  onFinish={this.onFinish}
                  autoComplete="off"
                >
                  <Row className={styles.displayFlex}>
                    <Col span={12} className={styles.col8}>
                      <Form.Item label="是否独立写病历" >
                        {doctorInfo.isWriteEmr==0?'是':doctorInfo.isWriteEmr==1?'否':''}
                      </Form.Item>
                    </Col>
                    <Col span={12} className={styles.col8}>
                      <Form.Item label="带教医生">{doctorInfo.hsmUserName || '无带教医生'}</Form.Item>
                    </Col>
                  </Row>
                  <Row className={styles.displayFlex}>
                    <Col span={12} className={styles.col8}>
                      <Form.Item label="专业">
                        {doctorInfo && (doctorInfo.majorList || []).map((item,index)=>(
                          doctorInfo.majorList.length>1?
                            <>
                              <span>{item.majorName}</span>
                              {doctorInfo.majorList.length==index+1?
                              "":
                                <span>、</span>
                              }
                            </> :
                          <span>{item.majorName}</span>
                          )
                        )}
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              )}
            </Row>
          </Spin>
        </Modal>
      </GridContent>
    );
  }
}

// export default MedicalRecordAuthority;
export default connect(({ doctorList, loading }) => ({
  doctorList

}))(MedicalRecordAuthority);
