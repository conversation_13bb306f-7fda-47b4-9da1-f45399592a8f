/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */
export default {
  dev: {
    '/api/medical': {
      // target: 'http://**************:9022',
      target: 'https://emr-test.friday.tech/',
      changeOrigin: true,
      pathRewrite: {
        '^/api/medical': '/api/test/medical',
      },
    },
    '/api': {
      target: 'https://emr-test.friday.tech/',
      // target: 'http://**************:9021/',
      // target: 'http://192.168.3.115:9021',
      // target: 'http://192.168.3.110:9011',
      // target: 'http://192.168.3.128:8010',
      changeOrigin: true,
      pathRewrite: {
        '^/api/emr': '',
      },
    },
    // [test] 配置登录相关使用 换取Tenantid使用
    '/Platform/auditing': {
      target: `https://dental-test.friday.tech/Platform/auditing`,
      changeOrigin: true,
      pathRewrite: { '^/Platform/auditing': '' },
    },
    // [test] 配置登录相关使用 换取token使用
    '/api/businessusers': {
      target: `https://dental-test.friday.tech/api`,
      changeOrigin: true,
      pathRewrite: { '^/api/businessusers': '' },
    },
  },
  test: {
    '/api/appointment': {
      // target: 'https://dental-dev.friday.tech',
      target: 'https://emr-test.friday.tech',
      // target: 'http://**************:9022',
      changeOrigin: true,
      pathRewrite: {
        '^/api/appointment': '/api/appointment',
      },
    },
    '/api/medical': {
      target: 'https://emr-test.friday.tech',
      // target: 'http://**************:9022',
      changeOrigin: true,
      pathRewrite: {
        '^/api/medical': '/api/medical',
      },
    },
    '/api': {
      target: 'https://emr-test.friday.tech',
      // target: 'http://localhost:9021/',
      changeOrigin: true,
      pathRewrite: {
        '^/api/emr': '/api/emr',
      },
    },
    // [test] 配置登录相关使用
    '/Platform/auditing': {
      target: `https://dental-test.friday.tech/Platform/auditing`,
      changeOrigin: true,
      pathRewrite: { '^/Platform/auditing': '' },
    },
    // [test] 配置登录相关使用 换取token使用
    '/api/businessusers': {
      target: `https://dental-test.friday.tech/api`,
      changeOrigin: true,
      pathRewrite: { '^/api/businessusers': '' },
    },
  },
  virtual: {
    '/api': {
      target: 'https://dental-virtual.friday.tech',
      changeOrigin: true,
      pathRewrite: {
        '^/api/emr': '',
      },
    },
    '/medical': {
      target: 'https://dental-virtual.friday.tech',
      changeOrigin: true,
      pathRewrite: {
        '^/medical': '/api/medical',
      },
    },
    '/businessusers': {
      target: 'https://dental-virtual.friday.tech',
      changeOrigin: true,
      pathRewrite: {
        '^/businessusers': '/api/businessusers',
      },
    },
  },
  pre: {
    '/api': {
      target: 'https://dental-pre.friday.tech',
      changeOrigin: true,
      pathRewrite: {
        '^/api/emr': '',
      },
    },
    '/medical': {
      target: 'https://dental-pre.friday.tech',
      changeOrigin: true,
      pathRewrite: {
        '^/medical': '/api/medical',
      },
    },
    '/businessusers': {
      target: 'https://dental-pre.friday.tech',
      changeOrigin: true,
      pathRewrite: {
        '^/businessusers': '/api/businessusers',
      },
    },
    // [test] 配置登录相关使用
    '/Platform/auditing': {
      target: `https://dental-test.friday.tech/Platform/auditing`,
      changeOrigin: true,
      pathRewrite: { '^/Platform/auditing': '' },
    },
    // [test] 配置登录相关使用 换取token使用
    '/api/businessusers': {
      target: `https://dental-test.friday.tech/api`,
      changeOrigin: true,
      pathRewrite: { '^/api/businessusers': '' },
    },
  },
};
