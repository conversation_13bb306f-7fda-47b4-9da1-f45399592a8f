/**
 * 晨会列表页面
 * */
import { Input, Table, Select, Tag } from 'antd';
import React, { Component } from 'react';
import styles from './index.less';
import { connect } from 'dva';
import { history } from 'umi';
import { doctorList } from '@/services/doctorManage';
const { Search } = Input;

class List extends Component {
  constructor(props) {
    super(props);
    this.state = {
      CodeType: null, // 判断接口数据状态
      DoctorList: [], // 筛选的医生列表
      Columns: [
        {
          title: '病历号',
          dataIndex: 'fileNumber',
          key: '1',
          align: 'left',
          ellipsis: true,
          width: 104,
          render: (text, record) => (
            <div
              style={{
                wordWrap: 'break-word',
                wordBreak: 'break-all',
                width: '100%',
                whiteSpace: 'normal',
              }}
            >
              {text}
            </div>
          ),
        },
        {
          title: '患者姓名',
          dataIndex: 'name',
          key: '2',
          align: 'left',
          width: 115,
          render: (text) => <div>{text && text}</div>,
        },
        {
          title: '预约主诉',
          dataIndex: 'currentCure',
          key: '3',
          align: 'left',
          width: 154,
          render: (text) => <div>{text && text}</div>,
        },
        {
          title: '预约时间',
          dataIndex: 'appointmentDate',
          key: '4',
          align: 'left',
          width: 120,
          render: (text) => <div>{text && text}</div>,
        },
        {
          title: '接诊医生',
          dataIndex: 'doctorName',
          key: '4',
          align: 'left',
          width: 80,
          render: (text) => <div>{text && text}</div>,
        },
        //   "appointmentStatus": 6,//1预约正常(未接诊)   已接诊：4已到诊 5已就诊 6已结算
        {
          title: '接诊状态',
          dataIndex: 'appointmentStatus',
          key: '5',
          align: 'left',
          width: 80,
          ellipsis: true,
          render: (appointmentStatus, record) => (
            // 未接诊 1预约正常 4已到诊    已接诊：5已就诊 6已结算
            <div>
              {appointmentStatus == 5 || appointmentStatus == 6 ? (
                <div>
                  <i className={styles.dotGreen} /> 已接诊
                </div>
              ) : (
                <div>
                  <i className={styles.dotRed} /> 未接诊
                </div>
              )}
            </div>
          ),
        },
        {
          title: '晨会意见',
          dataIndex: 'opinion',
          key: '5',
          align: 'left',
          width: 358,
          ellipsis: true,
          render: (text, record) => (
            <>
              {!!text && (
                <div
                  style={{
                    width: 326,
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  {text && text}
                </div>
              )}

              {!text && <div style={{ width: '100%', textAlign: 'center' }}>-</div>}
            </>
          ),
        },
        {
          title: '操作',
          dataIndex: 'operation',
          key: '8',
          align: 'center',
          width: 114,
          render: (text, record) => (
            <div
              style={{
                wordWrap: 'break-word',
                wordBreak: 'break-all',
                width: '100%',
                whiteSpace: 'normal',
              }}
            >
              <span
                style={{ color: '#4BA2FF', marginRight: '5px' }}
                onClick={() => {
                  this.goClinicMornMeetDetails(record);
                }}
              >
                记录
              </span>
            </div>
          ),
        },
      ],
    };

    this.refByQueryTypeSelect = React.createRef();
  }

  componentDidMount() {
    // 获取预约列表
    this.appointmentList();
    // 获取预约医生
    this.fetchDoctorList();
  }

  // 跳转到晨会详情页面
  goClinicMornMeetDetails = (value) => {
    /*
     id: 116,                 //预约ID
     patientId: 47,           //患者ID
     fileNumber: "YN0A000012",//病历号
    * */
    const { id, patientId } = value || {};
    console.log('valuevalue :: 123123 ', value);
    history.push({
      pathname: `/emr/ClinicMornMeet/Details/${id}/${patientId}`,
      state: {
        /*patientId: value.patientId,
        patientData: {
          name: value.name,  //患者姓名
          patientId: value.patientId,//患者id
          fileNumber: value.fileNumber//病历号

        }*/
      },
    });
  };

  // 获取医生列表
  fetchDoctorList = async () => {
    let getByDoctorList = await doctorList({
      pageNum: 1,
      pageSize: 100,
      userName: null, //医生姓名
      tenantId: localStorage.getItem('tenantId'),
      organizationId: localStorage.getItem('organizationId'),
    });

    const { content, code } = getByDoctorList || {};
    if (code == 200 && content) {
      const { pageNum, pageSize, resultList } = content || {};
      //     { value: '1', label: '既往病历' },
      this.setState({
        DoctorList: Array.isArray(resultList)
          ? resultList.map((item) => {
              return { value: item.userId, label: item.userName };
            })
          : [],
      });
    }

    console.log('getByDoctorListgetByDoctorList123 :: ', getByDoctorList);
  };

  // 请求列表内容
  appointmentList = (params) => {
    console.log('paramsparams123 :: ', params);
    const { dispatch } = this.props;
    dispatch({
      type: 'ClinicMornMeetModel/appointmentList',
      payload: params ? params : {},
      callback: (res) => {},
    });
  };

  // 切换页数和列表条数
  changePage = (value, value1) => {
    console.log('value11231 :: ', value1);
    this.appointmentList({ pageNow: value, pageSize: value1 });
  };

  // 搜素
  onSearch = (value) => {
    this.appointmentList({ pageNow: 1, searchFiled: value });
  };

  onShowSizeChange = (value, value1) => {
    // this.appointmentList({pageNow:1,pageSize:value1})
  };

  render() {
    const { Columns, DoctorList } = this.state;
    const { loading, ClinicMornMeetModel } = this.props;
    const { paramsByClinicMornMeetList, dataByAppointmentList } = ClinicMornMeetModel || {};
    const { code: codeByDataByAppointmentList, content: contentByDataByAppointmentList } =
      dataByAppointmentList || {};
    const {
      pageNum: pageNumByData,
      pageSize: pageSizeByData,
      resultList: resultListByData,
      total: totalByData,
    } = contentByDataByAppointmentList || {};

    const {
      pageSize, // 获取条数
      pageNow, // 获取页数
      total, // 总条数
      searchValue, // 搜索值resultList
      searchFiled, // 患者姓名、病历号、主诉
      queryType, // null 全部 1今日病历 2.既往病历 3.未来病历
      doctorId, // 搜索医生用户ID
    } = paramsByClinicMornMeetList || {};

    return (
      <div className={styles.CustomerArchivesBox} id="CustomerArchivesId">
        <div className={styles.headerSearch}>
          <div className={styles.screenBox}>
            <div className={styles.screenBoxBtn}>
              <i
                onClick={() => {
                  console.log(
                    'this.refByQueryTypeSelect123123 :: ',
                    this.refByQueryTypeSelect.current,
                  );
                  this.refByQueryTypeSelect.current.focus();
                }}
                className={styles.ClinicMornMeetFiltrateIcon}
              ></i>
              <Select
                ref={this.refByQueryTypeSelect}
                // open={false}
                defaultValue={null}
                value={queryType}
                style={{ width: 100 }}
                bordered={false}
                options={[
                  { value: '1', label: '今日病历' },
                  { value: '2', label: '既往病历' },
                  { value: '3', label: '未来病历' },
                  { value: null, label: '全部病历' },
                ]}
                onSelect={(value) => {
                  this.appointmentList({ queryType: value, pageNow: 1 });
                }}
              />
            </div>

            <div className={styles.screenBoxBtn}>
              <i className={styles.ClinicMornMeetFiltrateIcon}></i>
              <Select
                value={doctorId}
                style={{ width: 100 }}
                bordered={false}
                options={[{ value: null, label: '全部医生' }, ...DoctorList]}
                onSelect={(value) => {
                  console.log('valuevalue12312 :: ', value);
                  this.appointmentList({ doctorId: value, pageNow: 1 });
                }}
              />
            </div>
          </div>
          <div>
            <Search
              defaultValue={searchFiled}
              placeholder="请输入患者姓名/病历号/主诉"
              keyName="GlobalSearch1"
              onSearch={this.onSearch}
              // onChange={this.ChangeSearch}
              style={{ width: 320 }}
            />
          </div>
        </div>
        <div className={styles.TableWarp}>
          <Table
            dataSource={resultListByData}
            bordered
            columns={Columns}
            loading={!!loading.effects['ClinicMornMeetModel/appointmentList']}
            scroll={{ y: '70vh' }}
            /*locale={{
              emptyText:
                <DataFailure
                  DataFailMsg={(CodeType===4 && '暂无人员列表') || (CodeType===0 && '数据加载失败！') || '加载中...'}
                  CodeType={CodeType}
                  key={CodeType!==null?`${CodeType}`: 'CustomerArchivesKey'}
                />
            }}*/
            pagination={{
              total: totalByData,
              onChange: this.changePage,
              pageSize: pageSizeByData,
              showSizeChanger: true,
              onShowSizeChange: this.onShowSizeChange,
              current: pageNumByData,
            }}
            rowKey={(record, index) => index}
          />
        </div>
      </div>
    );
  }
}
export default connect(({ ClinicMornMeetModel, loading }) => ({
  ClinicMornMeetModel,
  loading,
}))(List);
