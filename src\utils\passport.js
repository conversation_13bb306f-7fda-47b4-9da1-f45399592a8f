const CryptoJS = require('crypto-js');  //引用AES源码js
// 获得AesString
export function getAesString(data, key1, iv1, digital = '') {
  let key = CryptoJS.enc.Utf8.parse(key1);
  let iv = CryptoJS.enc.Utf8.parse(iv1);
  let srcs = CryptoJS.enc.Utf8.parse(data);
  let encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  if (digital == '') {
    return encrypted.toString();
  } else {
    return encrypted.ciphertext.toString();
  }
}
//获取AES
function getAES(password, key, iv, digital = '') {
  let encrypted;
  if (digital == '') {
    encrypted = getAesString(password, key, iv);
  } else {
    encrypted = getAesString(password, key, iv, digital);
  }
  return encrypted;
}
export default {
  getAES,
}
