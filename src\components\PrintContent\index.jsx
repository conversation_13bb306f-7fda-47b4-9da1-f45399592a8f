import { Row, Col, Input, Select, Tooltip } from 'antd';
import React, { useState, useRef, Component } from 'react';
import { visitIndicatorType, status } from '@/utils/common.js';
import { connect } from 'dva';
import { toothUtils } from '@/utils/toothUtils'; //牙位选择显示
import styles from './style.less'; //样式
import CdnImgs from '@/components/CdnImgs'; //获取图片路径

// 打印病历预览
class PrintContent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      baseInfoLonding: false, //加载状态
      emrLoading: false, //加载状态
      basicByIdInfo: {}, //患者信息
      healthCondition: {}, //健康状况
      medicalLists: [], //已归档病历
    };
  }
  //生命周期初始化
  componentDidMount() {
    this.findCustomerBasicById();
    this.getPatHealthCondition();
    if (this.props.patientInfo && this.props.patientInfo.fileNumber) {
      this.getAllMedicalRecords();
    }
  }
  //患者信息
  findCustomerBasicById = () => {
    const { dispatch } = this.props;
    let historyOptionsParams = {
      tenantId: localStorage.getItem('tenantId'),
      organizationId: localStorage.getItem('organizationId'),
      customerId: this.props.patientInfo ? this.props.patientInfo.patientId : '',
      userId: localStorage.getItem('userId'),
      userName: localStorage.getItem('userName'),
    };
    // console.log("参数----",historyOptionsParams)
    this.setState({
      baseInfoLonding: true,
    });
    if (dispatch) {
      dispatch({
        type: 'homePage/findCustomerBasicByIdService',
        payload: historyOptionsParams,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              baseInfoLonding: false,
              basicByIdInfo: res.content,
            });
          }
          // console.log("患者信息===",JSON.stringify(res.content))
        },
      });
    }
  };
  //健康状况
  getPatHealthCondition = () => {
    const { dispatch } = this.props;
    let healthConditionParams = {
      tenantId: localStorage.getItem('tenantId'), //平台标识
      organizationId: localStorage.getItem('organizationId'), //机构标识
      customerId: this.props.patientInfo ? this.props.patientInfo.patientId : '',
    };
    if (dispatch) {
      dispatch({
        type: 'homePage/patHealthCondition',
        payload: healthConditionParams,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              healthCondition: res.content,
            });
          }
        },
      });
    }
  };
  // 请求全部病历接口
  getAllMedicalRecords = () => {
    const { dispatch, oneErmSearchInfo } = this.props;
    let param =
      oneErmSearchInfo && oneErmSearchInfo.emrId
        ? {
            tenantId: localStorage.getItem('tenantId'), //平台标识
            emrId: oneErmSearchInfo.emrId, //大病历号
            emrSubId: oneErmSearchInfo.emrSubId, //小病历号
          }
        : {
            tenantId: localStorage.getItem('tenantId'), //平台标识
            patientId: this.props.patientInfo ? this.props.patientInfo.patientId : '', //患者标识
            emrId: this.props.patientInfo ? this.props.patientInfo.fileNumber : '', //大病历号
            status: 4, //状态：2编辑中；3已完成；4归档
            sort: 'DESC', //排序：正序是ASC 倒叙是DESC
          };
    this.setState({
      emrLoading: true,
    });
    if (dispatch) {
      // 如果有emrId则打印的是单条病例信息。布局与打印全部病例有差异
      if (oneErmSearchInfo && oneErmSearchInfo.emrId) {
        dispatch({
          type: 'homePage/medicalRecordDetail',
          payload: param,
          callback: (res) => {
            if (res.code == 200) {
              this.setState({
                medicalLists: res.content != null ? [res.content] : [],
              });
            }
          },
        });
      } else {
        dispatch({
          type: 'homePage/allMedicalRecords',
          payload: param,
          callback: (res) => {
            if (res.code == 200) {
              // console.log("病历数据==",JSON.stringify(res))
              this.setState({
                medicalLists: res.rows,
              });
            }
          },
        });
      }
    }
  };
  render() {
    const { basicByIdInfo, healthCondition, medicalLists } = this.state;
    const { oneErmSearchInfo } = this.props; // 判断是否单条病例打印
    return (
      <div>
        <div
          style={{ borderBottom: '1px solid rgba(0,0,0,0.06)', width: '100%', paddingBottom: 8 }}
        >
          <div
            style={{
              fontSize: 24,
              fontWeight: 500,
              color: 'rgba(0,0,0,1)',
              lineHeight: '33px',
              textAlign: 'center',
            }}
          >
            {localStorage.getItem('organizationName')}
          </div>
          {oneErmSearchInfo && oneErmSearchInfo.emrId ? null : (
            <div
              style={{
                fontSize: 20,
                fontWeight: 400,
                color: 'rgba(0,0,0,0.85)',
                lineHeight: '28px',
                textAlign: 'center',
              }}
            >
              病历首页
            </div>
          )}
          {oneErmSearchInfo && oneErmSearchInfo.emrId ? null : (
            <div
              style={{
                fontSize: 20,
                fontWeight: 400,
                color: 'rgba(0,0,0,0.85)',
                lineHeight: '28px',
                textAlign: 'right',
              }}
            >
              <span
                style={{
                  fontSize: 14,
                  fontWeight: 400,
                  lineHeight: '24px',
                  color: 'rgba(0,0,0,0.85)',
                }}
              >
                病历号：{basicByIdInfo.fileNumber}
              </span>
              <span
                style={{
                  fontSize: 14,
                  fontWeight: 400,
                  lineHeight: '24px',
                  color: 'rgba(0,0,0,0.85)',
                  marginLeft: 24,
                }}
              >
                日期：{basicByIdInfo.createdGmtAt}
              </span>
            </div>
          )}
        </div>
        <div style={{ width: '100%', margin: '15px 30px', borderBottom: '1px solid #E5E6EB' }}>
          <div
            style={{ color: 'rgba(0,0,0,1)', lineHeight: '25px', fontWeight: 900, fontSize: 18 }}
          >
            客户信息
          </div>
          <div style={{ padding: '14px 90px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <div>
                  <span
                    style={{
                      color: 'rgba(0,0,0,0.85)',
                      lineHeight: '24px',
                      fontWeight: 400,
                      fontSize: 14,
                    }}
                  >
                    姓名：
                  </span>
                  <span
                    style={{
                      color: 'rgba(0,0,0,1)',
                      lineHeight: '22px',
                      fontWeight: 400,
                      fontSize: 14,
                    }}
                  >
                    {basicByIdInfo.name || ''}
                  </span>
                </div>
                <div style={{ marginTop: 10 }}>
                  <span
                    style={{
                      color: 'rgba(0,0,0,0.85)',
                      lineHeight: '24px',
                      fontWeight: 400,
                      fontSize: 14,
                    }}
                  >
                    国籍：
                  </span>
                  <span
                    style={{
                      color: 'rgba(0,0,0,1)',
                      lineHeight: '22px',
                      fontWeight: 400,
                      fontSize: 14,
                    }}
                  >
                    {basicByIdInfo.nationality || ''}
                  </span>
                </div>
              </div>
              <div>
                <div>
                  <span
                    style={{
                      color: 'rgba(0,0,0,0.85)',
                      lineHeight: '24px',
                      fontWeight: 400,
                      fontSize: 14,
                    }}
                  >
                    性别：
                  </span>
                  <span
                    style={{
                      color: 'rgba(0,0,0,1)',
                      lineHeight: '22px',
                      fontWeight: 400,
                      fontSize: 14,
                    }}
                  >
                    {basicByIdInfo.sex == 1 ? '男' : basicByIdInfo.sex == 2 ? '女' : ''}
                  </span>
                </div>
                <div style={{ marginTop: 10 }}>
                  <span
                    style={{
                      color: 'rgba(0,0,0,0.85)',
                      lineHeight: '24px',
                      fontWeight: 400,
                      fontSize: 14,
                    }}
                  >
                    电话：
                  </span>
                  <span
                    style={{
                      color: 'rgba(0,0,0,1)',
                      lineHeight: '22px',
                      fontWeight: 400,
                      fontSize: 14,
                    }}
                  >
                    {basicByIdInfo.oftenTel}
                  </span>
                </div>
              </div>
              <div>
                <div>
                  <span
                    style={{
                      color: 'rgba(0,0,0,0.85)',
                      lineHeight: '24px',
                      fontWeight: 400,
                      fontSize: 14,
                    }}
                  >
                    出生日期：
                  </span>
                  <span
                    style={{
                      color: 'rgba(0,0,0,1)',
                      lineHeight: '22px',
                      fontWeight: 400,
                      fontSize: 14,
                    }}
                  >
                    {basicByIdInfo.birthday}
                  </span>
                </div>
                {oneErmSearchInfo && oneErmSearchInfo.emrId ? (
                  <div style={{ marginTop: 10 }}>
                    <span
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                      }}
                    >
                      病历号：
                    </span>
                    <span
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                      }}
                    >
                      {basicByIdInfo.fileNumber}
                    </span>
                  </div>
                ) : null}
              </div>
            </div>
          </div>
        </div>
        {oneErmSearchInfo && oneErmSearchInfo.emrId ? null : (
          <div style={{ width: '100%', margin: '15px 30px', borderBottom: '1px solid #E5E6EB' }}>
            <div
              style={{ color: 'rgba(0,0,0,1)', lineHeight: '25px', fontWeight: 900, fontSize: 18 }}
            >
              健康状况
            </div>
            <div style={{ padding: '14px 41px' }}>
              <div>
                <div>
                  <div style={{ display: 'flex' }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 22,
                      }}
                    >
                      既往病史：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      {healthCondition.previousHistory ? healthCondition.previousHistory : ' 无'}
                    </div>
                  </div>
                  <div style={{ marginTop: 10, display: 'flex' }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 36,
                      }}
                    >
                      家族史：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      {healthCondition.familyHistory ? healthCondition.familyHistory : ' 无'}
                    </div>
                  </div>
                  <div style={{ marginTop: 10, display: 'flex' }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 36,
                      }}
                    >
                      过敏史：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      {healthCondition.allergicHistory ? healthCondition.allergicHistory : '  无'}
                    </div>
                  </div>
                  <div style={{ marginTop: 10, display: 'flex' }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 22,
                      }}
                    >
                      在服药物：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      {healthCondition.inTaking ? healthCondition.inTaking : '无'}
                    </div>
                  </div>
                  <div style={{ marginTop: 10, display: 'flex' }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 50,
                      }}
                    >
                      怀孕：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      {healthCondition.pregnant ? healthCondition.pregnant : '无'}
                    </div>
                  </div>
                  <div style={{ marginTop: 10, display: 'flex' }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: -6,
                      }}
                    >
                      关节置换手术：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      {healthCondition.jointReplacement ? healthCondition.jointReplacement : '否认'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {(medicalLists || []).map((item, index) => (
          <div>
            <div
              style={{
                width: '100%',
                height: 44,
                backgroundColor: '#F7F8FA',
                borderRadius: '2px',
                paddingLeft: 24,
              }}
            >
              <span
                style={{
                  fontSize: 14,
                  fontWeight: 400,
                  color: 'rgba(0,0,0,0.85)',
                  lineHeight: '43px',
                }}
              >
                {item.clinicTime}
              </span>
              <span
                style={{
                  fontSize: 14,
                  fontWeight: 400,
                  color: 'rgba(0,0,0,0.85)',
                  lineHeight: '43px',
                  marginLeft: 30,
                }}
              >
                {visitIndicatorType[item.visitIndicator]}
              </span>
              <span
                style={{
                  fontSize: 14,
                  fontWeight: 400,
                  color: 'rgba(0,0,0,0.85)',
                  lineHeight: '43px',
                  marginLeft: 30,
                }}
              >
                医生:{item.userName}
              </span>
            </div>
            <div style={{ padding: '14px 41px' }}>
              <div>
                <div>
                  <div style={{ display: 'flex' }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 50,
                      }}
                    >
                      主诉：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      {item.illnessDesc}
                    </div>
                  </div>
                  <div style={{ marginTop: 12, display: 'flex' }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 36,
                      }}
                    >
                      现病史：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      {item.preIllness}
                    </div>
                  </div>
                  <div style={{ marginTop: 12, display: 'flex' }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 36,
                      }}
                    >
                      既往史：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      {item.pastHist}
                    </div>
                  </div>
                  <div style={{ marginTop: 12, display: 'flex' }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 22,
                      }}
                    >
                      全身状况：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      {item.genCond}
                    </div>
                  </div>
                  <div style={{ marginTop: 12, display: 'flex' }}>
                    <div
                      style={{
                        width: '70px',
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 50,
                        marginTop: item.examine && item.examine.length > 0 ? 23 : 0,
                      }}
                    >
                      检查：
                    </div>
                    <div style={{ marginLeft: '-2%' }}>
                      {(item.examine || []).map((item, i) => (
                        <div style={{ display: 'flex', marginTop: 7 }} key={i}>
                          <table className={styles.table_row}>
                            <tbody>
                              <tr>
                                <td className={styles.line_th_row}>
                                  {toothUtils.showTooth(1, item.toothPosition)}
                                </td>
                                <td className={styles.line_th_col}>
                                  {toothUtils.showTooth(2, item.toothPosition)}
                                </td>
                              </tr>
                            </tbody>
                            <tbody>
                              <tr>
                                <td className={styles.line_row}>
                                  {toothUtils.showTooth(4, item.toothPosition)}
                                </td>
                                <td className={styles.line_col}>
                                  {toothUtils.showTooth(3, item.toothPosition)}
                                </td>
                              </tr>
                            </tbody>
                          </table>
                          <span
                            style={{
                              marginTop: 10,
                              marginLeft: 10,
                              fontSize: 14,
                              maxWidth: 700,
                              display: 'flex',
                              alignItems: 'center',
                            }}
                          >
                            {item.value}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div style={{ marginTop: 12, display: 'flex' }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 22,
                      }}
                    >
                      辅助检查：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      {item.auxiExam}
                    </div>
                  </div>
                  <div style={{ display: 'flex', marginTop: 12 }}>
                    <div
                      style={{
                        width: '70px',
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 50,
                        marginTop: item.diag && item.diag.length > 0 ? 23 : 0,
                      }}
                    >
                      诊断：
                    </div>
                    <div style={{ marginLeft: '-2%' }}>
                      {(item.diag || []).map((item1, i) => (
                        <div style={{ display: 'flex', marginTop: 7 }} key={i}>
                          <table className={styles.table_row}>
                            <tbody>
                              <tr>
                                <td className={styles.line_th_row}>
                                  {toothUtils.showTooth(1, item1.toothPosition)}
                                </td>
                                <td className={styles.line_th_col}>
                                  {toothUtils.showTooth(2, item1.toothPosition)}
                                </td>
                              </tr>
                            </tbody>
                            <tbody>
                              <tr>
                                <td className={styles.line_row}>
                                  {toothUtils.showTooth(4, item1.toothPosition)}
                                </td>
                                <td className={styles.line_col}>
                                  {toothUtils.showTooth(3, item1.toothPosition)}
                                </td>
                              </tr>
                            </tbody>
                          </table>
                          <span
                            style={{
                              marginTop: 10,
                              marginLeft: 10,
                              fontSize: 14,
                              maxWidth: 700,
                              display: 'flex',
                              alignItems: 'center',
                            }}
                          >
                            {item1.value}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div style={{ display: 'flex', marginTop: 12 }}>
                    <div
                      style={{
                        width: '105px',
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 22,
                        marginTop: item.treat && item.treat.length > 0 ? 17 : 0,
                      }}
                    >
                      本次治疗：
                    </div>
                    <div style={{ marginLeft: '-2%' }}>
                      {item.treat && item.treat.length > 0 && item.treat[0].value !== ''
                        ? (item.treat || []).map((item, i) => (
                            <div style={{ display: 'flex', marginTop: 7 }}>
                              <table className={styles.table_row}>
                                <tbody>
                                  <tr>
                                    <td className={styles.line_th_row}>
                                      {toothUtils.showTooth(1, item.toothPosition)}
                                    </td>
                                    <td className={styles.line_th_col}>
                                      {toothUtils.showTooth(2, item.toothPosition)}
                                    </td>
                                  </tr>
                                </tbody>
                                <tbody>
                                  <tr>
                                    <td className={styles.line_row}>
                                      {toothUtils.showTooth(4, item.toothPosition)}
                                    </td>
                                    <td className={styles.line_col}>
                                      {toothUtils.showTooth(3, item.toothPosition)}
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                              <span
                                style={{
                                  marginTop: 10,
                                  marginLeft: 10,
                                  fontSize: 14,
                                  maxWidth: 700,
                                }}
                              >
                                {item.value}
                              </span>
                            </div>
                          ))
                        : null}
                    </div>
                  </div>
                  <div style={{ display: 'flex', marginTop: 12 }}>
                    <div
                      style={{
                        width: '70px',
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 50,
                        marginTop: item.dispose && item.dispose.length > 0 ? 17 : 0,
                      }}
                    >
                      处置：
                    </div>
                    <div style={{ marginLeft: '-2%' }}>
                      {item.dispose && item.dispose.length > 0 && item.dispose[0].value !== ''
                        ? (item.dispose || []).map((item, i) => (
                            <div style={{ display: 'flex', marginTop: 7 }}>
                              <table className={styles.table_row}>
                                <tbody>
                                  <tr>
                                    <td className={styles.line_th_row}>
                                      {toothUtils.showTooth(1, item.toothPosition)}
                                    </td>
                                    <td className={styles.line_th_col}>
                                      {toothUtils.showTooth(2, item.toothPosition)}
                                    </td>
                                  </tr>
                                </tbody>
                                <tbody>
                                  <tr>
                                    <td className={styles.line_row}>
                                      {toothUtils.showTooth(4, item.toothPosition)}
                                    </td>
                                    <td className={styles.line_col}>
                                      {toothUtils.showTooth(3, item.toothPosition)}
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                              <span
                                style={{
                                  marginTop: 10,
                                  marginLeft: 10,
                                  fontSize: 14,
                                  maxWidth: 700,
                                  display: 'flex',
                                  alignItems: 'center',
                                }}
                              >
                                {item.value}
                              </span>
                            </div>
                          ))
                        : null}
                    </div>
                  </div>
                  <div style={{ display: 'flex', marginTop: 12 }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 50,
                      }}
                    >
                      医嘱：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      {item.docOrder}
                    </div>
                  </div>
                  <div style={{ marginTop: 12, display: 'flex' }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 22,
                      }}
                    >
                      影像资料：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      <div className={styles.videoImg}>
                        {item.linkImgs
                          ? (item.linkImgs || []).map((item, index) => (
                              <>
                                <div className={styles.videomargin} key={index}>
                                  <div style={{ margin: '0 0 5px' }}>{item.className}</div>
                                  <div className={styles.images}>
                                    <div className={styles.imgborder}>
                                      <CdnImgs
                                        onRef={(ref) => (this.child = ref)}
                                        fileUrl={item.url}
                                        height={'88px'}
                                      />
                                    </div>
                                    <div className={styles.ctimgInfo}>
                                      <div className={styles.lineHeightStyle}>
                                        拍摄时间：{item.shootingTime}
                                      </div>
                                      <div className={styles.lineHeightStyle}>
                                        上传时间：{item.createdGmtAt}
                                      </div>
                                      <Tooltip title={item.fileDesc}>
                                        <div
                                          className={`${styles.fontLineStyle} ${styles.lineHeightStyle}`}
                                        >
                                          影像分析：{item.fileDesc}
                                        </div>
                                      </Tooltip>
                                    </div>
                                  </div>
                                </div>
                              </>
                            ))
                          : null}
                      </div>
                    </div>
                  </div>
                  <div style={{ marginTop: 12, display: 'flex' }}>
                    <div
                      style={{
                        color: 'rgba(0,0,0,0.85)',
                        lineHeight: '24px',
                        fontWeight: 400,
                        fontSize: 14,
                        marginLeft: 9,
                      }}
                    >
                      知情同意书：
                    </div>
                    <div
                      style={{
                        color: 'rgba(0,0,0,1)',
                        lineHeight: '22px',
                        fontWeight: 400,
                        fontSize: 14,
                        width: '80%',
                      }}
                    >
                      {item.mrcs
                        ? (item.mrcs || []).map((item, index) => (
                            <Col key={index} style={{ display: 'inline-block', marginRight: 10 }}>
                              <Row style={{ margin: '5px 0' }}>{item.tmptName}</Row>
                              <Row>
                                <Row>
                                  <Col>
                                    <div style={{ width: '132px' }} className={styles.imgborder}>
                                      <CdnImgs fileUrl={item.url} />
                                    </div>
                                  </Col>
                                </Row>
                              </Row>
                            </Col>
                          ))
                        : null}
                    </div>
                  </div>
                  <div
                    style={{
                      width: '100%',
                      textAlign: 'right',
                      marginTop: '20px',
                      paddingRight: '150px',
                    }}
                  >
                    医生签名：
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }
}

export default connect(({}) => ({}))(PrintContent);
