import request from '@/utils/request';

//获取词条列表
export async function EntryList(params) {
  return request('/api/emr/system/findWordsByType', {
    method: 'POST',
    data: params,
  });
}

//新增/编辑词条
export async function saveWords(params) {
  return request('/api/emr/system/saveWords', {
    method: 'POST',
    data: params,
  });
}

//删除词条
export async function deleteWord(params) {
  return request('/api/emr/system/deleteWord', {
    method: 'POST',
    data: params,
  });
}
