## 全局搜索框组件 支持联想词

* keywordsUrl 表示调用当前搜索联想词接口地址
* onSearch 回调获取搜索值 选中联想词返回联想词的值
* onChange 回调获取搜索框内值改变事件 

```jsx

import React, { Component } from 'react';
import GlobalSearch from '@/components/GlobalSearch'


export default class AnchorSwitch extends Component {
  render() {
    return (
      <div>
        <Card>
          <GlobalSearch
            keyName="GlobalSearch1"
            placeholder="全局搜索"
            keywordsUrl="/api/seanchdata"
            onSearch={value => {console.log('onSearch ===', value);}}
            onChange={value=>{console.log('change --', value);}}
            style={{width:'500px'}}
          />
        </Card>
      </div>
    )
  }
}

```
