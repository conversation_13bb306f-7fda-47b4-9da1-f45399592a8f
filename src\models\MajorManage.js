import {findMajores} from '@/services/MajorManage';
import {notification} from "antd";
const findMajoresCenterModel = {
  namespace: 'findMajoresModel',
  state: {
    findMajoresData:{},//专业字典维护
    loading:false,
    loadTip:"加载中",
  },
  //异步
  effects: {
    // 专业字典维护
    *findMajoresService({payload, callback} , { call, put }) {
      const response = yield call(findMajores ,payload);
      yield put({
        type: 'findMajoresInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
  },

  //同步
  reducers: {
    findMajoresInfo(state, action) {
      return {
        ...state,
        findMajoresData: action.payload || {},
      };
    },
  },
};
export default findMajoresCenterModel;
