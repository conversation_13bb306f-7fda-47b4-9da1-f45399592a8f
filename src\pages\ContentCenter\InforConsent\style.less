@import '~antd/lib/style/themes/default.less';

.leftMenu{
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-left: 0;
  border-bottom: 0;
  .MenuTitle{
    padding: 16px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  }
}
.Rightcontent{
  position: relative;
  padding: 16px;
  .titles{
    text-align: center;
  }
  .title{
    font-size: 18px;
    font-family: PingFang SC;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.85);
  }
  .status{
    color: #00B42A;
    font-size: 14px;
    margin-left: 12px;
  }
  .smallTitle{
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
  }
}
.Intertranslation{
  display: flex;
  position:absolute;
  right:60px;
  top:17px;
  .Choose{
    cursor: pointer;
    width: 24px;
    height: 24px;
    background: rgba(66, 146, 255, 0.12);
    border-radius: 2px;
    border: 1px solid #4292FF;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #4292FF;
    text-align: center;
  }
  .notChoose{
    cursor: pointer;
    width: 20px;
    height: 20px;
    background: #FFF;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.45);
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    text-align: center;
    margin-top: 1px;
  }
  .editor{
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #4292FF;
    margin-left: 16px;
  }
}
.inner{
  padding: 16px;
  //border: 1px solid #333333;
}
:global{
  .rdw-editor-main{
    max-height: 420px;
  }
  .ant-form-item{
    margin-bottom: 12px;
  }
  .editor-container .editor-contenteditable-div .editable-range{
    height: 400px !important;
  }
}
.Bookcontent{
  padding: 16px;
}
.editorBorder{
  //overflow-y: scroll;
  //height: 500px;
  //border: 1px solid rgba(0, 0, 0, 0.15);
}
.bottomBtns{
  position:absolute;
  bottom: 0;
  //right:0;
  //left:0;
  width: 100%;
  height: 56px;
  background: #FFF;
  box-shadow: 0 -2px 6px -2px rgba(0, 0, 0, 0.25);
  .btn_position{
    float: right;
    margin-right: 3%;
    margin-top: 12px;
  }
}
.mL_16{
  margin-left: 16px;
}
.modelContent{
  margin: 0 8px;
}
  .pointer{
    position: relative;
    display: flex;
  }
  .arrows{
    width: 12px;
    height: 12px;
    margin-top: 3px;
  }
  .fileIcon{
    width: 20px;
    height: 20px;
    margin-left: 4px;
  }
  .filetitle{
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.45);
    line-height: 23px;
  }
  .fileName{
    display: flex;
  }
  .Unselected{
    width: 20px;
    height: 20px;
  }

.chooseBgcolor{
  background: rgba(216, 216, 216, 0.5);
  border-radius: 2px;
}
.hidden{
  display: none;
}
.show{
  display: block;
}
.chooseFontcolor{
  color:#4292FF !important;
}
.SelectedStyle{
  color:#4292FF;
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 23px;
}
.UnselectedStyle{
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 23px;
}
.addBtns{
  cursor: pointer;
  position: absolute;
  right: 4px;
  top:-5px;
  &:hover{
    color: #4292FF;
  }
}
.addFile{
  position:absolute;
  z-index: 999;
  right:-115px;
  width: 132px;
  height: 104px;
  padding: 4px;
  background: #FFF;
  box-shadow: 0 5px 13px 8px rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  .iconStyle{
    width: 20px;
    height: 20px;
  }
  .addFileName{
    width: 56px;
    height: 20px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    line-height: 20px;
    margin-left: 4px;
  }
}
.addFileLine{
  cursor: pointer;
}
.addFileLine:hover{
  background: rgba(0, 0, 0, 0.06);
}
