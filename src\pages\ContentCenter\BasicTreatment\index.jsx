import {
  Col,
  Row,
  Input,
  Button,
  Form,
  Modal,
  Table,
  Space,
  Popconfirm,
  Select,
  Radio,
  Spin,
  message,
  Pagination
} from 'antd';
import React, { Component } from 'react';
import { GridContent } from '@ant-design/pro-layout';
//引入样式
import styles from './style.less';
import commonStyle from "@/pages/common.less";
//图标
import Unfold from "@/assets/<EMAIL>";
import Fold from "@/assets/<EMAIL>";
import Folder from "@/assets/<EMAIL>";
import FolderSelected from "@/assets/<EMAIL>";
import Delete from "@/assets/<EMAIL>";
import Edit from "@/assets/<EMAIL>";
import NewFolder from "@/assets/<EMAIL>";
import noData from "@/assets/<EMAIL>";
import {connect} from "dva";
//公共验证
import {StringUtils} from "@/utils/StringUtils";

const { Search } = Input;
const {TextArea} = Input;
class BasicTreatment extends Component {
  constructor(props) {
    super(props);
    this.state = {
      firstStatus:false,
      show1:null,// 第一级文件夹点击状态
      show2:null,// 第二级文件夹点击状态
      show3:null,// 第三级文件夹点击状态
      editorStatus:false,//编辑显示状态
      addFileBorderStatus:false,//点击...弹窗
      newFileStatus:false,//新建模板弹窗
      title:"",
      addFileBorderStatus2:false,//新建文件目录状态
      hover1:false,// 第一级文件夹hover状态
      setVisible:false,//删除气泡框
      addFileBorderStatus3:false,//新建模板目录状态
      hover2:false,// 第二级文件夹hover状态
      newModel:false,//新建模板文件弹窗
      editorName:false,//修改模板名称弹窗
      addFileBorderStatus4:false,
      type1:'11',
      clientHeight: document.documentElement.clientHeight, // 屏幕高度
      ListStatus:false,//左侧列表loading
      MenuList:[],//菜单列表
      addFolder:{},//新建文件夹参数
      addNewFolder:false,//新建文件夹弹窗状态
      getMsgListparams:{
        classCode:"",//类别标识
        tenantId:localStorage.getItem('tenantId'),//租户标识,
        pageNum:1,//分页页码
        pageSize:10//分页大小
      },
      messageListData:[],//右侧表格数据
      TableLoading:false,//表格数据loading
      totals:0,//列表数量
      firstData:"",//默认取第一条数据
      secondCode:"",//classCode暂存
      secondData:"",//className暂存
      addParams:{
        status:0,
      },//添加参数
      addLoading:false,//新建文件按钮loading状态
      addModelLoading:false,//新建分类创建按钮loading
      editorLoading:false,//编辑名称确定按钮loading
      addLabelLoading:false,//新增/编辑治疗确定按钮loading
      delete1Loading:false,//确定删除气泡
      delete2Loading:false,//确定删除气泡
      searchval:"",//搜索的内容
    };
    this.resize = this.resize.bind(this);//监听屏幕高度
  }
  //初始化
  componentDidMount() {
    this.getMenuList();//获取基础治疗分组列表
    // 点击其他地方隐藏输入框
    document.addEventListener('click', (e) => {
      if(this.state.addFileBorderStatus2){
        this.setState({
          addFileBorderStatus2:false,
        })
      }
      if(this.state.addFileBorderStatus3){
        this.setState({
          addFileBorderStatus3:false
        })
      }
      if(this.state.addFileBorderStatus4){
        this.setState({
          addFileBorderStatus4:false
        })
      }
    })
    window.addEventListener("resize", this.resize); //监听屏幕高度

  }
  //在组件卸载及销毁之前直接调用
  componentWillUnmount() {
    window.removeEventListener("resize", this.resize); //监听屏幕高度
  }
  //获取基础治疗分组列表
  getMenuList=()=>{
    const {dispatch} = this.props
    this.setState({
      ListStatus:true
    })
    let param={
      tenantId: localStorage.getItem('tenantId'),//平台标识
    }
    if (dispatch) {
      dispatch({
        type: 'BasicTreatments/findTreatClassService',
        payload: param,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              MenuList:res.rows,
              ListStatus:false,
              firstData:res.rows.length>0&&res.rows[0].className?res.rows[0].className:"",
            })
            let arr=res.rows.length>0&&res.rows[0].children?res.rows[0].children[0].className:"";
            let arr1=res.rows.length>0&&res.rows[0].children?res.rows[0].children[0].classCode:"";
            this.state.secondData=arr;
            this.state.secondCode=arr1;
            if(this.state.show1==null&&this.state.show2==null){
              this.setState({
                show1:res.rows.length>0&&res.rows[0].id?res.rows[0].id:null,
                show2:res.rows.length>0&&res.rows[0].children?res.rows[0].children[0].id:null,
              })
              if(res.rows.length>0&&res.rows[0].children){
                this.state.getMsgListparams.classCode=res.rows[0].children[0].classCode;
                if(this.state.getMsgListparams.classCode){
                  this.MessageList()
                }
              }
            }else{
              let art=res.rows;
              let art1=[];
              art.forEach((text,index)=>{
                if(text.id==this.state.show1){
                  art1=text.children;
                  this.setState({
                    firstData:text.className
                  })
                }
              })
              if(art1 && art1.length>0){
                this.setState({
                  show2:art1[0].id,
                  secondData:art1[0].className,
                  secondCode:art1[0].classCode,
                })
                this.state.getMsgListparams.classCode=art1[0].classCode;
                this.MessageList()
                this.getclassType(art1[0].classCode)
              }else{
                this.setState({
                  messageListData:[],
                  totals:0,
                  firstData:"",
                  secondCode:"",
                  secondData:"",
                })
              }
            }
          }else{
            this.setState({
              ListStatus:false
            })
          }
        }
      });
    }
  }
  //监听屏幕高度
  resize() {
    this.setState({ clientHeight: document.documentElement.clientHeight });
  }
  // 搜索菜单
  onSearch=(value)=>{
    this.setState({
      show2:null,
      show1:null,
      messageListData:[],
      totals:0
    })
   if(StringUtils.isNotBlank(value)){
     this.state.searchval=value;
     this.getSearchVal(value)
   }else{
     this.state.searchval="";
     this.getMenuList()
   }
  }
  //搜索有数据时
  getSearchVal=(value)=>{
    const {dispatch} = this.props
    this.setState({
      ListStatus:true
    })
    let param={
      className:value,
      tenantId: localStorage.getItem('tenantId'),//平台标识
    }
    if (dispatch) {
      dispatch({
        type: 'BasicTreatments/findTreatClassSearchService',
        payload: param,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              MenuList:res.rows,
              ListStatus:false,
              firstData:res.rows.length>0&&res.rows[0].className?res.rows[0].className:"",
            })
            let arr=res.rows.length>0&&res.rows[0].children?res.rows[0].children[0].className:"";
            let arr1=res.rows.length>0&&res.rows[0].children?res.rows[0].children[0].classCode:"";
            this.state.secondData=arr;
            this.state.secondCode=arr1;
            if(this.state.show1==null&&this.state.show2==null){
              this.setState({
                show1:res.rows.length>0&&res.rows[0].id?res.rows[0].id:null,
                show2:res.rows.length>0&&res.rows[0].children?res.rows[0].children[0].id:null,
              })
              if(res.rows.length>0&&res.rows[0].children){
                this.state.getMsgListparams.classCode=res.rows[0].children[0].classCode;
                if(this.state.getMsgListparams.classCode){
                  this.MessageList()
                }
              }
            }else{
              let art=res.rows;
              let art1=[];
              art.forEach((text,index)=>{
                if(text.id==this.state.show1){
                  art1=text.children;
                  this.setState({
                    firstData:text.className
                  })
                }
              })
              if(art1 && art1.length>0){
                this.setState({
                  show2:art1[0].id,
                  secondData:art1[0].className,
                  secondCode:art1[0].classCode,
                })
                this.state.getMsgListparams.classCode=art1[0].classCode;
                this.MessageList()
                this.getclassType(art1[0].classCode)
              }else{
                this.setState({
                  messageListData:[],
                  totals:0,
                  firstData:"",
                  secondCode:"",
                  secondData:"",
                })
              }
            }
          }else{
            this.setState({
              ListStatus:false
            })
          }
        }
      });
    }
  }
  // 新增弹出框
  openAdd=(key,record)=>{
    this.setState({
      newFileStatus:true,
    })
    if(key=='editor'){
      this.setState({
        addParams:record
      })
    }
    if(key=='add'){
      this.setState({
        addParams:{
          status:0
        }
      })
    }
  }
  //新增/编辑治疗确定
  creatModel=()=>{
    const { dispatch } = this.props;
    const {addParams} = this.state;
    let params;
    if(addParams.id){
      params={
        id:addParams.id,
        tenantId: localStorage.getItem('tenantId'),//平台标识
        stdCode: addParams.stdCode,//治疗编码
        classCode: this.state.secondCode,//分类编码
        treatName: addParams.treatName,//治疗名称
        status: addParams.status,//状态：0正常，1停用,
        updateName: localStorage.getItem('userName'),//更新者
        updateId:localStorage.getItem('userId')//更新者id
      }
    }else{
      params = {
        tenantId: localStorage.getItem('tenantId'),//平台标识
        stdCode: addParams.stdCode,//治疗编码
        classCode: this.state.secondCode,//分类编码
        treatName: addParams.treatName,//治疗名称
        status: addParams.status,//状态：0正常，1停用,
        createName:localStorage.getItem('userName'),//创建者
        createId:localStorage.getItem('userId'),//创建者id
      }
    }
    if(params.stdCode && params.stdCode.length>32){
      message.warning({
        content: '编码最大长度为32',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if(!params.treatName){
      message.warning({
        content: '治疗名称不能为空',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if(params.treatName.length>100){
      message.warning({
        content: '治疗名称最大长度100个文字',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    this.setState({
      addLabelLoading:true
    })
    if (dispatch) {
      dispatch({
        type: 'BasicTreatments/saveTreatService',
        payload: params,
        callback: (res) => {
          if (res.code === 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              newFileStatus:false,
              addLabelLoading:false
            })
          this.state.getMsgListparams.classCode=params.classCode;
          this.MessageList()
          } else {
            message.error({
              content: res.msg,
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              addLabelLoading:false
            })
          }
        },
      });
    }
  }
  //新增/编辑治疗取消
  cancelCreatModel=()=>{
    this.setState({
      newFileStatus:false,
      addLabelLoading:false
    })
    // console.log(this.state.newFileStatus)
  }
  // 是否删除气泡
  sureDelete=(record)=>{
    const { dispatch } = this.props;
    let params = {
      id:record.id,
      tenantId: localStorage.getItem('tenantId'),//平台标识
    };
    this.setState({
      delete1Loading:true
    })
    if (dispatch) {
      dispatch({
        type: 'BasicTreatments/deleteTreatService',
        payload: params,
        callback: (res) => {
          if (res.code === 200) {
            message.success({
              content: '删除成功',
            });
            this.setState({
              delete1Loading:false
            })
            this.MessageList()
          } else {
            message.error({
              content: '删除失败',
            });
            this.setState({
              delete1Loading:false
            })
          }
        },
      });
    }
  }
  // 是否删除气泡关闭
  noDelete=()=>{
    this.setState({
      delete1Loading:false
    })
  }
  // 第一级鼠标hover菜单事件
  hoverFirst = (hoverFirst,id) => {
    this.setState({
      ['hoverFirst'+id]: hoverFirst
    })
  }
  // 第一级鼠标点击菜单事件
  openfirst=(item,index,id)=>{
    if(this.state.show1==id){
      this.setState({
        show1:null,
        show2:null,
        show3:null
      })
    }else{
      this.setState({
        show1:id
      })
    }
  }
  // 第二级鼠标hover菜单事件
  hoverTwo= (hoverTwo,id) => {
    this.setState({
      ['hoverTwo'+id]: hoverTwo
    })
  }
  // 第二级鼠标点击菜单事件
  opentwo=(item1,index1,id)=>{
    if(this.state.show2==id){
      this.setState({
        show2:null
      })
    }else{
      this.setState({
        show2:id
      })
    }
    this.getclassType(item1.classCode)
    this.state.getMsgListparams.classCode=item1.classCode;
    if(this.state.getMsgListparams.classCode){
      this.MessageList()
    }
  }
  //获取当前分类和上级分类
  getclassType(classCode){
    const { dispatch } = this.props;
    const {addParams} = this.state;
    let params = {
      classCode:classCode,//分类编码
      tenantId:localStorage.getItem('tenantId'),//租户标识
    }
    if (dispatch) {
      dispatch({
        type: 'BasicTreatments/getTreatClassesService',
        payload: params,
        callback: (res) => {
          if (res.code === 200) {
            this.setState({
              firstData:res.content.parentClassName,
              secondCode:res.content.classCode,
              secondData:res.content.className,
            })
          }
        },
      });
    }
  }
  //获取基础治疗列表
  MessageList=()=>{
    const {dispatch} = this.props
    const {getMsgListparams} = this.state;
    this.setState({
      TableLoading:true,
    })
    if (dispatch) {
      dispatch({
        type: 'BasicTreatments/findTreatsByClassService',
        payload: getMsgListparams,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              messageListData:res.rows,
              totals:res.total,
              TableLoading:false
            })
          }else{
            this.setState({
              TableLoading:false
            })
          }
        }
      });
    }
  }
  // 第三级鼠标hover菜单事件
  hoverThree= (hoverThree,id) => {
    this.setState({
      ['hoverThree'+id]: hoverThree
    })
  }
  //新建文件
  addfile=(e,id)=>{
    e.nativeEvent.stopImmediatePropagation()
    if(this.state.addFileBorderStatus2){
      this.setState({
        addFileBorderStatus2:false,
        addFileBorderStatus3:false,
        addFileBorderStatus4:false
      })
    }else{
      this.setState({
        hover1:id,
        addFileBorderStatus2:true,
        addFileBorderStatus3:false,
        addFileBorderStatus4:false
      })
    }

  }
  //新建模板
  addModel=(e,id)=>{
    e.nativeEvent.stopImmediatePropagation()
    if(this.state.addFileBorderStatus3){
      this.setState({
        addFileBorderStatus3:false,
        addFileBorderStatus2:false,
        addFileBorderStatus4:false,
      })
    }else{
      this.setState({
        hover2:id,
        addFileBorderStatus3:true,
        addFileBorderStatus2:false,
        addFileBorderStatus4:false,
      })
    }
  }
  //文件操作
  fileModel=(e,id)=>{
    e.nativeEvent.stopImmediatePropagation()
    if(this.state.addFileBorderStatus4){
      this.setState({
        addFileBorderStatus3:false,
        addFileBorderStatus2:false,
        addFileBorderStatus4:false,
      })
    }else{
      this.setState({
        hover3:id,
        addFileBorderStatus3:false,
        addFileBorderStatus2:false,
        addFileBorderStatus4:true,
      })
    }
  }
  //新建文件弹窗
  newFile=(item)=>{
    this.state.addFolder.classCode=item.classCode;//存第一级的classcode
    this.setState({
      addNewFolder:true,
      ['hoverFirst'+item.id]: false
    })
  }
  //新建文件夹
  newFileOk=()=>{
    const {dispatch} = this.props;
    const {addFolder}=this.state;
    let params = {
      className: addFolder.className,//分类夹名称
      parentCode: addFolder.classCode,//父级标识
      classRank: 2,//分类等级 1/2/3
      classType: 2,//模板类型-0系统分类；1病历模板；2基础治疗；3知情同意书
      tenantId:localStorage.getItem('tenantId'),//平台标识
      createName:localStorage.getItem('userName'),//创建者
      createId:localStorage.getItem('userId')//创建者id
    }
    if(!params.className){
      message.warning({
        content: '文件夹名称不能为空',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    this.setState({
      addLoading:true
    })
    if (dispatch) {
      dispatch({
        type: 'BasicTreatments/saveContentClassService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.state.addFolder.className="";
            this.setState({
              addNewFolder:false,
              addLoading:false
            })
            if(this.state.searchval){
              this.getSearchVal(this.state.searchval)
            }else{
              this.getMenuList()
            }
          }else{
            this.state.addFolder.className="";
            message.error({
              content: res.msg,
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              addLoading:false
            })
          }
        }
      });
    }
  }
  //新建文件夹取消
  newFileCancel=()=>{
    this.setState({
      addNewFolder:false,
      addLoading:false
    })
  }
  //删除文件
  showPopconfirm=(e)=>{
    e.nativeEvent.stopImmediatePropagation()
    this.setState({
      setVisible:true,
    })
  }
  //新建模板
  newModel=(id)=>{
    this.setState({
      newModel:true,
      ['hoverTwo'+id]: false
    })
  }
  //新建分类确定
  newModelOk=()=>{
    this.setState({
      newModel:false,
    })
  }
  //新建分类取消
  newModelCancel=()=>{
    this.setState({
      newModel:false,
    })
  }
  //修改模板名称
  editorFile=(fileName,item1)=>{
    if(fileName=='file'){
      this.setState({
        modelName:item1.className,
        modelid:item1.id,
        editorName:true,
        ['hoverTwo'+item1.id]: false,
        ['hoverThree'+item1.id]: false,
      })
    }
    if(fileName=='model'){
      this.setState({
        modelName:item1.className,
        modelid:item1.id,
        editorName:true,
        ['hoverTwo'+item1.id]: false,
        ['hoverThree'+item1.id]: false,
      })
    }
  }
  //编辑名称确定
  editorNameOk=()=>{
    const { dispatch } = this.props;
    let params =
      {
        id:this.state.modelid,
        className: this.state.modelName,//分类夹名称
        tenantId:localStorage.getItem('tenantId'),//平台标识
        updateName:localStorage.getItem('userName'),//更新者
        updateId:localStorage.getItem('userId')//更新者id
      }
    if(!params.className){
      message.warning({
        content: '文件夹名称不能为空',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    this.setState({
      editorLoading:true
    })
    if (dispatch) {
      dispatch({
        type: 'BasicTreatments/updateContentClassService',
        payload: params,
        callback: (res) => {
          if (res.code === 200) {
            message.success({
              content: '操作成功',
            });
            if(this.state.searchval){
              this.getSearchVal(this.state.searchval)
            }else{
              this.getMenuList()
            }
            this.setState({
              editorName:false,
              editorLoading:false
            })
          } else {
            this.setState({
              editorLoading:false
            })
            message.error({
              content: res.msg,
            });
          }
        },
      });
    }
  }
  //编辑名称取消
  editorNameCancel=()=>{
    this.setState({
      editorName:false,
      editorLoading:false
    })
  }
  //删除文件夹确定
  DeletehandleOk=(item1)=>{
    this.setState({
      setVisible:false,
      ['hoverFirst'+item1.id]: false,
      ['hoverTwo'+item1.id]: false,
      ['hoverThree'+item1.id]: false,
    })
    const { dispatch } = this.props;
    let params = {
      id:item1.id
    };
    this.setState({
      delete2Loading:true
    })
    if (dispatch) {
      dispatch({
        type: 'BasicTreatments/deleteContentClassService',
        payload: params,
        callback: (res) => {
          if (res.code === 200) {
            message.success({
              content: '删除成功',
            });
            if(this.state.searchval){
              this.getSearchVal(this.state.searchval)
            }else{
              this.getMenuList()
            }
            this.setState({
              // show1:null,
              // show2:null,
              delete2Loading:false
            })
          } else {
            message.error({
              content: '删除失败',
            });
            this.setState({
              delete2Loading:false
            })
          }
        },
      });
    }
  }
  //确定删除气泡取消
  DeletehandleCancel=(item)=>{
    this.setState({
      delete2Loading:false,
      setVisible:false,
      ['hoverFirst'+item.id]: false,
      ['hoverTwo'+item.fileid]: false,
      ['hoverThree'+item.contentid]: false,
    })
  }
  //状态启用停用事件
  onChangestatus=(e)=>{
    this.state.addParams.status=e.target.value;
  }
  //分页数据
  pageNumberOnChange = (pageNum,pageSize) => {
    this.state.getMsgListparams.pageNum=pageNum;
    this.state.getMsgListparams.pageSize=pageSize;
    this.MessageList()
  };
  render() {
    const {addParams,newFileStatus,addNewFolder,
      clientHeight,ListStatus,MenuList,addFolder,
      messageListData,TableLoading,getMsgListparams,
      totals,addLoading,addModelLoading,editorLoading,addLabelLoading,delete2Loading,delete1Loading
    }=this.state;
    //表格题目
    const columns = [
      {
        title: '编码',
        width:'30%',
        dataIndex: 'stdCode',
        key: 'stdCode',
      },
      {
        title: '治疗项名称',
        width:'30%',
        dataIndex: 'treatName',
        key: 'treatName',
      },
      {
        title: '状态',
        width:'20%',
        dataIndex: 'status',
        key: 'status',
        render: (text, record) => {
          let span = '';
          if (record.status === 0) {
            span = <span style={{ color: '#00B42A' }}>使用中</span>;
          } else if (record.status === 1) {
            span = <span style={{ color: 'rgba(0,0,0,0.45)' }}>已停用</span>;
          }
          return <span>{span}</span>;
        },
      },
      {
        width:'20%',
        title: '操作',
        key: 'action',
        render: (text, record) => (
          <Space size="middle">
            <a onClick={()=>{this.openAdd('editor',record)}}>编辑</a>
            <Popconfirm
              title="确定删除?"
              okButtonProps={{ loading: delete1Loading }}
              onConfirm={()=>{this.sureDelete(record)}}
              onCancel={()=>{this.noDelete}}
              okText="是"
              cancelText="否"
            >
              <a>删除</a>
            </Popconfirm>
          </Space>
        ),
      },
    ];
    return (
      <GridContent>
        <Row>
          <Col span={5}>
            <div className={styles.leftMenu} style={{height:clientHeight-25}}>
              <div className={styles.MenuTitle}>基础治疗</div>
              <div style={{margin:12}}>
                <Search
                  placeholder="搜索"
                  onSearch={this.onSearch}
                  style={{
                    width: '100%',
                  }}
                />
              </div>
              <div className={styles.modelContent}>
                <Spin spinning={ListStatus}>
                  {MenuList?
                  <>
                    {MenuList.length>0?
                    <>
                      {MenuList.map((item, index) => (
                        <div className={styles.menuFirst} key={index}>
                          <div
                            className={this.state['hoverFirst'+item.id]? (`${styles.pointer} ${styles.chooseBgcolor}`) :  (`${styles.pointer}`)}
                            style={{cursor:'pointer',marginTop:12,position:'relative'}}
                            onMouseEnter={()=>this.hoverFirst(true,item.id)}
                            onMouseLeave={()=>this.hoverFirst(false,item.id)}
                          >
                            <div
                              onClick={()=>this.openfirst(item,index,item.id)}
                              style={{display:'flex',width:'80%'}}>
                              {this.state.show1==item.id?
                                <img
                                  onMouseEnter={()=>this.hoverFirst(true,item.id)}
                                  onMouseLeave={()=>this.hoverFirst(false,item.id)}
                                  src={Unfold}
                                  className={styles.arrows} alt=""/>:
                                <img
                                  onMouseEnter={()=>this.hoverFirst(true,item.id)}
                                  onMouseLeave={()=>this.hoverFirst(false,item.id)}
                                  src={Fold}
                                  className={styles.arrows} alt=""/>
                              }
                              {this.state['hoverFirst'+item.id] ?
                                <img
                                  onMouseEnter={()=>this.hoverFirst(true,item.id)}
                                  onMouseLeave={()=>this.hoverFirst(false,item.id)}
                                  src={FolderSelected}
                                  className={styles.fileIcon} alt=""/> :
                                <img
                                  onMouseEnter={()=>this.hoverFirst(true,item.id)}
                                  onMouseLeave={()=>this.hoverFirst(false,item.id)}
                                  src={Folder}
                                  className={styles.fileIcon}
                                  alt=""/>
                              }
                              <div
                                className={this.state['hoverFirst'+item.id]?(`${styles.filetitle} ${styles.chooseFontcolor}`):(`${styles.filetitle}`)}
                                style={{marginLeft:8}}>{item.className}</div>
                            </div>
                            {this.state['hoverFirst'+item.id] ?
                              <div className={styles.addBtns} onClick={e=>this.addfile(e,item.id)}>...</div>:
                              ""
                            }
                            {this.state.addFileBorderStatus2 &&this.state.hover1==item.id ?
                              <div className={styles.addFile} style={{top:25,height:35}}>
                                <div
                                  onClick={()=>this.newFile(item)}
                                  className={styles.addFileLine}>
                                  <img src={NewFolder} className={styles.iconStyle} alt=""/>
                                  <span className={styles.addFileName}>新建文件夹</span>
                                </div>
                              </div>:
                              null}
                          </div>
                          {item.children?
                            <>
                              {item.children.map((item1,index1)=>(
                                <div style={{marginLeft:15}} key={index1} className={this.state.show1==item.id?(styles.show) : (styles.hidden)}>
                                  <div
                                    className={this.state['hoverTwo'+item1.id]? (`${styles.pointer} ${styles.chooseBgcolor}`) :  (`${styles.pointer}`)}
                                    style={{cursor:'pointer',marginTop:12}}
                                    onMouseEnter={()=>this.hoverTwo(true,item1.id)}
                                    onMouseLeave={()=>this.hoverTwo(false,item1.id)}
                                  >
                                    <div
                                      onClick={()=>this.opentwo(item1,index1,item1.id)}
                                      style={{display:'flex',width:'80%'}}>
                                      {/*{this.state.show2==item1.id?*/}
                                      {/*  <img src={Unfold} className={styles.arrows} alt=""/>:*/}
                                      {/*  <img src={Fold} className={styles.arrows} alt=""/>*/}
                                      {/*}*/}
                                      {this.state.show2==item1.id ||this.state['hoverTwo'+item1.id] ?
                                        <img
                                          onMouseEnter={()=>this.hoverTwo(true,item1.id)}
                                          onMouseLeave={()=>this.hoverTwo(false,item1.id)}
                                          src={FolderSelected}
                                          className={styles.fileIcon} alt=""/> :
                                        <img
                                          onMouseEnter={()=>this.hoverTwo(true,item1.id)}
                                          onMouseLeave={()=>this.hoverTwo(false,item1.id)}
                                          src={Folder}
                                          className={styles.fileIcon}
                                          alt=""/>
                                      }
                                      <div
                                        className={this.state.show2==item1.id || this.state['hoverTwo'+item1.id]?(`${styles.filetitle} ${styles.chooseFontcolor}`):(`${styles.filetitle}`)}
                                        style={{marginLeft:8}}>{item1.className}</div>
                                    </div>
                                    {this.state['hoverTwo'+item1.id] ?
                                      <div className={styles.addBtns} onClick={e=>this.addModel(e,item1.id)}>...</div>:
                                      ""
                                    }
                                    {this.state.addFileBorderStatus3 &&this.state.hover2==item1.id ?
                                      <div className={styles.addFile} style={{top:25,height:60}}>
                                        {/*<div*/}
                                        {/*  onClick={()=>this.newModel(item1.id)}*/}
                                        {/*  className={styles.addFileLine}>*/}
                                        {/*  <img src={NewFile} className={styles.iconStyle} alt=""/>*/}
                                        {/*  <span className={styles.addFileName}>新建模板</span>*/}
                                        {/*</div>*/}
                                        <div
                                          onClick={this.editorFile.bind(this,'file',item1)}
                                          className={styles.addFileLine}>
                                          <img src={Edit} className={styles.iconStyle} alt=""/>
                                          <span className={styles.addFileName}>编辑名称</span>
                                        </div>
                                        <Popconfirm
                                          title="确定删除？"
                                          visible={this.state.setVisible}
                                          okButtonProps={{ loading: delete2Loading }}
                                          onConfirm={this.DeletehandleOk.bind(this,item1)}
                                          // okButtonProps={{
                                          //   loading: confirmLoading,
                                          // }}
                                          onCancel={()=>this.DeletehandleCancel(item1)}
                                        >
                                          <div
                                            onClick={this.showPopconfirm}
                                            className={styles.addFileLine}>
                                            <img src={Delete} className={styles.iconStyle} alt=""/>
                                            <span className={styles.addFileName}>删除</span>
                                          </div>
                                        </Popconfirm>
                                      </div>:
                                      null}
                                  </div>
                                </div>
                              ))}
                            </>
                            :""}
                        </div>
                      ))}
                    </>:
                      <div className={commonStyle.nodataContent} style={{marginTop:'45%'}}>
                        <img src={noData} className={commonStyle.imgStyle} alt=""/>
                        <div className={commonStyle.fontStyle}>暂无数据</div>
                      </div>
                    }

                  </>:""
                  }

                </Spin>
              </div>
            </div>
          </Col>
          <Col span={19}>
            <div className={styles.Rightcontent}>
              <Button
                disabled={!this.state.firstData || !this.state.secondCode}
                type="primary"
                onClick={()=>this.openAdd('add')}
                className={styles.btnstyle}>新增</Button>
              <Table
                rowKey={messageListData => messageListData.id}
                columns={columns}
                dataSource={messageListData}
                pagination={false}
                loading={TableLoading}
                style={{ margin: '16px' }}
              />
              <Pagination
                style={{ float: 'right' }}
                total={totals}
                showTotal={(totals) => `共 ${totals} 条记录`}
                defaultPageSize={getMsgListparams.pageSize}
                defaultCurrent={getMsgListparams.pageNum}
                onChange={(pageNum,pageSize) => this.pageNumberOnChange(pageNum,pageSize)}
              />
            </div>
          </Col>
        </Row>
        {/*新增/编辑治疗*/}
        <Modal
          title="新增/编辑治疗"
          visible={newFileStatus}
          destroyOnClose={true}
          onOk={this.creatModel}
          onCancel={this.cancelCreatModel}
          okText="保存"
          cancelText="取消"
          width={660}
          maskClosable={false}
          confirmLoading={addLabelLoading}
        >
          <Form
            name="basic"
            labelCol={{
              span: 3,
            }}
            wrapperCol={{
              span: 20,
            }}
            initialValues={{
              remember: true,
              status:addParams.status,
              stdCode:addParams.stdCode,
              treatName:addParams.treatName
            }}
            autoComplete="off"
          >
            <Form.Item
              label="一级学科"
              name="type1"
            >
              <span>{this.state.firstData}</span>
            </Form.Item>
            <Form.Item
              label="二级分类"
              name="type2"
            >
              <span>{this.state.secondData}</span>
            </Form.Item>
            <Form.Item
              label="治疗编码"
              name="stdCode"
              rules={[{
                  max:32,
                  message: '最多可输入32个文字'
                }
              ]}
            >
              <Input
                onChange={e=>{addParams.stdCode = e.target.value}}
                placeholder="请输入治疗编码"/>
            </Form.Item>
            <Form.Item
              label="治疗名称"
              name="treatName"
              rules={[
                {
                  required: true,
                  message: '请输入治疗名称'
                },{
                  max:100,
                  message: '最多可输入100个文字'
                }
              ]}
            >
              <Input
                onChange={e=>{addParams.treatName = e.target.value}}
                placeholder="请输入治疗名称"/>
            </Form.Item>
            <Form.Item
              label="状态"
              name="status">
              <Radio.Group onChange={this.onChangestatus}>
                <Radio value={0}>启用</Radio>
                <Radio value={1}>停用</Radio>
              </Radio.Group>
            </Form.Item>
          </Form>
        </Modal>
        {/*新建文件夹*/}
        <Modal
          title="新建文件夹"
          visible={addNewFolder}
          destroyOnClose={true}
          onOk={this.newFileOk}
          onCancel={this.newFileCancel}
          okText="创建"
          cancelText="取消"
          width={660}
          maskClosable={false}
          confirmLoading={addLoading}
        >
          <TextArea
            showCount
            maxLength={100}
            autoSize={{minRows: 1, maxRows: 10}}
            onChange={e=>{addFolder.className=e.target.value}}
            placeholder="请输入文件夹名称"/>
        </Modal>
        {/*新建模板文件*/}
        <Modal
          title="新建分类"
          visible={this.state.newModel}
          destroyOnClose={true}
          onOk={this.newModelOk}
          onCancel={this.newModelCancel}
          okText="创建"
          cancelText="取消"
          width={660}
          maskClosable={false}
          confirmLoading={addModelLoading}
        >
          <TextArea
            showCount
            maxLength={100}
            autoSize={{minRows: 1, maxRows: 10}}
            placeholder="请输入分类名称"/>
        </Modal>
        {/*编辑名称*/}
        <Modal
          title="编辑名称"
          visible={this.state.editorName}
          destroyOnClose={true}
          onOk={this.editorNameOk}
          onCancel={this.editorNameCancel}
          okText="确定"
          cancelText="取消"
          width={660}
          maskClosable={false}
          confirmLoading={editorLoading}
        >
          <TextArea
            showCount
            maxLength={100}
            onChange={e=>{this.state.modelName=e.target.value}}
            defaultValue={this.state.modelName}
            autoSize={{minRows: 1, maxRows: 10}}
            placeholder="请输入文件夹名称"/>
        </Modal>
      </GridContent>
    );
  }
}

export default connect(({ BasicTreatmentData, }) => ({
  BasicTreatmentData
}))(BasicTreatment);
