@import '~@/utils/utils.less';
.patientEventicon {
  //float: right;
  float: left;
  margin-right: 5px;
  // margin-top: 2px;
  width: 18px;
  height: 18px;
}

.Eventicon {
  float: left;
  margin-right: 1px;
}

.patientEventiconText {
  color: #C82AE5;
  font-size: 12px;
}

.huizhenText {
  //float: right;
  margin-right: 8px;
  font-size:16px;
  font-family:'MicrosoftYaHei-Bold';
  font-weight:bold;
  color:rgba(200,42,229,1);
}

.Diamonds {
  margin-top: -3px;
}

.referralIcon {
  width:17px;
  height:18px;
}


.eventElent {
  font-size: 14px;
  // height: auto;
  color: #444444;
  padding-left: 3px;
  position: absolute;
  width:99%;
  height: 100%;
  top: 50%;
  transform: translate(0, -50%);
  box-sizing: border-box;
  margin-top: 7px;
  overflow: hidden;
  p {
    margin-top: 10px;
    margin-bottom: 5px;
    line-height: 19px;
  }
}

.eventElent div {
  //height: auto!important;
}

.eventElentOther {
  font-size: 14px;
  font-weight: 700;
  color: #fff;
  font-family: 'MicrosoftYaHei';
  //padding-left: 8%;
  position: absolute;
  width:99%;
  top: 50%;
  overflow: hidden;
  transform: translate(0, -50%);
  text-align: center;
  line-height: 60px;
  p {
    margin-top: 5px;
    margin-bottom: 5px;
    line-height: 19px;
  }
}

.age {
  margin-left: 8px;
}


.dingwei5 {
  width: 18px;
  height: 18px;
  border: 0;
  background: url("../../../assets/imgqq.png") no-repeat;
  background-size: 90px 18px;
  background-position: -18px 0;
  margin-top: 1px;
}

.jichu {
  width: 25px;
  height: 25px;
  background: url("../../../assets/calendar/event/jichu.png") no-repeat;
  background-size: 25px 25px;
  display: inline-block;
  position: absolute;
  top:0px;
}

.zhongda {
  width: 25px;
  height: 25px;
  background: url("../../../assets/calendar/event/zhongda.png") no-repeat;
  background-size: 25px 25px;
  display: inline-block;
  position: absolute;
  top:0px;
}

/* 预约确认 */
.appmentEnter {
  width: 27px;
  height: 27px;
  display: inline-block;
  background: url('../../../assets/calendar/event/appmentEnter.png') no-repeat;
  background-size: 27px 27px;
  display: inline-block;
  position: absolute;
  bottom:0px;
  right: 0px;
}

/*治疗项目标签*/

.EventTypeSpan{
  margin-top: 6px;
  margin-left: 9px;
}

.EventTypeIcon {
  :global {
    i {
      // vertical-align: text-bottom;
      // margin-left: 2px;
      // margin-right: 2px;
    }
  }
}

.cureProjectIcon i {
  vertical-align: text-bottom;
  margin-left: 2px;
  margin-right: 2px;
}

.daiguan {
  width: 12px;
  height: 16px;
  background: url('../../../assets/calendar/event/daiguan.png') no-repeat;
  background-size: 12px 16px;
  display: inline-block;
}


.meibai {
  width: 12px;
  height: 16px;
  background: url('../../../assets/calendar/event/meibai.png') no-repeat;
  background-size: 12px 16px;
  display: inline-block;
}

.yachixiufu {
  width: 12px;
  height: 16px;
  background: url('../../../assets/calendar/event/yachixiufu.png') no-repeat;
  background-size: 12px 16px;
  display: inline-block;
}

.yagen {
  width: 12px;
  height: 16px;
  background: url('../../../assets/calendar/event/yagen.png') no-repeat;
  background-size: 12px 16px;
  display: inline-block;
}

.zhengji {
  width: 21px;
  height: 13px;
  background: url('../../../assets/calendar/event/zhengji.png') no-repeat;
  background-size: 21px 12px;
  display: inline-block;
}

.zhongzhi {
  width: 12px;
  height: 16px;
  background: url('../../../assets/calendar/event/zhongzhi.png') no-repeat;
  background-size: 12px 16px;
  display: inline-block;
}

.zhaobaijin {
  width: 26px;
  height: 10px;
  display: inline-block;
  background: url('../../../assets/calendar/event/zhaobajin.png') no-repeat;
  background-size: 26px 10px;
}

.vipicon {
  width: 18px;
  height: 18px;
  display: inline-block;
  background: url('../../../assets/calendar/event/vipicon.png') no-repeat;
  background-size: 18px 18px;
  margin-top: 2px;
  //float: left;
}

.MSH {
  width: 26px;
  height: 9px;
  display: inline-block;
  background: url('../../../assets/calendar/event/MSH.png') no-repeat;
  background-size: 26px 9px;
}

.LAB {
  width: 22px;
  height: 9px;
  display: inline-block;
  background: url('../../../assets/calendar/event/LAB.png') no-repeat;
  background-size: 22px 9px;
}

.jizhen {
  width: 20px;
  height: 10px;
  display: inline-block;
  background: url('../../../assets/calendar/event/jizhen.png') no-repeat;
  background-size: 20px 10px;
}


.patientName{
  margin-right: 5px;
  //width:60%;
  float: left;
  font-weight: 700;
  font-size: 15px;
  position: relative;
  z-index: 99;
  position: relative;
}

.PatienticonRigth {
  float: right;
}

.patienBox {
  .clearfix()
}

.evnetOffTextColor {
  color: #444;
}

.stayToDirectorWarp {
  display: inline-block;
  width: 48px;
  height: 20px;
  border-radius: 48px;
  background: #FC9E2E;
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  text-align: center;
  padding-top: 1px;
}

.alreadyToDirectorWarp {
  display: inline-block;
  width: 48px;
  height: 20px;
  border-radius: 48px;
  background: #31CB21;
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  text-align: center;
  padding-top: 1px;
}


.ShutDownBtn {
  background: red;
  border-radius: 50%;
  opacity: 1;
  width: 20px;
  height: 20px;
  background: url("../../../assets/AppointmentRules/TemporaryStorageEvent_ShutDownBtn.png");
  background-color: #FFFFFF;
  background-size: 20px 20px;
  border-radius: 50%;
  opacity: 1;
  position: absolute;
  top: -6px;
  right: -8px;
  cursor: pointer;
  z-index: 10;
}

.waitByElement {

}

.boxWaitWarp {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border: 1px solid #FF9A5F;
  background: rgba(255, 156, 94, 0.3);
  border-radius: 8px;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.stateByWait {
  height: 20px;
  line-height: 20px;
  background: #FF9A5F;
  border-bottom-left-radius: 4px;
  color: #FFFFFF;
  text-align: center;
  padding-left: 5px;
  padding-right: 3px;
  font-size: 12px;
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.stateByWaitIcon {
  width: 14px;
  height: 14px;
  background: url('../../../assets/calendar/event/waitIcon.png') no-repeat;
  background-size: 14px 14px;
  display: inline-block;
  margin-right: 2px;
}
.TimeByText {
  align-items: center;
  display: inline-block;
  height: 20px;
  line-height: 20px;
  color: #FF9A5F;
  z-index: 20;
  font-size: 14px;
  font-weight: 500;
}


.timeoutBorder {
  border: 1px solid #FF2E2E;
}
.timeoutBackground {
  background: #FF2E2E;
}
.timeoutBackgroundTransparency {
  background: rgba(255, 46, 46, 0.2);
}
.timeoutColor {
  color: #FF2E2E;
}

.warp_element_notName {
  opacity: 0.1;
}
.warp_element_notName_none {
  display: none;
}

.isHiddenElement {
  display: none;
}

