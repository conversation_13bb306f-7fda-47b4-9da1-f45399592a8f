import React, {Component} from 'react';
import * as echarts from "echarts";
import Immutable from 'immutable';

// eslint-disable-next-line @typescript-eslint/no-unused-vars

class Index extends Component {
  constructor(props) {
    super(props);
    this.state={
      data:[],
      yAxisData:[],
      type:'',
    }
    this.myChart = null;
    this.myRef = React.createRef()
  }

  static getDerivedStateFromProps( props,state ) {
    if(!Immutable.is(Immutable.fromJS(props.data), Immutable.fromJS(state.data)) || !Immutable.is(Immutable.fromJS(props.yAxisData), Immutable.fromJS(state.yAxisData))){
      return {
        data:props.data,
        yAxisData:props.yAxisData,
        type:'1'
      }
    }
    return null
  }

  componentDidMount() {
    const { data,yAxisData } = this.state
    const { color=['#FCDE1C','#74AAFB','#FF8398',] } = this.props
    const chartDom = this.myRef.current ;
    this.myChart=echarts.init(chartDom)
    const option = {
            grid:{
              show:true,
              top:0,
              bottom:0,
              left:20,
              right:20,
              containLabel: true,
            },
            xAxis: {
              type: 'value',
              boundaryGap: [0, 0.2],
              position:'top',
              axisLine: {
                show:false,
              },
            },
            yAxis: {
              type: 'category',
              data:yAxisData,
              axisTick: {
                show: false,
              },
              axisLine: {
                show:false,
              },
              splitLine : {
                lineStyle: {
                  type: 'dashed'
                },
              }
            },
            series: [
              {
                type: 'bar',
                barWidth:8,
                data,
                itemStyle: {
                  normal: {
                    color: (params) =>color[params.dataIndex]
                  }
                },
                label: {
                  normal: {
                    show: true,
                    position: 'right',
                    formatter: '{c}%'
                  }
                }
              },
            ]
      };
    // eslint-disable-next-line no-unused-expressions
    option && this.myChart.setOption(option);
    window.addEventListener("resize",  () =>{
      this.myChart.resize();
    });
  }

  componentDidUpdate() {
    const { data,type,yAxisData } = this.state
    if(type==='1'){
      this.setState({
        type:''
      })
      this.myChart.setOption({
        yAxis: {
          data:yAxisData,
          },
        series: {
          data,
        }
      })
    }
  }

  render() {
    const { width='100%' ,height= 300 } = this.props
    return (
      <div>
        <div ref={this.myRef} style={{ width, height,minWidth:310}} />
      </div>
    );
  }
}

export default Index;
