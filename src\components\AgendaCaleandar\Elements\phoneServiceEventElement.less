@import '~@/utils/utils.less';
.patientEventicon {
  float: right;
  margin-right: 1px;
  width: 18px;
  height: 18px;
}

.Diamonds {
  margin-top: -3px;
}

.referralIcon {
  width:17px;
  height:18px;
}


.eventElent {
  font-size: 14px;
  color: #444444;
  font-family: 'MicrosoftYaHei';
  padding-left: 8%;
  position: absolute;
  width:98%;
  top: 50%;
  transform: translate(0, -50%);
  box-sizing: border-box;
  p {
    margin-top: 5px;
    margin-bottom: 5px;
    line-height: 19px;
  }
}

.eventElentOther {
  font-size: 14px;
  font-weight: 700;
  color: #fff;
  text-align: center;
  font-family: 'MicrosoftYaHei';
  //padding-left: 8%;
  position: absolute;
  width:99%;
  top: 50%;
  overflow: hidden;
  transform: translate(0, -50%);

  p {
    margin-top: 5px;
    margin-bottom: 5px;
    line-height: 19px;
  }
}

.age {
  margin-left: 8px;
}


.dingwei5 {
  width: 18px;
  height: 18px;
  border: 0;
  background: url("../../../assets/imgqq.png") no-repeat;
  background-size: 90px 18px;
  background-position: -18px 0;
  margin-top: 1px;
}

.jichu {
  width: 35px;
  height: 35px;
  background: url("../../../assets/calendar/event/jichu.png") no-repeat;
  background-size: 35px 35px;
  display: inline-block;
  position: absolute;
  top:0px;
}

.zhongda {
  width: 35px;
  height: 35px;
  background: url("../../../assets/calendar/event/zhongda.png") no-repeat;
  background-size: 35px 35px;
  display: inline-block;
  position: absolute;
  top:0px;
}

/*治疗项目标签*/

.cureProjectIcon {
  width:98%;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}

.cureProjectIcon i {
  vertical-align: text-bottom;
  margin-left: 2px;
  margin-right: 2px;
}

.daiguan {
  width: 16px;
  height: 20px;
  background: url('../../../assets/calendar/event/daiguan.png') no-repeat;
  background-size: 16px 20px;
  display: inline-block;
}


.meibai {
  width: 17px;
  height: 22px;
  background: url('../../../assets/calendar/event/meibai.png') no-repeat;
  background-size: 17px 22px;
  display: inline-block;
}

.yachixiufu {
  width: 16px;
  height: 20px;
  background: url('../../../assets/calendar/event/yachixiufu.png') no-repeat;
  background-size: 16px 20px;
  display: inline-block;
}

.yagen {
  width: 16px;
  height: 20px;
  background: url('../../../assets/calendar/event/yagen.png') no-repeat;
  background-size: 16px 20px;
  display: inline-block;
}

.zhengji {
  width: 24px;
  height: 14px;
  background: url('../../../assets/calendar/event/zhengji.png') no-repeat;
  background-size: 24px 14px;
  display: inline-block;
}

.zhiding {
  width: 14px;
  height: 14px;
  background: url('../../../assets/tuli/A88.png') no-repeat;
  background-size: 14px 14px;
  display: inline-block;
}

.zhongzhi {
  width: 16px;
  height: 20px;
  background: url('../../../assets/calendar/event/zhongzhi.png') no-repeat;
  background-size: 16px 20px;
  display: inline-block;
}

.zhaobaijin {
  width: 26px;
  height: 10px;
  display: inline-block;
  background: url('../../../assets/calendar/event/zhaobajin.png') no-repeat;
  background-size: 26px 10px;
}

.vipicon {
  width: 12px;
  height: 12px;
  display: inline-block;
  background: url('../../../assets/calendar/event/vipicon.png') no-repeat;
  background-size: 12px 12px;
  float: left;
}

.MSH {
  width: 26px;
  height: 9px;
  display: inline-block;
  background: url('../../../assets/calendar/event/MSH.png') no-repeat;
  background-size: 26px 9px;
}

.LAB {
  width: 22px;
  height: 9px;
  display: inline-block;
  background: url('../../../assets/calendar/event/LAB.png') no-repeat;
  background-size: 22px 9px;
}

.jizhen {
  width: 20px;
  height: 10px;
  display: inline-block;
  background: url('../../../assets/calendar/event/jizhen.png') no-repeat;
  background-size: 20px 10px;
}

.closeWarp {
  width: 22px;
  height: 22px;
  float: right;
  margin-right: 0%;
  margin-top: 1%;
  position: relative;
  z-index: 1;
}

.closeIcon {
  width: 15px;
  height: 15px;
  display: inline-block;
  background: url('../../../assets/calendar/event/close.png') no-repeat;
  background-size: 15px 15px;
}


.nameBox {
  float: left;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}

.patientName{
  margin-right: 5px;
  width: 59%;
  float: left;
  font-weight: 700;
  font-size: 15px;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}

.patientNameWidth1 {
  width: 50%;
}
.patientNameWidth2 {
  width: 41%;
}

.patientNameWidth3 {
  width: 40%;
}
.patientNameWidth4 {
  width: 35%;
}

.remark {
  width:98%;
  /*overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;*/
}

.complaintList {
  width:98%;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}

.PatienticonRigth {
  float: right;
}

.patienBox {
  .clearfix()
}

.patienBoxMarginleft {
  margin-left: 8px;
}


.huizhenText {
  float: right;
  margin-right: 8px;
  font-size:16px;
  font-family:'MicrosoftYaHei-Bold';
  font-weight:bold;
  color:rgba(200,42,229,1);
}

.evnetOffTextColor {
  color: #444;
}

/* 预约确认 */
.appmentEnter {
  width: 27px;
  height: 27px;
  display: inline-block;
  background: url('../../../assets/calendar/event/appmentEnter.png') no-repeat;
  background-size: 27px 27px;
  display: inline-block;
  position: absolute;
  bottom:0px;
  right: 0px;
}

.stayToDirectorWarp {
  display: inline-block;
  width: 48px;
  height: 18px;
  border-radius: 48px;
  background: #FC9E2E;
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  text-align: center;
  margin-right: 2px;
  margin-top: 1px;
}

.alreadyToDirectorWarp {
  display: inline-block;
  width: 48px;
  height: 18px;
  border-radius: 48px;
  background: #31CB21;
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  text-align: center;
  margin-right: 2px;
  margin-top: 1px;
}

