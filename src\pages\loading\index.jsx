import { Spin, Space } from 'antd';
import React, { Component } from 'react';
import styles from './style.less';
import { GridContent } from '@ant-design/pro-layout';
import { connect } from 'dva';
import { getArrailUrl } from '@/utils/arrailUrl';
import { history } from 'umi';

//中间页
class Loading extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }
  //初始化
  componentDidMount() {
    window.addEventListener('message', this.receiveMessage,false)
    // simulate()
  }
  simulate() {
    localStorage.setItem('username', 'rc2008190009/3a9d9c680c364df9a84bfd5de91e9324');
    localStorage.setItem('tenantId', '3a9d9c680c364df9a84bfd5de91e9324');
    localStorage.setItem('organizationId', 'e88824a05f7a48029233821cf231b88c');
    localStorage.setItem('organizationName', '刘医生演示诊所');
    localStorage.setItem('userName', '岳丽丽');
    localStorage.setItem('userId', 3883);
    localStorage.setItem('access_token', 'wpGtetBxLcu9XLPYwKKPqF135nQ');

    // localStorage.setItem('username', 'rc20220629001/161d09c531cbb2c481dd1ceea7493e35');
    // localStorage.setItem('tenantId', '161d09c531cbb2c481dd1ceea7493e35');
    // localStorage.setItem('organizationId', '77cb976a5ca344168c8e582911c82746');
    // localStorage.setItem('organizationName', '贝瑞口腔门诊部');
    // localStorage.setItem('userName', 'uat用户');
    // localStorage.setItem('userId', 4276);
    // localStorage.setItem('access_token', 'd0ed7400-e191-4f84-9fff-36283a657f3b');
    history.push('/emr/ArriveToday');
  }
  //postMessage接收数据
  receiveMessage = (e) => {
    // console.log("接收到的消息===",JSON.stringify(e))
    if (e.data) {
      // console.log("接收到的消息===e.data",JSON.stringify(e.data))
      let loginInfo = {
        emrMenuPath: e.data.emrMenuPath,
        id: e.data.id,
        organizationlnfo: e.data.organizationlnfo,
        roleCodes: e.data.roleCodes,
        tenantId: e.data.tenantId,
        userName: e.data.userName,
      };
      let userRoleList = JSON.parse(e.data.roleCodes);
      let roleCodes = [];
      for (let i = 0; i < userRoleList.length; i++) {
        roleCodes.push(userRoleList[i].roleCode);
      }
      localStorage.setItem('access_token', e.data.access_token);
      localStorage.setItem('emrMenuPath', e.data.emrMenuPath);
      localStorage.setItem('userId', e.data.id);
      localStorage.setItem('organizationlnfo', e.data.organizationlnfo);
      localStorage.setItem('organizationId', JSON.parse(e.data.organizationlnfo).id);
      localStorage.setItem('organizationName', JSON.parse(e.data.organizationlnfo).organizationName);
      // console.log("organizationIdorganizationId===",JSON.parse(e.data.organizationlnfo).id)
      // console.log("organizationName===",JSON.parse(e.data.organizationlnfo).organizationName)
      localStorage.setItem('tenantId', e.data.tenantId);
      localStorage.setItem('userName', e.data.userName);
      localStorage.setItem('accountNumber', e.data.accountNumber);
      localStorage.setItem('token_userName', e.data.token_userName);
      localStorage.setItem('username', e.data.token_userName+'/'+e.data.tenantId);
      localStorage.setItem('loginInfo', JSON.stringify(loginInfo));
      localStorage.setItem('loadingInfo', JSON.stringify(e.data));
      let openPage = localStorage.getItem('openPage');
      let newPage = localStorage.getItem('newPage');
      if (openPage == null) {
        localStorage.setItem('openPage', 'true');
      } else {
      }
      if (newPage == null) {
        localStorage.setItem('newPage', 'false');
      } else {
      }

      localStorage.setItem('roleCodes', roleCodes);
      // if(JSON.parse(localStorage.getItem('openPage'))){
      //   localStorage.setItem("openPage",'true')
      // }else{
      //   localStorage.setItem("openPage","false")
      // }
      // if(JSON.parse(localStorage.getItem('newPage'))){
      //   localStorage.setItem("newPage",'true')
      // }else{
      //   localStorage.setItem("newPage","false")
      // }
      window.location.href = e.data.emrMenuPath;
      let isLogin = e.data.access_token;
      if (!isLogin) {
        let LoginData = {
          emrMenuPath: '/user/login', // 需要跳转的地址
          funType: 3,
          newPage: JSON.parse(localStorage.getItem('newPage')),
        };
        return window.parent.postMessage(LoginData, getArrailUrl());
      }
    }
  };

  render() {
    return (
      <GridContent className={styles.loadContent}>
        {JSON.parse(localStorage.getItem('loadingInfo')) ? null : (
          <Space size="middle">
            <Spin size="large" />
          </Space>
        )}
      </GridContent>
    );
  }
}

/*与models里的namespace: 'SystemLayout'和initData数据相连接*/
export default connect(({ QualityCenter, loading }) => ({
  QualityCenter,
  loading: loading.models.QualityCenter,
}))(Loading);
