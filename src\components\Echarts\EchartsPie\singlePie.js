import React, {Component} from 'react';
import * as echarts from "echarts";
import Immutable from 'immutable';
import styles from './singlePie.less'
// eslint-disable-next-line @typescript-eslint/no-unused-vars

class Index extends Component {
  constructor(props) {
    super(props);
    this.state={
      data:[],         // 图例数据
      pieText:'',      // 图列title
      type:null,       // 判断数据是否改变更新渲染图例
    }
    this.myChart = null;
    this.myRefPie = React.createRef()
  }

  static getDerivedStateFromProps( props,state ) {
    if(!Immutable.is(Immutable.fromJS(props.data), Immutable.fromJS(state.data)) || !Immutable.is(Immutable.fromJS(props.pieText), Immutable.fromJS(state.pieText))){
      return {
        data:props.data,
        pieText:props.pieText,
        type:'1'
      }
    }
    return null
  }

  componentDidMount() {
    const { data,pieText } = this.state
    const { color=['#C189FF','#EDF2F9','#F0F0F0'] ,radius=['90%', '60%'],pieType,EventClickPie} = this.props
    const chartDom = this.myRefPie.current ;
    this.myChart=echarts.init(chartDom)
    const option = {
      tooltip: {
        trigger: 'item',
        show:pieType || false,
        position: ['50%','50%']
      },
      title:{
        show: true,
        text:pieText,
        top: 'center',
        left: 'center',
        triggerEvent:true,
        textStyle:{
          width: 40,
          overflow: 'breakAll',
          fontSize:12,
        },
      },
      legend: {
        show: false,
      },
      series:[
        {
          type:'pie',
          radius,
          avoidLabelOverlap: false,
          silent:!pieType,
          label:pieType?{
            show:true,
            position: 'inner',
          }:{
            show: false,
            position: 'center'
          },
          emphasis: {
            scale:false,
          },
          legendHoverLink:false,
          labelLine: {
            show: false
          },
          data:data && !pieType && data.findIndex(items=>items>0)>-1?data:pieType?data:[0],
          itemStyle:pieType?null:{
            normal: {
              color:data && data.findIndex(items=> items.value>0 || items>0)>-1?(params) =>color[params.dataIndex]:'#F0F0F0'
            }
          },
        },
      ]
    };
    this.myChart.clear();
    option && this.myChart.setOption(option);
    this.myChart.on('click', (params)=> {
      EventClickPie && EventClickPie()
    });
  }

  componentDidUpdate() {
    if(this.state.type==='1'){
      const { data,pieText } = this.state
      const { color=['#C189FF','#EDF2F9','#F0F0F0'],pieType} = this.props
      this.setState({
        type:null
      })
      this.myChart.setOption({
          series:{
            data:data && !pieType && data.findIndex(items=>items>0)>-1?data:pieType?data:[0],
            itemStyle:pieType?null:{
              normal: {
                color:data && data.findIndex(items=> items.value>0 || items>0)>-1?(params) =>color[params.dataIndex]:'#F0F0F0'
              }
            },
          },
          title:{
            text:pieText
          }
        })
    }
  }

  render() {
    const { width= 300,height= 300, stylesType} = this.props
    return (
      <div style={{width: width, height: height}} className={stylesType?styles.singlePieBox:null}>
        <div ref={this.myRefPie} style={{width: width, height: height}} />
      </div>

    );
  }
}

export default Index;
