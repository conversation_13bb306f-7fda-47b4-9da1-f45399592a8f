import { connect } from 'dva';

const { GridContent } = require('@ant-design/pro-layout');
import React, { Component, useState } from 'react';
import { Row, Col, Spin } from 'antd';
import ImageCard from '@/components/ImageCard';//卡片信息
import styles from './style.less';//引入样式
import commonStyle from '@/components/common.less';//公共样式
// 引入图片
import upArrow from '@/assets/<EMAIL>';
import downArrow from '@/assets/downArrow.png';
import noData from '@/assets/<EMAIL>';
import {StringUtils} from "@/utils/StringUtils";//公共验证

const imgByClass = [{ id: 1 }, { id: 2 }, { id: 3 }];
class TypeRecord extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isShow: 'up', // 显示隐藏小箭头
      key: '1', //要隐藏的key
      newImgByClass: [], // 新的数据列表
      emrNextStatus: false, //分页加载数据
      emrLoading: false,  //分页加载数据
      patientData:props.patientData,//患者详情
      imageListParams: {
        tenantId: localStorage.getItem('tenantId'),//平台标识
        emrId: props.patientData?props.patientData.fileNumber:null,//大病历号
        pageNum: 1,//页数
        pageSize: 10,//每页数量
      },
      imgByClassCount: 0,//影像资料总数量
    };
  }
 // 初始化
  componentDidMount() {
    this.props.onRef(this);
    if(this.state.patientData){
      this.getCheckImgByClass(1);// 请求影像资料--按分类
    }
  }

  // 监听患者信息数据变化
  componentWillReceiveProps(nextProps) {
    if (nextProps.patientData) {
      if (!this.state.patientData || nextProps.patientData.patientId != this.state.patientData.patientId) {
        // 页面中的操作都初始化一下
        this.setState(  {
          isShow: 'up', // 显示隐藏小箭头
          key: '1', //要隐藏的key
          newImgByClass: [], // 新的数据列表
          emrNextStatus: false, //分页加载数据
          emrLoading: false,  //分页加载数据
          imageListParams: {
            tenantId: localStorage.getItem('tenantId'),//平台标识
            emrId: nextProps.patientData?nextProps.patientData.fileNumber:null,//大病历号
            pageNum: 1,//页数
            pageSize: 10,//每页数量
          },
          imgByClassCount: 0,//影像资料总数量
        },()=>this.getCheckImgByClass(1));
      }
    }
  }

  // 请求影像资料--按分类
  getCheckImgByClass = (pageNum) => {
    const { dispatch } = this.props;
    const { imageListParams } = this.state;

    if (pageNum) {
      imageListParams.pageNum = pageNum;
    }

    if(StringUtils.isBlank(imageListParams.emrId)){
      this.setState({
        newImgByClass: [],
        imgByClassCount: 0,//影像资料总数量
      });
      return false;
    }
    this.setState({
      emrLoading: true,
    });

    if (dispatch) {
      dispatch({
        type: 'homePage/checkImgByClass',
        payload: imageListParams,
        callback: (res) => {
          if (res.code == 200) {
            if (!res.rows || res.rows.length === 0) {
              this.setState({
                emrNextStatus: false,
                emrLoading: false,
                newImgByClass:[],
                imgByClassCount:0
              });
              return;
            }
            let arr;

            (res.rows || []).map((item) => {
              item.isShow = 'up';
            });
            if (imageListParams.pageNum == 1) {
              arr = res.rows;
            } else {
              arr = [...this.state.newImgByClass, ...res.rows];
            }
            this.setState({
              emrLoading: false,
              newImgByClass: arr,
              imgByClassCount: res.total,
            });
          }
          this.setState({
            emrLoading: false,
          });
        },
      });
    }
  };

  // 加载更多
  emrNext = () => {
    this.setState({
      emrLoading: true,
    });
    const { imageListParams, imgByClassCount } = this.state;
    if (imageListParams.pageNum <= imgByClassCount / imageListParams.pageSize) {
      imageListParams.pageNum++;
      this.getCheckImgByClass();
    }
  };
// 箭头点击事件
  isShow = (key, value) => {
    const { newImgByClass } = this.state;
    this.setState({
      key: key,
      newImgByClass: newImgByClass.map((item, index) =>
        index == key ? { ...item, isShow: value } : item,
      ),
    });
  };
  render() {
    const { isShow, key, emrLoading, newImgByClass, imageListParams, imgByClassCount, emrNextStatus } = this.state;

    imgByClass &&
    imgByClass.map((item) => {
      item.isShow = 'up';
    });
    return (
      <GridContent>
        <div>
          {newImgByClass.length > 0 ? (
            <>{ newImgByClass.map((item, index) => (
                <div key={index}>
                  <Row>
                    <Row className={`${styles.width100}`} key="1">
                      <Col span={20} className={styles.displayFlex}>
                        <h3 style={{ fontWeight: 900, fontSize: '16', marginRight: '8px',display:'inline-block' }}>
                          {item.className}
                        </h3>
                        {item.isShow == 'up' ? (
                          <img
                            src={upArrow}
                            alt=""
                            className={styles.icon_boy}
                            onClick={() => this.isShow(index, 'down')}
                          />
                        ) : (
                          <img
                            src={downArrow}
                            alt=""
                            className={styles.icon_boy}
                            onClick={() => this.isShow(index, 'up')}
                          />
                        )}
                      </Col>
                    </Row>
                    {item.isShow != 'down' ? (
                      <div>
                        {(item.times || []).map((childItem, childIndex) => (
                          <Row key={childIndex}>
                            <Row className={styles.width100}>
                              <p className={styles.typeTitle}>{childItem.createdGmtAt}</p>
                            </Row>
                            <Row style={{ display: 'flex' }}>
                              {childItem.checkList &&
                              childItem.checkList.map((secondItem, secondIndex) => (
                                <ImageCard
                                  type={'dateType'}
                                  key={secondItem.id}
                                  connected={secondItem.isRelation}
                                  imageData={secondItem}
                                  getList={this.getCheckImgByClass}
                                />
                              ))}

                              {/*<ImageCard connected="false" type="classType" getCheckImgByClass={this.getCheckImgByClass} />*/}
                            </Row>
                          </Row>
                        ))}
                      </div>
                    ) : (
                      <></>
                    )}
                  </Row>
                </div>

            ))}

              {/* <div>
                {newImgByClass.length < imgByClassCount ? (
                  <Spin spinning={emrLoading} tip="加载中...">
                    <div
                      className={styles.loading_more}
                      hidden={imgByClassCount == newImgByClass.length}
                      onClick={this.emrNext}
                    >
                      加载更多
                    </div>
                  </Spin>
                ) : null}
              </div> */}


              <Spin spinning={emrLoading} tip="加载中...">
                {imageListParams.pageNum >= imgByClassCount / imageListParams.pageSize?
                  <div
                    className={styles.loading_more}
                    hidden={emrLoading}
                  >
                    <Row className={styles.width100}>
                      <p className={styles.typeTitle}>
                        没有更多啦！
                      </p>
                    </Row>
                  </div>
                  : <div
                    className={styles.loading_more}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                    hidden={emrLoading}
                    onClick={this.emrNext.bind(this)}
                  >
                    加载更多
                  </div>}
              </Spin>
            </>
          ) : (
            <Spin spinning={emrLoading} tip="加载中...">
              <div className={commonStyle.nodataContent} style={{ marginTop: '25%' }}>
                <img src={noData} className={commonStyle.imgStyle} alt="" />
                <div className={commonStyle.fontStyle}>暂无数据</div>
              </div>
            </Spin>
          )}
        </div>
      </GridContent>
    );
  }
}
export default connect(({ TypeRecord, loading }) => ({
  TypeRecord,
  loading: loading.models.TypeRecord,
}))(TypeRecord);
