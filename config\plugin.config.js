// Change theme plugin

// import MergeLessPlugin from 'antd-pro-merge-less';
// import AntDesignThemePlugin from 'antd-theme-webpack-plugin';
import UglifyWebpackPlugin from 'terser-webpack-plugin';
import path from 'path';
import SvgWebpack from '@svgr/webpack';
// import path from 'path';

// 生产环境清除console
// let FlagInfo;
// if(process.env.NODE_ENV=='development'){
//   FlagInfo=false;
// }else{
//   FlagInfo=true;
// }



const PluginsArr = ['@antv_data-set','@antv','fullcalendar','@fullcalendar','fullcalendar-scheduler','bizcharts','antd','uuid','echarts'];

function getModulePackageName(module) {
  if (!module.context) return null;

  const nodeModulesPath = path.join(__dirname, '../node_modules/');
  if (module.context.substring(0, nodeModulesPath.length) !== nodeModulesPath) {
    return null;
  }

  const moduleRelativePath = module.context.substring(nodeModulesPath.length);
  const [moduleDirName] = moduleRelativePath.split(path.sep);
  let packageName = moduleDirName;
  // handle tree shaking
  if (packageName.match('^_')) {
    // eslint-disable-next-line prefer-destructuring
    packageName = packageName.match(/^_(@?[^@]+)/)[1];
  }
  return packageName;
}


export default config => {

  // pro 和 开发环境再添加这个插件
  // if (process.env.APP_TYPE === 'site' || process.env.NODE_ENV !== 'production') {
  //   // 将所有 less 合并为一个供 themePlugin使用
  //   const outFile = path.join(__dirname, '../.temp/ant-design-pro.less');
  //   const stylesDir = path.join(__dirname, '../src/');
  //
  //   config.plugin('merge-less').use(MergeLessPlugin, [
  //     {
  //       stylesDir,
  //       outFile,
  //     },
  //   ]);
  //
  //   config.plugin('ant-design-theme').use(AntDesignThemePlugin, [
  //     {
  //       antDir: path.join(__dirname, '../node_modules/antd'),
  //       stylesDir,
  //       varFile: path.join(__dirname, '../node_modules/antd/lib/style/themes/default.less'),
  //       mainLessFile: outFile, //     themeVariables: ['@primary-color'],
  //       indexFileName: 'index.html',
  //       generateOne: true,
  //       lessUrl: 'https://gw.alipayobjects.com/os/lib/less.js/3.8.1/less.min.js',
  //     },
  //   ]);
  // }
  config.module
    .rule('svg')
    .test(/.svg(\?v=\d+.\d+.\d+)?$/)
    .use([
      {
        loader: 'babel-loader'
      },
      {
        loader: '@svgr/webpack',
        options: {
          babel: false,
          icon: true
        }
      }
    ])
    .loader(require.resolve('@svgr/webpack'));
  config.optimization
    .runtimeChunk(false) // share the same chunks across different modules
    .splitChunks({
      chunks: 'async',
      name: 'vendors',
      maxInitialRequests: Infinity,
      minSize: 0,
      cacheGroups: {
        vendors: {
          test: module => {
            const packageName = getModulePackageName(module);
            if (packageName) {
              return PluginsArr.indexOf(packageName) >= 0;
            }
            return false;
          },
          name(module) {
            const packageName = getModulePackageName(module);

            if (PluginsArr.indexOf(packageName) >= 0) {
              return 'plugins'; // visualization package
            }
            return 'misc';
          },
        },
        brafteditor:{
          test:/[\\/]node_modules[\\/]braft-editor/,
          name: 'brafteditor'
        },
      },
    });
  if(process.env.NODE_ENV==='production'){
      config.plugin('terser-webpack-plugin').use(UglifyWebpackPlugin,[{
        terserOptions:{
              compress: {
                drop_console:true,
              }
          }
      }])
  }
};
