import React, { Fragment } from 'react';
import Link from 'umi/link';
import { connect } from 'dva';
import styles from './MarketLayout.less';


@connect(({ login }) => ({
    login
}))

class MarketLoayout extends React.Component {
    state = {

    }
    componentDidMount() {
    }

    render() {
        const { children } = this.props;
        return (
            <div className={styles.MarketHome}>
                {children}
            </div>
        );
    }
}

export default MarketLoayout;
