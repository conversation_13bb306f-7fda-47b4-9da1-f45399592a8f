import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Card } from 'antd';
import React, { useState, useRef, Component } from 'react';
import { useIntl, FormattedMessage, history } from 'umi';
import { GridContent } from '@ant-design/pro-layout';
import styles from './style.less';
import AllPanel from '@/pages/MedicalCenter/AllPanel';
import { MenuOutlined } from '@ant-design/icons';
const { TabPane } = Tabs;

function callback(key) {
  // console.log(key);
}

class ImageTypeManage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      DownLoadStatus: true,
      MedicalModelStatus: false,
      EntryStatus: false,
      StandardStatus: false,
      visible: false,
    };
  }
  chooseMenuitem = (item) => {
    if (item.key == 'item-1') {
      this.setState({
        DownLoadStatus: true,
        MedicalModelStatus: false,
        EntryStatus: false,
        StandardStatus: false,
      });
    }
    if (item.key == 'item-2') {
      this.setState({
        DownLoadStatus: false,
        MedicalModelStatus: true,
        EntryStatus: false,
        StandardStatus: false,
      });
    }
    if (item.key == 'item-3') {
      this.setState({
        DownLoadStatus: false,
        MedicalModelStatus: false,
        EntryStatus: true,
        StandardStatus: false,
      });
    }
    if (item.key == 'item-4') {
      this.setState({
        DownLoadStatus: false,
        MedicalModelStatus: false,
        EntryStatus: false,
        StandardStatus: true,
      });
    }
  }
  //首页跳转事件
  handleLook = () => {
    this.setState({
      visible:false
    })
    history.push('/emr/ArriveToday')
    // window.location.reload();
  }
  //医疗中心跳转事件
  handleHomepage = () => {
    this.setState({
      visible: false,
    });
    history.push('/emr/MedicalCenter/AllPanel');
    // window.location.reload();
  };
  //影像类型/专业字典跳转事件
  handleTreatmentCenter = () => {
    this.setState({
      visible: false,
    });
    history.push('/emr/SystemSetup');
  };
  //内容中心跳转事件
  handleContentCenter = () => {
    this.setState({
      visible:false
    })
    history.push('/emr/ContentCenter')
  }
  //医生管理跳转事件
  handleDoctorMsg=()=>{
    this.setState({
      visible:false
    })
    history.push('/emr/DoctorManage/MedicalRecordAuthority')
  }
  //菜单点击事件弹框
  showModal = () => {
    this.setState({
      visible: true,
    });
  };

  render() {
    const { DownLoadStatus, MedicalModelStatus, EntryStatus, StandardStatus } = this.state;
    const items = [{ label: '医疗中心', key: 'item-1' }];
    return (
      <GridContent>
        <div className={styles.imageTypecontent}>
          <MenuOutlined onClick={this.showModal} />

          {/*<Row>*/}
          {/*  <Col span={3}>*/}
          {/*    <Menu items={items} defaultSelectedKeys={['item-1']} onClick={this.chooseMenuitem} />*/}
          {/*  </Col>*/}
          {/*</Row>*/}
        </div>
        <Modal
          visible={this.state.visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={800}
          style={{ top: '51px', left: '0', position: 'absolute' }}
          className={styles.header_pop}
          mask={false}
        >
          <div style={{ width: 800, display: 'flex', flexWrap: 'wrap' }}>
            <div>
              <Card bordered={true} className={styles.menuCard}
                    onClick={this.handleLook}>
                <p className={styles.content}>今日就诊-看板视图</p>
              </Card>
            </div>
            <div>
              <Card bordered={true} className={styles.menuCard}
                    onClick={this.handleHomepage}>
                <p className={styles.content}>医疗中心</p>
              </Card>
            </div>
            <div>
              <Card
                bordered={true}
                className={styles.menuCard}
                onClick={this.handleTreatmentCenter}
              >
                <p className={styles.content}>影像类型/专业字典</p>
              </Card>
            </div>
          </div>
          <div style={{display:'flex',flexWrap:'warp'}}>
            <div>
              <Card bordered={true} className={styles.menuCard} onClick={this.handleContentCenter}>
                <p className={styles.content}>内容中心</p>
              </Card>
            </div>
            <div>
              <Card bordered={true} className={styles.menuCard}
                    onClick={this.handleDoctorMsg}>
                <p className={styles.content}>医生管理</p>
              </Card>
            </div>
          </div>
        </Modal>
      </GridContent>
    );
  }
}

export default ImageTypeManage;
