@import '~antd/es/style/themes/default.less';

.templateListContent{
  .modelContent{
    padding-bottom: 30px;
    margin: 0 8px;
    overflow-y: scroll;
  }
  .menuFirst{
    .pointer{
      display: flex;
    }
    .arrows{
      width: 12px;
      height: 12px;
      margin-top: 3px;
    }
    .fileIcon{
      width: 20px;
      height: 20px;
      margin-left: 4px;
    }
    .filetitle{
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.45);
      line-height: 23px;
      &:hover{
        color: #4292FF;
      }
    }
    .fileName{
      display: flex;
    }
    .Unselected{
      width: 20px;
      height: 20px;
    }
    .fileContent{
      padding:8px;
      background-color: #F7F8FA;
      margin-top: 12px;
      .ContentLine{
        display: flex;
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 23px;
        .title{
          text-align: right;
          width: 60px;
          color: rgba(0, 0, 0, 0.45);
        }
        .content{
          width: 120px;
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }
  }
}
.chooseBgcolor{
  background: rgba(216, 216, 216, 0.5);
  border-radius: 2px;
}
.hidden{
  display: none;
}
.show{
  display: block;
}
.chooseFontcolor{
  color:#4292FF !important;
}
.SelectedStyle{
  color:#4292FF;
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 23px;
}
.UnselectedStyle{
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 23px;
}
.checkagress{
  width:20px;
  height: 20px;
}
.Choose{
  cursor: pointer;
  width: 24px;
  height: 24px;
  background: rgba(66, 146, 255, 0.12);
  border-radius: 2px;
  border: 1px solid #4292FF;
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 400;
  color: #4292FF;
  text-align: center;
}
.notChoose{
  cursor: pointer;
  width: 20px;
  height: 20px;
  background: #FFF;
  border-radius: 2px;
  border: 1px solid rgba(0, 0, 0, 0.45);
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.45);
  text-align: center;
  margin-top: 1px;
}
.Intertranslation{
  display: flex;
}
.contentTop{
  display: flex;
  justify-content: space-between;
}
.contentBook{
  margin-top: 16px;
  height: 500px;
  overflow: scroll;
}
