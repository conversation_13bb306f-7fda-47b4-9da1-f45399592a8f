import {
  arrivalSearch,   //到诊，搜索
  getCustomerWayTree,//个人信息  地区市场渠道和小牙医活动数据
  arrivalVisitIpadCode,// 生成pad二维码
  getAllAreas,// 电子档案获取城市列表
} from '@/services/registerAndArrival';

import {
  getAddRecommend,      // 修改添加推荐人
} from '@/services/appointmentApi';

export default {
  namespace: 'registerAndArrival',
  state: {
    patientInfo: {},   //患者信息
    registerList: [],   //右侧挂号list
    registerCostList: [],   //有预约挂号页面，右上角的挂号费显示
    selectedApptId: [],   //选择的所有预约的id
    selectedApptValue: [],   //选择到诊的所有预约的value
    visitNameDtos:[],        //所有的就诊人信息
    Complaintlist: [],   //主诉
    appointmentCacheShowData: {},
    BtType: 1,              //判断到诊 或 挂号按钮是否为disabled
    dataAppointment: [],    // 同一个患者多个预约数据
    upData: 0,              //跟新数据用
    upTable: 0,             //更改tabs切换
    SaveLeft: 0,             //更改tabs切换
    TemporaryStorageAppointment: [], //暂存预约数据
    directorData:null,               // 咨询师数据
  },
  effects: {
    // 电子档案获取城市列表
    *getAllAreas({ payload }, { call }) {
      const res = yield call(getAllAreas, payload);
      return res;
    },

    // 电子档案生成二维码
    *arrivalVisitIpadCode({ payload }, { call }) {
      const res = yield call(arrivalVisitIpadCode, payload);
      return res;
    },


    //到诊，搜索
    *arrivalSearch({ payload }, { call }) {
      const res = yield call(arrivalSearch, payload);
      return res;
    },
    //地区市场渠道和小牙医活动数据
    *getCustomerWayTree({ payload }, { call }) {
      const response = yield call(getCustomerWayTree, payload);
      return response;
    },

    // 添加 修改  推荐人
    *getAddRecommend({ payload }, { put, call }) {
      const res = yield call(getAddRecommend, payload);
      return res
    },
  },



  reducers: {
    //保存更新的值
    save(state, { payload }) {
      return {
        ...state,
        ...payload
      }
    }
  }
};
