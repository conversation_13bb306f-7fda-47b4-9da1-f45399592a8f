import React, {Component} from 'react';
import {Card,Radio,Popover,message,Button,Icon,Modal,Spin} from 'antd';
import $ from 'jquery';
import moment from 'moment';
import FullCalendar from '@fullcalendar/react'
import resourceTimeGridPlugin from '@fullcalendar/resource-timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import './index2.less' // webpack must be configured to do this
import classNames from 'classnames';
import styles from './TimeAgendaCalendar.less'
import commonStyles from '../AgendaCaleander.less'
import PropTypes from 'prop-types';
import Tooltip from 'tooltip.js';
import {findDOMNode} from 'react-dom'
import './Popper.less'
import AppointmentParticulars from '@/pages/Appointment/appointmentParticulars/index'
import CalendarDesk from './CalendarDesk'
import EventElement from '../Elements/EventElement'
import ReactDOM from 'react-dom';
import {
  getScrollbarWidth,
  minTime,
  maxTime,
  getTiemDeskInfo,
  getDateClickTime,
  getCurrentPositionDateTime,
  eventBus,
  CLOSE_AppointDetailsHungWindow,
  CHANGE_RemarkByAppointmentInfoSmallWindow
} from '@/utils/CalendarUtils'
import { getUrlParam,serilizeURL } from '@/utils/utils'
import Immutable from 'immutable'
import { connect } from 'dva'
import doctorIcon  from '@/assets/AppointmentRules/doctorIcon.png'
import seatIcon  from '@/assets/AppointmentRules/seatIcon.png'
import _ from 'lodash'


const workStatusForDayOff =  2;                          // 休息日
const workStatusForOther  =  3;                          // 走诊医生
const workStatusForNot    =  4;                          // 未排班医生
const workStatusForOrganizationStatus  =  5;             // 走诊机构
const rowTotal = 7;                                      // 列表项总列数
const lableClickClassName = 'labelTh';                   // 表头时间行点击className

/**
 * @NewType 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
 */
const typeClassName = {
  1:'oldPatientClass',
  2:'newPatientClass',
  3:'consultationClass',
  4:'personalTimeClass',
  5:'diagnosisClass',
  6:'officeHoursClass',
}

const typeEventCommonName = {
  1:'notClinicType.', // 未到诊
  2:'comeClinicType', // 已到诊1
  3:'lateType',       // 过预约时间未到诊
  4:'finishType'      // 已结算
}

import {
  getEventElement,
  getEventElementClass, // 更新Event结构方法
  getChairNum,
  newCustomerEventType,
  oldCustomerEventType,
  otherEventType,
  backgroundType,
  LarageHeight,
  MediumHeight,
  SmallHeight,
  tittleHight,
  slotLabelFormat,
  schedulerLicenseKey,
  eventPatientNameEllipsis // 患者名称添加省略号
} from '@/utils/CalendarUtils'

import  TimeCalendarHeader  from './TimeAgendaCalendar1.js'

// 防抖
function debounce(callback,delay){
  let timerId = null;
  return function(args){
    let that = this;
    clearTimeout(timerId)
    timerId = setTimeout(function(){
      callback.call(that,args)
    },delay)
  }
}

/**
 * 时间制 预约组件
 * 全屏或半屏 使用时间制度 预约组件
 */
@connect(({Getscreen,TimeCalendar,appointmentCalendarMode,loading}) => ({
  Getscreen,
  TimeCalendar,
  appointmentCalendarMode,
  loading
}))
export default class TimeAgendaCalendar extends Component {

  static propTypes = {
    onRemoveEvent:PropTypes.func,                 // 点击删除事件方法
    onEventClick:PropTypes.func,                  // 点击Event预约事件的回调方法
    onTemporaryStorageEventClick:PropTypes.func,  // 点击暂存的预约事件的回调方法
    onTimeBlankClick:PropTypes.func,              // 点击空白区域回调事件的方法
    goNextIcon:PropTypes.func,                    // 点击下一页的回调方法
    goLastIcon:PropTypes.func,                    // 点击上一页的回调方法
    calendarId:PropTypes.string,                  // 当前预约组件绑定的ID名称
    Option:PropTypes.object,                      // 需要扩展的预约组件配置字段
    calendarTableList:PropTypes.array,            // 预约组件表格表头格式
    EventList:PropTypes.array,                    // 预约组件预约事件集合
    onRef:PropTypes.func,
    AgendaHeadType: PropTypes.string,             // 当前组件是姓名头 还是 时间头
    onClickDateByHead:PropTypes.func,             // 点击时间头 获取会诊时间
    appointmentPageInfo:PropTypes.object,         // 预约头列表分页信息
    consultationId:PropTypes.any,                 // 会诊医生id
    onEventDrop:PropTypes.any,                    // 回调函数，当拖动结束且日程移动另一个时间时触发
    onEventResize:PropTypes.any,                  // 当日程事件调整（resize）结束并且事件被改变时触发
    onClickTitleForDoctor:PropTypes.func,         // 点击表格头中的医生回调
    onClickTitleForData:PropTypes.func,           // 点击表格头中的日期回调
    onEventDragStart:PropTypes.func,              // 开始预约拖拽的回调
    isShowDeskRight:PropTypes.bool,               // 是否展示左侧时间栏
    appointmentCountAfterWorkTime:PropTypes.any,  // 机构上班时间以外预约数据量
    appointmentCountBeforeWorkTime:PropTypes.any, // 机构上班时间以外预约数据量
    isfullScreen:PropTypes.bool,                  // 是否是全屏页面
    isWaitingInfo:PropTypes.bool,                 // 是否展示waitinglist等待列表详情信息
    onClickWaitingInfo:PropTypes.func,            // 点击waitingInfo详情
    HighlightedResources:PropTypes.any,           // 高亮资源项
  };
  static defaultProps = {
    onRemoveEvent:()=>{},
    onEventClick:()=>{},
    onTimeBlankClick:()=>{},
    onTemporaryStorageEventClick:()=>{},
    goNextIcon:()=>{},
    goLastIcon:()=>{},
    consultationId:null,
    calendarId:'calendar',   // 默认组件ID
    Option:{},               // 默认组件公共配置项目
    calendarTableList:[],
    EventList:[],
    onRef:()=>{},
    AgendaHeadType:'1',       // 姓名展示:1  事件展示:2
    appointmentPageInfo:{},   // 分页信息初始化参数
    onClickDateByHead:()=>{}, // 点击时间头获取时间
    onEventDrop:()=>{},       // 回调函数，当拖动结束且日程移动另一个时间时触发
    onEventResize:()=>{},      // 当日程事件调整（resize）结束并且事件被改变时触发
    onClickTitleForDoctor:()=>{},
    onClickTitleForData:()=>{},
    onEventDragStart:()=>{},   // 开始预约拖拽的回调
    isShowDeskRight:false,      // 是否展示左侧时间栏
    appointmentCountAfterWorkTime:null,
    appointmentCountBeforeWorkTime:null,
    isfullScreen:false,          // 是否是全屏页面
    isWaitingInfo:false,          // 是否展示等待列表详情信息
    onClickWaitingInfo:()=>{},
    HighlightedResources:null,
  };

  /**
   * 删除Event事件
   * @param eventId
   */
  onRemoveEvent=(taskId)=>{
    this.props.onRemoveEvent(taskId)
  }
  /**
   * 点击Event事件
   * @param calEvent
   */
  onEventClick=(calEvent)=>{
    //type 1:老患者预约   2:新患者预约  3:会诊   4:个人时间占用  5:已到诊  6:上班时间
    // @NewType 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊

    const {
      eventExtendedProps,
      id,
      newResourceId,
    } = calEvent || {}

    const {
      appointmentCount,
      appointmentInfoOfTableDto,
      checked,
      consultationInfoOfTableDto,
      operationType,
      remarks,
      reservationRule,
      resourceIdValue,
      titleInfo,
      type,
    } = eventExtendedProps || {}

    /**
     * 获取event 组件中的内容
     */
    if (type == 1 || type == 2 || type == 4){
      //this.AppointmentParticulars&&this.AppointmentParticulars.LabelData&&this.AppointmentParticulars.LabelData(); ///  点击 event 事件 通知手工标签 调取数据函数
      this.props.onEventClick(calEvent);
    }

    if(type == 1 || type == 2 ){
      //this.AppointmentParticulars&&this.AppointmentParticulars.LabelData&&this.AppointmentParticulars.LabelData();
    }

  }
  /**
   * 点击空白事件
   * @param date
   */
  onTimeBlankClick=({dateMonent,resourceObj:res,info})=>{
    let resourceObj = getChairNum(res.id,this.props.calendarTableList);
    this.props.onTimeBlankClick({dateMonent,resourceObj,info})
  }

  /**
   * 点击时间头
   */
  onClickDateByHead=(value)=>{
    let dateClickStatus = this.props.consultationId // 会诊医生id
    this.props.onClickDateByHead(value)
  }

  /**
   * 点击下一页
   */
  goNextIcon=()=>{
    let { current,pageSize,total,pageCount} = this.props.appointmentPageInfo;
    current = current || 1
    pageSize = pageSize || 0
    total = total || 0
    pageCount = pageCount || 0
    let nextPage = current >= pageCount ? pageCount : ++current

    this.props.goNextIcon(nextPage);
  }

  /**
   * 点击上一页
   */
  goLastIcon=()=>{
    let { current,pageSize,total,pageCount} = this.props.appointmentPageInfo;
    current = current || 1
    pageSize = pageSize || 0
    total = total || 0
    pageCount = pageCount || 0
    let lastPage = current <= 1 ? 1 : --current

    this.props.goLastIcon(lastPage);
  }

  /**
   * 获取resources列表项目数据
   * 遍历列表结构的同时添加椅位资源列事件
   * @returns {Array}
   *
   * private Integer organizationStatus;

   * 走诊机构名

   *  private String organizationName;
   *
   */
  getResources=()=>{
    let calendarTableList = this.state.calendarTableList;
    let resources = [];                         // 椅位资源列
    let groupTotal = calendarTableList.length;  // 医生总数
    let EventListPlue = [];
    calendarTableList.forEach((val,idx)=>{
      if(val.resources){
        val.resources.forEach((res,resIdx)=>{
          const {
            staffStatus,
            freeTime,
            waitingListCount,
            type,
            workStatus,
            workTimeEnd,
            workTimeStart,
            date,
            doctorId,
            groupId,
            name,
            organizationName,
            operationType,
            doctorUserId,
            chairTotal,
          } = val || {}

          resources.push({
            ...res,
            chairTotal,
            workStatus:res.organizationStatus == 2 ? 5 : workStatus,
            workTimeEnd,
            workTimeStart,
            staffStatus,
            type,
            date,
            doctorId,
            groupId,
            name,
            organizationName:res.organizationStatus == 2 ? res.organizationName : organizationName,
            operationType,
            doctorUserId,
            freeTime,
            waitingListCount,
          });
        })
      }
    })
    return resources;
  }

  /**
   * 列表中外部机构上班event
   */
  setDayOffEvent=(list)=>{
    let eventListPlue = []
    list.forEach((res)=>{
      let {
        workStatus,
        workTimeEnd,
        workTimeStart,
        staffStatus,
        type,
        date,
        doctorId,
        groupId,
        name,
        organizationName,
        id,
        chairNum,
        title,
      } = res || {}

      if(workStatus == workStatusForOrganizationStatus) { // 其他诊所椅位出诊
        eventListPlue.push({
          start: moment('00:00:00','HH:mm:ss').format(),
          end: moment('24:00:00','HH:mm:ss').format(),
          title: organizationName,
          resourceId:res.id,
          resourceIdValue: res.id,
          backgroundColor: '#DCDCDC',
          className:'surplusBackgroundEvent',
          //backgroundColor: "#5f5c6b",
          rendering: "background",
          titleInfo: organizationName,
          type: 7,
          workStatus,
          color: null,
          id: `dayOff${res.id}`
        })
      }

    })
    return eventListPlue
  }


  /**
   * 添加列表中展示休息日(现在由后端返回)
   */
  _setRestdayEvent=(list)=>{
    let eventListPlue = []
    list.forEach((res)=> {
      let {
        workStatus,
        workTimeEnd,
        workTimeStart,
        staffStatus,
        type,
        date,
        doctorId,
        groupId,
        name,
        organizationName,
        id,
        chairNum,
        title,
        chairStatus,
      } = res || {}

      if (chairStatus == 2){
        eventListPlue.push({
          resourceId:res.id,
          resourceIdValue: res.id,
          titleInfo: "休息",
          type: 7,
          workStatus,
          color: null,
          rendering: 'background',
          id: 'otherTimeRestDey' + res.id,
          start: moment('00:00:00','HH:mm:ss').format(),
          end: moment('23:59:59','HH:mm:ss').format(),
          title: '',
          backgroundColor: '#DCDCDC',
          className:'otherRestTime'
        })
      }
    })
    return eventListPlue
  }


  closeTooltip=()=>{
    if (this.tooltip) {
      this.tooltip.hide();
      this.tooltip.dispose();
      this.tooltip = null
      // 广播: 关闭预约详情时发送关闭详情窗口事件的广播,此广播用于将详情弹窗组件中的状态都重置为默认
      // 以及详情弹窗组件中的子组件状态全部清空
      // 接收组件: AppointmentInfoSmallWindow.js  (搜索CLOSE_AppointDetailsHungWindow)
      eventBus.emit(CLOSE_AppointDetailsHungWindow)
    }
  }

  /**
   * 绑定外部点击事件方法
   */
  bindClickForCalendar=()=>{
    const _this = this
    $('body').on("click",function(event){
      let target = $(event.target);
      let thisTargetState = target.attr('class') && target.attr('class').indexOf('fc-event') != -1
      let parentsTarState = target.parents('.fc-event').attr('class') && target.parents('.fc-event').attr('class').indexOf('fc-event') != -1
      if (!(thisTargetState || parentsTarState)) {
        let _con = $('#patientAppointmentById');   // 设置目标区域
        let _PopoverText = $('#PopoverText');
        let _PopoverByConfirmation = $('#PopoverByConfirmation');
        if (
          !_con.is(event.target) && _con.has(event.target).length === 0 &&
          !_PopoverText.is(event.target) && _PopoverText.has(event.target).length === 0 &&
          !_PopoverByConfirmation.is(event.target) && _PopoverByConfirmation.has(event.target).length === 0
        ) {
          _this.closeTooltip()
        };
      }
    })
  }

  /**
   * 过滤事件方法
   * @param eventList
   * @returns {*}
   */
  filterEventList=(eventList)=>{
    let doctorIdentification = localStorage.getItem('doctorIdentification') == 1
    /*eventList = eventList.filter((val)=>{
      if((val.type != 6)){
        if((val.workStatus == workStatusForDayOff && doctorIdentification)) {
        }else {
          return val
        }
      }
    })*/
    return eventList
  }


  /**
   * 以到诊预约预约 修改预约时长
   * appointmentId [long]	是	预约ID
     appointmentDate [date]	是	预约日期
     timeStart [date]	是	预约起始时间
     timeLength [int]	是	预约时长
     chairCode [int]	是	椅位
   */
  appointmentTimeLengthAlter=(info)=>{
    const { event,thisResource } = info
    const { end,start,extendedProps } = event
    const { appointmentInfoOfTableDto,resourceIdValue } = extendedProps || {}
    const { appointmentId,appointmentDate,chairCode,appointmentDatetimeStr } = appointmentInfoOfTableDto || {}
    const { dispatch } = this.props
    let timeLength = moment(end).diff(moment(start),'minute');
    let timeStart = moment(start).format('HH:mm')

    let params = {
      appointmentId,
      appointmentDate,
      chairCode,
      timeLength,
      timeStart,
      doctorIdOfAppointment:thisResource && thisResource.doctorId,
    }

    dispatch({
      type:'TimeCalendar/appointmentTimeLengthAlter',
      payload:{...params}
    }).then((res)=>{
      if(res && res.code == 200){
        message.success('修改预约时长成功')
        this.props.updateStatuss && this.props.updateStatuss()
      }else {
        let msg = res && res.msg ? res.msg : '修改预约时长失败'
        message.error(msg)
        info.revert();
      }
    })
  }


  /**
   * 回调函数，当拖动结束且日程移动另一个时间时触发：
   * @param info
   */
  eventDrop=(info)=>{
    //_this.props.onEventDrop && _this.props.onEventDrop(event,delta)
    /*const {
      appointmentInfoDto, // 主诉信息
      end,                // 结束时间
      start,              // 开始时间
      resourceId,         // 改变的垂直资源列id
      id,                 // 当前事件ID
    } = event*/

    const {
      el,
      event,
      oldEvent,
      view,
      delta,
      newResource,
      oldResource
    } = info || {}
    /* extendedProps 事件扩展中包含信息
        appointmentCount: null
        appointmentInfoDto: {appointmentId: 73426, patientId: 1115583, patientName: "大闹天宫", sex: 1, sexDescribe: "先生"}
        appointmentInfoDtoList: null
        canceled: false
        operationType: 1
        reservationRule: null
        titleInfo: "Event Title1"
        type: 5
     */
    const {
      extendedProps:eventExtendedProps, // event事件中的扩展信息
      start,         // 事件开始时间
      end,           // 事件结束时间
      id:EventId,    // 事件ID
      groupId,       // 事件组ID
      title,         // 事件标题
      extendedProps
    } = event || {}
    const {
      appointmentInfoDto,
      resourceIdValue
    } = eventExtendedProps || {}
    /*
     newResourceExtendedProps 资源列中包含信息
     chairNum: 1
     date: "2019-05-07"
     doctorId: 405
     groupId: 2
     name: "王医生小组测试别动"
     organizationName: null
     staffStatus: "0"
     type: 1
     workStatus: 4
     workTimeEnd: null
     workTimeStart: null
     */
    const {
      extendedProps:newResourceExtendedProps,  // 新资源列信息
      id:newResourceId,                        // 新资源列ID
      title:newResourceTitle,                  // 新资源列标题
    } = newResource || {}


    let stateMoment = moment(start) // 获取新event的开始时间
    let endMoment = moment(end)     // 获取新event的结束时间


    let eventObj = {
      start:stateMoment,        // 开始时间
      end:endMoment,            // 结束时间
      id:EventId,               // 事件id
      eventExtendedProps,       // 扩展Event携带参数
      newResource,              // 移动到的资源列对象
      newResourceId,            // 资源列ID
      newResourceTitle,         // 资源列title
      newResourceExtendedProps, // 资源列扩展对象
    }

    const { _def:def } = event || {}


    let resources = this.getResources()
    let thisResource = resources.find((res)=>{
      if(res.id == resourceIdValue){
        return res
      }
    })

    //info.revert()
    // 抛出拖拽事件回调
    this.props.onEventDrop && this.props.onEventDrop({...info,thisResource})
  }

  /**
   * 当日程事件调整（resize）结束并且事件被改变时触发：
   * 官方组件返回缺少 resourceId
   */
  eventResize=(info)=>{
    if(this.tooltip){
      this.tooltip.dispose();
      this.tooltip = null
    }

    const {
      el,
      endDelta,
      event,
      jsEvent,
      prevEvent,
      revert,
      startDelta,
      view
    } = info || {}

    const {
      extendedProps:eventExtendedProps, // event事件中的扩展信息
      start,                            // 事件开始时间
      end,                              // 事件结束时间
      id:EventId,                       // 事件ID
      groupId,                          // 事件组ID
      title,                            // 事件标题
      _def:def,
    } = event || {}
    info.revert();
    const {
      resourceIdValue:newResourceId
    } = eventExtendedProps || {}


      let stateMoment = moment(start) // 获取新event的开始时间
      let endMoment = moment(end)     // 获取新event的结束时间

      let eventObj = {
        start: stateMoment,        // 开始时间
        end: endMoment,            // 结束时间
        id: EventId,               // 事件id
        newResourceId,            // event 事件中对应的列资源id
        eventExtendedProps,       // event事件中扩展信息
      }

      let resources = this.getResources()
      let thisResource = resources.find((res) => {
        return res.id == newResourceId
      })
      info.thisResource = thisResource


      const { appointmentInfoOfTableDto } = eventExtendedProps;
      const { isComeVisit } = appointmentInfoOfTableDto || {}
      if (isComeVisit == 1) {
        this.appointmentTimeLengthAlter(info)
        return
      }


      this.props.onEventResize && this.props.onEventResize(info)
  }

  /**
   * 确定是否允许拖拽和调整大小的事件可以互相重叠
   */
  eventOverlap=(stillEvent, movingEvent)=>{
    const { extendedProps:stillEventExtendedProps } = stillEvent;
    const { extendedProps:movingEventExtendedProps } = movingEvent;
    const { type:typeFoStill } = stillEventExtendedProps;
    const { type:typeFoMoving } = movingEventExtendedProps;
    return true
  };



  //

  /**
   * 当点击日历中某个事件的时候触发回调
   */
  eventClick=(eventClickInfo,tag)=>{
    //this.AppointmentParticulars&&this.AppointmentParticulars.clearTooltipDATA&&this.AppointmentParticulars.clearTooltipDATA()
    const {
      el,
      event,
      jsEvent,
      view,
    } = eventClickInfo

    console.log('eventClickInfo :: ',eventClickInfo,tag);

    const {
      extendedProps:eventExtendedProps, // event事件中的扩展信息
      start,                            // 事件开始时间
      end,                              // 事件结束时间
      id:EventId,                       // 事件ID
      groupId,                          // 事件组ID
      title,                            // 事件标题
    } = event || {}

    /*eventExtendedProps.appointmentInfoOfTableDto.patientInfoDto.name = 'hahahaha'
    this.upDataEventById('3',eventExtendedProps)
    if(checked == 1){element.addClass('checkedEvent')} // 圈住当前搜索到的预约
    */

    if(EventId == 'checkedBackEvent'){ return }
    const {
      resourceIdValue:newResourceId,     // 选中newResourceId
      operationType,                     // 当前用户是否可编辑此条预约 1:可以编辑 0:不可编辑
      type,                              // 当前点击Event的类型
      appointmentInfoOfTableDto,
    } = eventExtendedProps || {}


    const {
      appointmentStatus // 预约状态 预约状态 1正常 2已改约 3已取消 10预约占用
    } = appointmentInfoOfTableDto || {}


    // 预约状态 预约状态 1正常 2已改约 3已取消 10预约占用   预约占用不记录
    if(appointmentStatus == 10){
      const { chairCode,appointmentDate,timeStart,appointmentId } = appointmentInfoOfTableDto || {}
      let chairInfo = this.getResources().find(item=> item.id == newResourceId)
      let dateStr = `${appointmentDate}T${timeStart}`
      const { target } = jsEvent
      let classNameTest = $(target).prop("className");
      const isCancel = classNameTest && classNameTest.indexOf('ShutDownBtn') != -1
      /**
       isCancel:false
       appointmentId: 6852
       appointmentInfoOfTableDto: {appointmentId: 6852, taskId: null, taskType: 3, appointmentStatus: 10, chairCode: 1, …}
       chairInfo: {id: 1, chairNum: 1, title: '椅位1', organizationStatus: 1, organizationName: null, …}
       dateStr: "2022-03-25T15:00"
       */

      this.props.onTemporaryStorageEventClick({
        isCancel:isCancel,                                   // 是否是取消暂存
        dateStr:dateStr,                                     // 时间
        chairInfo:chairInfo,                                 // 椅位信息
        appointmentId:appointmentId,                         // 预约id
        appointmentInfoOfTableDto:appointmentInfoOfTableDto, // 原始预约信息
      })
      return;
    }


    let stateMoment = moment(start) // 获取新event的开始时间
    let endMoment = moment(end)     // 获取新event的结束时间
    let resources = this.getResources()
    let thisResource = resources.find((res)=>{
      return res.id == newResourceId
    })
    let eventObj = {
      start:stateMoment,        // 开始时间
      end:endMoment,            // 结束时间
      id:EventId,               // 事件id
      newResourceId,            // event 事件中对应的列资源id
      eventExtendedProps,       // event 事件中扩展信息
      el,                       // event 中的Dom
      thisResource
    }


    eventClickInfo.thisResource = thisResource
    const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1  // 1医生  2非医生
    console.log('eventClickInfoeventClickInfo :: ',eventClickInfo);

    if (doctorIdentificationState) { // 医生登录
      if (type == 4){  // 当前点击事件为个人占用时间事件
        this.onPopperShow(eventClickInfo, () => {
          this.AppointmentParticulars.updateCurrentClickEvent(eventClickInfo)
        });
        return
      }
      if (operationType == 1) {
        this.onPopperShow(eventClickInfo, () => {
          this.AppointmentParticulars.updateCurrentClickEvent(eventClickInfo)
        });
        this.onEventClick(eventObj);
      } else {
        /*if (eventExtendedProps.type == 1) {
          message.warning('当前医生无操作权限', 1)
        }*/
        this.onPopperShow(eventClickInfo, () => {
          this.AppointmentParticulars.updateCurrentClickEvent(eventClickInfo)
        });
        //this.onEventClick(eventObj);
      }
    }else { // 客服登录
      //if (type != 4) {
      this.onPopperShow(eventClickInfo, () => {
        this.AppointmentParticulars.updateCurrentClickEvent(eventClickInfo)
      });
      this.onEventClick(eventObj);
      //}
    }

  };

  // 展示悬窗的样式
  applyReactStyle=(data)=>{
    this.setState({
      visibleAppointmentParticularsStyle:{
        position:'absolute',
        top:'1px',
        left:'1px'
      },
      visibleAppointmentParticulars:true,
    })
  }
  onPopperShow=({el, event, jsEvent, view,},callBack)=> {
    const {
      extendedProps: eventExtendedProps, // event事件中的扩展信息
      start,                            // 事件开始时间
      end,                              // 事件结束时间
      id: EventId,                       // 事件ID
      groupId,                          // 事件组ID
      title,                            // 事件标题
    } = event || {}

    console.log('el 123123 :: ',el);

    const {
      type,
      appointmentInfoOfTableDto
    } = eventExtendedProps || {}

    const { appointmentStatus } =  appointmentInfoOfTableDto || {}

    console.log('appointmentInfoOfTableDto :: ',appointmentInfoOfTableDto);

    /**
     * 通过EventId获取到点击的EventDom结构
     */

    // 非个人占用时间弹出弹窗
    // if(type == 1) {
    //   /* 展示弹窗 */
    //   const {
    //     appointmentId
    //   } = appointmentInfoOfTableDto || {}
    //
    //   $('.fc-event').removeClass('checkedEvent')
    //   $(el).addClass('checkedEvent')
    //   this.clickAppintmentId = appointmentId // 记录当前点击的Event预约id
    //
    //   this.AppointmentParticulars && this.AppointmentParticulars.showModelVisible()
    //   /*this.setState({
    //     //modelVisible: true,
    //     clickEvent:appointmentId,
    //   }, () => {*/
    //   callBack && callBack()
    //   //})
    //   return
    // }

    // 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
    if(this.tooltip){
      const { popperInstance } = this.tooltip;
      const { popper } = popperInstance || {}
      const { id } = popper || {}
      //let myElement = document.getElementById(id+'');
      //myElement && myElement.removeNode(true);
      //this.closeTooltip();
      this.AppointmentParticulars&&this.AppointmentParticulars.clearTooltipDATA&&this.AppointmentParticulars.clearTooltipDATA()
      $('#' + id).remove()
      $('body').remove('.tooltip')
      // this.tooltip.hide()
      // this.tooltip.dispose();
      this.tooltip = null
    }
    if (!this.tooltip) {
      // 非个人占用时间弹出弹窗
      if(type == 1 || type == 4) {
        /* 展示弹窗 */
        if (type == 1 && !this.props.isfullScreen) {
          return
        }
        const {
          appointmentId
        } = appointmentInfoOfTableDto || {}

        $('.fc-event').removeClass('checkedEvent')
        if(appointmentStatus != 10) { $(el).addClass('checkedEvent') }
        this.clickAppintmentId = appointmentId // 记录当前点击的Event预约id
        let elByEvnetId = $(el);
        let fc_body =  elByEvnetId.parents('.fc-body')
        console.log('elByEvnetIdelByEvnetId :: ',fc_body);
        let popperContent = document.getElementById('droppableById')
        this.tooltip = new Tooltip(elByEvnetId, {
          html: true,
          container: fc_body[0],
          trigger: 'click',
          popperOptions: {
            modifiers: {
              flip: {behavior: ['left', 'right']},
            }
          },
          placement: 'left-start',
        });
        this.tooltip.updateTitleContent(findDOMNode(this.refs.foo))
        this.tooltip.show();
        this.tooltip.evnetId = EventId; // 对悬窗对象添加唯一标识
        // this.AppointmentParticulars && this.AppointmentParticulars.showModelVisible()
        /*this.setState({
          //modelVisible: true,
          clickEvent:appointmentId,
        }, () => {*/
        // callBack && callBack()
        //})
        // return\
        console.log('tooltiptooltip :: ',this.tooltip);
      }
    }
    callBack && callBack()
  }

  /**
   * 获取空白区域覆盖的event
   */
  getBackEvent=({eventList,id,type,dateStr})=>{
    let eventFilterWork = eventList.filter((res)=> {  //资源的上班时间
      if (res.resourceIdValue == id) {
        if (moment(dateStr).isBetween(res.start, res.end, 'minutes') || moment(dateStr).isSame(res.start,'minutes')) {
          if (Array.isArray(type)) {
            let typeList = type.filter((val)=>{
              return val == res.type
            })
              return typeList.length != 0
          }else {
            if (res.type == type) {
              return res
            }
          }
        }
      }
    })
    return eventFilterWork
  }

  /**
   * 当点击日历上面的某一时间触发
   */
  dateClick=(info)=>{

    let { dateStr,resource } = info
    console.log('info 123123 :: ',info,resource);
    let origindateStr = dateStr; // 保存记录当前点击时间

    const {
      id,
      title,
    } = resource;

    const {
      chairNum,
      date,
      doctorId,
      groupId,
      name,
      organizationName,
      organizationStatus,
      staffStatus,
      type,
      workStatus,
      workTimeEnd,
      workTimeStart,
      operationType,
      doctorUserId,
      chairStatus
    } = resource.extendedProps || {}

    let resourceObj = {
      id,
      title,
      chairNum,
      date,
      doctorId,
      groupId,
      name,
      organizationName,
      staffStatus,    // 判断当前医生是否已离职
      type,
      workStatus,     // 是否上班 1:上班 2:休息
      workTimeEnd,
      workTimeStart,
      operationType,  // 判断当前列表是否可操作 1可操作 0不可操作
      doctorUserId,
      chairStatus,
    }


    /**
     * 处理点击区域所在时间边界
     * @type {Array}
     *
     * 00 15 30 45 边界
     */
    dateStr = getDateClickTime(dateStr)

    let eventList = this.state.EventList;
    let eventFilter = eventList.filter((res)=>{
      if (res.resourceIdValue == id) {
        if (moment(dateStr).isBetween(res.start, res.end, 'minutes')) {
          // @NewType 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
          //return res
          /*if ( res.type == 4 || res.type == 7 || res.type == 5) {
            return res
          }*/
          if (res.type == 7) {
            return res
          }
        }
      }
    })

    // 获取当前点击事件包含的Event 判断当前点击时间是否包含预约或占用
    let eventFilterTypeUPTiem = this.getBackEvent({
      eventList,id,type:[1,2,4],dateStr
    })
    if (eventFilterTypeUPTiem.length != 0) {
      dateStr = moment(dateStr).add(15,'minute').format()
    }
    let eventFilterTypeDownTiem = this.getBackEvent({
      eventList,id,type:[1,2,4],dateStr
    })
    if (eventFilterTypeDownTiem.length != 0) {
      dateStr = moment(origindateStr).format()
    }

    /**
     * 处理时间和日期拼接
     * @type {string}
     */
    let dateTimeText = moment(dateStr).format('HH:mm:ss')
    let dataDayText = moment(date).format('YYYY-MM-DD')
    let dataText = dataDayText + ' ' + dateTimeText;
    let dateMonent = moment(dataText,'YYYY-MM-DD HH:mm:ss')
    info.dateStr = dateStr

    /*let eventFilterWork = eventList.filter((res)=>{  //资源的上班时间
      if (res.resourceIdValue == id) {
        if (moment(dateStr).isBetween(res.start, res.end, 'minutes') || moment(dateStr).isSame(res.start,'minutes')) {
          if(res.type == 3){
            return res
          }
        }
      }
    })*/

    //当前已过日期 点击空不展示弹框回调
    let resourceMonent = moment(resourceObj.date,'YYYY-MM-DD')
    let isBefore = false
    isBefore = moment().isSame(resourceMonent,'day')
    if(!isBefore) {
      isBefore = moment().isBefore(resourceMonent,'day')
    }

    // 禁用外诊机构椅位点击事件
    if(organizationStatus == 2 || workStatus == 5){
      return
    }

    // 判断当前医生是否离职 离职医生不可预约
    if(staffStatus == 1){
      message.warning('当前医生已离职,不可新建预约')
      return
    }

    if (eventFilter.length == 0) {
      const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1  // 1医生  2非医生
      if (doctorIdentificationState) { // 医生登录
        if (operationType == 1) {
          if (isBefore) {
            this.onTimeBlankClick({dateMonent, resourceObj, info});
          }else {
            message.warning('当前选中日期已过')
          }
        } else {
          message.warning('当前医生无操作权限', 1)
        }
      } else { // 客服登录
          if (isBefore) {
            if (chairStatus == 2){
              message.warning('当前椅位状态为休息不可新建预约')
              return
            }
            if(chairStatus == 1) {
              this.onTimeBlankClick({dateMonent, resourceObj, info});
            }
          }else {
            message.warning('当前选中日期已过')
          }
      }
    }
  }

  /**
   * 组件窗口改变大小时触发
   */
  onWindowResize=()=>{
    // eventPatientNameEllipsis()
    this.windowResize()
  }

  windowResize=()=>{
    if ($('.fc-slats tr')[0]) {
      let heightDesk = $('.fc-slats tr').eq(1).height()
      this.heightDesk = heightDesk
      //this.setState({heightDesk: heightDesk},()=>{})
    }
  }

  /**
   * 取消个人占用时间窗口
   * @param event
   */
  eventPersonalTimeClose=(event)=>{
    this.closeTooltip()
    if(event && event.extendedProps) {
      if (localStorage.getItem('doctorIdentification') == 1 && event.extendedProps.operationType == 0) {
        message.warning('当前医生无操作权限')
        return
      }
      this.dispatchPersonslTimeClose(event.extendedProps)
    }
  }

  dispatchPersonslTimeClose=(extendedProp)=>{
    const { occupyTimeDto } = extendedProp||{};
    const { dispatch } = this.props;
    dispatch({
      type:'Getscreen/postOccupyTimeRemove',
      payload:{
        tenantId:localStorage.getItem('tenantId'),
        organizationId:localStorage.getItem('organizationInfoId'),
        occupyId:occupyTimeDto.occupyId,                                                                    // 复制[string]	是	时间占用主键id	展开
        systemType:JSON.parse(localStorage.getItem('organizationInfoJson')).systems,//是	机构制度(0挂号制, 1预约制)
        userId:localStorage.getItem('id'),                                          // 复制	[string]//	是	用户id	展开
        username:localStorage.getItem('userName'),
        resourceId:occupyTimeDto.resourceId,//[string]	是	用户名称	展开
      }
    }).then((res)=>{
      if(res&&res.code === 200 ){
        message.success('删除时间占用成功！');
        this.updateStatus && this.updateStatus()
      }else {
        message.warning('删除时间占用失败，请稍后重试！');
      }
    }).catch((err)=>{
    })
  }


  /**
   * 渲染Event事件方法
   */
  eventRender=(info)=>{
    const { event, el, view} = info
    /*event.formatRange({
      hour: 'numeric',
      minute: '2-digit',
      hour12: false,
      omitZeroMinute: false,
      meridiem: false
    })*/

    const {
      extendedProps,
      id,
      start,
      end,
    } = event || {}; //changeEvent
    const {
      appointmentInfoOfTableDto,
      consultationInfoOfTableDto,
      type,
      operationType,
      checked,
      EventData,
    } = extendedProps || {};


    let appointmentInfoOfTableDtoObj = {};
    if(type == 2){
      appointmentInfoOfTableDtoObj = consultationInfoOfTableDto
    }else {
      appointmentInfoOfTableDtoObj = appointmentInfoOfTableDto
    }

    let {
      appointmentId,
      isComeVisit,  // 0未到诊 1已到诊
      isLate,       // 过预约时间未到诊 0没有迟到  1迟到
      isSettlement, // 结算状态 0:未结算 1:已结算
      isShuangAbout, // 是否爽约 0:未爽约 1:爽约
      appointmentStatus // 预约状态 预约状态 1正常 2已改约 3已取消 10预约占用
    } = appointmentInfoOfTableDtoObj || {};

    const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1  // 1医生  2非医生
    let element = $(el)

    if(type == 2){
      let diffMinutes = 0;


      if(start && end){
        let startMoment = moment(start);
        let endMoment = moment(end);
        diffMinutes = endMoment.diff(startMoment,'minutes');
      }
      if(diffMinutes <= 5) {
        element.addClass('unset')
      }
    }
    element.addClass(typeClassName[type])

    if(checked == 1){
      element.addClass('checkedEvent') // 圈住当前搜索到的预约
    }


    if(id != 'changeEvent') {
      ReactDOM.render(<EventElement key={`${id}`}  eventObj={{...info,isfullScreen:this.props.isfullScreen}}/>, el)
    }

    if (type == 1 && appointmentId) {
      let activeKey = this.getUrlParamsActiveKey();
      if(activeKey == appointmentId){ // 圈住改约中当前预约的逻辑
        element.addClass('checkedEvent');
        this.setHalfScreenChangeContractPageScrollTop()
      }
      if(this.clickAppintmentId &&  this.clickAppintmentId == appointmentId && appointmentStatus != 10){
        element.addClass('checkedEvent')
      }
      //if (isSettlement == 0 && isComeVisit == 0){
      if (isShuangAbout == 0) {
        element.append(`<div class='fc-resizer fc-end-resizer'></div>`)
      }
      //}
    }

    // 其他占用时间展示
    if(type == 4){
      element.addClass('eventOtherEvent')
      ReactDOM.render(<EventElement  key={`${id}`} eventPersonalTimeClose={this.eventPersonalTimeClose} eventObj={info}/>, el)
      //if(doctorIdentificationState) {  // 客服登录不可以下拉拖拽占用时间
      //客服或医生登录都可下拉拖动
      element.append(`<div class='fc-resizer fc-end-resizer'></div>`)
      //}
    }

    /*if(EventData){
      element.hide()
    }*/

    // 判断是否可拖动类型样式
    /*if(operationType == 0 || (type != 1 &&  type != 2 && type != 6)){
      element.css({cursor:'no-drop'})
    }*/
  }

  /**
   * 获取
   */
  getResourcesItem=(id)=>{
    let resList = this.getResources()
    if (id) {
      return resList.find((res) => {
        return res.id == id
      })
    }else {
      return null
    }

    /*let calendarTableList = this.state.calendarTableList;
    /!**
     * staffStatus,
     * type,
     * workStatus,
     * workTimeEnd,
     * workTimeStart,
     * date,
     * doctorId,
     * groupId,
     * name,
     * organizationName
     *!/
    if(id) {
      return calendarTableList.find((res) => {
        if (res.groupId == id) {
          return res
        }
      })
    }else {
      return null
    }*/
  };


  /**
   * 从外部事件拖动回调
   * @param info
   */
  eventReceive=(info)=>{
    const { event } = info || {}
    const { _def:def } = event || {}
    const { resourceIds } = def
    const resourceId = Array.isArray(resourceIds) && resourceIds.length != 0 ? resourceIds[0] : null
    let resourcesItem = this.getResourcesItem(resourceId)
    console.log('resourcesItem 123123 :: ',resourcesItem);
    this.props.onEventReceive && this.props.onEventReceive({
      info,
      resourcesItem
    })
  }

  /**
   * 开始拖拽
   * @param info
   */
  eventDragStart=(info)=>{
    this.props.onEventDragStart && this.props.onEventDragStart(info)
    if(this.tooltip){
      this.tooltip.dispose();
      this.tooltip = null
    }
  }

  /**
   * 拖拽是否允许回调
   * @param dropInfo
   * @param draggedEvent
   */
  eventAllow=(dropInfo, draggedEvent)=>{


    const { resource } = dropInfo || {};
    const { extendedProps,id:resId } = resource || {}
    const { operationType,workStatus,type,doctorId,organizationStatus,chairStatus } = extendedProps || {};
    const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1  // 1医生  2非医生

    const { extendedProps:draggedEventExtendedProps } = draggedEvent;
    const {
      type:draggedEventExtendedPropsType,
      listIndex,
      notAllow
    } = draggedEventExtendedProps;

    // 该预约是否禁止拖入
    if (notAllow) {
      return false
    }
    // 为waitingList 占用位置留出拖拽空间 不影响日历拖拽冲突问题
    if (listIndex == 'right') {
      if(resId == 6 || resId == 7) {
        return false
      }
    }

    if(listIndex == 'left') {
      if(resId == 1 || resId == 2) {
        return false
      }
    }



    // 外部椅子位列禁止拖拽
    if(organizationStatus == 2 || workStatus == 5){
      return false
    }

    if (doctorIdentificationState) { // 医生登录
      return operationType == 1
    }else { // 客服登录  客服登录不可以拖拽个人占用时间 && draggedEventExtendedPropsType != 4
      if(draggedEventExtendedPropsType == 4){
        // 拖拽个人占用时间 不可拖拽到其他医生下 只可拖拽到当前医生下的所有椅位置
        const { occupyTimeDto } = draggedEventExtendedProps || {}
        const { resourceId:EventResourceId } = occupyTimeDto || {};
        return operationType == 1 && chairStatus == 1 && EventResourceId == doctorId
      }
      return operationType == 1 && chairStatus == 1
    }
  }

  upDataCalendar=()=>{
    let calendarApi = this.calendarRef && this.calendarRef.current && this.calendarRef.current.getApi();
    if(calendarApi) {
      calendarApi.render();
      calendarApi.rerenderEvents();
      let changeEvent = calendarApi.getEventById('changeEvent');
      changeEvent && changeEvent.remove()
    }
  }

  upDataEventById=(id,extendedProps)=>{
    console.log('123123123upDataEventById :: ',id, extendedProps);
    let calendarApi = this.calendarRef.current.getApi()
    let changeEvent = calendarApi.getEventById(id)
    //extendedProps&&extendedProps.appointmentInfoOfTableDto.patientInfoDto.isFirstVisit,
    const { appointmentInfoOfTableDto } = extendedProps || {}
    const { appointmentIconDto,patientInfoDto } = appointmentInfoOfTableDto || {}
    const { vipClient } = appointmentIconDto || {}
    const { vipGrade } = patientInfoDto || {}
    if (vipClient != vipGrade) {
      if(extendedProps && appointmentInfoOfTableDto && patientInfoDto) {
        extendedProps.appointmentInfoOfTableDto.patientInfoDto.vipGrade = vipClient
      }
    }
    changeEvent && changeEvent.setProp('extendedProps',{
      ...extendedProps
    })
  }


  upDataEventByIdByRemark=(id,remark)=>{
    let calendarApi = this.calendarRef.current.getApi()
    let EventLists = calendarApi.getEvents()
    let eventObj = EventLists.find((itemByEventLists)=>{
      const { extendedProps } = itemByEventLists || {}
      const { appointmentInfoOfTableDto } = extendedProps || {}
      const { appointmentId } = appointmentInfoOfTableDto || {}
      return appointmentId == id
    })

    let extendedPropsByEventObj = null;
    if (!!eventObj && eventObj.extendedProps && eventObj.extendedProps.appointmentInfoOfTableDto) {
      // eventObj.extendedProps.appointmentInfoOfTableDto.remark
      extendedPropsByEventObj = {
        ...eventObj.extendedProps,
        appointmentInfoOfTableDto:{
          ...eventObj.extendedProps.appointmentInfoOfTableDto,
          remark:remark,
        }
      }
    }
    if(!!extendedPropsByEventObj) {
      eventObj && eventObj.setProp('extendedProps', {
        ...extendedPropsByEventObj
      })
    }
  }

  clearTooltip=()=>{
    this.closeTooltip()
    this.closeInfoModel()
    /*if (this.tooltip) {
      this.tooltip.hide()
      this.tooltip.dispose();
      this.tooltip = null
    }*/
  }

  closeInfoModel=()=>{
    this.setState({
      modelVisible:false
    })
  }

  getUrlParamsActiveKey=()=>{
    let href = window.location.href;
    let regArr = ['/subscribe/therearepatient','/customerservice/subscribe/appointment']
    let urlState = false
    regArr.forEach((res)=>{if (href.indexOf(res) != -1){urlState = true}})
    if (urlState) {
      // 首先判断当前页面是否输入半屏改约页面
      //let urlIndex = href.indexOf('therearepatient')
      let urlIndexParams = href.indexOf('?')
      let urlParamsActiveKey = serilizeURL(href).activeKey
      return urlParamsActiveKey
    }else {
      return null
    }
  }

  // 刷新列表方法
  updateStatus = () =>{
    console.log('index2 -- updateStatus');
    this.props.updateStatuss && this.props.updateStatuss()
    // this.upDataEventById('1',);
  };

  /**
   * 搜索结果定位添加
   * */
  setPageScrollTop=()=> {
    let checkEvent = this.state.EventList.find((event)=>{return event.checked == 1});
    if (checkEvent && checkEvent.start ) {

      let minTiem = '00:00:00';
      let eventStartTiem = moment(checkEvent.start).format('HH:mm:ss');
      let tiemDiffNum = moment(eventStartTiem,'HH:mm:ss').diff(moment(minTiem, 'HH:mm:ss'), 'minute');

      if (tiemDiffNum >= 0){
        let scrollTopNum = (tiemDiffNum / 15) * 32;
        $('.FullCalendarWarp').animate({
          scrollTop:scrollTopNum
        })
      }
    }
  };

  /**
   * 半屏定位
   * Half-screen ChangeContract
   */
  setHalfScreenChangeContractPageScrollTop=()=>{
    if(this.shouldHalfScreenChangeContractPageScrollTop) {
      this.shouldHalfScreenChangeContractPageScrollTop = false;
      let activeKey = this.getUrlParamsActiveKey();
      let checkEvent = this.state.EventList.find((event) => {
        if (event.appointmentInfoOfTableDto) {
          return event.appointmentInfoOfTableDto.appointmentId == activeKey
        }
      });
      if (checkEvent && checkEvent.start) {
        let minTiem = this.state.OtherUp ? '00:00:00' : minTime();
        let eventStartTiem = moment(checkEvent.start).format('HH:mm:ss');
        let tiemDiffNum = moment(eventStartTiem, 'HH:mm:ss').diff(moment(minTiem, 'HH:mm:ss'), 'minute');

        if (tiemDiffNum >= 0) {
          let scrollTopNum = (tiemDiffNum / 15) * 32;
          $('.FullCalendarWarp').animate({
            scrollTop: scrollTopNum
          })
        }
      }/*else {
        // 如果当前无定位时间默认定位到8点
        let eventStartTiem = moment('08:00:00','HH:mm:ss').format('HH:mm:ss');
        let tiemDiffNum = moment(eventStartTiem, 'HH:mm:ss').diff(moment(minTiem, 'HH:mm:ss'), 'minute');

        if (tiemDiffNum >= 0) {
          let scrollTopNum = (tiemDiffNum / 15) * 32;
          $('.FullCalendarWarp').animate({
            scrollTop: scrollTopNum
          })
        }
      }*/
    }
  };

  /**
   * 滚动到指定空白区域
   */
  setFreeTimePositionPageScrollTop=()=>{
    console.log('setFreeTimePositionPageScrollTopthis.state.EventList :: ',this.state.EventList,this.getEventList());
    const { appointmentCalendarMode } = this.props;
    const { onTimeBlankClickByFull:checkBlack } = appointmentCalendarMode || {};
    const { doctorId,date,chairNum,startTime } = checkBlack || {};

    if(!this.props.isfullScreen) {
      if (!!startTime){
          let minTiem = '00:00:00'; // : minTime()
          let eventStartTiem = moment(startTime).format('HH:mm:ss');
          let tiemDiffNum = moment(startTime, 'HH:mm:ss').diff(moment(minTiem, 'HH:mm:ss'), 'minute');
        console.log('tiemDiffNumtiemDiffNum :: ',eventStartTiem,tiemDiffNum);
        if (tiemDiffNum >= 0) {
            let scrollTopNum = (tiemDiffNum / 15) * 32;
            $('.FullCalendarWarp').animate({
              scrollTop: scrollTopNum
            })
          }
        }else {
            let minTiem = '00:00:00'; // : minTime()
            let eventStartTiem = moment("08:00:00", "HH:mm:ss").format('HH:mm:ss');
            console.log('setFreeTimePositionPageScrollTop-eventStartTiem :: ', eventStartTiem);
            let tiemDiffNum = moment(eventStartTiem, 'HH:mm:ss').diff(moment(minTiem, 'HH:mm:ss'), 'minute');

            if (tiemDiffNum >= 0) {
              let scrollTopNum = (tiemDiffNum / 15) * 32;
              $('.FullCalendarWarp').animate({
                scrollTop: scrollTopNum
              })
            }
        }
      }
  };

  /**
   * 滚动到当前时间CurrentTime
   */
  setCurrentTimePositionPageScrollTop=()=>{
    let PositionTime = getCurrentPositionDateTime(moment().format())
    let PositionTimeDate = moment(PositionTime).format('HH:mm:ss')
    if (PositionTimeDate) {
      let minTiem = '00:00:00';
      let eventStartTiem = PositionTimeDate;
      let tiemDiffNum = moment(eventStartTiem, 'HH:mm:ss').diff(moment(minTiem, 'HH:mm:ss'), 'minute');

      if (tiemDiffNum >= 0) {
        let scrollTopNum = (tiemDiffNum / 15) * 32;
        $('.FullCalendarWarp').animate({
          scrollTop: scrollTopNum
        })
      }
    }
  };

  /**
   * 滚动到指定位置
   */
  setPageScrollTopByIndex=(scrollTopNum)=>{
    setTimeout(()=>{
      $('.FullCalendarWarp').animate({
        scrollTop:scrollTopNum
      })
    },100)
  };
  /**
   * 点击展示营业外时间 上时间
   */
  onClickOtherUp=()=>{
    this.setState({OtherUp:!this.state.OtherUp},()=>{
      this.setPageScrollTopByIndex(0)
    })
  };

  /**
   * 点击展示营业外时间 下时间
   */
  onClickOtherDown=()=>{
    this.setState({OtherDown:!this.state.OtherDown},()=>{
      let setPageEndScrollTop = $('.FullCalendarWarp').find('.fc-view-container').height() - $('.FullCalendarWarp').height()
      this.setPageScrollTopByIndex(setPageEndScrollTop)
    })
  };


  // 区分半屏全屏 半屏不可使用页面拖拽
  getEventList=()=>{
    console.log('this.state.EventListthis.state.EventList1231 :: ',this.state.EventList);
    if(this.props.isfullScreen) {
      return this.state.EventList
    }else {
      return this.state.EventList.map((res)=>{
        let {
          appointmentInfoOfTableDto,
          operationType,        // 判断当前预约是否可编辑 0:不可编辑  1:可编辑
        } = res;
        let {
          isComeVisit,         // 0未到诊 1已到诊
          isLate,              // 过预约时间未到诊 0没有迟到  1迟到
          isSettlement,        // 结算状态 0:未结算 1:已结算
          isConsultationVisit, // 0:未接诊 1:已接诊
          isShuangAbout        // 是否爽约 0:未爽约 1:爽约
        } = appointmentInfoOfTableDto || {}

        if(res.type == 1){
         return {
           ...res,
           startEditable: false,
           resourceEditable: false,
           durationEditable: operationType == 1 && isComeVisit == 1 && isShuangAbout != 1,
           editable:false
         }
        }else  {
          return res
        }
      })
    }
  }


  /**
   * ---------------------------------------------生命周期---------------------------------------------
   * @param props
   */
  constructor(props) {
    super(props);
    this.windowResize = debounce(this.windowResize,500);
    this.setHighlightedResources = _.debounce(this.setHighlightedResources,500);
    this.setTimeHour = _.debounce(this.setTimeHour,500);
    this.setFreeTimePositionPageScrollTop = _.debounce(this.setFreeTimePositionPageScrollTop,300);
    // this.refreshBespeakComponent = _.debounce(this.refreshBespeakComponent,500);
    this.state = {
      calendarTableList:[...props.calendarTableList],
      EventList:[...props.EventList],
      contrastCalendarTableList:[...props.calendarTableList],  // 用于判断是否需要更新组件的原数据
      contrastEventList:[...props.EventList],                  // 用于判断是否需要更新组件的原数据

      visibleAppointmentParticulars :false,           // 弹窗状态是否展示
      visibleAppointmentParticularsStyle:{},          // 弹窗定位样式
      currentClickEvent:{},                           // 当前点击的预约数据
      showFullCalendar:true,                          // 展示预约数据,
      AgendaHeadType:null,                            // 回显列表头类型数据
      commonOption:{},                                // 时间颗粒度改变
      appointmentPageInfo:{},                         // 预约列表分页信息
      OtherUp:false,                                  // 是否展示预约时间之外的上时间
      OtherDown:false,                                // 是否展示预约时间之外的下时间
      modelVisible:false,                             // 弹窗展示
      clickEvent:null,                                // 点击的EventId
      heightDesk:null,                                // 单元格高度获取
      tooltipType: false,                             // 预约详情小窗展示状态
    };
    this.calendarRef = React.createRef();
  }

  dayRender=({date,el,view})=>{}

  datesRender=({el,view})=>{
    $('.fc-scroller').css({ overflow:'hidden' })
    this.setTimeHour()
  }

  setTimeHour=()=>{
    $('.fc-slats tr').each((idx,valEl)=>{
      let dataTime = $(valEl).attr('data-time');
      let mm = moment(dataTime, 'HH:mm:ss').minute()
      //$(valEl).css({height: '58.8px'})
      if(mm == 0){
        if (dataTime != '00:00:00' && dataTime != '24:00:00') {
          $(valEl).find('td.fc-widget-content').css({
            'border-top-color': '#DBDBDB',
            'border-top-width': '1px',
            'border-top-style': 'solid',
          }).addClass('timeHour')
        }
      }
    })

    let heightDesk = $('.fc-slats tr').eq(1).height()
    this.heightDesk = heightDesk
    //this.setState({ heightDesk:heightDesk })
  };

  // 点击患者 详情中 加入等待列表 床底数据

  lineUpListModal = (patientInfoDto,complaintList,objAppointment,type,that) =>{
    this.props.TimeAgendaCalendarLineUpListModal&&this.props.TimeAgendaCalendarLineUpListModal(patientInfoDto, complaintList,objAppointment,type,that)
  };


  // 详情 关闭 弹窗 状态

  modelVisibles = ()=>{
    this.AppointmentParticulars&&this.AppointmentParticulars.closeInfoModel()
  };

  render(){
    let href = window.location.href;
    let hightByTitleList = this.props.isfullScreen ? '45' : '38'
    let customerserviceState = href.indexOf('customerservice') != -1
    let Option = {
      ...this.props.Option,
      // maxTime: this.state.OtherDown ? '24:00:00' : this.props.Option.maxTime,
      // minTime: this.state.OtherUp ? '00:00:00' : this.props.Option.minTime,
      maxTime: '24:00:00',
      minTime: '00:00:00'
    }
    const {current, pageSize, total, pageCount } = this.props.appointmentPageInfo
    return (
      <div id={`${this.props.fullScreenOrhalfSize}`}>
        <div id="AppintmentCalendar" className={'AppintmentCalendar'}>
          {this.state.calendarTableList.length > 0 &&
          <div className={classNames(styles.TimeAgendaCalendar,'TimeAgendaCalendarTitleList')}>
            <div className={styles.titleList}>

              <div style={{height: `${hightByTitleList}px`,borderBottom: '1px solid #ccc'}}>
                <img src={doctorIcon} className={styles.doctorIcon}/>
              </div>
              <div style={{height: '31.9px'}}>
                <img src={seatIcon} className={styles.seatIcon}/>
              </div>
            </div>
            <div className={classNames({
              [styles.titleMiddle]:true,
              [styles.middleNotRight]:!this.props.isShowDeskRight
            })}>
              <TimeCalendarHeader
                resources={this.getResources()}
                calendarTableList={this.state.calendarTableList}
                AgendaHeadType={this.props.AgendaHeadType}
                appointmentPageInfo={this.props.appointmentPageInfo}
                goLast={this.goLastIcon}
                goNext={this.goNextIcon}
                onClickTitleForDoctor={this.props.onClickTitleForDoctor}
                onClickTitleForData={this.props.onClickTitleForData}
                isWaitingInfo={this.props.isWaitingInfo}            // 是否展示日历详情信息
                onClickWaitingInfo={this.props.onClickWaitingInfo}  // 点击日历
                isfullScreen={this.props.isfullScreen}              // 是否是全屏页面
              />
            </div>
            {this.props.isShowDeskRight && <div className={classNames('titleRight',styles.titleRight)}>
              <div className="">
                <div style={{height: '45px',borderBottom: '1px solid #ccc'}}>
                  <img src={doctorIcon} className={styles.doctorIcon}/>
                </div>
                <div style={{height: '31.9px'}}>
                  <img src={seatIcon} className={styles.seatIcon}/>
                </div>
              </div>
            </div> }
          </div>
          }
          {this.state.calendarTableList.length == 0 &&
          <div className={styles.TimeAgendaCalendar}>
            <div className={styles.TimeAgnedaNotData}>
              <img src={doctorIcon} className={styles.doctorNotDataIcon}/>
            </div>
          </div>
          }

          {/*<Spin spinning={!!this.props.loading.effects['TimeCalendar/appointmentTimeLengthAlter']}>*/}
          {/*<div onClick={()=>{*/}
          {/*  this.onClickOtherUp()*/}
          {/*}} className={classNames({*/}
          {/*  [styles.otherUp]:true,*/}
          {/*  [styles.otherActive]:this.state.OtherUp*/}
          {/*})}>*/}
          {/*  <div className={styles.otherContent}>营业时间外预约</div>*/}
          {/*  <div className={styles.otherContent}><span className={styles.redText}>{*/}
          {/*    moment(minTime(),'HH:mm:ss').format("HH:mm")*/}
          {/*  } 前: {*/}
          {/*    this.props.appointmentCountBeforeWorkTime ? this.props.appointmentCountBeforeWorkTime : 0*/}
          {/*  }人</span></div>*/}
          {/*  <div className={styles.otherContent}>{*/}
          {/*    moment(maxTime(),'HH:mm:ss').format('HH:mm')*/}
          {/*  } 后: {this.props.appointmentCountAfterWorkTime ? this.props.appointmentCountAfterWorkTime : 0}人</div>*/}
          {/*  <div className={classNames({*/}
          {/*    [styles.doubleUp]:true,*/}
          {/*    [styles.doubleDownToUp]:this.state.OtherUp,*/}
          {/*    [styles.doubleUpToDown]:!this.state.OtherUp,*/}
          {/*  })}><Icon type="double-right" /></div>*/}
          {/*</div>*/}
          <div className={styles.BoxWarp}>
            { current > 1 &&  <div onClick={this.goLastIcon} className={styles.goToLast}></div> }
            { current < pageCount && <div onClick={this.goNextIcon} className={styles.goToNext}></div> }
            <div id="scrollBox" className={commonStyles.scrollBox}>
              <div className={classNames({
                [commonStyles.scroll]:true,
                [commonStyles.calendar]:true,
                'FullCalendarWarp':true,
              },)} style={{width: `calc(100% + ${this.scrollbarWidth}px)`}}
              >
                {this.state.showFullCalendar &&
                <div className={styles.fullCalendarContentWarp}>
                  <div className={styles.contentList}>
                    {/*左侧Desk时间栏*/}
                    <CalendarDesk
                      Option={{...Option}}
                      heightDesk={this.heightDesk}
                    />
                  </div>
                  <div className={classNames({
                    [styles.contentMiddle]:true,
                    [styles.middleNotRight]:!this.props.isShowDeskRight
                  })}>
                    <FullCalendar
                      ref={this.calendarRef}
                      id={`${this.props.calendarId}`}
                      defaultView="resourceTimeGridDay"
                      height={'auto'}
                      contentHeight={'auto'}
                      className={classNames(commonStyles.calendar)}
                      style={{maxHigth: '900px', margin: '0 auto'}}
                      plugins={[resourceTimeGridPlugin, interactionPlugin]}
                      header={false}
                      allDaySlot={false}
                      resources={this.getResources()}
                      events={this.getEventList()}
                      schedulerLicenseKey={schedulerLicenseKey}
                      snapDuration={'00:05:00'}
                      slotLabelFormat={{
                        hour: 'numeric',
                        minute: '2-digit',
                        hour12: false,
                        omitZeroMinute: false,
                        meridiem: false
                      }}
                      eventTimeFormat={{
                        hour: 'numeric',
                        minute: '2-digit',
                        meridiem: false,
                        hour12: false,
                      }}
                      editable={true}
                      droppable={true}
                      now={(info) => {return moment().subtract(1, 'year').format('YYYY-MM-DD')}}
                      {...Option}
                      slotEventOverlap={false} // 禁止重叠Event
                      eventRender={this.eventRender}   // 渲染Event时间回调函数
                      eventDrop={this.eventDrop}
                      eventResize={this.eventResize}
                      eventDragStop={(info)=>{}}
                      eventOverlap={this.eventOverlap}
                      eventClick={this.eventClick}
                      dayRender={this.dayRender}
                      //viewSkeletonRender={this.dayRender}
                      datesRender={this.datesRender}
                      //resourceRender={this.datesRender}
                      dateClick={this.dateClick}
                      //windowResize={this.windowResize}
                      eventDragStart={this.eventDragStart}
                      eventReceive={this.eventReceive}
                      eventAllow={this.props.eventAllow ? this.props.eventAllow : this.eventAllow}

                    />
                  </div>
                  {this.props.isShowDeskRight &&
                  <div className={styles.contentRight}>
                    {/*右侧Desk时间栏*/}
                    <CalendarDesk
                      Option={{...Option}}
                      isRight={true}
                      heightDesk={this.heightDesk}
                    />
                  </div>
                  }
                </div>
                }
              </div>
            </div>
          </div>
          {/*<div onClick={this.onClickOtherDown} className={classNames({*/}
          {/*  [styles.otherDown]:true,*/}
          {/*  [styles.otherActive]:this.state.OtherDown*/}
          {/*})}>*/}
          {/*  <div className={styles.otherContent}>营业时间外预约</div>*/}
          {/*  <div className={styles.otherContent}>{*/}
          {/*    moment(minTime(),'HH:mm:ss').format('HH:mm')*/}
          {/*  } 前: {*/}
          {/*    this.props.appointmentCountBeforeWorkTime*/}
          {/*  }人</div>*/}
          {/*  <div className={styles.otherContent}><span className={styles.redText}>{*/}
          {/*    moment(maxTime(),'HH:mm:ss').format('HH:mm')*/}
          {/*  } 后: {*/}
          {/*    this.props.appointmentCountAfterWorkTime*/}
          {/*  }人</span></div>*/}
          {/*  <div className={classNames({*/}
          {/*    [styles.doubleDown]:true,*/}
          {/*    [styles.doubleDownToUp]:!this.state.OtherDown,*/}
          {/*    [styles.doubleUpToDown]:this.state.OtherDown,*/}
          {/*  })}><Icon type="double-right" /></div>*/}
          {/*</div>*/}
          {/*</Spin>*/}
          {/* 全屏预约点击详情功能弹窗 */}
          <div>
            <div ref='foo'>
              {!customerserviceState &&
              <AppointmentParticulars
                className={'eventShowPopper'}
                arrival={this.props.arrival} // 点击到诊
                currentClickEvent={this.state.currentClickEvent}
                //visible={this.state.modelVisible}
                onRef={(that)=>{this.AppointmentParticulars = that}}
                upDataEventById={this.upDataEventById}
                updateStatus={()=>{this.updateStatus()}}
                clearTooltip={()=>{this.clearTooltip()}}
                handleCancel={()=>{this.clearTooltip()}}
                closeInfoModel={()=>{this.closeInfoModel()}}
                lineUpListModal={this.lineUpListModal}
                cancelModalClick={this.props.cancelModalClick}
                maximizeAppointmentDetails={this.props.maximizeAppointmentDetails}
                changeContract={this.props.changeContract} // 改约点击
              >
              </AppointmentParticulars>
              }
            </div>
          </div>

          <div className={styles.LineAppointmentWarp}>
            <div className={styles.lineAppintTimeBar}></div>
            {
              Array.isArray(this.getResources()) &&
              this.getResources().map((item)=>{
                const { doctorId,doctorUserId } = item || {}
                return (
                  <div
                    doctorId={doctorId}
                    doctorUserId={doctorUserId}
                    className={classNames({
                      [styles.lineAppintByRes]:true,
                      'lineAppintByRes':true
                    })}/>
                  )
              })
            }

            {this.props.isfullScreen &&
              <div className={styles.lineAppintTimeBar}></div>
            }
          </div>

          {/*<div className={classNames({[styles.poppperWarp]:true})}>
            <div ref='foo'>
              {!customerserviceState &&
                <AppointmentParticulars
                  className={'eventShowPopper'}
                  currentClickEvent={this.state.currentClickEvent}
                  onRef={(that)=>{this.AppointmentParticulars = that}}
                  upDataEventById={this.upDataEventById}
                  updateStatus={()=>{this.updateStatus()}}
                  clearTooltip={()=>{this.clearTooltip()}}
                  handleCancel={()=>{this.clearTooltip()}}
                >
                </AppointmentParticulars>
              }

              {customerserviceState &&
              <AppointmentParticularsCS
                className={'eventShowPopper'}
                currentClickEvent={this.state.currentClickEvent}
                onRef={(that) => {this.AppointmentParticulars = that}}
                updateStatus={() => {this.updateStatus()}}
                clearTooltip={() => {this.clearTooltip()}}
                handleCancel={() => {this.clearTooltip()}}
              >
              </AppointmentParticularsCS>
              }
            </div>
          </div>*/}

        </div>
      </div>
    )
  }


  componentDidMount() {
    const _this = this;
    this.props.onRef && this.props.onRef(this);
    this.bindClickForCalendar()
    this.firstLoad = true // 是否是第一次加载
    this.refreshBespeakComponent(this.props);
    this.scrollbarWidth = getScrollbarWidth()
    window.addEventListener('resize',this.onWindowResize);
    //this.setPageScrollTop()
    this.setFreeTimePositionPageScrollTop();
    this.onChangeRemarkByAppointmentInfoSmallWindow()
  }

  // 切换预约详情的备注
  onChangeRemarkByAppointmentInfoSmallWindow=()=>{
    eventBus.on(CHANGE_RemarkByAppointmentInfoSmallWindow,(value)=>{
        const {
          appointmentId,
          remark,
        } = value || {}
      console.log('CHANGE_RemarkByAppointmentInfoSmallWindow',{appointmentId,
        remark});
      this.upDataEventByIdByRemark(appointmentId,remark)
    })
  }


  shouldComponentUpdate(nextProps,nextState){
    // 判断当前组件是否需要更新 防止内存溢出
    let stateDlend = true

    // 判断如果预约list相同无需更新
    if (Array.isArray(nextProps.EventList)) {
      let nextPropsList = nextProps.EventList
      let stateEventList = this.props.EventList;

      let map1 = Immutable.fromJS(nextPropsList);
      let map2 = Immutable.fromJS(stateEventList);

      let appointmentPageInfoImmutable = nextProps.appointmentPageInfo;
      let appointmentPageInfoStateImmutable = this.props.appointmentPageInfo;

      stateDlend = !Immutable.is(map1, map2);
      stateDlend = !Immutable.is(appointmentPageInfoImmutable, appointmentPageInfoStateImmutable);
    }
    // 判断如果头部列表相同无需更新

    // 判断如果切换了事件颗粒度则需要更新
    //let nextslotLabel = nextProps.Option.slotLabelInterval
    //let currentSlotLabel = this.state.commonOption.slotLabelInterval
    //if(nextslotLabel != currentSlotLabel){ stateDlend = true }

    let loading = this.props.loading.effects['TimeCalendar/appointmentTimeLengthAlter']
    let nextloading = nextProps.loading.effects['TimeCalendar/appointmentTimeLengthAlter']
    if(loading != nextloading){stateDlend = true}

    // 点击营业以外时间
    if (nextState.OtherUp != this.state.OtherUp)     {stateDlend = true}
    if (nextState.OtherDown != this.state.OtherDown) {stateDlend = true}

    //
    if (nextState.tooltipType != this.state.tooltipType) {stateDlend = true}
    if(this.firstLoad){ stateDlend = true }
    this.firstLoad = false  // 将第一次加载状态置为否
    return stateDlend
  }


  componentWillReceiveProps(nextProps){
    this.refreshBespeakComponent(nextProps);
  }
  /**
   * 刷新列表组件方法
   */
  refreshBespeakComponent=(nextProps)=>{
    console.log('nextProps123123 :: ',nextProps);
    let calendarTableList = nextProps.calendarTableList;
    let resources = [];                         // 椅位资源列
    let EventListPlue = [];
    let groupTotal = Array.isArray(nextProps.calendarTableList) ? calendarTableList.length : 0;  // 医生总数
      if (Array.isArray(calendarTableList)) {
      calendarTableList.forEach((val, idx) => {
        if (val.resources) {
          val.resources.forEach((res, resIdx) => {
            const {
              staffStatus,
              chairTotal,
              type,
              workStatus,
              workTimeEnd,
              workTimeStart,
              date,
              doctorId,
              groupId,
              name,
              organizationName,
              operationType,
              doctorUserId,
            } = val || {}

            resources.push({
              ...res,
              chairTotal,
              workStatus:res.organizationStatus == 2 ? 5 : workStatus,
              workTimeEnd,
              workTimeStart,
              staffStatus,
              type,
              date,
              doctorId,
              groupId,
              name,
              organizationName:res.organizationStatus == 2 ? res.organizationName : organizationName,
              operationType,
              doctorUserId
            });
          })
        }
      })
    }
    let tableList = Array.isArray(nextProps.calendarTableList) ? [...nextProps.calendarTableList] : []
    let {
      current,
      pageCount,
      pageSize,
      total,
    } = nextProps.appointmentPageInfo || {}

    let surplusNum = 0;
    if(pageCount && pageSize) {
      surplusNum = pageSize - resources.length;
    }
    let surplusObj = null;
    if(surplusNum > 0){
      let surplusReslist= []
      for (let x = 1;x <= surplusNum; x++){
        surplusReslist.push({
          chairNum: x,
          id: `surplusNum_${x}`,
          title: null,
        })
      }

      /**
       date: "2019-07-24"
       doctorId: 53
       doctorUserId: 3304
       groupId: 1
       name: "岳丽丽医生"
       operationType: 1
       organizationName: null
       resources: (2) [{…}, {…}]
       staffStatus: "0"
       type: 1
       workStatus: 1
       workTimeEnd: null
       workTimeStart: null
       * */

      /**
       * chairNum: 1
       id: 3
       title: "椅位1"
       * @type {{date: null, doctorId: null, doctorUserId: null, groupId: string, name: null, operationType: number, organizationName: null, resources: Array, staffStatus: string, type: number, workStatus: number, workTimeEnd: null, workTimeStart: null}}
       */
      surplusObj = {
        date: null,
        doctorId: null,
        doctorUserId: null,
        groupId: 'suplus',
        name: null,
        operationType: 1,
        organizationName: null,
        resources: surplusReslist,
        staffStatus: "0",
        type: 1,
        workStatus: 5,
        workTimeEnd: null,
        workTimeStart: null,
      }
    }

    let surplusBackageEvent = []
    surplusObj && surplusObj.resources.forEach((val)=>{
      surplusBackageEvent.push({
        rendering: 'background',
        id: 'surplusEvent' + val.id,
        start: moment('00:00:00','HH:mm:ss').format(),
        end: moment('24:00:00','HH:mm:ss').format(),
        titleInfo: '',
        title: '',
        resourceId:val.id,
        resourceIdValue: val.id,
        backgroundColor: '#DCDCDC',
        className:'surplusBackgroundEvent'
      })
    })

    /**
     * 补位列和Event添加
     */
    let surplusList = nextProps.calendarTableList
    let surplusEventList = nextProps.EventList
    if (surplusObj && nextProps.calendarTableList){
      surplusList = [...nextProps.calendarTableList,surplusObj]
      surplusEventList = [...nextProps.EventList,...surplusBackageEvent]
    }

    // 添加其他机构椅位置灰
    let resList = this.setDayOffEvent(resources);
    //let restdayEventlist = this.setRestdayEvent(resources);


    this.setState({
      commonOption:{
        ...this.state.commonOption,
        ...nextProps.Option,
      },
      calendarTableList:surplusList,
      EventList:[...surplusEventList,...resList],
      contrastCalendarTableList:nextProps.calendarTableList,  // 用于判断是否需要更新组件的原数据
      contrastEventList:nextProps.EventList,                  // 用于判断是否需要更新组件的原数据
      option:nextProps.Option,
      AgendaHeadType:nextProps.AgendaHeadType,
      appointmentPageInfo:nextProps.appointmentPageInfo,

    },()=>{

      setTimeout(()=>{
        // 为dom重新渲染预留划线时间
        this.clearHighlightedResources()
        this.setTimeHour()
        this.setHighlightedResources()
        // this.setFreeTimePositionPageScrollTop()
      },100)
    })
  };

  // 清空高亮的先
  clearHighlightedResources=()=>{
    // .fc-body
    $(`#${this.props.fullScreenOrhalfSize} .fc-body`).css({ "border-left": "#ccc solid 1px" })
    $(`#${this.props.fullScreenOrhalfSize} .fc-body`).css({ "border-right": "#ccc solid 1px" })
    // .lineAppintByRes
    $(`.lineAppintByRes`).each((item, value) => {
      $(value).css({ "background": "transparent","border-bottom":"none"})
    })
    // .fc-resource-cell
    $(`#${this.props.fullScreenOrhalfSize} .fc-resource-cell`).each((item, value) => {
      $(value).css({ "border-left": "#ccc solid 1px" })
      $(value).css({ "border-right": "#ccc solid 1px" })
    })
    // .fc-day
    $(`#${this.props.fullScreenOrhalfSize} .fc-day`).each((item, value) => {
      $(value).css({ "border-left": "#ccc solid 1px" })
      $(value).css({ "border-right":"#ccc solid 1px" })
    })
    // .agendaTitileTh
    $(`#${this.props.fullScreenOrhalfSize} .agendaTitileTh`).each((item, value) => {
        $(value).css({ "border-left": "#ccc solid 1px" })
        $(value).css({ "border-right": "#ccc solid 1px" })
        $(value).css({ "border-top": "#ccc solid 1px" })
    })
  }

  // 设置高亮资源线传递需要圈中的资源
  setHighlightedResources=()=>{
    const { HighlightedResources:doctorID } = this.props
    // let doctorID = 41;

    console.log('1123123123setHighlightedResources ::  ',`#${this.props.fullScreenOrhalfSize}`,$(`#${this.props.fullScreenOrhalfSize}`)[0]);
    // 当前是半屏/并且有上次就诊医生id/一人多天情况下 蓝色边框圈住上次看诊医生资源
    if ($(`#${this.props.fullScreenOrhalfSize}`)[0] && doctorID && this.props.AgendaHeadType == 1) {
      const resList = this.getResources()
      let filterByResList = []
      if (Array.isArray(resList)) {
        filterByResList = resList.filter((resItem) => {
          const { doctorId, doctorUserId } = resItem || {}
          console.log('resItem :: ',resItem);
          return doctorUserId == doctorID
        })
      }
      if (filterByResList.length > 0) {
        if (filterByResList[0].id == 1) {
          $(`#${this.props.fullScreenOrhalfSize} .fc-body`).css({ "border-left": "rgba(66, 146, 255, 0.5) solid 2px" })
        }
        if (filterByResList[filterByResList.length - 1].id == 4) {
          $(`#${this.props.fullScreenOrhalfSize} .fc-body`).css({ "border-right": "rgba(66, 146, 255, 0.5) solid 2px" })
        }
        // .lineAppintByRes
        $(`#${this.props.fullScreenOrhalfSize} .lineAppintByRes`).each((item, value) => {
          if ($(value).attr('doctorUserId') == doctorID) {
            $(value).css({ "border-bottom": "rgba(66, 146, 255, 0.5) dashed 2px" })
          }
        })

        // agendaTitileTh .agendaTitileTh
        $(`#${this.props.fullScreenOrhalfSize} .agendaTitileTh`).each((item, value) => {
          if (doctorID == $(value).attr('doctorUserId')) {
            $(value).css({ "border-left": "rgba(66, 146, 255, 0.5) dashed 2px" })
            $(value).css({ "border-right": "rgba(66, 146, 255, 0.5) dashed 2px" })
            $(value).css({ "border-top": "rgba(66, 146, 255, 0.5) dashed 2px" })
          }
        })

        // .fc-resource-cell
        $(`#${this.props.fullScreenOrhalfSize} .fc-resource-cell`).each((item, value) => {
          if ($(value).attr('data-resource-id') == (filterByResList[0].id)) {
            $(value).css({ "border-left": "rgba(66, 146, 255, 0.5) dashed 2px" })
          }
          if ($(value).attr('data-resource-id') == filterByResList[filterByResList.length - 1].id) {
            $(value).css({ "border-right": "rgba(66, 146, 255, 0.5) dashed 2px" })
          }
        })

        // .fc-day
        $(`#${this.props.fullScreenOrhalfSize} .fc-day`).each((item, value) => {
          if ($(value).attr('data-resource-id') == filterByResList[0].id) {
            $(value).css({ "border-left": "rgba(66, 146, 255, 0.5) dashed 2px" })
          }
          if ($(value).attr('data-resource-id') == filterByResList[filterByResList.length - 1].id) {
            $(value).css({ "border-right": "rgba(66, 146, 255, 0.5) dashed 2px" })
          }
        })
      } else {
        this.clearHighlightedResources()
      }
    }
  }

  componentDidUpdate(){
    //this.setTimeHour()
  }

  //组件销毁回调
  componentWillUnmount(){
    if (this.tooltip) {
      this.tooltip.hide()
      this.tooltip.dispose();
      this.tooltip = null
    }
    let { dispatch } = this.props;
    dispatch({
      type:'TimeCalendar/save',
      payload:{
        appointmentInfoEventDtoList:[],        // ①预约列表展示 ,预约事件集合
        calendarTableDtoList:[],               // ①预约列表展示  , table列表椅位集合
        appointmentPageInfo:{},                //  预约列表分页信息
      }
    });
    $('body').unbind()
    window.removeEventListener('resize', this.onWindowResize);
    // 删除所有监听器

    this.setState = (state,callback)=>{
      return;
    };
  }
}
