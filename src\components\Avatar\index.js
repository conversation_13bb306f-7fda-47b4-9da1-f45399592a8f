import React, { Component } from 'react';
import styles from './index.less';

import ChildGirl from '@/assets/userAvatar/ChildGirl.png';          // 儿童女
import ChildBoy from '@/assets/userAvatar/ChildBoy.png';            // 儿童男
import YouthGirl from '@/assets/userAvatar/YouthGirl.png';          // 青年女
import YouthBoy from '@/assets/userAvatar/YouthBoy.png';            // 青年男
import MidlifeGirl from '@/assets/userAvatar/MidlifeGirl.png';      // 成年女
import MidlifeBoy from '@/assets/userAvatar/MidlifeBoy.png';        // 成年男
import OldageGirl from '@/assets/userAvatar/OldageGirl.png';        // 老年女
import OldageBoy from '@/assets/userAvatar/OldageBoy.png';          // 老年男

import UnknownBoy from '@/assets/userAvatar/UnknownBoy.png';        // 未知男
import UnknownGirl from '@/assets/userAvatar/UnknownGirl.png';      // 未知女          

import VipIcon from '@/assets/userAvatar/VipIcon.png';                  // vip icon图

class UserAvatar extends Component {
    state = {

    };

    // 计算患者处于儿童、青少年、成年、老年
    CalculateAge = (age) => {
        // return  1.儿童   2.青少年   3.成年   4.老年   5.未知

        if (age != undefined && age != null) {
            let v = age.toString();
            if (v == '儿童' || v == '未成年' || v == '未成年人' || v == '成年' || v == '成人' || v == '成年人' || v == '老年' || v == '老人' || v == '老年人' || v == '不详' || v == '未知') {
                const arr = ['未', '儿', '未', '成', '老', '不'];
                if (arr.indexOf(v.substring(0, 1) != -1)) {
                    return arr.indexOf(v.substring(0, 1)) == 0 ? 5 : arr.indexOf(v.substring(0, 1))
                }
            } else if (v.includes('岁以上')) {
                // 岁以上，只要数字
                const ageNum = parseInt(v.substring(0, v.indexOf('岁')));
                return ageNum >= 0 && ageNum < 12 ? 1 :
                    ageNum >= 12 && ageNum < 18 ? 2 :
                        ageNum >= 18 && ageNum < 60 ? 3 :
                            ageNum >= 60 ? 4 : 5;
            } else if (v.includes('岁') && v.includes('~')) {
                // 包含岁  并且包含~
                const pageArr = v.split('岁');
                const smallPage = parseInt(pageArr[0].split('~')[0]);
                const largePage = parseInt(pageArr[0].split('~')[1]);
                return smallPage >= 0 && largePage < 12 ? 1 :
                    smallPage >= 12 && largePage < 18 ? 2 :
                        smallPage >= 18 && largePage < 60 ? 3 :
                            largePage >= 60 ? 4 : 5;
            } else {
                const ageNum = parseInt(v);
                return ageNum >= 0 && ageNum < 12 ? 1 :
                    ageNum >= 12 && ageNum < 18 ? 2 :
                        ageNum >= 18 && ageNum < 60 ? 3 :
                            ageNum >= 60 ? 4 : 5;
            }
        } else {
            return 5
        }

    }
    render() {
        const { sex, age, isVip, width, height } = this.props;

        const GirlAvatars = ['', ChildGirl, YouthGirl, MidlifeGirl, OldageGirl, UnknownGirl];
        const BoyAvatars = ['', ChildBoy, YouthBoy, MidlifeBoy, OldageBoy, UnknownBoy];
        return (
            <div className={styles.AvatarContent}>
                <img
                    style={{ width: !!width ? width : 80, height: !!height ? height : 80 }}
                    src={!!sex && sex == 1 ? BoyAvatars[this.CalculateAge(age)] :
                        !!sex && sex == 2 ? GirlAvatars[this.CalculateAge(age)] : UnknownBoy}
                />
                {!!isVip && <img src={VipIcon} className={styles.AvatarContent_VipIcon} />}
            </div>
        );
    }
}

export default UserAvatar


