import {
  getDetail, getImage
} from '@/services/caseQualityDetail';


export default {
  namespace: 'caseQualityDetail',
  state: {},

  effects: {
    // 列表
    *getDetail({ payload }, { call }) {
      const response = yield call(getDetail, payload);
      return response;
    },
    // 获取图片
    *getImage({ payload }, { call }) {
      const response = yield call(getImage, payload);
      return response;
    },
   
  },

  reducers: {
   
  },
  subscriptions: {
    setup({ dispatch, history }) {

    }
  }
};
