import {Radio, Row, Col, Input, Tabs, Form, Checkbox, Select, Tag, Button, Modal, message} from 'antd';
import React, {Component} from 'react';
import styles from './style.less';//样式

import ToothShow from "@/components/ToothShow";//牙位展示
import {StringUtils} from "@/utils/StringUtils";//验证

//牙周检查
class Periodontal extends Component {
  constructor(props) {
    super(props)
    this.state = {
      update: false,//更新状态
      visibleToothBit2: false,//牙位状态2
      visibleToothBit: false,//牙位状态2
      visibleToothBit3: false,//牙位状态3
      visibleToothBit4: false,//牙位状态4
      odontolith: 0,//'牙周检查牙石等级 0未知；1/2/3等级',
      cleanStatus: 0,//'牙周检查洁治等级 0未知；1洁治前；2洁治后',
      examRemark: "",//备注
      pagination6: {...props.state},//获取传参数据
      patientData: {...props.patientData},//获取传参患者数据
      params:{},//获取牙周检查数据
      activeTdId: "",//动态id点击状态
      nextActiveDentition: "",//下一个点亮数据值
      currentToothCol: "",//获取当前牙位数据
      nextTdId: "",//下一个牙位id
      nextTdIndex: "",//下一个牙位id点亮状态
      tdData: {},//牙周大表没给单元格对应的数据，需提交的数据
      missesTooth: [],//牙周大表中设置为缺失牙的牙位集合，需给交的数据
      triangleTd: [1, 2, 3, 4, 5, 6, 7, 8, 9, 20, 21, 22, 23, 24, 25, 26, 27, 28, 509, 510, 511, 512, 513, 514, 525, 526, 527, 528, 529, 530],
      toothCell: {//牙周单元格
        denture18: [1, 2, 3, 29, 45, 61, 77, 108, 109, 140, 141, 172, 173, 174, 175, 266, 267, 268],
        denture17: [4, 5, 6, 30, 46, 62, 78, 107, 110, 139, 142, 171, 176, 177, 178, 263, 264, 265],
        denture16: [7, 8, 9, 31, 47, 63, 79, 106, 111, 138, 143, 170, 179, 180, 181, 260, 261, 262],
        denture15: [10, 32, 48, 64, 80, 105, 112, 137, 144, 169, 182, 183, 184, 257, 258, 259],
        denture14: [11, 33, 49, 65, 81, 104, 113, 136, 145, 168, 185, 186, 187, 254, 255, 256],
        denture13: [12, 34, 50, 66, 82, 103, 114, 135, 146, 167, 188, 189, 190, 251, 252, 253],
        denture12: [13, 35, 51, 67, 83, 102, 115, 134, 147, 166, 191, 192, 193, 248, 249, 250],
        denture11: [14, 36, 52, 68, 84, 101, 116, 133, 148, 165, 194, 195, 196, 245, 246, 247],
        denture21: [15, 37, 53, 69, 85, 100, 117, 132, 149, 164, 197, 198, 199, 242, 243, 244],
        denture22: [16, 38, 54, 70, 86, 99, 118, 131, 150, 163, 200, 201, 202, 239, 240, 241],
        denture23: [17, 39, 55, 71, 87, 98, 119, 130, 151, 162, 203, 204, 205, 236, 237, 238],
        denture24: [18, 40, 56, 72, 88, 97, 120, 129, 152, 161, 206, 207, 208, 233, 234, 235],
        denture25: [19, 41, 57, 73, 89, 96, 121, 128, 153, 160, 209, 210, 211, 230, 231, 232],
        denture26: [20, 21, 22, 42, 58, 74, 90, 95, 122, 127, 154, 159, 212, 213, 214, 227, 228, 229],
        denture27: [23, 24, 25, 43, 59, 75, 91, 94, 123, 126, 155, 158, 215, 216, 217, 224, 225, 226],
        denture28: [26, 27, 28, 44, 60, 76, 92, 93, 124, 125, 156, 157, 218, 219, 220, 221, 222, 223],
        denture31: [340, 339, 338, 295, 294, 293, 388, 373, 420, 405, 452, 437, 469, 485, 501, 520],
        denture32: [337, 336, 335, 298, 297, 296, 387, 374, 419, 406, 451, 438, 470, 486, 502, 521],
        denture33: [334, 333, 332, 301, 300, 299, 386, 375, 418, 407, 450, 439, 471, 487, 503, 522],
        denture34: [331, 330, 329, 304, 303, 302, 385, 376, 417, 408, 449, 440, 472, 488, 504, 523],
        denture35: [328, 327, 326, 307, 306, 305, 384, 377, 416, 409, 448, 441, 473, 489, 505, 524],
        denture36: [325, 324, 323, 310, 309, 308, 383, 378, 415, 410, 447, 442, 474, 490, 506, 525, 526],
        denture37: [322, 321, 320, 224, 312, 311, 382, 379, 414, 411, 446, 443, 475, 491, 507, 527, 528],
        denture38: [319, 318, 317, 316, 315, 314, 381, 380, 413, 412, 445, 444, 476, 492, 508, 529, 530],
        denture41: [343, 342, 341, 292, 291, 290, 389, 372, 421, 404, 453, 436, 468, 484, 500, 519],
        denture42: [346, 345, 344, 289, 288, 287, 390, 371, 422, 403, 454, 435, 467, 483, 499, 518],
        denture43: [349, 348, 347, 286, 285, 284, 391, 370, 423, 402, 455, 434, 466, 482, 498, 517],
        denture44: [352, 351, 350, 283, 282, 281, 392, 369, 424, 401, 456, 433, 465, 481, 497, 516],
        denture45: [355, 354, 353, 280, 279, 278, 393, 368, 425, 400, 457, 432, 464, 480, 496, 515],
        denture46: [358, 357, 356, 277, 276, 275, 394, 367, 426, 399, 458, 431, 463, 479, 495, 513, 514],
        denture47: [361, 360, 359, 274, 273, 272, 395, 366, 427, 398, 459, 430, 462, 478, 494, 511, 512],
        denture48: [364, 363, 362, 271, 270, 269, 396, 365, 428, 397, 460, 429, 461, 477, 493, 509, 510],
      },
      missesData: {//牙缺失显示状态
        dentureStatus18: false,
        dentureStatus17: false,
        dentureStatus16: false,
        dentureStatus15: false,
        dentureStatus14: false,
        dentureStatus13: false,
        dentureStatus12: false,
        dentureStatus11: false,
        dentureStatus21: false,
        dentureStatus22: false,
        dentureStatus23: false,
        dentureStatus24: false,
        dentureStatus25: false,
        dentureStatus26: false,
        dentureStatus27: false,
        dentureStatus28: false,
        dentureStatus48: false,
        dentureStatus47: false,
        dentureStatus46: false,
        dentureStatus45: false,
        dentureStatus44: false,
        dentureStatus43: false,
        dentureStatus42: false,
        dentureStatus41: false,
        dentureStatus31: false,
        dentureStatus32: false,
        dentureStatus33: false,
        dentureStatus34: false,
        dentureStatus35: false,
        dentureStatus36: false,
        dentureStatus37: false,
        dentureStatus38: false,
      },
      tableCell: {},
      checklist: [],//检查数据
      hoverIndex: '',//ya
      // clickIndex1: false,
      // // clickIndex1: '',
      // clickIndex: false,
      // clickColor: false,
      toothPosition: null,//获取牙位
      tabKey: "occRelation",
      typeKey: "deepOverbite",
    }
  }

  //生命周期初始化
  componentDidMount() {
    let tdData = {};
    let toothPosition = this.getToothPosition();
    let missesData = this.getMissesData();
    let params;
    if(this.props.data){
      tdData = this.props.data.perChart.tdData;
      missesData = this.props.data.perChart.missesData;
      toothPosition = this.props.data.toothDesc;
      params = this.props.data;
    }else{
      for (let i = 1; i < 530; i++) {
        let tdId = "td" + i;
        tdData[tdId] = ""
      }
      params = {};
    }
    this.setState(
      {
        params: params,
        tdData: tdData,
        missesData: missesData,
        toothPosition:toothPosition,
        newKey: Math.random()
      }
    )

    document.addEventListener('keydown', this.handleKeyDown);
  }
//牙缺失状态
  getMissesData = ()=>{
    return {//牙缺失
      dentureStatus18: false,
      dentureStatus17: false,
      dentureStatus16: false,
      dentureStatus15: false,
      dentureStatus14: false,
      dentureStatus13: false,
      dentureStatus12: false,
      dentureStatus11: false,
      dentureStatus21: false,
      dentureStatus22: false,
      dentureStatus23: false,
      dentureStatus24: false,
      dentureStatus25: false,
      dentureStatus26: false,
      dentureStatus27: false,
      dentureStatus28: false,
      dentureStatus48: false,
      dentureStatus47: false,
      dentureStatus46: false,
      dentureStatus45: false,
      dentureStatus44: false,
      dentureStatus43: false,
      dentureStatus42: false,
      dentureStatus41: false,
      dentureStatus31: false,
      dentureStatus32: false,
      dentureStatus33: false,
      dentureStatus34: false,
      dentureStatus35: false,
      dentureStatus36: false,
      dentureStatus37: false,
      dentureStatus38: false,
    };
  }
  //获取牙位数据
  getToothPosition = () => {
    return {
    occRelation: {
      deepOverbite: {//深复合
        name: "深复合",
        toothPosition: "",
      },
      deepCover: {//深复盖
        name: "深复盖",
        toothPosition: "",
      },
      malocclusionCrowded: {//错合拥挤
        name: "错合拥挤",
        toothPosition: "",
      },
      toTheBlade: {//对刃合
        name: "对刃合",
        toothPosition: "",
      },

    },
    other: {
      crossBite: {//反合
        name: "反合",
        toothPosition: "",
      },
      migrationTooth: {//牙移位
        name: "牙移位",
        toothPosition: "",
      },
      foodImpaction: {//食物嵌塞
        name: "食物嵌塞",
        toothPosition: "",
      },
      restorativeOverhanging: {//充填体悬突
        name: "充填体悬突",
        toothPosition: "",
      },
      movableTeeth: {//开合
        name: "开合",
        toothPosition: "",
      },
      decayed: {//龋齿
        name: "龋齿",
        toothPosition: "",
      },
      wedgeShapedDefect: {//楔形缺损
        name: "楔形缺损",
        toothPosition: "",
      },
      defectiveProsthesis: {//不良修复体
        name: "不良修复体",
        toothPosition: "",
      },
    }
  }
  }

  //生命周期初始化前
  componentWillUnmount() {
    document.removeEventListener('keydown', this.handleKeyDown);
  }
  //牙周检查表格点击事件
  handleClickTd = (tdId, nextTd, currentToothCol, nextDentitionNum) => {
    let isMisses = this.state.missesData["dentureStatus" + currentToothCol];
    if (!isMisses) {
      this.setState({
        activeTdId: tdId,
        nextActiveDentition: nextDentitionNum,
        currentToothCol: currentToothCol,
        nextTdId: "td" + (nextTd + 1),
        nextTdIndex: nextTd
      }, () => {
      })
    } else {
      let tdId = "td" + (nextTd + 1);
      this.handleClickTd(tdId, (nextTd + 1), this.getCurrentToothCol(nextTd + 1), this.getCurrentToothCol(nextTd + 2));
    }
    this.props.ok(this.state.odontolith, this.state.cleanStatus, this.state.examRemark, this.state.missesData, this.state.tdData, this.state.update, this.state.patientData, this.state.toothPosition, this.state.toothPosition);
  }
  //每一格点击事件获取值
  getCurrentToothCol = (index) => {
    for (let i = 11; i <= 18; i++) {
      let array = this.state.toothCell["denture" + i];
      if (this.isInArray(array, index) != -1) {
        return i
      }
    }
    for (let i = 21; i <= 28; i++) {
      let array = this.state.toothCell["denture" + i];
      if (this.isInArray(array, index) != -1) {
        return i
      }
    }
    for (let i = 31; i <= 38; i++) {
      let array = this.state.toothCell["denture" + i];
      if (this.isInArray(array, index) != -1) {
        return i
      }
    }
    for (let i = 41; i <= 48; i++) {
      let array = this.state.toothCell["denture" + i];
      if (this.isInArray(array, index) != -1) {
        return i
      }
    }
  }
  //进入事件
  isInArray = (array, index) => {
    return array.indexOf(index);
  }
  //牙周中按钮点击事件
  handleClick = (e, item) => {
    let tdId = this.state.activeTdId;
    this.state.tdData[tdId] = item.descripts;
    this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
  }
  //获取key状态值  动态id值
  handleKeyDown = (e) => {
    let keyCode = e.keyCode;
    let tdId = this.state.activeTdId;
    if (tdId !== "") {
      if (keyCode === 48 || keyCode === 96) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) === -1)
          this.state.tdData[tdId] = 0;
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 49 || keyCode === 97) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "Ⅰ";
        } else {
          this.state.tdData[tdId] = 1;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 50 || keyCode === 98) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "Ⅱ";
        } else {
          this.state.tdData[tdId] = 2;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 51 || keyCode === 99) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "Ⅲ";
        } else {
          this.state.tdData[tdId] = 3;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 52 || keyCode === 100) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "Ⅳ";
        } else {
          this.state.tdData[tdId] = 4;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 53 || keyCode === 101) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "";
        } else {
          this.state.tdData[tdId] = 5;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 54 || keyCode === 102) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "";
        } else {
          this.state.tdData[tdId] = 6;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 55 || keyCode === 103) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "";
        } else {
          this.state.tdData[tdId] = 7;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 56 || keyCode === 104) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "";
        } else {
          this.state.tdData[tdId] = 8;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if (keyCode === 57 || keyCode === 105) {
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "";
        } else {
          this.state.tdData[tdId] = 9;
        }
        this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex + 1, this.getCurrentToothCol(this.state.nextTdIndex + 1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
      if(keyCode === 8){
        if (this.isInArray(this.state.triangleTd, this.state.nextTdIndex) !== -1) {
          this.state.tdData[tdId] = "";
        } else {
          this.state.tdData[tdId] = "";
        }
        this.setState({
          activeTdId:""
        })
        //this.handleClickTd(this.state.nextTdId, this.state.nextTdIndex+1, this.getCurrentToothCol(this.state.nextTdIndex+1), this.getCurrentToothCol(this.state.nextTdIndex + 2));
      }
    }
  }
  //牙缺失
  toothMissesChange = (e, toothIndex) => {
    if (e.target.checked) {
      this.state.missesData["dentureStatus" + toothIndex] = true
    } else {
      this.state.missesData["dentureStatus" + toothIndex] = false
    }
    let missesTooth = this.state.missesTooth;

    if (this.isInArray(missesTooth, toothIndex) != -1) {
      let index = missesTooth.indexOf(toothIndex);
      missesTooth.splice(index, 1);
    } else {
      missesTooth.push(toothIndex);
    }
    this.setState({
      missesData: this.state.missesData,
      missesTooth: missesTooth
    }, () => {
      let td = this.state.toothCell["denture" + toothIndex];
      for (let i = 0; i < td.length; i++) {
        this.state.tdData["td" + td[i]] = "";
      }
      this.setState({
        tdData: this.state.tdData
      })
    })
    this.props.ok(this.state.odontolith, this.state.cleanStatus, this.state.examRemark, this.state.missesData, this.state.tdData, this.state.update, this.state.patientData, this.state.toothPosition, this.state.toothPosition);
  }



  render() {
    const {toothPosition} = this.state;
    const numberArr = [
      {
        descriptsSPCode: "00",
        descripts: "0",
        id: "0",
        descriptsEN: "zero",
        seq: "0"
      },
      {
        descriptsSPCode: "01",
        descripts: "1",
        id: "1",
        descriptsEN: "one",
        seq: "1"
      },
      {
        descriptsSPCode: "02",
        descripts: "2",
        id: "2",
        descriptsEN: "two",
        seq: "2"
      },
      {
        descriptsSPCode: "03",
        descripts: "3",
        id: "3",
        descriptsEN: "three",
        seq: "3"
      },
      {
        descriptsSPCode: "04",
        descripts: "4",
        id: "4",
        descriptsEN: "four",
        seq: "4"
      },
      {
        descriptsSPCode: "05",
        descripts: "5",
        id: "5",
        descriptsEN: "five",
        seq: "5"
      },
      {
        descriptsSPCode: "06",
        descripts: "6",
        id: "6",
        descriptsEN: "six",
        seq: "6"
      },
      {
        descriptsSPCode: "07",
        descripts: "7",
        id: "7",
        descriptsEN: "seven",
        seq: "7"
      },
      {
        descriptsSPCode: "08",
        descripts: "8",
        id: "8",
        descriptsEN: "eight",
        seq: "8"
      }, {
        descriptsSPCode: "09",
        descripts: "9",
        id: "9",
        descriptsEN: "nine",
        seq: "9"
      }, {
        descriptsSPCode: "10",
        descripts: "10",
        id: "10",
        descriptsEN: "ten",
        seq: "10"
      },
      {
        descriptsSPCode: "11",
        descripts: "11",
        id: "11",
        descriptsEN: "eleven",
        seq: "11"
      },
      {
        descriptsSPCode: "12",
        descripts: "12",
        id: "12",
        descriptsEN: "twelve",
        seq: "12"
      },
      {
        descriptsSPCode: "13",
        descripts: "13",
        id: "13",
        descriptsEN: "thirteen",
        seq: "13"
      },
      {
        descriptsSPCode: "14",
        descripts: "14",
        id: "14",
        descriptsEN: "fourteen",
        seq: "14"
      },
      {
        descriptsSPCode: "I",
        descripts: "I",
        id: "15",
        descriptsEN: "fifteen",
        seq: "15"
      },
      {
        descriptsSPCode: "II",
        descripts: "II",
        id: "16",
        descriptsEN: "sixteen",
        seq: "16"
      },
      {
        descriptsSPCode: "III",
        descripts: "III",
        id: "17",
        descriptsEN: "seventeen",
        seq: "17"
      },
      {
        descriptsSPCode: "IV",
        descripts: "IV",
        id: "18",
        descriptsEN: "eighteen",
        seq: "18"
      },
    ]
    //牙石
    let odontolithContrast = {1: "Ⅰ",2: "Ⅱ",3: "Ⅲ"};
    let cleanStatusContrast = {1: "洁治前",2: "洁治后"};
    return (
      <div style={{height:980,overflow:'hidden',display:'flex'}}>
        <div>
          <div className={styles.all_content}>
            <div className={styles.toothMap}>
              <div className={styles.smallBox}>
                <span className={styles.leftTitle}>牙缺失</span>
                <Checkbox style={{"width": "55px", textAlign: "center"}}
                          checked={this.state.missesData.dentureStatus18}
                          >18</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus17}
                          >17</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus16}
                          >16</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus15}
                          >15</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus14}
                          >14</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus13}
                          >13</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus12}
                          >12</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus11}
                          >11</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: "6px"}}
                          checked={this.state.missesData.dentureStatus21}
                          >21</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus22}
                          >22</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus23}
                          >23</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus24}
                          >24</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus25}
                          >25</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus26}
                          >26</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus27}
                          >27</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus28}
                          >28</Checkbox>
              </div>
              <div className={styles.table_inner}>
                <div className={styles.table_box}>
                  <table className={styles.lineBlueLeft}>
                    <tbody>
                    <tr>
                      <th>FI</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>角化龈宽</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>溢脓</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>动度</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>PLI</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>龈缘CEJ</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>BI</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th style={{height: '80px'}} className={styles.pdl}>
                        <div>B</div>
                        <div>PD</div>
                        <div>L</div>
                      </th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th >
                        {/*style={{height: '61px'}}*/}
                        <table className={styles.abscissa}>
                          <tbody>
                          <tr>
                            <td className={styles.ableTd}>8</td>
                            <td className={styles.ableTd}>7</td>
                            <td className={styles.ableTd}>6</td>
                            <td className={styles.ableTd}>5</td>
                            <td className={styles.ableTd}>4</td>
                            <td className={styles.ableTd}>3</td>
                            <td className={styles.ableTd}>2</td>
                            <td className={styles.ableTd}>1</td>
                          </tr>
                          </tbody>
                        </table>
                        <table className={styles.abscissa} style={{left: 524}}>
                          <tbody>
                          <tr>
                            <td className={styles.ableTd}>1</td>
                            <td className={styles.ableTd}>2</td>
                            <td className={styles.ableTd}>3</td>
                            <td className={styles.ableTd}>4</td>
                            <td className={styles.ableTd}>5</td>
                            <td className={styles.ableTd}>6</td>
                            <td className={styles.ableTd}>7</td>
                            <td className={styles.ableTd}>8</td>
                          </tr>
                          </tbody>
                        </table>
                      </th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th style={{height: '57px',position: 'relative',top:' -12px'}} className={styles.lpd}>
                        <div>L</div>
                        <div>PD</div>
                        <div>B</div>
                      </th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>BI</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>龈缘CEJ</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>PLI</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>动度</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>溢脓</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>角化龈宽</th>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th>FI</th>
                    </tr>
                    </tbody>
                  </table>
                  <div className={styles.lineBlueH} id="table1"></div>
                  <table className={styles.upperLeftTable} cellSpacing={0} cellPadding={0}>
                    <tbody>
                      <tr className={styles.trH40}>
                        <td colSpan={3}>
                          <div
                            className={[this.state.missesData.dentureStatus18 ? styles.divBorder2Misses : styles.divBorder2, this.state.activeTdId == "td1" ? styles.triangleActive2 : " "].join(' ')}
                            >
                            {this.state.tdData.td1>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td1}</div>:this.state.tdData.td1}
                          </div>
                          <div
                            className={[this.state.activeTdId == "td2" ? styles.triangleActive : " ", this.state.missesData.dentureStatus18 ? styles.triangleMisses18 : styles.triangle18].join(' ')}
                            ><span
                            className={styles.word18}>{this.state.tdData.td2}</span></div>
                          <div className={styles.triangleBg18}></div>
                          {/*此行代码只是为了显示顶部三角形的边框，不可删除*/}

                          <div
                            className={[this.state.activeTdId == "td3" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.divBorder3Misses : styles.divBorder3].join(' ')}
                            >
                            {this.state.tdData.td3>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td3}</div>:this.state.tdData.td3}
                          </div>
                        </td>
                        <td colSpan={3}>
                          <div
                            className={[this.state.missesData.dentureStatus17 ? styles.divBorder2Misses : styles.divBorder2, this.state.activeTdId == "td4" ? styles.triangleActive2 : " "].join(' ')}
                            id="td15" >
                            {this.state.tdData.td4>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td4}</div>:this.state.tdData.td4}
                          </div>
                          <div
                            className={[this.state.missesData.dentureStatus17 ? styles.triangleMisses17 : styles.triangle17, this.state.activeTdId == "td5" ? styles.triangleActive : " "].join(' ')}
                            id="td14" ><span
                            className={styles.word17}>{this.state.tdData.td5}</span></div>
                          <div className={styles.triangleBg17}></div>
                          {/*此行代码只是为了显示顶部三角形的边框，不可删除*/}

                          <div
                            className={[this.state.missesData.dentureStatus17 ? styles.divBorder3Misses : styles.divBorder3, this.state.activeTdId == "td6" ? styles.triangleActive2 : " "].join(' ')}
                            id="td16">
                            {this.state.tdData.td6>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td6}</div>:this.state.tdData.td6}
                          </div>
                        </td>
                        <td colSpan={3}>
                          <div
                            className={[this.state.missesData.dentureStatus16 ? styles.divBorder2Misses : styles.divBorder2, this.state.activeTdId == "td7" ? styles.triangleActive2 : " "].join(' ')}
                            >
                            {this.state.tdData.td7>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td7}</div>:this.state.tdData.td7}
                          </div>
                          <div
                            className={[this.state.missesData.dentureStatus16 ? styles.triangleMisses16 : styles.triangle16, this.state.activeTdId == "td8" ? styles.triangleActive : " "].join(' ')}
                            ><span
                            className={styles.word16}>{this.state.tdData.td8>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td8}</div>:this.state.tdData.td8}</span></div>
                          <div className={styles.triangleBg16}></div>
                          {/*此行代码只是为了显示顶部三角形的边框，不可删除*/}

                          <div
                            className={[this.state.missesData.dentureStatus16 ? styles.divBorder3Misses : styles.divBorder3, this.state.activeTdId == "td9" ? styles.triangleActive2 : " "].join(' ')}
                            >
                            {this.state.tdData.td9>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td9}</div>:this.state.tdData.td9}
                          </div>
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td10" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td10>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td10}</div>:this.state.tdData.td10}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td11" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td11>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td11}</div>:this.state.tdData.td11}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td12" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td12>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td12}</div>:this.state.tdData.td12}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td13" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td13>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td13}</div>:this.state.tdData.td13}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td14" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td14>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td14}</div>:this.state.tdData.td14}
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className={styles.trH40}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td29" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td29>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td29}</div>:this.state.tdData.td29}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td30" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td30>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td30}</div>:this.state.tdData.td30}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td31" ? styles.triangleActive2 : " "].join(' ')}
                           >
                          {this.state.tdData.td31>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td31}</div>:this.state.tdData.td31}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td32" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td32>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td32}</div>:this.state.tdData.td32}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td33" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td33>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td33}</div>:this.state.tdData.td33}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td34" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td34>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td34}</div>:this.state.tdData.td34}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td35" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td35>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td35}</div>:this.state.tdData.td35}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td36" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td36>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td36}</div>:this.state.tdData.td36}
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className={styles.trH40}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td45" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td45>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td45}</div>:this.state.tdData.td45}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td46" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td46>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td46}</div>:this.state.tdData.td46}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td47" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td47>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td47}</div>:this.state.tdData.td47}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td48" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td48>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td48}</div>:this.state.tdData.td48}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td49" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td49>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td49}</div>:this.state.tdData.td49}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td50" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td50>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td50}</div>:this.state.tdData.td50}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td51" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td51>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td51}</div>:this.state.tdData.td51}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td52" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td52>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td52}</div>:this.state.tdData.td52}
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className={styles.trH40}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td61" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td61>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td61}</div>:this.state.tdData.td61}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td62" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td62>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td62}</div>:this.state.tdData.td62}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td63" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td63>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td63}</div>:this.state.tdData.td63}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td64" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td64>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td64}</div>:this.state.tdData.td64}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td65" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td65>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td65}</div>:this.state.tdData.td65}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td66" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td66>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td66}</div>:this.state.tdData.td66}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td67" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td67>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td67}</div>:this.state.tdData.td67}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td68" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td68>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td68}</div>:this.state.tdData.td68}
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td77" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td77>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td77}</div>:this.state.tdData.td77}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td78" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td78>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td78}</div>:this.state.tdData.td78}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td79" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td79>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td79}</div>:this.state.tdData.td79}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td80" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td80>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td80}</div>:this.state.tdData.td80}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td81" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td81>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td81}</div>:this.state.tdData.td81}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td82" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td82>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td82}</div>:this.state.tdData.td82}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td83" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td83>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td83}</div>:this.state.tdData.td83}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td84" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td84>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td84}</div>:this.state.tdData.td84}
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td108" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td108>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td108}</div>:this.state.tdData.td108}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td107" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td107>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td107}</div>:this.state.tdData.td107}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td106" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td106>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td106}</div>:this.state.tdData.td106}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td105" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td105>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td105}</div>:this.state.tdData.td105}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td104" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td104>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td104}</div>:this.state.tdData.td104}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td103" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td103>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td103}</div>:this.state.tdData.td103}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td102" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td102>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td102}</div>:this.state.tdData.td102}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td101" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td101>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td101}</div>:this.state.tdData.td101}
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td109" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td109>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td109}</div>:this.state.tdData.td109}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td110" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td110>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td110}</div>:this.state.tdData.td110}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td111" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td111>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td111}</div>:this.state.tdData.td111}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td112" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td112>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td112}</div>:this.state.tdData.td112}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td113" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td113>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td113}</div>:this.state.tdData.td113}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td114" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td114>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td114}</div>:this.state.tdData.td114}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td115" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td115>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td115}</div>:this.state.tdData.td115}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td116" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td116>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td116}</div>:this.state.tdData.td116}
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td140" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td140>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td140}</div>:this.state.tdData.td140}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td139" ? styles.triangleActive2 : " ",].join(' ')}
                            >
                          {this.state.tdData.td139>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td139}</div>:this.state.tdData.td139}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td138" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td138>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td138}</div>:this.state.tdData.td138}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td137" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td137>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td137}</div>:this.state.tdData.td137}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td136" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td136>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td136}</div>:this.state.tdData.td136}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td135" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td135>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td135}</div>:this.state.tdData.td135}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td134" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td134>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td134}</div>:this.state.tdData.td134}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td133" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td133>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td133}</div>:this.state.tdData.td133}
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td141" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td141>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td141}</div>:this.state.tdData.td141}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td142" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td142>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td142}</div>:this.state.tdData.td142}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td143" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td143>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td143}</div>:this.state.tdData.td143}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td144" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td144>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td144}</div>:this.state.tdData.td144}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td145" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td145>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td145}</div>:this.state.tdData.td145}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td146" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td146>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td146}</div>:this.state.tdData.td146}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td147" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td147>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td147}</div>:this.state.tdData.td147}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td148" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td148>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td148}</div>:this.state.tdData.td148}
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td172" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td172>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td172}</div>:this.state.tdData.td172}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus17 ? styles.missingTooth : "", this.state.activeTdId == "td171" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td171>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td171}</div>:this.state.tdData.td171}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus16 ? styles.missingTooth : "", this.state.activeTdId == "td170" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td170>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td170}</div>:this.state.tdData.td170}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus15 ? styles.missingTooth : "", this.state.activeTdId == "td169" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td169>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td169}</div>:this.state.tdData.td169}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus14 ? styles.missingTooth : "", this.state.activeTdId == "td168" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td168>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td168}</div>:this.state.tdData.td168}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus13 ? styles.missingTooth : "", this.state.activeTdId == "td167" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td167>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td167}</div>:this.state.tdData.td167}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus12 ? styles.missingTooth : "", this.state.activeTdId == "td166" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td166>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td166}</div>:this.state.tdData.td166}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus11 ? styles.missingTooth : "", this.state.activeTdId == "td165" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td165>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td165}</div>:this.state.tdData.td165}
                        </td>
                      </tr>
                    </tbody>

                    <tbody>
                      <tr>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td173" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td173>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td173}</div>:this.state.tdData.td173}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td174" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td174>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td174}</div>:this.state.tdData.td174}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td175" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td175>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td175}</div>:this.state.tdData.td175}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td176" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td176>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td176}</div>:this.state.tdData.td176}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td177" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td177>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td177}</div>:this.state.tdData.td177}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td178" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td178>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td178}</div>:this.state.tdData.td178}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td179" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus16 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td179>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td179}</div>:this.state.tdData.td179}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td180" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus16 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td180>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td180}</div>:this.state.tdData.td180}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td181" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus16 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td181>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td181}</div>:this.state.tdData.td181}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td182" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus15 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td182>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td182}</div>:this.state.tdData.td182}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td183" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus15 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td183>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td183}</div>:this.state.tdData.td183}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td184" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus15 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td184>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td184}</div>:this.state.tdData.td184}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td185" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus14 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td185>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td185}</div>:this.state.tdData.td185}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td186" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus14 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td186>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td186}</div>:this.state.tdData.td186}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td187" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus14 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td187>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td187}</div>:this.state.tdData.td187}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td188" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus13 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td188>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td188}</div>:this.state.tdData.td188}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td189" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus13 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td189>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td189}</div>:this.state.tdData.td189}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td190" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus13 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td190>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td190}</div>:this.state.tdData.td190}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td191" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus12 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td191>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td191}</div>:this.state.tdData.td191}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td192" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus12 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td192>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td192}</div>:this.state.tdData.td192}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td193" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus12 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td193>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td193}</div>:this.state.tdData.td193}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td194" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus11 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td194>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td194}</div>:this.state.tdData.td194}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td195" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus11 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td195>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td195}</div>:this.state.tdData.td195}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td196" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus11 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td196>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td196}</div>:this.state.tdData.td196}
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td268" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td268>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td268}</div>:this.state.tdData.td268}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td267" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td267>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td267}</div>:this.state.tdData.td267}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td266" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus18 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td266>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td266}</div>:this.state.tdData.td266}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td265" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td265>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td265}</div>:this.state.tdData.td265}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td264" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td264>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td264}</div>:this.state.tdData.td264}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td263" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus17 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td263>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td263}</div>:this.state.tdData.td263}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td262" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus16 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td262>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td262}</div>:this.state.tdData.td262}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td261" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus16 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td261>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td261}</div>:this.state.tdData.td261}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td260" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus16 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td260>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td260}</div>:this.state.tdData.td260}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td259" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus15 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td259>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td259}</div>:this.state.tdData.td259}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td258" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus15 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td258>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td258}</div>:this.state.tdData.td258}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td257" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus15 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td257>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td257}</div>:this.state.tdData.td257}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td256" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus14 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td256>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td256}</div>:this.state.tdData.td256}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td255" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus14 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td255>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td255}</div>:this.state.tdData.td255}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td254" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus14 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td254>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td254}</div>:this.state.tdData.td254}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td253" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus13 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td253>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td253}</div>:this.state.tdData.td253}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td252" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus13 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td252>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td252}</div>:this.state.tdData.td252}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td251" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus13 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td251>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td251}</div>:this.state.tdData.td251}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td250" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus12 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td250>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td250}</div>:this.state.tdData.td250}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td249" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus12 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td249>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td249}</div>:this.state.tdData.td249}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td248" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus12 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td248>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td248}</div>:this.state.tdData.td248}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td247" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus11 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td247>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td247}</div>:this.state.tdData.td247}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td246" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus11 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td246>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td246}</div>:this.state.tdData.td246}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td245" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus11 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td245>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td245}</div>:this.state.tdData.td245}
                        </td>
                      </tr>
                    </tbody>
                  </table>

                  <table className={styles.upperRightTable} cellSpacing={0} cellPadding={0}>
                    {/*<tbody>*/}
                      <tr className={styles.trH40}>
                        <td colSpan={3}
                            className={[, this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td15" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td15>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td15}</div>:this.state.tdData.td15}
                        </td>
                        <td colSpan={3}
                            className={[, this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td16" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td16>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td16}</div>:this.state.tdData.td16}
                        </td>
                        <td colSpan={3}
                            className={[, this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td17" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td17>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td17}</div>:this.state.tdData.td17}
                        </td>
                        <td colSpan={3}
                            className={[, this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td18" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td18>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td18}</div>:this.state.tdData.td18}
                        </td>
                        <td colSpan={3}
                            className={[, this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td19" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td19>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td19}</div>:this.state.tdData.td19}
                        </td>
                        <td colSpan={3}>
                          <div
                            className={[this.state.missesData.dentureStatus26 ? styles.divBorder2Misses : styles.divBorder2, this.state.activeTdId == "td20" ? styles.triangleActive2 : " "].join(' ')}
                            >
                            {this.state.tdData.td20>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td20}</div>:this.state.tdData.td20}
                          </div>
                          <div
                            className={[this.state.missesData.dentureStatus26 ? styles.triangleMisses26 : styles.triangle26, this.state.activeTdId == "td21" ? styles.triangleActive : " "].join(' ')}
                            ><span
                            className={styles.word26}>{this.state.tdData.td21}</span></div>
                          <div className={styles.triangleBg26}></div>
                          {/*此行代码只是为了显示顶部三角形的边框，不可删除*/}

                          <div
                            className={[this.state.missesData.dentureStatus26 ? styles.divBorder3Misses : styles.divBorder3, this.state.activeTdId == "td22" ? styles.triangleActive2 : " "].join(' ')}
                            >
                            {this.state.tdData.td22>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td22}</div>:this.state.tdData.td22}
                          </div>
                        </td>
                        <td colSpan={3}>
                          <div
                            className={[this.state.missesData.dentureStatus27 ? styles.divBorder2Misses : styles.divBorder2, this.state.activeTdId == "td23" ? styles.triangleActive2 : " "].join(' ')}
                            >
                            {this.state.tdData.td23>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td23}</div>:this.state.tdData.td23}
                          </div>
                          <div
                            className={[this.state.missesData.dentureStatus27 ? styles.triangleMisses27 : styles.triangle27, this.state.activeTdId == "td24" ? styles.triangleActive : " "].join(' ')}
                            ><span
                            className={styles.word27}>{this.state.tdData.td24}</span></div>
                          <div className={styles.triangleBg27}></div>
                          {/*此行代码只是为了显示顶部三角形的边框，不可删除*/}

                          <div
                            className={[this.state.missesData.dentureStatus27 ? styles.divBorder3Misses : styles.divBorder3, this.state.activeTdId == "td25" ? styles.triangleActive2 : " "].join(' ')}
                            >
                            {this.state.tdData.td25>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td25}</div>:this.state.tdData.td25}
                          </div>
                        </td>
                        <td colSpan={3}>
                          <div
                            className={[this.state.missesData.dentureStatus28 ? styles.divBorder2Misses : styles.divBorder2, this.state.activeTdId == "td26" ? styles.triangleActive2 : " "].join(' ')}
                            >
                            {this.state.tdData.td26>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td26}</div>:this.state.tdData.td26}
                          </div>
                          <div
                            className={[this.state.missesData.dentureStatus28 ? styles.triangleMisses28 : styles.triangle28, this.state.activeTdId == "td27" ? styles.triangleActive : " "].join(' ')}
                            ><span
                            className={styles.word28}>{this.state.tdData.td27}</span></div>
                          <div className={styles.triangleBg28}></div>
                          {/*此行代码只是为了显示顶部三角形的边框，不可删除*/}

                          <div
                            className={[this.state.missesData.dentureStatus28 ? styles.divBorder3Misses : styles.divBorder3, this.state.activeTdId == "td28" ? styles.triangleActive2 : " "].join(' ')}
                            >
                            {this.state.tdData.td28>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td28}</div>:this.state.tdData.td28}
                          </div>
                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH40}>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td37" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td37>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td37}</div>:this.state.tdData.td37}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td38" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td38>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td38}</div>:this.state.tdData.td38}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td39" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td39>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td39}</div>:this.state.tdData.td39}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td40" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td40>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td40}</div>:this.state.tdData.td40}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td41" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td41>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td41}</div>:this.state.tdData.td41}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td42" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td42>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td42}</div>:this.state.tdData.td42}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td43" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td43>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td43}</div>:this.state.tdData.td43}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td44" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td44>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td44}</div>:this.state.tdData.td44}
                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH40}>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td53" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td53>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td53}</div>:this.state.tdData.td53}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td54" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td54>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td54}</div>:this.state.tdData.td54}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td55" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td55>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td55}</div>:this.state.tdData.td55}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td56" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td56>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td56}</div>:this.state.tdData.td56}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td57" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td57>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td57}</div>:this.state.tdData.td57}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td58" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td58>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td58}</div>:this.state.tdData.td58}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td59" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td59>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td59}</div>:this.state.tdData.td59}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td60" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td60>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td60}</div>:this.state.tdData.td60}
                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH40}>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td69" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td69>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td69}</div>:this.state.tdData.td69}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td70" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td70>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td70}</div>:this.state.tdData.td70}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td71" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td71>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td71}</div>:this.state.tdData.td71}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td72" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td72>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td72}</div>:this.state.tdData.td72}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td73" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td73>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td73}</div>:this.state.tdData.td73}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td74" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td74>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td74}</div>:this.state.tdData.td74}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td75" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td75>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td75}</div>:this.state.tdData.td75}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td76" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td76>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td76}</div>:this.state.tdData.td76}
                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td85" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td85>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td85}</div>:this.state.tdData.td85}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td86" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td86>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td86}</div>:this.state.tdData.td86}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td87" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td87>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td87}</div>:this.state.tdData.td87}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td88" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td88>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td88}</div>:this.state.tdData.td88}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td89" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td89>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td89}</div>:this.state.tdData.td89}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td90" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td90>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td90}</div>:this.state.tdData.td90}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td91" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td91>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td91}</div>:this.state.tdData.td91}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td92" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td92>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td92}</div>:this.state.tdData.td92}
                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td100" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td100>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td100}</div>:this.state.tdData.td100}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td99" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td99>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td99}</div>:this.state.tdData.td99
                          }
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td98" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td98>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td98}</div>:this.state.tdData.td98}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td97" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td97>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td97}</div>:this.state.tdData.td97}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td96" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td96>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td96}</div>:this.state.tdData.td96}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td95" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td95>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td95}</div>:this.state.tdData.td95}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td94" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td94>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td94}</div>:this.state.tdData.td94}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td93" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td93>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td93}</div>:this.state.tdData.td93}
                        </td>
                      </tr>
                    {/*</tbody>*/}


                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td117" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td117>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td117}</div>:this.state.tdData.td117}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td118" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td118>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td118}</div>:this.state.tdData.td118}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td119" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td119>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td119}</div>:this.state.tdData.td119}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td120" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td120>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td120}</div>:this.state.tdData.td120}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td121" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td121>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td121}</div>:this.state.tdData.td121}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td122" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td122>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td122}</div>:this.state.tdData.td122}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td123" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td123>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td123}</div>:this.state.tdData.td123}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td124" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td124>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td124}</div>:this.state.tdData.td124}
                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td132" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td132>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td132}</div>:this.state.tdData.td132}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td131" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td131>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td131}</div>:this.state.tdData.td131}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td130" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td130>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td130}</div>:this.state.tdData.td130}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td129" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td129>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td129}</div>:this.state.tdData.td129}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td128" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td128>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td128}</div>:this.state.tdData.td128}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td127" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td127>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td127}</div>:this.state.tdData.td127}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td126" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td126>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td126}</div>:this.state.tdData.td126}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td125" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td125>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td125}</div>:this.state.tdData.td125}
                        </td>
                      </tr>
                    {/*</tbody>*/}


                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td149" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td149>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td149}</div>:this.state.tdData.td149}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td150" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td150>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td150}</div>:this.state.tdData.td150}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td151" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td151>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td151}</div>:this.state.tdData.td151}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td152" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td152>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td152}</div>:this.state.tdData.td152}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td153" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td153>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td153}</div>:this.state.tdData.td153}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td154" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td154>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td154}</div>:this.state.tdData.td154}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td155" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td155>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td155}</div>:this.state.tdData.td155}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td156" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td156>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td156}</div>:this.state.tdData.td156}
                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus21 ? styles.missingTooth : "", this.state.activeTdId == "td164" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td164>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td164}</div>:this.state.tdData.td164}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus22 ? styles.missingTooth : "", this.state.activeTdId == "td163" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td163>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td163}</div>:this.state.tdData.td163}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus23 ? styles.missingTooth : "", this.state.activeTdId == "td162" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td162>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td162}</div>:this.state.tdData.td162}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus24 ? styles.missingTooth : "", this.state.activeTdId == "td161" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td161>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td161}</div>:this.state.tdData.td161}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus25 ? styles.missingTooth : "", this.state.activeTdId == "td160" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td160>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td160}</div>:this.state.tdData.td160}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus26 ? styles.missingTooth : "", this.state.activeTdId == "td159" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td159>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td159}</div>:this.state.tdData.td159}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus27 ? styles.missingTooth : "", this.state.activeTdId == "td158" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td158>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td158}</div>:this.state.tdData.td158}
                        </td>
                        <td colSpan={3}
                            className={[this.state.missesData.dentureStatus28 ? styles.missingTooth : "", this.state.activeTdId == "td157" ? styles.triangleActive2 : " "].join(' ')}
                            >
                          {this.state.tdData.td157>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td157}</div>:this.state.tdData.td157}
                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td197" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus21 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td197>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td197}</div>:this.state.tdData.td197}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td198" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus21 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td198>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td198}</div>:this.state.tdData.td198}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td199" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus21 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td199>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td199}</div>:this.state.tdData.td199}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td200" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus22 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td200>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td200}</div>:this.state.tdData.td200}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td201" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus22 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td201>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td201}</div>:this.state.tdData.td201}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td202" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus22 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td202>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td202}</div>:this.state.tdData.td202}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td203" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus23 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td203>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td203}</div>:this.state.tdData.td203 }
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td204" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus23 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td204>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td204}</div>:this.state.tdData.td204}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td205" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus23 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td205>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td205}</div>:this.state.tdData.td205}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td206" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus24 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td206>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td206}</div>:this.state.tdData.td206}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td207" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus24 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td207>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td207}</div>:this.state.tdData.td207}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td208" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus24 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td208>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td208}</div>:this.state.tdData.td208}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td209" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus25 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td209>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td209}</div>:this.state.tdData.td209}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td210" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus25 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td210>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td210}</div>:this.state.tdData.td210}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td211" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus25 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td211>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td211}</div>:this.state.tdData.td211}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td212" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus26 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td212>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td212}</div>:this.state.tdData.td212}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td213" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus26 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td213>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td213}</div>:this.state.tdData.td213}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td214" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus26 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td214>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td214}</div>:this.state.tdData.td214}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td215" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus27 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td215>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td215}</div>:this.state.tdData.td215}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td216" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus27 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td216>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td216}</div>:this.state.tdData.td216}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td217" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus27 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td217>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td217}</div>:this.state.tdData.td217}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td218" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus28 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td218>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td218}</div>:this.state.tdData.td218}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td219" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus28 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td219>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td219}</div>:this.state.tdData.td219}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td220" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus28 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td220>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td220}</div>:this.state.tdData.td220}
                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td244" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus21 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td244>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td244}</div>:this.state.tdData.td244}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td243" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus21 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td243>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td243}</div>:this.state.tdData.td243}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td242" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus21 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td242>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td242}</div>:this.state.tdData.td242}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td241" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus22 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td241>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td241}</div>:this.state.tdData.td241}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td240" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus22 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td240>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td240}</div>:this.state.tdData.td240}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td239" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus22 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td239>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td239}</div>:this.state.tdData.td239}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td238" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus23 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td238>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td238}</div>:this.state.tdData.td238}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td237" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus23 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td237>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td237}</div>:this.state.tdData.td237}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td236" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus23 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td236>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td236}</div>:this.state.tdData.td236}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td235" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus24 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td235>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td235}</div>:this.state.tdData.td235}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td234" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus24 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td234>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td234}</div>:this.state.tdData.td234}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td233" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus24 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td233>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td233}</div>:this.state.tdData.td233}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td232" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus25 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td232>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td232}</div>:this.state.tdData.td232}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td231" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus25 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td231>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td231}</div>:this.state.tdData.td231}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td230" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus25 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td230>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td230}</div>:this.state.tdData.td230}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td229" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus26 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td229>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td229}</div>:this.state.tdData.td229}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td228" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus26 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td228>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td228}</div>:this.state.tdData.td228}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td227" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus26 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td227>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td227}</div>:this.state.tdData.td227}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td226" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus27 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td226>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td226}</div>:this.state.tdData.td226}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td225" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus27 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td225>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td225}</div>:this.state.tdData.td225}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td224" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus27 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td224>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td224}</div>:this.state.tdData.td224}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td223" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus28 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td223>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td223}</div>:this.state.tdData.td223}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td222" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus28 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td222>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td222}</div>:this.state.tdData.td222}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td221" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus28 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td221>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td221}</div>:this.state.tdData.td221}
                        </td>
                      </tr>
                    {/*</tbody>*/}
                  </table>
                  <table className={styles.lowerRightTable} cellSpacing={0} cellPadding={0}>
                      <tr>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td340" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td340>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td340}</div>:this.state.tdData.td340}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td339" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td339>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td339}</div>:this.state.tdData.td339}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td338" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td338>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td338}</div>:this.state.tdData.td338}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td337" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td337>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td337}</div>:this.state.tdData.td337}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td336" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td336>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td336}</div>:this.state.tdData.td336}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td335" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td335>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td335}</div>:this.state.tdData.td335}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td334" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td334>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td334}</div>:this.state.tdData.td334}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td333" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td333>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td333}</div>:this.state.tdData.td333}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td332" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td332>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td332}</div>:this.state.tdData.td332}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td331" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td331>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td331}</div>:this.state.tdData.td331}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td330" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td330>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td330}</div>:this.state.tdData.td330}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td329" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td329>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td329}</div>:this.state.tdData.td329}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td328" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td328>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td328}</div>:this.state.tdData.td328}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td327" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td327>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td327}</div>:this.state.tdData.td327}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td326" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td326>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td326}</div>:this.state.tdData.td326}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td325" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td325>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td325}</div>:this.state.tdData.td325}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td324" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td324>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td324}</div>:this.state.tdData.td324}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td323" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td323>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td323}</div>:this.state.tdData.td323}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td322" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td322>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td322}</div>:this.state.tdData.td322}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td321" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td321>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td321}</div>:this.state.tdData.td321}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td320" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td320>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td320}</div>:this.state.tdData.td320}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td319" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td319>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td319}</div>:this.state.tdData.td319}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td318" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td318>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td318}</div>:this.state.tdData.td318}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td317" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td317>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td317}</div>:this.state.tdData.td317}
                        </td>
                      </tr>
                      <tr>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td293" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td293>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td293}</div>:this.state.tdData.td293}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td294" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td294>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td294}</div>:this.state.tdData.td294}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td295" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td295>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td295}</div>:this.state.tdData.td295}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td296" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td296>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td296}</div>:this.state.tdData.td296}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td297" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td297>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td297}</div>:this.state.tdData.td297}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td298" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td298>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td298}</div>:this.state.tdData.td298}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td299" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td299>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td299}</div>:this.state.tdData.td299}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td300" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td300>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td300}</div>:this.state.tdData.td300}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td301" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td301>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td301}</div>:this.state.tdData.td301}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td302" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td302>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td302}</div>:this.state.tdData.td302}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td303" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td303>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td303}</div>:this.state.tdData.td303}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td304" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td304>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td304}</div>:this.state.tdData.td304}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td305" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td305>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td305}</div>:this.state.tdData.td305}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td306" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td306>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td306}</div>:this.state.tdData.td306}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td307" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td307>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td307}</div>:this.state.tdData.td307}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td308" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td308>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td308}</div>:this.state.tdData.td308}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td309" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td309>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td309}</div>:this.state.tdData.td309}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td310" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td310>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td310}</div>:this.state.tdData.td310}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td311" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td311>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td311}</div>:this.state.tdData.td311}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td312" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td312>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td312}</div>:this.state.tdData.td312}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td313" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td313>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td313}</div>:this.state.tdData.td313}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td314" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td314>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td314}</div>:this.state.tdData.td314}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td315" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td315>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td315}</div>:this.state.tdData.td315}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td316" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td316>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td316}</div>:this.state.tdData.td316}
                        </td>
                      </tr>
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td388" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td388>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td388}</div>:this.state.tdData.td388}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td387" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td387>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td387}</div>:this.state.tdData.td387}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td386" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td386>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td386}</div>:this.state.tdData.td386}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td385" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td385>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td385}</div>:this.state.tdData.td385}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td384" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td384>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td384}</div>:this.state.tdData.td384}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td383" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td383>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td383}</div>:this.state.tdData.td383}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td382" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td382>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td382}</div>:this.state.tdData.td382}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td381" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td381>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td381}</div>:this.state.tdData.td381}

                        </td>
                      </tr>
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td373" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td373>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td373}</div>:this.state.tdData.td373}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td374" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td374>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td374}</div>:this.state.tdData.td374}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td375" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td375>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td375}</div>:this.state.tdData.td375}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td376" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td376>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td376}</div>:this.state.tdData.td376}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td377" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td377>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td377}</div>:this.state.tdData.td377}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td378" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td378>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td378}</div>:this.state.tdData.td378}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td379" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td379>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td379}</div>:this.state.tdData.td379}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td380" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td380>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td380}</div>:this.state.tdData.td380}

                        </td>
                      </tr>
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td420" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td420>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td420}</div>:this.state.tdData.td420}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td419" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td419>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td419}</div>:this.state.tdData.td419}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td418" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td418>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td418}</div>:this.state.tdData.td418}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td417" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td417>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td417}</div>:this.state.tdData.td417}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td416" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td416>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td416}</div>:this.state.tdData.td416}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td415" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td415>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td415}</div>:this.state.tdData.td415}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td414" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td414>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td414}</div>:this.state.tdData.td414}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td413" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td413>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td413}</div>:this.state.tdData.td413}

                        </td>
                      </tr>
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td405" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td405>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td405}</div>:this.state.tdData.td405}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td406" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td406>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td406}</div>:this.state.tdData.td406}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td407" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td407>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td407}</div>:this.state.tdData.td407}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td408" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td408>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td408}</div>:this.state.tdData.td408}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td409" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td409>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td409}</div>:this.state.tdData.td409}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td410" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td410>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td410}</div>:this.state.tdData.td410}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td411" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td411>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td411}</div>:this.state.tdData.td411}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td412" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td412>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td412}</div>:this.state.tdData.td412}

                        </td>
                      </tr>
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td452" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td452>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td452}</div>:this.state.tdData.td452}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td451" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td451>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td451}</div>:this.state.tdData.td451}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td450" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td450>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td450}</div>:this.state.tdData.td450}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td449" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td449>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td449}</div>:this.state.tdData.td449}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td448" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td448>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td448}</div>:this.state.tdData.td448}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td447" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td447>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td447}</div>:this.state.tdData.td447}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td446" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td446>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td446}</div>:this.state.tdData.td446}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td445" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td445>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td445}</div>:this.state.tdData.td445}

                        </td>
                      </tr>
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td437" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td437>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td437}</div>:this.state.tdData.td437}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td438" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td438>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td438}</div>:this.state.tdData.td438}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td439" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td439>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td439}</div>:this.state.tdData.td439}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td440" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td440>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td440}</div>:this.state.tdData.td440}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td441" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td441>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td441}</div>:this.state.tdData.td441}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td442" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td442>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td442}</div>:this.state.tdData.td442}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td443" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td443>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td443}</div>:this.state.tdData.td443}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td444" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td444>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td444}</div>:this.state.tdData.td444}

                        </td>
                      </tr>
                      <tr className={styles.trH40}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td469" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td469>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td469}</div>:this.state.tdData.td469}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td470" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td470>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td470}</div>:this.state.tdData.td470}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td471" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td471>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td471}</div>:this.state.tdData.td471}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td472" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td472>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td472}</div>:this.state.tdData.td472}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td473" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td473>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td473}</div>:this.state.tdData.td473}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td474" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td474>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td474}</div>:this.state.tdData.td474}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td475" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td475>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td475}</div>:this.state.tdData.td475}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td476" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td476>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td476}</div>:this.state.tdData.td476}

                        </td>
                      </tr>
                      <tr className={styles.trH40}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td485" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td485>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td485}</div>:this.state.tdData.td485}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td486" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td486>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td486}</div>:this.state.tdData.td486}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td487" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td487>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td487}</div>:this.state.tdData.td487}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td488" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td488>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td488}</div>:this.state.tdData.td488}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td489" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td489>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td489}</div>:this.state.tdData.td489}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td490" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td490>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td490}</div>:this.state.tdData.td490}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td491" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td491>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td491}</div>:this.state.tdData.td491}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td492" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td492>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td492}</div>:this.state.tdData.td492}

                        </td>
                      </tr>
                      <tr className={styles.trH40}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td501" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td501>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td501}</div>:this.state.tdData.td501}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td502" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td502>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td502}</div>:this.state.tdData.td502}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td503" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td503>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td503}</div>:this.state.tdData.td503}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td504" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td504>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td504}</div>:this.state.tdData.td504}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td505" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td505>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td505}</div>:this.state.tdData.td505}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td506" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td506>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td506}</div>:this.state.tdData.td506}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td507" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td507>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td507}</div>:this.state.tdData.td507}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td508" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td508>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td508}</div>:this.state.tdData.td508}

                        </td>
                      </tr>
                      <tr className={styles.trH20}>
                        <td colSpan={3} rowSpan={2}
                            className={[this.state.activeTdId == "td520" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus31 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td520>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td520}</div>:this.state.tdData.td520}</td>
                        <td colSpan={3} rowSpan={2}
                            className={[this.state.activeTdId == "td521" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus32 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td521>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td521}</div>:this.state.tdData.td521}</td>
                        <td colSpan={3} rowSpan={2}
                            className={[this.state.activeTdId == "td522" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus33 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td522>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td522}</div>:this.state.tdData.td522}</td>

                        <td colSpan={3} rowSpan={2}
                            className={[this.state.activeTdId == "td523" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus34 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td523>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td523}</div>:this.state.tdData.td523}</td>
                        <td colSpan={3} rowSpan={2}
                            className={[this.state.activeTdId == "td524" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus35 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td524>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td524}</div>:this.state.tdData.td524}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td525" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td525>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td525}</div>:this.state.tdData.td525}</td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td527" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td527>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td527}</div>:this.state.tdData.td527}
                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td529" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td529>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td529}</div>:this.state.tdData.td529}</td>
                      </tr>
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td526" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus36 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td526>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td526}</div>:this.state.tdData.td526}</td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td528" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus37 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td528>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td528}</div>:this.state.tdData.td528}</td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td530" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus38 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td530>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td530}</div>:this.state.tdData.td530}
                        </td>
                      </tr>
                  </table>
                  <table className={styles.lowerLeftTable} cellSpacing={0} cellPadding={0}>
                    {/*<tbody>*/}
                      <tr>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td364" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td364>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td364}</div>:this.state.tdData.td364}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td363" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td363>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td363}</div>:this.state.tdData.td363}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td362" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td362>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td362}</div>:this.state.tdData.td362}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td361" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td361>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td361}</div>:this.state.tdData.td361}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td360" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td360>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td360}</div>:this.state.tdData.td360}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td359" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td359>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td359}</div>:this.state.tdData.td359}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td358" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td358>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td358}</div>:this.state.tdData.td358}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td357" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td357>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td357}</div>:this.state.tdData.td357}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td356" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td356>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td356}</div>:this.state.tdData.td356}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td355" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td355>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td355}</div>:this.state.tdData.td355}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td354" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td354>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td354}</div>:this.state.tdData.td354}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td353" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td353>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td353}</div>:this.state.tdData.td353}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td352" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td352>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td352}</div>:this.state.tdData.td352}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td351" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td351>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td351}</div>:this.state.tdData.td351}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td350" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td350>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td350}</div>:this.state.tdData.td350}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td349" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td349>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td349}</div>:this.state.tdData.td349}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td348" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td348>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td348}</div>:this.state.tdData.td348}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td347" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td347>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td347}</div>:this.state.tdData.td347}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td346" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td346>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td346}</div>:this.state.tdData.td346}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td345" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td345>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td345}</div>:this.state.tdData.td345}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td344" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td344>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td344}</div>:this.state.tdData.td344}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td343" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td343>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td343}</div>:this.state.tdData.td343}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td342" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td342>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td342}</div>:this.state.tdData.td342}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td341" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td341>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td341}</div>:this.state.tdData.td341}
                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td269" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td269>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td269}</div>:this.state.tdData.td269}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td270" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td270>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td270}</div>:this.state.tdData.td270}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td271" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td271>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td271}</div>:this.state.tdData.td271}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td272" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td272>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td272}</div>:this.state.tdData.td272}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td273" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td273>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td273}</div>:this.state.tdData.td273}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td274" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td274>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td274}</div>:this.state.tdData.td274}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td275" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td275>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td275}</div>:this.state.tdData.td275}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td276" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td276>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td276}</div>:this.state.tdData.td276}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td277" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td277>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td277}</div>:this.state.tdData.td277}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td278" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td278>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td278}</div>:this.state.tdData.td278}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td279" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td279>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td279}</div>:this.state.tdData.td279}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td280" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td280>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td280}</div>:this.state.tdData.td280}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td281" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td281>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td281}</div>:this.state.tdData.td281}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td282" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td282>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td282}</div>:this.state.tdData.td282}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td283" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td283>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td283}</div>:this.state.tdData.td283}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td284" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td284>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td284}</div>:this.state.tdData.td284}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td285" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td285>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td285}</div>:this.state.tdData.td285}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td286" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td286>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td286}</div>:this.state.tdData.td286}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td287" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td287>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td287}</div>:this.state.tdData.td287}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td288" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td288>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td288}</div>:this.state.tdData.td288}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td289" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td289>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td289}</div>:this.state.tdData.td289}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td290" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td290>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td290}</div>:this.state.tdData.td290}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td291" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td291>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td291}</div>:this.state.tdData.td291}
                        </td>
                        <td
                          className={[styles.tdW18, this.state.activeTdId == "td292" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          >
                          {this.state.tdData.td292>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td292}</div>:this.state.tdData.td292}
                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td396" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td396>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td396}</div>:this.state.tdData.td396}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td395" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td395>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td395}</div>:this.state.tdData.td395}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td394" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td394>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td394}</div>:this.state.tdData.td394}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td393" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td393>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td393}</div>:this.state.tdData.td393}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td392" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td392>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td392}</div>:this.state.tdData.td392}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td391" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td391>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td391}</div>:this.state.tdData.td391}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td390" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td390>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td390}</div>:this.state.tdData.td390}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td389" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td389>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td389}</div>:this.state.tdData.td389}

                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td365" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td365>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td365}</div>:this.state.tdData.td365}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td366" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td366>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td366}</div>:this.state.tdData.td366}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td367" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td367>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td367}</div>:this.state.tdData.td367}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td368" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td368>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td368}</div>:this.state.tdData.td368}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td369" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td369>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td369}</div>:this.state.tdData.td369}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td370" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td370>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td370}</div>:this.state.tdData.td370}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td371" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td371>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td371}</div>:this.state.tdData.td371}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td372" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td372>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td372}</div>:this.state.tdData.td372}

                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td428" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td428>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td428}</div>:this.state.tdData.td428}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td427" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td427>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td427}</div>:this.state.tdData.td427}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td426" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td426>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td426}</div>:this.state.tdData.td426}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td425" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td425>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td425}</div>:this.state.tdData.td425}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td424" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td424>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td424}</div>:this.state.tdData.td424}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td423" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td423>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td423}</div>:this.state.tdData.td423}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td422" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td422>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td422}</div>:this.state.tdData.td422}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td421" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td421>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td421}</div>:this.state.tdData.td421}

                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td397" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td397>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td397}</div>:this.state.tdData.td397}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td398" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td398>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td398}</div>:this.state.tdData.td398}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td399" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td399>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td399}</div>:this.state.tdData.td399}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td400" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td400>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td400}</div>:this.state.tdData.td400}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td401" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td401>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td401}</div>:this.state.tdData.td401}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td402" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td402>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td402}</div>:this.state.tdData.td402}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td403" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td403>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td403}</div>:this.state.tdData.td403}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td404" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td404>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td404}</div>:this.state.tdData.td404}

                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td460" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td460>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td460}</div>:this.state.tdData.td460}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td459" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td459>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td459}</div>:this.state.tdData.td459}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td458" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td458>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td458}</div>:this.state.tdData.td458}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td457" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td457>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td457}</div>:this.state.tdData.td457}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td456" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td456>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td456}</div>:this.state.tdData.td456}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td455" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td455>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td455}</div>:this.state.tdData.td455}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td454" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td454>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td454}</div>:this.state.tdData.td454}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td453" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td453>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td453}</div>:this.state.tdData.td453}

                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td429" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td429>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td429}</div>:this.state.tdData.td429}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td430" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td430>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td430}</div>:this.state.tdData.td430}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td431" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td431>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td431}</div>:this.state.tdData.td431}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td432" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td432>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td432}</div>:this.state.tdData.td432}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td433" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td433>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td433}</div>:this.state.tdData.td433}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td434" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td434>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td434}</div>:this.state.tdData.td434}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td435" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td435>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td435}</div>:this.state.tdData.td435}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td436" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td436>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td436}</div>:this.state.tdData.td436}

                        </td>
                      </tr>

                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH40}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td461" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td461>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td461}</div>:this.state.tdData.td461}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td462" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td462>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td462}</div>:this.state.tdData.td462}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td463" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td463>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td463}</div>:this.state.tdData.td463}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td464" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td464>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td464}</div>:this.state.tdData.td464}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td465" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td465>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td465}</div>:this.state.tdData.td465}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td466" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td466>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td466}</div>:this.state.tdData.td466}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td467" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td467>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td467}</div>:this.state.tdData.td467}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td468" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td468>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td468}</div>:this.state.tdData.td468}

                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH40}>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td477" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td477>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td477}</div>:this.state.tdData.td477}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td478" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td478>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td478}</div>:this.state.tdData.td478}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td479" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td479>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td479}</div>:this.state.tdData.td479}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td480" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td480>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td480}</div>:this.state.tdData.td480}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td481" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td481>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td481}</div>:this.state.tdData.td481}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td482" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td482>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td482}</div>:this.state.tdData.td482}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td483" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td483>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td483}</div>:this.state.tdData.td483}

                        </td>
                        <td colSpan={3}
                            className={[this.state.activeTdId == "td484" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                            >
                          {this.state.tdData.td484>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td484}</div>:this.state.tdData.td484}

                        </td>
                      </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH40}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td493" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td493>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td493}</div>:this.state.tdData.td493}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td494" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td494>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td494}</div>:this.state.tdData.td494}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td495" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td495>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td495}</div>:this.state.tdData.td495}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td496" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td496>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td496}</div>:this.state.tdData.td496}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td497" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td497>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td497}</div>:this.state.tdData.td497}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td498" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td498>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td498}</div>:this.state.tdData.td498}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td499" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td499>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td499}</div>:this.state.tdData.td499}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td500" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td500>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td500}</div>:this.state.tdData.td500}

                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td509" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td509>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td509}</div>:this.state.tdData.td509}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td511" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td511>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td511}</div>:this.state.tdData.td511}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td513" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td513>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td513}</div>:this.state.tdData.td513}

                      </td>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td515" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus45 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td515>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td515}</div>:this.state.tdData.td515}
                      </td>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td516" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus44 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td516>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td516}</div>:this.state.tdData.td516}
                      </td>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td517" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus43 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td517>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td517}</div>:this.state.tdData.td517}
                      </td>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td518" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus42 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td518>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td518}</div>:this.state.tdData.td518}
                      </td>
                      <td colSpan={3} rowSpan={2}
                          className={[this.state.activeTdId == "td519" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus41 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td519>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td519}</div>:this.state.tdData.td519}
                      </td>
                    </tr>
                    {/*</tbody>*/}
                    {/*<tbody>*/}
                      <tr className={styles.trH20}>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td510" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus48 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td510>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td510}</div>:this.state.tdData.td510}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td512" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus47 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td512>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td512}</div>:this.state.tdData.td512}

                      </td>
                      <td colSpan={3}
                          className={[this.state.activeTdId == "td514" ? styles.triangleActive2 : " ", this.state.missesData.dentureStatus46 ? styles.missingTooth : ""].join(' ')}
                          >
                        {this.state.tdData.td514>3?<div style={{fontWeight:'bolder',fontSize:'14px'}}>{this.state.tdData.td514}</div>:this.state.tdData.td514}

                      </td>
                    </tr>
                    {/*</tbody>*/}
                  </table>
                </div>
              </div>
              <div className={styles.smallBox2}>
                <span className={styles.leftTitle}>牙缺失</span>
                <Checkbox style={{"width": "55px", textAlign: "center"}}
                          checked={this.state.missesData.dentureStatus48}
                          >48</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus47}
                          >47</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus46}
                          >46</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus45}
                          >45</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus44}
                          >44</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus43}
                          >43</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus42}
                          >42</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus41}
                          >41</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: "6px"}}
                          checked={this.state.missesData.dentureStatus31}
                          >31</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus32}
                          >32</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus33}
                          >33</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus34}
                          >34</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus35}
                          >35</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus36}
                          >36</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus37}
                          >37</Checkbox>
                <Checkbox style={{"width": "55px", textAlign: "center", marginLeft: 0}}
                          checked={this.state.missesData.dentureStatus38}
                          >38</Checkbox>
              </div>
            </div>
          </div>
        </div>
        <div style={{height:937,overflow:'scroll'}}>
          <div className={styles.tabs}>
                <Form>
                  <Form.Item>
                    {/*odontolith: 0,//'牙周检查牙石等级 0未知；1/2/3等级',*/}
                    {/*cleanStatus: 0,//'牙周检查洁治等级 0未知；1洁治前；2洁治后',*/}
                    <div className={styles.rightContent} hidden={(!this.state.params.odontolith||this.state.params.odontolith===0)&&(!this.state.params.cleanStatus||this.state.params.cleanStatus===0)}>
                      <div hidden={!this.state.params.odontolith||this.state.params.odontolith===0}>牙石 {odontolithContrast[this.state.params.odontolith]}</div>
                      <div style={{marginLeft:24}} hidden={!this.state.params.cleanStatus||this.state.params.cleanStatus===0}>{cleanStatusContrast[this.state.params.cleanStatus]}</div>
                    </div>
                  </Form.Item>
                  {toothPosition? Object.keys((toothPosition.occRelation||{})).map(key=>
                    <Form.Item
                      name={key}
                      hidden={StringUtils.isBlank(toothPosition.occRelation[key].toothPosition)&&StringUtils.isBlank(toothPosition.occRelation[key].value)}
                    >
                      <div style={{display:'flex'}}>
                        <div>{toothPosition.occRelation[key].name}：</div>
                        <div style={{marginLeft:5}}>
                          <ToothShow tooth={toothPosition.occRelation[key].toothPosition}/>
                          <div>{toothPosition.occRelation[key].value}</div>
                        </div>
                      </div>

                    </Form.Item>

                  ):null}
                  {toothPosition? Object.keys((toothPosition.other||{})).map(key=>
                    <Form.Item
                      name={key}
                      hidden={StringUtils.isBlank(toothPosition.other[key].toothPosition)&&StringUtils.isBlank(toothPosition.other[key].value)}
                    >
                      <div style={{display:'flex'}}>
                        <div>{toothPosition.other[key].name}：</div>
                        <div style={{marginLeft:5}}>
                          <ToothShow tooth={toothPosition.other[key].toothPosition}/>
                          <div>{toothPosition.other[key].value}</div>
                        </div>
                      </div>
                    </Form.Item>

                  ):null}
                  <Form.Item>
                    <div className={styles.rightContent}>
                      <div>
                        <div>检查描述：</div>
                        <div>
                          {this.state.params.examRemark}
                        </div>
                      </div>
                    </div>
                  </Form.Item>
                </Form>
          </div>
        </div>
      </div>
    );
  }
}

export default Periodontal;
