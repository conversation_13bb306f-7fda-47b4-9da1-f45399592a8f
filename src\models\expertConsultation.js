import {
  getOrgExpertsList,
  getOrgFilterDict,
  getOrgExpertsInfo,
  isOrgFocus,
  getCaseInfoByExpertsUserId,
  getOrgStarSpaceListBySearchUserId,
  getOrgExpertsQrCode,
  checkOrgUserBound,
  getMsgCode,
  boundStarUser,
  getConsultationOrderList,      // 获取指导订单列表
} from '@/services/expertConsultation';


export default {
  namespace: 'expertConsultation',
  state: {
    searchKey: '',          // 搜索关键字
    abilityLevelDict: null, // 能力等级字典
    city: null,             // 所属机构城市
    depSubjectDict: null,   // 科室字典
    postTitleDict: null,    // 职称字典
    tabKey:1,               // 空间、病例tab选中 1空间、2病例切换

    // 我的病例使用
    myConsultaionSearchKey:null,      // 搜索值
    selectByModalByType: null,        // 指导类型
    selectByModalByProcessNode:[],    // 进度
    selectByModalByStatus:[],         // 支付状态
    valueByRangePicker:null,          // 创建时间
    checkedTabkey:1,                  // tab选中
    checkBannerStatus:0,              // 导航选中数据
    searchType:3,                     // 搜索类型数据  1:订单号 2:病历号 3:患者姓名 4:创建人
  },

  effects: {
    // 诊所-分页获取专家信息列表
    *getOrgExpertsList({ payload }, { call }) {
      const response = yield call(getOrgExpertsList, payload);
      return response;
    },
    // 诊所-专家字典项
    *getOrgFilterDict({ payload }, { call }) {
      const response = yield call(getOrgFilterDict, payload);
      return response;
    },
    // 诊所-获取专家（个人）详情信息
    *getOrgExpertsInfo({ payload }, { call }) {
      const response = yield call(getOrgExpertsInfo, payload);
      return response;
    },
    // 诊所端-关注、取关 专家
    *isOrgFocus({ payload }, { call }) {
      const response = yield call(isOrgFocus, payload);
      return response;
    },
    // 诊所- 获取检索用户的空间数据列表
    *getOrgStarSpaceListBySearchUserId({ payload }, { call }) {
      const response = yield call(getOrgStarSpaceListBySearchUserId, payload);
      return response;
    },
    // 诊所- 通过专家用户ID，获取专家关联的优秀病历信息
    *getCaseInfoByExpertsUserId({ payload }, { call }) {
      const response = yield call(getCaseInfoByExpertsUserId, payload);
      return response;
    },
    // 视频获取专家二维码
    *getOrgExpertsQrCode({ payload }, { call }) {
      const response = yield call(getOrgExpertsQrCode, payload);
      return response;
    },
    // 校验用户是否绑定医生星球
    *checkOrgUserBound({ payload }, { call }) {
      const response = yield call(checkOrgUserBound, payload);
      return response;
    },
    // 获取短信验证码
    *getMsgCode({ payload }, { call }) {
      const response = yield call(getMsgCode, payload);
      return response;
    },
    // 绑定医生星球用户
    *boundStarUser({ payload }, { call }) {
      const response = yield call(boundStarUser, payload);
      return response;
    },
    // 获取指导订单列表
    *getConsultationOrderList({ payload }, { call }) {
      const response = yield call(getConsultationOrderList, payload);
      return response;
    },

  },

  reducers: {
    // 更新状态值数据
    setScreenState(state, { payload }) {
      return {
        ...state,
        ...payload
      }
    },

    // 清空数据
    clean(state, { payload }){
      return {
        ...state,
        searchKey: '',          // 搜索关键字
        abilityLevelDict: null, // 能力等级字典
        city: null,             // 所属机构城市
        depSubjectDict: null,   // 科室字典
        postTitleDict: null,    // 职称字典
        tabKey:1,               // 空间、病例tab选中 1空间、2病例切换

        // 我的病例使用
        myConsultaionSearchKey:null,        // 搜索值
        selectByModalByType: null,          // 指导类型
        selectByModalByProcessNode:[],    // 指导进度
        selectByModalByStatus:[],         // 支付状态
        valueByRangePicker:null,          // 创建时间
        checkedTabkey:1,                  // tab选中
        checkBannerStatus:0,              // 导航选中数据
        searchType:3,                     // 搜索类型数据
      }
    }
  },
  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, search }) => {
        if(pathname.indexOf('/excellentCaseStudy/details')== -1 && pathname.indexOf('/ExpertConsultation/ExpertDetail')== -1){
          dispatch({
            type: "setScreenState",
            payload: {
              tabKey:1
            }
          })
        }

        if (!(/\/ExpertConsultation\/index/.test(pathname) || /\/ExpertConsultation\/ExpertDetail/.test(pathname) || /\/excellentCaseStudy\/details/.test(pathname))) {
          dispatch({
            type: "clean",
            payload: {}
          })
        }
      })
    }
  }
};
