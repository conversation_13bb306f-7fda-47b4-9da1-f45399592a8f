@import '~antd/es/style/themes/default.less';

.popUp_check .select {
  width: 100px;
}

.popUp_check_box .result {
  width: 100%;
  display: inline-block;
  margin-bottom: 10px;
}

.popUp_check_box  .result>span {
  float: left;
}

.popUp_check_box .checkResult>li {
  float: right;
  list-style: none;
  margin-right: 5px;
}

.popUp_check_box .checkResult>li>span {
  color: #1890ff;
}
.popUp_check_box .reset{
  width: 40px;
  font-size: 12px;
  height: 20px;
}
.popUp_check_box .leftBox {
  position: relative;
  width: 20%;
  height: 400px;
  border-left: 1px solid gainsboro;
  border-top: 1px solid gainsboro;
  border-right: 1px solid gainsboro;
  display: inline-block;
  border-bottom: 1px solid gainsboro;
  overflow: hidden;
}
.popUp_check_box .choice{
  margin-left: 24% !important;
  margin-top: 5px;
}
.popUp_check_box .leftBox>div:nth-of-type(1){
  border-bottom: 1px solid gainsboro;
}
.centerBox>div:nth-of-type(1){
  border-bottom: 1px solid gainsboro;
}
.popUp_check_box .siteBox {
  height: 40px;
  line-height: 40px;
}
.popUp_check_box .siteBoxs{
  position: absolute;
  display: flex;
  justify-content: space-around;
  border-bottom: 1px solid gainsboro;
  //width: 17.5%;
  width: 20.5%;
  background: white;
  z-index: 100;
  height: 40px;
  line-height: 40px;
}
.popUp_check_box .siteBox>span {
  display: inline-block;
  margin-left: 7px;
}
.popUp_check_box .siteBoxs>div {
  //margin-left: 7px;
}
//
.popUp_check_box .siteBox>button {
  float: right;
  margin-top: 10px;
  margin-right: 10px;
}

.popUp_check_box .pterTeeth {
  clear: both;
  width: 85%;
  margin-left: 8%;
  margin-top: 5px;
}
.decidteeth {
  clear: both;
  width: 75%;
  margin-left: 14%;
  margin-top: 5px;
}
.popUp_check_box .pterTeeth>span {
  float: left;
  width: 11px;
  text-align: center;
  height: 20px;
  line-height: 20px;
}
.decidteeth>span {
  float: left;
  width: 15px;
  text-align: center;
  height: 20px;
  line-height: 20px;
}

.popUp_check_box .parment>div:nth-of-type(2) {
  border-bottom: 1px solid gainsboro;
}
.popUp_check_box .decid>p:nth-of-type(2) {
  border-bottom: 1px solid gainsboro;
}
.popUp_check_box .lineborder{
  position: absolute;
  border-right: 1px solid gainsboro;
  height: 40px;
  top: 73px;
  //left: 50.2%;
  left: 42.4%;
}
.popUp_check_box  .decidline{
  position: absolute;
  border-right: 1px solid gainsboro;
  height: 40px;
  top: 80px;
  left: 43.3%;
}
.popUp_check_box .cusp{
  display: flex;
  align-items:center;
  justify-content: center;
  width: 100%;
  height: 287px;
}
.popUp_check_box .centerBox{
  float: right;
  width: 22%;
  height: 400px;
  border-right: 1px solid gainsboro;
  border-top: 1px solid gainsboro;
  border-bottom: 1px solid gainsboro;
  overflow-x:hidden;
  overflow-y: scroll
}
.popUp_check_box .rightBox{
  float: right;
  width: 58%;
  height: 400px;
  /* border-right: 1px solid gainsboro;*/
  border-top: 1px solid gainsboro;
  border-bottom: 1px solid gainsboro;
  overflow-x:scroll;
  overflow-y: scroll
}

.popUp_check_box{
  .notesadd{
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #f3f1f1;
  }
  .notesadd>span:nth-of-type(1) {
    display: inline-block;
    width: 15%;
    text-align: center;
    padding: 4px;
    background-color: #fafafa;
    border-right: 1px solid #f3f1f1;
    line-height: 30px;
  }
  .notes>span:nth-of-type(1) {
    display: inline-block;
    width: 15%;
    text-align: center;
    line-height: 30px;
    background-color: #fafafa;
    border-right: 1px solid #f3f1f1;
    height: 30px;
    margin: 0;
  }
  .notes>input{
    display: inline-block;
    width: 85%;
  }
  /* 扇形 */
  .sector{
    margin-left: 20%;
    position: relative;
    width: 200px;
    height: 200px;
    border-radius: 100%;
    background-color: gray;
  }
  .sectorchild1{
    position: absolute;
    border: 100px solid transparent;
    width: 0;
    border-radius: 100px;
    border-top-color: white;
    z-index: 0;
  }

  .sectorchild2{
    position: absolute;
    border: 100px solid transparent;
    width: 0;
    border-radius: 100px;
    border-left-color: white;
  }
  .sectorchild3{
    position: absolute;
    border: 100px solid transparent;
    width: 0;
    border-radius: 100px;
    border-right-color: white;
  }
  .sectorchild4{
    position: absolute;
    border: 100px solid transparent;
    width: 0;
    border-radius: 100px;
    border-bottom-color: white;
  }
  .sectorchild5{
    position: absolute;
    top: 57px;
    left: 57px;
    width: 85px;
    height: 85px;
    border: 1px solid  gainsboro;
    z-index: 1;
    border-radius: 100%;
    background-color: white;
  }
  .sector>span:nth-of-type(1){
    position: absolute;
    top: 20px;
    left: 43%;
    z-index: 1;
  }
  .sector>span:nth-of-type(2){
    position: absolute;
    top: 90px;
    left: 75%;
    z-index: 1;
  }
  .sector>span:nth-of-type(3){
    position: absolute;
    top: 90px;
    left: 5%;
    z-index: 1;
  }
  .sector>span:nth-of-type(4){
    position: absolute;
    top: 160px;
    left: 43%;
    z-index: 1;
  }
  .sector>span:nth-of-type(5){
    position: absolute;
    top: 90px;
    left: 43%;
    z-index: 1;
  }
  .hide{
    display: none;
  }
  .red{
    background-color: red;
    color: white;
    cursor: pointer;
  }
  .gray{
    background-color: gray;
    color: white;
    cursor: pointer;
  }
  .grays{
    text-align: center;
    cursor: pointer;
  }
  .customSelect {
    :global {
      .ant-tooltip-inner {
        min-width: 495px !important;
        min-height: 32px;
        padding: 0px 0px !important;
        color: #130c0c !important;
        text-align: left;
        text-decoration: none;
        word-wrap: break-word;
        background-color:white !important;
        border-radius: 2px;
        box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
      }
    }
  }

}


// table
.popUp_check_box .tablenav{
  background-color: #fafafa;
  height: 34px;
  line-height: 34px;
}
.popUp_check_box .tablenav>span{
  display: inline-block;
  //width: 14.25%;
  width: 18.25%;
  height: 34px;
  line-height: 34px;
  text-align: center;
  border-top: 1px solid #f1f1f1;
  border-right: 1px solid #f1f1f1;
  border-bottom: 1px solid #f1f1f1;

}
.popUp_check_box .datasoure{
  height: 34px;
  background: #f3f9fe;
}
.popUp_check_box .datasoure>span{
  display: inline-block;
  //width: 14.25%;
  width: 18.25%;
  height: 34px;
  line-height: 34px;
  text-align: center;
  border-right: 1px solid #f1f1f1;
  border-bottom: 1px solid #f1f1f1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.popUp_check_box .datasoure>:last-child{
  color: #1890ff;
  cursor: pointer;
}
.popUp_check_box .tablenav>div:nth-child(odd){
  background-color: #fafafa;
}


.toothImg{
  position: absolute;
}
.imgNoInfo{
  padding:110px 0;
  text-align: center;
  margin-left: 17px;
  img{
    width:90px;
    height:90px;
    margin:auto;
  }
  .fontSize{
    padding: 18px 12px 0 0;
    color: rgba(0, 0, 0, 0.45);
  }
}
