import { query as queryUsers, queryCurrent } from '@/services/user';
import { getPageQuery } from '@/utils/utils';
import { batchUpdateStatus,  getMessageCount, getMessageList, getOrganizationList, getSwitchLogin, updateMessage, replaceShowUrl } from '@/services/inform';
import { stat } from 'fs';
import { routerRedux } from 'dva/router';
import { setOrganizationInfo } from '@/utils/utils';
import { setAuthority } from '@/utils/authority';
import { reloadAuthorized } from '@/utils/Authorized';
import { orgDeployInfoList } from '@/services/Setup';
export default {
  namespace: 'user',

  state: {
    list: [],
    currentUser: {},
    currentUserList: [],
    OrganizationList: [], // 机构列表
    NoticeActiveKey: "",   // 地址key 通知列表用
  },

  effects: {
    //获取用户头像
    *getReplaceShowUrl({ payload }, { call, put }) {
      const response = yield call(replaceShowUrl, payload);
      if (response && response.code == 200 && response.content) {
        localStorage.setItem("avatar", response.content)
      }
      return response
    },
    //获取机构列表
    *getOrganizationList({ payload }, { call, put }) {
      const response = yield call(getOrganizationList, payload);
      yield put({
        type: 'OrganizationList',
        payload: response,
      });
    },
    // 登陆后推送 返回信息
    // *getQueryTask({ payload }, { call, put }) {
    //   const response = yield call(getQueryTask, payload);
    //   return response
    // },
    // 切换机构id后获取信息
    *getSwitchLogin({ payload }, { call, put }) {
      const response = yield call(getSwitchLogin, payload);
      const responseOrgDeployInfoList = yield call(orgDeployInfoList, payload);
      if (response.code == 200) {
        if(responseOrgDeployInfoList && responseOrgDeployInfoList.code == 200) {
          const { chargeModel,waitOvertime } = responseOrgDeployInfoList.content || {}
          setOrganizationInfo({
            ...response.content.organizationInfo,
            chargeModel,  // 保存当前机构的收费模式
            waitOvertime, // 保存当前机构的等待超时时间
          });  // 保存所属机构信息
        }else {
          setOrganizationInfo(response.content.organizationInfo);  // 保存所属机构信息
        }
        localStorage.setItem('organizationInfoId', response.content.organizationInfo.id); // 保存机构Id
        localStorage.setItem('organizationSize', response.content.organizationSize); // 用户下有多少机构
        // 拿到每个权限的id通过 item.platformId
        let scope = [];
        response.content.menuInfoList.map((item) => {
          scope.push(item.platformId)
        })
        setAuthority(scope);
        // 角色
        let role = [];
        response.content.userRoleList.map(item => {
          role.push({
            roleCode: item.roleCode,
            roleName: item.roleName,
            tenantId: item.tenantId,
            roleId: item.roleId
          })
        })
        localStorage.setItem('role', JSON.stringify(role)) // 多角色
        localStorage.setItem('doctorIdentification', response.content.doctorIdentification)  // 1医生  2非医生
        localStorage.setItem("accountNumber", response.content.accountNumber); // 账号
        localStorage.setItem("id", response.content.id); // 用户id
        localStorage.setItem("tenantId", response.content.tenantId); // 租户id
        localStorage.setItem("phone", response.content.phone); // 用户手机号
        localStorage.setItem("email", response.content.email); // 用户邮箱
        localStorage.setItem("avatar", response.content.headUrlView); // 用户头像
        localStorage.setItem("userName", response.content.userName); // 用户名字
        localStorage.setItem("isResources", response.content.isResources); // 是否是资源  可以进入其他设置  1是
        localStorage.setItem('tenantName', response.content.tenantName);     // 品牌名称
        localStorage.setItem('isNeedSignRecord', response.content.organizationInfo && response.content.organizationInfo.isNeedSignRecord);     // 判断到诊图例小程序预约患者图例是否显示  0需要 1不需要
        localStorage.setItem('fourOnState',response.content.fourOnState);    //   400列表开启状态(0：关闭，1：开启)
        localStorage.setItem('procurementAuth',response.content.procurementAuth);    // 登录后的授权信息
        // 切换诊所确认是否开启人脸识别
        if(response.content.wjUrl != null && response.content.wjUrl != ""){
          window.location.replace(response.content.wjUrl);
        }else {
          yield put(routerRedux.replace("/"));
        }
      } else {
        Modal.error(response.msg);
        return false;
      }
      reloadAuthorized();
    },
    // 获取未读消息数量
    *getMessageCount(_, { call, put }) {
      const response = yield call(getMessageCount);
      yield put({
        type: 'saveCurrentUser',
        payload: response,
      });
    },
    // 清空未读信息数量
    *batchUpdateStatus(_, { call, put }) {
      const response = yield call(batchUpdateStatus);
      return response;
    },
    // 获取未读消息列表
    *getMessageList({ payload }, { call, put, select }) {
      const response = yield call(getMessageList, payload);
      yield put({
        type: 'saveCurrentUserList',
        payload: response,
      });
    },
    // 点击未读
    *updateMessage({ payload }, { call, put }) {
      const response = yield call(updateMessage, payload);
      return response;
    },
    // 跳转通知列表页面
    *Jump(_, { call, put, select }) {
      const str = yield select((state => state.user.NoticeActiveKey))
      let key = "0"
      if (str) {
        key = str.NoticeActiveKey ? str.NoticeActiveKey : 0;
      }
      yield put(routerRedux.replace(
        {
          pathname: `/notification`,
          query: {
            NoticeActiveKey: key,
          }
        }));
    },
  },

  reducers: {
    OrganizationList(state, action) {
      return {
        ...state,
        OrganizationList: action.payload
      }
    },
    saveCurrentUser(state, action) {
      return {
        ...state,
        currentUser: action.payload || {},
      };
    },
    saveCurrentUserList(state, action) {
      let arr = action.payload && action.payload.data && action.payload.data.messageInfos && action.payload.data.messageInfos.list;
      return {
        ...state,
        currentUserList: arr || [],
      };
    },
    saveActive(state, action) {
      return {
        ...state,
        NoticeActiveKey: action.payload,
      };
    },
  },
  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, query, search }) => {
        // 获取地址参数
        const UrlName = window.location.search;
        if (UrlName.indexOf("NoticeActiveKey") > -1) {
          const NoticeActiveKey = getPageQuery().NoticeActiveKey;
          dispatch({
            type: 'saveActive', payload: {
              NoticeActiveKey: query.NoticeActiveKey
            }
          })
        }
      });
    },
  },
};
