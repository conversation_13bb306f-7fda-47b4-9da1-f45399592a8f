import React, {Component} from 'react';
import {Card,Radio,Popover,Tooltip} from 'antd';
import PropTypes from 'prop-types';
import $ from 'jquery';
import moment from 'moment';
import styles from './index.less'
import classNames from 'classnames';

/**
 * 手工标签组件
 */
export default class Manualicon extends Component {
  static propTypes = {
    appointmentIconNewDtoList:PropTypes.any,
    lineHeight:PropTypes.string,
    height:PropTypes.string,
    padding:PropTypes.string,
    marginBottom:PropTypes.string
  }

  static defaultProps = {
    appointmentIconNewDtoList:null
  }

  render() {
    const { appointmentIconNewDtoList, lineHeight, height, padding, marginBottom } = this.props
    return (
      <>
        {
          Array.isArray(appointmentIconNewDtoList) && appointmentIconNewDtoList.map((item,index)=>{
            return (
              <div
                key={index}
                style={
                  {
                    boxSizing: 'border-box',
                    border: '1px solid ',
                    borderColor: item.iconColour,
                    background: item.iconColour,
                    color: '#ffffff',
                    lineHeight: lineHeight ? lineHeight : null,
                    height: height ? height : null,
                    padding: padding ? padding : null,
                    marginBottom: marginBottom ? marginBottom : null,
                  }
                }
                className={styles.iconStyle}
              >
                {item.iconName}
              </div>
            )
          })
        }
      </>
    )
  }
}
