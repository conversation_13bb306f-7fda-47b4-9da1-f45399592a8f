@import '~antd/es/style/themes/default.less';

// 全部病历样式开始
@media print {
  //不显示按钮
  .noprint {
    display: none;
  }
}
.tagChecked {
  display: inline-block;
  width: 80px;
  height: 26px;
  color: #1890ff;
  line-height: 26px;
  text-align: center;
  background: rgba(24, 144, 255, 0.06);
  border: 0;
  border-radius: 2px;
  cursor: pointer;
}
.printMedicl{
  margin-left: 8px;
}
.tagNoCheck {
  display: inline-block;
  width: 80px;
  height: 26px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 26px;
  text-align: center;
  background: transparent;
  border: 0;
  border-radius: 2px;
  cursor: pointer;
}

.block9 {
  width: 97%;
  height: 44px;
  // margin: 0 0 10px 16px;
  background-color: rgba(247, 248, 250, 1);
  border-radius: 2px;
}

// .historyRecord{
//   height: 28px;
//   line-height: 28px;
// }
:global {
  // 折叠面板
  .ant-collapse-header {
    padding: 10px !important;
    background: #f8f8fa;
  }

  .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
    padding: 12px 10px 0 !important;
  }

  .ant-collapse-content > .ant-collapse-content-box {
    padding: 0 !important;
  }

  // form表单的label字体颜色
  .ant-form-item-label > label {
    color: rgba(0, 0, 0, 0.45);
    font-weight: 400;
    font-size: 14px;
    font-family: PingFang SC;
  }

  .ant-form-item {
    margin-bottom: 12px;
  }

  // 表格头部
  .ant-table-thead > tr > th {
    padding: 10px;
    color: rgba(0, 0, 0, 0.45);
  }

  // 弹窗样式
  .ant-modal-title {
    font-weight: 600;
  }

  .ant-btn ant-btn-default {
    display: none;
  }
}
.screen {
  :global {
    .ant-picker-suffix {
      display: none !important;
    }

    .ant-picker-input>input {
      color:#4292ff;
      &::placeholder {
        color:#4292ff;
        opacity: 1
      }
    }
    .ant-picker{
      padding: 0;
    }

  }
}
.main9 {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 25px;
}

.rowStyle{
  // display: flex;
  // justify-content: space-between;
  width: 100%;
  height: 25px;
}

.oftenTel {
  :global {
    .ant-input-group-addon {
      background-color: transparent;
      text-align: inherit;
    }
    .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-color: transparent;
      box-shadow: none;
    }
    .ant-input-group-addon .ant-select-open .ant-select-selector, .ant-input-group-addon .ant-select-focused .ant-select-selector {
      color: inherit;
    }
  }

}
.bd15 {
  width: 52px;
  height: 22px;
  margin-top: 1px;
  background-color: rgba(254, 235, 235, 1);
  border: 1px solid rgba(252, 197, 197, 1);
  border-radius: 4px;
}

.word24 {
  height: 20px;
  margin: 1px 0 0 8px;
  color: rgba(245, 63, 63, 1);
  font-size: 12px;
  font-family: PingFang SC;
  line-height: 20px;
  white-space: nowrap;
  text-align: left;
  overflow-wrap: break-word;
}

.txt9 {
  height: 25px;
  padding: 0 10px;
  color: #212121;
  font-size: 14px;
  font-family: PingFang SC;
  line-height: 25px;
  white-space: nowrap;
  text-align: left;
  overflow-wrap: break-word;
}

.info11 {
  height: 25px;
  padding: 0 10px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  font-family: PingFang SC;
  line-height: 25px;
  white-space: nowrap;
  text-align: left;
  overflow-wrap: break-word;
}

.word25 {
  display: inline-block;
  height: 25px;
  padding: 0 10px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  font-family: PingFang SC;
  line-height: 25px;
  white-space: nowrap;
  text-align: left;
  overflow-wrap: break-word;
}

.colFloat {
  float: right;
}

.txt11 {
  display: inline-block;
  height: 25px;
  padding-right: 10px;
  color: rgba(66, 146, 255, 1);
  font-size: 14px;
  font-family: PingFang SC;
  line-height: 25px;
  white-space: nowrap;
  text-align: right;
  overflow-wrap: break-word;
  cursor: pointer;
  // float: right;
}

.siftImg {
  width: 15px;
  height: 15px;
  margin-right: 8px;
  line-height: 25px;
  cursor: pointer;
}

// .screen {
//   // :global {}
// }

.tagSuccess {
  color: #00b42a !important;
  background: rgba(0, 180, 42, 0.1) !important;
  border-color: rgba(0, 180, 42, 0.3) !important;
  // padding: 0 10px;
  border-radius: 4px !important;
}

.tagRed {
  color: rgba(245, 63, 63) !important;
  background: rgba(245, 63, 63, 0.1) !important;
  border-color: rgba(245, 63, 63, 0.3) !important;
  // padding: 0 10px;
  border-radius: 4px !important;
}

.tagWarn {
  color: rgba(245, 63, 63) !important;
  background: rgba(255, 125, 0, 0.1) !important;
  border-color: rgba(255, 125, 0, 0.3) !important;
  border-radius: 4px !important;
}

.panel_h {
  width: 480px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.list_bottom {
  min-height: 200px;

  // :global {
  //   .ant-form-item {
  //     margin-bottom: 7px;
  //   }

  //   // 表格头部
  //   .ant-table-thead>tr>th {
  //     padding: 10px;
  //     color: rgba(0, 0, 0, 0.45)
  //   }

  //   // 弹窗样式
  //   .ant-modal-title {
  //     font-weight: 600;
  //   }

  //   .ant-btn ant-btn-default {
  //     display: none;
  //   }
  // }
}

.userModal{
  :global {
    .ant-modal-body {
      padding: 7px 15px 24px !important;
    }
  }
}
.contentinner {
  width: 100%;
  // padding: 0 20px;
  color: #212121;
  font-size: 14px;

  .ant-form-item-control-input-content {
    width: 70%;
  }

  .inntable {
    padding: 0 20px;

    .executory {
      color: #67c23a;
    }
  }

  .label {
    display: flex;

    span {
      margin-top: 2px;
    }
  }
}

.table {
  width: 45px;
  height: 25px;

  td {
    text-align: center;
  }

  .td {
    width: 100%;
    border: 1px solid #999;
    border-top: 0;
    border-left: 0;
  }

  .bottom_td {
    width: 100%;
    border: 1px solid #999;
    border-right: 0;
    border-bottom: 0;
  }
}

.compile {
  position: absolute;
  top: 15px;
  right: 20px;
  width: 18%;
  height: 118px;
  font-size: 35px;
  background-color: #e6a23c;
}

.check {
  display: flex;
  margin-bottom: 5px;

  .message {
    margin-top: 5px;
    margin-left: 10px;
  }
}

// 预览图片

.ctimgStyle {
  width: 128px;
  height: 88px;
}

.images {
  display: flex;
  width: 280px;
  border: 1px solid rgba(0, 0, 0, 0.06);

  .imgborder {
    position: relative;
  }

  .ctimgInfo {
    padding-top: 5px;
    padding-left: 8px;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 400;
    font-size: 14px;
  }

  .ctimgdelete {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #f0f0f0;
    text-align: center;
    background: rgba(0, 0, 0, 0.3);
  }

  .icon_delete {
    width: 20px;
    height: 20px;
  }

  .deleteFont {
    margin-top: 1px;
    margin-left: 4px;
    font-size: 14px;
  }
}

//牙位开始
// .tooth_position {
//   width: 200px;
//   height: 50px;
//   margin-bottom: 7px;

//   td {
//     width: 72px;
//     text-align: center;
//   }

//   .first {
//     height: 16px;
//     border-right: 1px solid rgba(0, 0, 0, 0.15);
//     border-bottom: 1px solid rgba(0, 0, 0, 0.15);
//   }

//   .second {
//     height: 16px;
//     border-bottom: 1px solid rgba(0, 0, 0, 0.15);
//   }

//   .third {
//     height: 16px;
//     border-right: 1px solid rgba(0,0,0,0.15);
//   }

//   .fourth {
//     height: 16px;
//   }
// }

// 牙位结束
// .ellipse {
//   display: -webkit-box;
//   height: 40px;
//   overflow: hidden;
//   -webkit-box-orient: vertical;
//   -webkit-line-clamp: 2;
// }

// 全部病历样式结束

// 病历首页样式开始

.content {
  padding: 20px;
}

.width100 {
  width: 100%;
}
.printText{
  position: absolute;
  top: 63px;
  right: 60px;
  float:right;
}
// 头部
.indexTop {
  border-bottom: 1px solid rgba(21, 0, 0, 0.06);
  padding: 0 30px;
}

.topTitle {
  margin-bottom: 10px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 24px;
  text-align: center;
}

.secondTitle {
  margin-bottom: 10px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 20px;
  text-align: center;
}

.date {
  margin-bottom: 10px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  text-align: right;
}

.userInfo {
  border-bottom: 1px dashed rgba(21, 0, 0, 0.06);
  padding:0 30px;
}

.displayFlex {
  display: flex;
  justify-content: space-between;
  margin-top: 21px;
}

.padding70 {
  padding: 12px 70px 16px;
  //   margin-top:15px;
  // display: flex;
  // justify-content: space-around;
}

.padding20 {
  padding: 12px 0 16px;
  margin-left: -3%
}

.textAlign {
  text-align: center;
}

.col8 {
  height: 24px;
  margin-bottom: 8px;
}

.names{
  margin-top:4px;
  color: rgba(0, 0, 0, 0.45);
  font-weight: 400;
  font-size: 14px;
  font-family: PingFang SC;
}
.userName {
  vertical-align: middle;
  color:rgba(0, 0, 0, 0.85);
  display: inline-block;
  width: 60%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}

// 健康状况
.pLineHeight {
  display: block;
  width: 100%;
  color: rgba(0, 0, 0, 0.45);
}

// 预览弹窗中的病历标题
.peopleInfo_border {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  // border-bottom: 1px solid #E5E6EB;
  padding: 10px;
  background: #f8f8fa;
}
.healthEllipse {
  max-width:400px;
  // min-width: 396px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.peopleInfo {
  display: flex;
}

//已收费(绿色框)
.group6 {
  width: 44px;
  height: 22px;
  margin-left: 8px;
  line-height: 18px;
  text-align: center;
  background-color: rgba(0, 180, 42, 0.1);
  border: 1px solid rgba(0, 180, 42, 0.3);
  border-radius: 4px;
}

.word6 {
  color: rgba(0, 180, 42, 1);
  font-size: 12px;
  font-family: PingFang SC;
  white-space: nowrap;
}

//病历(红色框)
.group7 {
  width: 44px;
  height: 22px;
  margin-left: 8px;
  line-height: 18px;
  text-align: center;
  background-color: rgba(245, 63, 63, 0.1);
  border: 1px solid rgba(245, 63, 63, 0.3);
  border-radius: 4px;
}

.txt8 {
  color: rgba(245, 63, 63, 1);
  font-size: 12px;
  font-family: PingFang SC;
  white-space: nowrap;
}

.fontgray {
  margin-left: 30px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  font-family: PingFang SC;
}

// @media screen and (min-width:1024px) {
.healthSpan {
  display: inline-block;
  max-width: 418px;
  // height: 22px;
  overflow: hidden
}
// }

// 病历首页样式结束
.loading_more {
  padding: 0 0 25px;
  color: #4292ff;
  font-size: 15px;
  text-align: center;
  // text-decoration: underline;
  cursor: pointer;
}
.AllContent{
  :global{
    .ant-collapse-header-text{
      width: 100% !important;
    }
  }
}
.textInputStyle {
  :global {
    .ant-input-textarea-show-count::after {
      position: relative;
      bottom: 25px;
      right: 5px;
    }
  }
}

.printModal{
  :global {
    .ant-modal-footer{
      border-top: none;
    }
  }
}
