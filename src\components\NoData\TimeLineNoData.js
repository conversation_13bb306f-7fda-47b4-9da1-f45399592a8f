import React, { Component } from 'react';
import classNames from 'classnames';
import NodataImg from "@/assets/nodata.png"
import styles from "./nodata.less"
class NoData extends Component{
    render(){
        const { className } = this.props;
        return(
            <div className={classNames(className,styles.TimeLinetablebox)}>
                <div className={styles.timeLineNodata}>
                    <img className={styles.nodataImg} src={NodataImg}/>
                    <p className={styles.nodataText}>暂无数据</p>
                </div>
            </div>
        )
    }
}
export default NoData;
