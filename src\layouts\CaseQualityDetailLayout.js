import React from 'react';
import { Layout, LocaleProvider } from 'antd';
import DocumentTitle from 'react-document-title';
import { connect } from 'dva';
import { ContainerQuery } from 'react-container-query';
import classNames from 'classnames';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import Header from './Header';
import Context from './MenuContext';

const { Content } = Layout;

const query = {
  'screen-xs': {
    maxWidth: 575,
  },
  'screen-sm': {
    minWidth: 576,
    maxWidth: 767,
  },
  'screen-md': {
    minWidth: 768,
    maxWidth: 991,
  },
  'screen-lg': {
    minWidth: 992,
    maxWidth: 1199,
  },
  'screen-xl': {
    minWidth: 1200,
    maxWidth: 1599,
  },
  'screen-xxl': {
    minWidth: 1600,
  },
};

class CaseQualityDetailLayout extends React.PureComponent {
  getContext() {
    const { location } = this.props;
    return {
      location,
      breadcrumbNameMap: {},
    };
  }

  getPageTitle = () => '病历质检详情 - FRIDAY口腔业务综合管理平台';

  render() {
    const {
      children,
      ...restProps
    } = this.props;
    const layout = (
      <Layout style={{ minHeight: '100vh', minWidth: '1024px' }}>
        <Header {...restProps} customLayoutType='topheader' />
        <Content>
          {children}
        </Content>
      </Layout>
    );

    return (
      <LocaleProvider locale={zhCN}>
        <React.Fragment>
          <DocumentTitle title={this.getPageTitle()}>
            <ContainerQuery query={query}>
              {params => <Context.Provider value={this.getContext()}><div className={classNames(params)}>{layout}</div></Context.Provider>}
            </ContainerQuery>
          </DocumentTitle>
        </React.Fragment>
      </LocaleProvider>
    );
  }
}

export default connect(({ global, setting, login, loading }) => ({
  collapsed: global.collapsed,
  menuCollapsed: global.menuCollapsed,
  layout: setting.layout,
  login,
  ...setting,
  loading
}))(CaseQualityDetailLayout);
