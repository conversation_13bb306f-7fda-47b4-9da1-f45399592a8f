import {
  getComplaintDetails,                       // 查询投诉详情
  getCurrentComplaintDetails,                // 跟进时，查询当前的投诉信息
  saveInfo,                                  // 保存投诉人信息
  addComplaintRecord,                        // 记录跟进
  searchComplaint,                           // 根据关键字搜索投诉
  getAllNode,                                // 转交他人，查询所有节点
  getAllUser,                                // 转交他人，查询所有用户
  deleteComplaintNumber,                     // 投诉跟进，删除关联单号
} from '@/services/complaint'

export default {
  namespace: 'complaint',
  state: {},
  effects: {
    // 查询投诉详情
    *getComplaintDetails({ payload: params }, { call }) {
      const res = yield call(getComplaintDetails, params)
      return res
    },
    // 跟进时，查询当前的投诉信息
    *getCurrentComplaintDetails({ payload: params }, { call }) {
      const res = yield call(getCurrentComplaintDetails, params)
      return res
    },
    // 保存投诉人信息
    *saveInfo({ payload: params }, { call }) {
      const res = yield call(saveInfo, params)
      return res
    },
    // 记录跟进
    *addComplaintRecord({ payload: params }, { call }) {
      const res = yield call(addComplaintRecord, params)
      return res
    },
    // 根据关键字搜索投诉
    *searchComplaint({ payload: params }, { call }) {
      const res = yield call(searchComplaint, params)
      return res
    },
    // 转交他人，查询所有节点
    *getAllNode({ payload: params }, { call }) {
      const res = yield call(getAllNode, params)
      return res
    },
    // 转交他人，查询所有用户
    *getAllUser({ payload: params }, { call }) {
      const res = yield call(getAllUser, params)
      return res
    },
    // 投诉跟进，删除关联单号
    *deleteComplaintNumber({ payload: params }, { call }) {
      const res = yield call(deleteComplaintNumber, params)
      return res
    },
  },
  reducers: {
    // 保存数据
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      }
    }
  }
}
