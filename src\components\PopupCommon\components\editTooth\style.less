.toothBox {
  width: 1200px;
  height: 458px;
  margin-top: 35px;
  font-size: 12px;
  position: relative;
  .btns {
    text-align: left;
    margin-bottom: 10px;
  }
  .ant-menu-item-selected {
    background-color: #fff !important;
  }
  .today {
    width: 70px;
    text-align: center;
    border-right: 1px solid #e0e0e0;
    display: inline-block;
  }

  .history {
    width: 70px;
    text-align: center;
    display: inline-block;
  }

  .btn {
    margin-left: 46%;
  }

  .btns {
    margin-left: 1%;
  }

  .toothBox {
    width: 1200px;
    height: 458px;
    margin-top: 35px;
    font-size: 12px;
    position: relative;
  }

  .topBox {
    height: 195px;
  }

  .bigimg {
    width: 40px;
    height: 133px;
    cursor: pointer;
  }
  .projectimg {
    position: absolute;
    left: 0px;
  }
  .inul {
    float: left;
    margin-left: 20px;
  }

  .inli {
    margin-top: 25px;
    display: block;
    position: relative;
    cursor: pointer;
  }
  .upimg{
    width: 40px;
    position: absolute;
    left: 0px;
    height: 133px;
  }
  .upimgs {
    margin-top: 10px;
    width: 40px;
    height: 40px;
    position: absolute;
    left: 5px;
  }
  .smallimg {
    margin-top: 10px;
    width: 40px;
    height: 40px;
  }
  .status{
    position: absolute;
    top: 0px;
    font-size: 16px;
    margin-left: 12px;
  }
  .statusbottom{
    display: block;
    left: 0;
    bottom: 0;
    font-size: 16px;
    margin-left: 12px;
  }
  .seat {
    display: block;
    width: 20px;
    height: 20px;
    border: 1px solid gainsboro;
    text-align: center;
    border-radius: 100%;
    margin-left: 10px;
    margin-top: 10px;
    cursor: pointer;
  }
  .seatm{
    border: none;
  }

  /*  左右 分隔 */

  .interval {
    width: 100%;
    height: 30px;
  }

  .interval>span {
    float: left;
  }

  .interval>span:nth-of-type(1) {
    border-bottom: 1px solid gainsboro;
    width: 220px;
    position: absolute;
    top: 225px;
    left: 20px;
  }

  .interval>span:nth-of-type(2) {
    position: absolute;
    top: 215px;
    left: 245px;
  }

  .interval>span:nth-of-type(3) {
    position: absolute;
    width: 460px;
    top: 225px;
    left: 260px;
    border-bottom: 1px solid gainsboro;
  }

  .interval>span:nth-of-type(4) {
    position: absolute;
    height: 130px;
    top: 160px;
    left: 490px;
    border-right: 1px solid gainsboro;
  }

  .interval>span:nth-of-type(5) {
    position: absolute;
    top: 215px;
    left: 725px;
  }

  .interval>span:nth-of-type(6) {
    border-bottom: 1px solid gainsboro;
    position: absolute;
    width: 220px;
    top: 225px;
    left: 740px;
  }

  /* ↓牙齿 */
  .process{
    width: 480px;
    height: auto;
  }
  .list>span{
    display: inline-block;
    width: 100px;
    text-align: center;
    border-right: 1px solid ghostwhite;
  }

  .list{
    width: 100%;
    height: 40px;
    line-height: 40px;
    background-color: white;
    display: flex;
  }

  .botttopBox {
    height: 195px;
  }

  .bottbigimg {
    margin-top: 10px;
    width: 40px;
    height: 133px;
    margin-bottom: 25px;
  }

  .bottsmallimg {
    margin-top: 10px;
    width: 40px;
    height: 40px;
  }

  .bottinul {
    float: left;
    margin-left: 20px;
    position: relative;
  }

  .bottinli {
    margin-top: 10px;
    display: block;
    position: relative;
  }

  .downimg{
    width: 40px;
    position: absolute;
    left: 2px;
    top: 10px;
    height: 40px;
  }
  .downimgs{
    width: 40px;
    position: absolute;
    left: 2px;
    height: 80px;
  }

  .bottBox>div:nth-of-type(1){
    margin-top: 30px;
  }
  .bottBox>div:nth-of-type(2){
    margin-top: 30px;
  }

  .bottBox>div:nth-of-type(3){
    margin-top: 30px;
  }
  .bottBox>div:nth-of-type(14){
    margin-top: 30px;
  }
  .bottBox>div:nth-of-type(15){
    margin-top: 30px;
  }

  .bottBox>div:nth-of-type(16){
    margin-top: 30px;
  }

  /* editableTable */

  .tableBox {
    width: 970px;
  }

  .select {
    width: 100px;
  }

  .result {
    width: 100%;
    display: inline-block;
    margin-bottom: 10px;
  }

  .result>span {
    float: left;
  }

  .checkResult>li {
    float: right;
    list-style: none;
    margin-right: 5px;
  }

  .checkResult>li>span {
    color: #1890ff;
  }
  .reset{
    width: 40px;
    font-size: 12px;
    height: 20px;
  }
  .leftBox {
    position: relative;
    width: 20%;
    height: 400px;
    border-left: 1px solid gainsboro;
    border-top: 1px solid gainsboro;
    border-right: 1px solid gainsboro;
    display: inline-block;
    border-bottom: 1px solid gainsboro;
  }
  .choice{
    margin-left: 24% !important;
    margin-top: 5px;
  }
  .leftBox>div:nth-of-type(1){
    border-bottom: 1px solid gainsboro;
  }
  .centerBox>div:nth-of-type(1){
    border-bottom: 1px solid gainsboro;
  }
  .siteBox {
    height: 40px;
    line-height: 40px;
  }
  .siteBoxs{
    position: absolute;
    border-bottom: 1px solid gainsboro;
    width: 17.5%;
    background: white;
    z-index: 100;
    height: 40px;
    line-height: 40px;
  }
  .siteBox>span {
    display: inline-block;
    margin-left: 7px;
  }
  .siteBoxs>span {
    display: inline-block;
    margin-left: 7px;
  }

  .siteBox>button {
    float: right;
    margin-top: 10px;
    margin-right: 10px;
  }

  .pterTeeth {
    clear: both;
    width: 85%;
    margin-left: 8%;
    margin-top: 5px;
  }
  .decidteeth {
    clear: both;
    width: 75%;
    margin-left: 14%;
    margin-top: 5px;
  }
  .pterTeeth>span {
    float: left;
    width: 11px;
    text-align: center;
    height: 20px;
    line-height: 20px;
    cursor: pointer;
  }
  .decidteeth>span {
    float: left;
    width: 15px;
    text-align: center;
    height: 20px;
    line-height: 20px;
  }

  .parment>div:nth-of-type(2) {
    border-bottom: 1px solid gainsboro;
  }
  .decid>p:nth-of-type(2) {
    border-bottom: 1px solid gainsboro;
  }
  .lineborder{
    position: absolute;
    border-right: 1px solid gainsboro;
    height: 40px;
    top: 73px;
    left: 50.2%;
  }
  .decidline{
    position: absolute;
    border-right: 1px solid gainsboro;
    height: 40px;
    top: 80px;
    left: 50.2%;
  }
  .cusp{
    display: flex;
    display:flex;
    align-items:center;/*垂直居中*/
    justify-content: center;/*水平居中*/
    width: 100%;
    height: 287px;
  }
  .centerBox{
    float: right;
    width: 20%;
    height: 400px;
    border-right: 1px solid gainsboro;
    border-top: 1px solid gainsboro;
    border-bottom: 1px solid gainsboro;
    overflow-x:hidden;
    overflow-y: scroll
  }
  .rightBox{
    float: right;
    width: 60%;
    height: 400px;
    /* border-right: 1px solid gainsboro;*/
    border-top: 1px solid gainsboro;
    border-bottom: 1px solid gainsboro;
    overflow-x:scroll;
    overflow-y: scroll
  }
  .tableHome{
    width: 1000px;
    height: auto;
    border-top: 1px solid gainsboro;

  }
  .tableDialg{
    width: 100% !important;
  }
  th{

    padding: 5px !important;

  }
  td{
    padding: 0 !important;
    text-align: center;
  }

  .notesadd{
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #f3f1f1;
  }
  .notesadd>span:nth-of-type(1) {
    display: inline-block;
    width: 15%;
    text-align: center;
    padding: 4px;
    background-color: #fafafa;
    border-right: 1px solid #f3f1f1;
    line-height: 30px;
  }
  .notes>span:nth-of-type(1) {
    display: inline-block;
    width: 15%;
    text-align: center;
    line-height: 30px;
    background-color: #fafafa;
    border-right: 1px solid #f3f1f1;
    height: 30px;
    margin: 0;
  }
  .notes>input{
    display: inline-block;
    width: 85%;
  }
  /* 扇形 */
  .sector{
    margin-left: 20%;
    position: relative;
    width: 200px;
    height: 200px;
    border-radius: 100%;
    background-color: gray;
  }
  .sectorchild1{
    position: absolute;
    border: 100px solid transparent;
    width: 0;
    border-radius: 100px;
    border-top-color: white;
    z-index: 0;
  }

  .sectorchild2{
    position: absolute;
    border: 100px solid transparent;
    width: 0;
    border-radius: 100px;
    border-left-color: white;
  }
  .sectorchild3{
    position: absolute;
    border: 100px solid transparent;
    width: 0;
    border-radius: 100px;
    border-right-color: white;
  }
  .sectorchild4{
    position: absolute;
    border: 100px solid transparent;
    width: 0;
    border-radius: 100px;
    border-bottom-color: white;
  }
  .sectorchild5{
    position: absolute;
    top: 57px;
    left: 57px;
    width: 85px;
    height: 85px;
    border: 1px solid  gainsboro;
    z-index: 1;
    border-radius: 100%;
    background-color: white;
  }
  .sector>span:nth-of-type(1){
    position: absolute;
    top: 20px;
    left: 43%;
    z-index: 1;
  }
  .sector>span:nth-of-type(2){
    position: absolute;
    top: 90px;
    left: 75%;
    z-index: 1;
  }
  .sector>span:nth-of-type(3){
    position: absolute;
    top: 90px;
    left: 5%;
    z-index: 1;
  }
  .sector>span:nth-of-type(4){
    position: absolute;
    top: 160px;
    left: 43%;
    z-index: 1;
  }
  .sector>span:nth-of-type(5){
    position: absolute;
    top: 90px;
    left: 43%;
    z-index: 1;
  }
  .hide{
    display: none;
  }
  .red{
    background-color: red;
    color: white;
  }
  .gray{
    background-color: gray;
    color: white;
  }
  .grays{
    text-align: center;
    cursor: pointer;
  }
  //
  .customSelect {
    :global {
      .ant-tooltip-inner {
        min-width: 495px !important;
        min-height: 32px;
        padding: 0px 0px !important;
        color: #130c0c !important;
        text-align: left;
        text-decoration: none;
        word-wrap: break-word;
        background-color:white !important;
        border-radius: 2px;
        box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
      }
    }
  }

  // table
  .tablenav{
    background-color: #fafafa;
    height: 34px;
    line-height: 34px;
  }
  .tablenav>span{
    display: inline-block;
    width: 14.25%;
    height: 34px;
    line-height: 34px;
    text-align: center;
    border-top: 1px solid #f1f1f1;
    border-right: 1px solid #f1f1f1;
    border-bottom: 1px solid #f1f1f1;

  }
  .datasoure{
    height: 34px;
    background: #f3f9fe;
  }
  .datasoure>span{
    display: inline-block;
    width: 14.25%;
    height: 34px;
    line-height: 34px;
    text-align: center;
    border-right: 1px solid #f1f1f1;
    border-bottom: 1px solid #f1f1f1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .datasoure>:last-child{
    color: #1890ff;
  }
  .tablenav>div:nth-child(odd){
    background-color: #fafafa;
  }

  .bgRed {
    background-color: #d53736;
  }
}
//.btns {
//  text-align: left;
//  margin-bottom: 10px;
//}
//.ant-menu-item-selected {
//  background-color: #fff !important;
//}
//.today {
//  width: 70px;
//  text-align: center;
//  border-right: 1px solid #e0e0e0;
//  display: inline-block;
//}
//
//.history {
//  width: 70px;
//  text-align: center;
//  display: inline-block;
//}
//
//.btn {
//  margin-left: 40%;
//}
//
//.btns {
//  margin-left: 1%;
//}
//


//.topBox {
//  height: 195px;
//}
////
//.bigimg {
//  width: 40px;
//  height: 133px;
//}
//.inul {
//  float: left;
//  margin-left: 12px;
//}
////
//.inli {
//  margin-top: 25px;
//  display: block;
//  position: relative;
//}
//.upimg{
//  width: 40px;
//  position: absolute;
//  left: 5px;
//  height: 80px;
//}
////.upimgs {
////  margin-top: 10px;
////  width: 40px;
////  height: 40px;
////  position: absolute;
////  left: 5px;
////}
////.smallimg {
////  margin-top: 10px;
////  width: 40px;
////  height: 40px;
////}
//.status{
//  position: absolute;
//  top: 0px;
//  font-size: 16px;
//  margin-left: 12px;
//}
//.statusbottom{
//  position: absolute;
//  left: 0;
//  bottom: 0;
//  font-size: 16px;
//  margin-left: 12px;
//}
//.seat {
//  display: block;
//  width: 20px;
//  height: 20px;
//  border: 1px solid gainsboro;
//  line-height: 20px;
//  text-align: center;
//  border-radius: 100%;
//  margin-left: 10px;
//  margin-top: 10px;
//}
//.seatm{
//  border: none;
//}
//.seats {
//  border: 1px solid gainsboro;
//  display: block;
//  width: 20px;
//  height: 20px;
//  line-height: 20px;
//  text-align: center;
//  border-radius: 100%;
//  margin-left: 10px;
//  margin-top: 10px;
//}
////
/////*  左右 分隔 */
////
//.interval {
//  width: 100%;
//  height: 30px;
//}
//
//.interval>span {
//  float: left;
//}
//
//.interval>span:nth-of-type(1) {
//  border-bottom: 1px solid gainsboro;
//  width: 183px;
//  position: absolute;
//  top: 225px;
//  left: 20px;
//}
//
//.interval>span:nth-of-type(2) {
//  position: absolute;
//  top: 215px;
//  left: 207px;
//}
//
//.interval>span:nth-of-type(3) {
//  position: absolute;
//  width: 398px;
//  top: 225px;
//  left: 223px;
//  border-bottom: 1px solid gainsboro;
//}
//
//.interval>span:nth-of-type(4) {
//  position: absolute;
//  height: 130px;
//  top: 160px;
//  left: 422px;
//  border-right: 1px solid gainsboro;
//}
//
//.interval>span:nth-of-type(5) {
//  position: absolute;
//  top: 215px;
//  left: 624px;
//}
//
//.interval>span:nth-of-type(6) {
//  border-bottom: 1px solid gainsboro;
//  position: absolute;
//  width: 190px;
//  top: 225px;
//  left: 640px;
//}
//
/////* ↓牙齿 */
.process{
  width: 480px;
  /* position: absolute;
  top: 200px; */
  height: auto;
  /* border: 1px solid ghostwhite;
  box-shadow: 0px 0px 10px gainsboro; */
}
.list>span{
  display: inline-block;
  width: 100px;
  text-align: center;
  border-right: 1px solid ghostwhite;
}
.list{
  width: 100%;
  height: 40px;
  line-height: 40px;
  background-color: white;
  display: flex;
  .list_examName{
    width: 320px;
    display:flex;
    margin:0 auto;
    overflow:hidden;
    text-align: left;
    text-overflow:ellipsis;
    -webkit-box-orient:vertical;
    -webkit-line-clamp:1;
    div{
      width: 100%;
    }
  }
}
