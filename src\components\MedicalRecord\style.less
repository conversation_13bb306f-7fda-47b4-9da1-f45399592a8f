@import '~antd/es/style/themes/default.less';

.list_bottom {
  min-height: 200px;

  :global {
    .ant-form-item {
      margin-bottom: 12px;
    }
  }
}
.width100 {
  width: 100%;
}
.contentinner {
  width: 100%;
  // padding: 0 20px;
  color: #212121;
  font-size: 14px;

  .ant-form-item-control-input-content {
    width: 70%;
  }

  .inntable {
    padding: 0 20px;

    .executory {
      color: #67c23a;
    }
  }

  .label {
    display: flex;

    span {
      margin-top: 2px;
    }
  }
}

.middleStyle{
  :global{
    .ant-form-item-label>label {
      margin-top: 9px;
    }
  }
}
.table {
  width: 45px;
  height: 25px;

  td {
    text-align: center;
  }

  .td {
    width: 100%;
    border: 1px solid #999;
    border-top: 0;
    border-left: 0;
  }

  .bottom_td {
    width: 100%;
    border: 1px solid #999;
    border-right: 0;
    border-bottom: 0;
  }
}

.compile {
  position: absolute;
  top: 15px;
  right: 20px;
  width: 18%;
  height: 118px;
  font-size: 35px;
  background-color: #e6a23c;
}

.check {
  display: flex;
  margin-bottom: 5px;

  .message {
    margin-top: 5px;
    margin-left: 10px;
  }
}

// 预览图片

.ctimgStyle {
  width: 100%;
  height: 88px;
}

// .images {
//   width: 290px;
//   display: flex;
//   border: 1px solid rgba(0, 0, 0, 0.06);
// }
 .imgborder {
   position: relative;
 }

 .ctimgInfo {
   padding-top: 5px;
   padding-left: 8px;
   color: rgba(0, 0, 0, 0.45);
   font-weight: 400;
   font-size: 14px;
 }

 .ctimgdelete {
   position: absolute;
   top: 0;
   left: 0;
   z-index: 10;
   align-items: center;
   width: 100%;
   height: 100%;
   color: #f0f0f0;
   text-align: center;
   background: rgba(0, 0, 0, 0.3);
 }

 .icon_delete {
   width: 20px;
   height: 20px;
 }

 .deleteFont {
   margin-top: 1px;
   margin-left: 4px;
   font-size: 14px;
 }
//牙位开始
.tooth_position {
  width: 200px;
  height: 50px;
  margin-bottom: 7px;

  td {
    width: 72px;
    text-align: center;
  }

  .first {
    height: 16px;
    border-right: 1px solid rgba(0,0,0,0.15);
    border-bottom: 1px solid rgba(0,0,0,0.15);
  }

  .second {
    height: 16px;
    border-bottom: 1px solid rgba(0,0,0,0.15);
  }

  .third {
    height: 16px;
    border-right: 1px solid rgba(0,0,0,0.15);
  }

  .fourth {
    height: 16px;
  }
}

// 显示一行，超出隐藏
.ellipse {
  width: 152px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.examineLookAll{
  display: flex;
  flex-wrap: wrap;
}
.examineLooks {
  margin: 8px 24px 8px 0;
  width: 300px;

  .examines {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .right {
    color: #4292FF;

    span {
      cursor: pointer;
    }
  }

  .ctimgStyle {
    width: 118px;
    height: 88px;
  }

  .examineBottom {
    margin-top: 5px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    width: 100%;
    display: flex;
  }

  .examineContent {
    margin-left: 8px;
  }
}
//显示一行，超出...
.fontLineStyle{
  width: 152px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.rightContent{
  display: flex;
}

.videoImg {
  display: flex;
  flex-wrap: wrap;

  .videomargin {
    margin-right: 10px;
    margin-top: 5px;
  }
}

.images {
  width: 290px;
  display: flex;
  border: 1px solid rgba(0, 0, 0, 0.06);

  .imgborder {
    position: relative;
  }

  .ctimgStyle {
    width: 118px;
    height: 88px;
  }

  .ctimgInfo {
    padding-left: 8px;
    padding-top: 5px;
    font-size: 14px;
    line-height: 25px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
  }

  .ctimgdelete {
    color: #F0F0F0;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, 0.3);
    text-align: center;
    align-items: center;
  }

  .icon_delete {
    width: 20px;
    height: 20px;
  }

  .deleteFont {
    font-size: 14px;
    margin-left: 4px;
    margin-top: 1px;
  }
}

.title {
  display: flex;
  justify-content: space-between;
  // width: 450px;
  height: 32px;
  margin-bottom: 8px;
  padding: 0 12px;
  color: #212121;
  line-height: 32px;
  background: #f7f8fa;
  border-radius: 2px;
}

.image {
  width: 16px;
  height: 16px;
  margin-right: 2px;
}

.tipsFont {
  margin-right: 16px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  font-family: PingFang SC;
}
.imageBorder {
  // width: 450px;
  height: 309px;
  overflow: auto;
}
