const getQuestionsheet = (req, res) =>
    res.json([
        {
            id: '1',
            illness: "1. 隐形正畸. 隐适美",
            practitioner: "普通医生计费",
            process: "按治疗流程收费",
            price: "60000",
            OriginalPrice: "70000",
            advance: "60000",
            Drug:"已退药",
            DrugDetailed:[
                {
                    drugName:"麻药",
                    num:1,
                    price:"200"
                },
                {
                    drugName:"麻药",
                    num:1,
                    price:"200"
                },
                {
                    drugName:"麻药",
                    num:1,
                    price:"200"
                },
                {
                    drugName:"麻药",
                    num:2,
                    price:"300"
                },
                {
                    drugName:"麻药",
                    num:6,
                    price:"600"
                }
            ],
            Technological: [
                {
                    "title": "口腔检查",
                    "type": 0
                },
                {
                    "title": "审核动画",
                    "type": 0
                },
                {
                    "title": "其他",
                    "type": 0
                },
                {
                    "title": "其他的其他",
                    "type": 0
                },
                {
                    "title": "完成",
                    "type": 1
                }
            ]
        },
        {
            id: '2',
            illness: "2. 隐形正畸. 隐适美",
            practitioner: "普通医生计费",
            process: "按治疗流程收费",
            price: "70000",
            OriginalPrice: "80000",
            advance: "70000",
            Drug:"已退药",
            DrugDetailed:[
                {
                    drugName:"麻药",
                    num:1,
                    price:"200"
                },
                {
                    drugName:"麻药",
                    num:1,
                    price:"200"
                },
                {
                    drugName:"麻药",
                    num:1,
                    price:"200"
                },
                {
                    drugName:"麻药",
                    num:2,
                    price:"300"
                },
                {
                    drugName:"麻药",
                    num:6,
                    price:"600"
                }
            ],
            Technological: [
                {
                    "title": "口腔检查",
                    "type": 1
                },
                {
                    "title": "审核动画",
                    "type": 0
                },
                {
                    "title": "其他",
                    "type": 0
                }
                ,
                {
                    "title": "其他的其他",
                    "type": 0
                },

                {
                    "title": "完成",
                    "type": 0
                }
            ]
        },
    ]);

export default {
    'GET /api/questionsheet': getQuestionsheet,
};
