import {
  EmrMessage,
  saveEmrTmpt,
  deleteEmrTmpt,
  findEmrTmptClassSearch,
  findEmrTmptClass,
  saveContentClass,
  findEmrTmptNamesByClass,
  deleteContentClass,
  updateContentClass
} from '@/services/medicalModel';
import {notification} from "antd";
const contentCenterModel = {
  namespace: 'medicalModel',
  state: {
    findEmrTmptData:{},//获取病历模版分组列表
    EmrMessageData:{},//病历模板详情返回信息
    saveEmrTmptData:{},//编辑病历模板返回信息
    deleteEmrTmptData:{},//删除病历模板返回信息
    ClassSearchData:{},//病历模板分组列表搜索
    saveContentData:{},//新建模板文件夹返回信息
    findEmrTmptNameData:{},//模板名称列表
    deleteContentData:{},//删除文件夹
    updateContentData:{},//编辑文件夹
    loading:false,
    loadTip:"加载中",
  },
  //异步
  effects: {
    //更改文件夹名称
    *updateContentClassService({payload, callback} , { call, put }) {
      const response = yield call(updateContentClass ,payload);
      yield put({
        type: 'updateContentInfo',
        payload: response,
      });
      if (response.code === 200 || response.code === 500) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //删除文件夹
    *deleteContentClassService({payload, callback} , { call, put }) {
      const response = yield call(deleteContentClass ,payload);
      yield put({
        type: 'deleteContentClassInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //模板名称列表
    *findEmrTmptNameService({payload, callback} , { call, put }) {
      const response = yield call(findEmrTmptNamesByClass ,payload);
      yield put({
        type: 'findEmrTmptNameInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    // 获取病历模版分组列表
    *findEmrTmptService({payload, callback} , { call, put }) {
      const response = yield call(findEmrTmptClass ,payload);
      yield put({
        type: 'findEmrTmptInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //新建模板文件夹
    *saveContentService({payload, callback} , { call, put }) {
      const response = yield call(saveContentClass ,payload);
      yield put({
        type: 'saveContentInfo',
        payload: response,
      });
      if (response.code === 200 || response.code === 500) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    // 获取病历模板详情
    *EmrMessageService({payload, callback} , { call, put }) {
      const response = yield call(EmrMessage ,payload);
      yield put({
        type: 'EmrMessageInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //编辑保存病历模板
    *saveEmrTmptService({payload, callback} , { call, put }) {
      const response = yield call(saveEmrTmpt ,payload);
      yield put({
        type: 'saveEmrTmptInfo',
        payload: response,
      });
      if (response.code === 200 || response.code === 500) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //删除病历模板
    *deleteEmrTmptService({payload, callback} , { call, put }) {
      const response = yield call(deleteEmrTmpt ,payload);
      yield put({
        type: 'deleteEmrTmptInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //病历模版分组列表搜索
    *ClassSearchService({payload, callback} , { call, put }) {
      const response = yield call(findEmrTmptClassSearch ,payload);
      yield put({
        type: 'ClassSearchInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
  },

  //同步
  reducers: {
    updateContentInfo(state, action) {
      return {
        ...state,
        updateContentData: action.payload || {},
      };
    },
    deleteContentClassInfo(state, action) {
      return {
        ...state,
        deleteContentData: action.payload || {},
      };
    },
    findEmrTmptNameInfo(state, action) {
      return {
        ...state,
        findEmrTmptNameData: action.payload || {},
      };
    },
    saveContentInfo(state, action) {
      return {
        ...state,
        saveContentData: action.payload || {},
      };
    },
    findEmrTmptInfo(state, action) {
      return {
        ...state,
        findEmrTmptData: action.payload || {},
      };
    },
    EmrMessageInfo(state, action) {
      return {
        ...state,
        EmrMessageData: action.payload || {},
      };
    },
    saveEmrTmptInfo(state, action) {
      return {
        ...state,
        saveEmrTmptData: action.payload || {},
      };
    },
    deleteEmrTmptInfo(state, action) {
      return {
        ...state,
        deleteEmrTmptData: action.payload || {},
      };
    },
    ClassSearchInfo(state, action) {
      return {
        ...state,
        ClassSearchData: action.payload || {},
      };
    },
  },
};
export default contentCenterModel;
