import { stringify } from 'qs';
import request from '@/utils/request';

/**医生列表**/
export async function doctorList(params) {
  return request(`/api/emr/externalCall/resourceApi/vagueResourceInfoList`, {
    method: 'POST',
    data: params,
  });
}
/** 单个医生详情 **/
export async function doctorInfoById(params) {
  return request(`/api/emr/externalCall/resourceApi/getResourceInfo`, {
    method: 'POST',
    data: params,
  });
}
/** 编辑医生详情 **/
export async function editDoctorInfo(params) {
  return request(`/api/emr/med/editDoctorInfo`, {
    method: 'POST',
    data: params,
  });
}
// 获取上级医生列表
export async function findHsmDoctors(params) {
  return request(`/api/emr/externalCall/findHsmDoctors`, {
    method: 'POST',
    data: params,
  });
}
