import React, { PureComponent } from 'react';
import { Layout, Menu, Icon, Row } from 'antd';
import Link from 'umi/link';
import styles from './../index.less';
import { formatMessage } from 'umi/locale';
import pathToRegexp from 'path-to-regexp';
import { urlToList } from './../../_utils/pathTools';
import router from 'umi/router';

import SubmenuLogo from '@/assets/submenu/menu_logo.png';
import SubmenuOpen from '@/assets/submenu/menu_open.png';
import SubmenuClose from '@/assets/submenu/menu_close.png';
import menu from '@/models/menu';
import { connect } from 'dva';

const { Sider } = Layout;
const { SubMenu } = Menu;




// 获取菜单数据
const getMenuData = (props) => {
    let { menuData } = props;
    let menuList = [];
    let menuGroup = [
        {
            subIcon: 'menu_medical',
            name: '医疗中心',
            path: '/medical',
            authority: ['75', '201', ''],
            subType: '3',
            children: [],
        },
        {
            subIcon: 'menu_run',
            name: '运营中心',
            path: '/run',
            authority: ['64', '63', '88', '92', '96'],
            subType: '5',
            children: [],
        },
        {
            subIcon: 'menu_stock',
            name: '支持中心',
            path: '/support',
            subType: '6',
            children: [],
        },
        {
            subIcon: 'menu_data',
            name: '数据中心',
            authority: ['94', '135'],
            path: '/data',
            subType: '8',
            children: [],
        },
        {
            subIcon: 'menu_manage',
            name: '管理中心',
            path: '/manage',
            authority: ['68', '91', '67', '66', '130', '131', '202', '203', '204'],
            subType: '9',
            children: [],
        },
    ]
    let GroupList = ['3', '5', '6', '8', '9'];   // 菜单分组类型
    menuData.filter((item) => item.subType) && menuData.filter((item) => item.subType).length > 0 &&
        menuData.filter((item) => item.subType).map((item, index) => {
            // 判断menulist是否存在subtype同一类型，如果存在不执行，不存在则添加进去
            if (
                (menuList.filter(subItem => item.subType != subItem.subType) && menuList.filter(subItem => item.subType != subItem.subType).length > 0)
                || menuList.length == 0
            ) {
                // 当数据菜单类型存在 则合并到当前分组下
                if (GroupList.includes(item.subType)) {
                    menuGroup[GroupList.indexOf(item.subType)].children.push(item)

                } else {
                    menuList.push({
                        ...item,
                        icon: item.subIcon,
                        name: !!item.subName ? item.subName : item.name,
                    })
                }
            }
        })

    menuList = menuList.concat(menuGroup);
    // 排序
    menuList.sort(function (a, b) {
        return a.subType - b.subType
    })

    return menuList
}

/**
 * 获得菜单子节点
 * @memberof SiderMenu
 */
const getDefaultCollapsedSubMenus = props => {
    const {
        location: { pathname },
    } = props;
    let menuOpenKeys = urlToList(pathname)
        .map(item => getFlatMenuKeys(getMenuData(props), item));
    return menuOpenKeys.filter(item => item.length > 0).length > 0 ? menuOpenKeys
        .filter(item => item.length > 0)[0]
        .filter(item => item) : null;
};
/**
 * Recursively flatten the data
 * [{path:string},{path:string}] => {path,path2}
 * @param  menus
 */
const getFlatMenuKeys = (menuData, subItem) => {
    let keys = [];
    menuData.forEach(item => {
        if (subItem != item.path) {
            if (item.children && item.children.filter(key => urlToList(key.path)[0] == urlToList(subItem)[0]).length > 0) {
                return keys.push(item.path);
            }
        } else {
            keys.push(item.path);
        }

    });
    return keys;
};


// Allow menu.js config icon as string or ReactNode
//   icon: 'setting',
//   icon: 'http://demo.com/icon.png',
//   icon: <Icon type="setting" />,
const getIcon = icon => {
    if (typeof icon === 'string' && icon.indexOf('http') === 0) {
        return <img src={icon} alt="icon" />;
    }
    if (typeof icon === 'string') {
        return <Icon type={icon} />;
    }
    return icon;
};

const getMenuMatches = (flatMenuKeys, path) =>
    flatMenuKeys.filter(item => item && pathToRegexp(urlToList(item)[0]).test(urlToList(path)[0]));


@connect(({ global, loading }) => ({
    global,
    loading
}))
export default class Submenu extends PureComponent {
    constructor(props) {
        super(props);
        this.flatMenuKeys = this.getFlatMenuKeys(getMenuData(props));
        this.state = {
            openKeys: getDefaultCollapsedSubMenus(props),
        };
    }

    static getDerivedStateFromProps(props, state) {
        const { pathname } = state;
        if (props.location.pathname !== pathname) {
            return {
                pathname: props.location.pathname,
                openKeys: getDefaultCollapsedSubMenus(props),
            };
        }
        return null;
    }

    /**
     * Recursively flatten the data
     * [{path:string},{path:string}] => {path,path2}
     * @param  menus
     */
    getFlatMenuKeys(menus) {
        let keys = [];
        menus.forEach(item => {
            if (item.children) {
                keys = keys.concat(this.getFlatMenuKeys(item.children));
            }
            keys.push(item.path);
        });
        return keys;
    }

    /**
     * 获得菜单子节点
     * @memberof SiderMenu
     */
    getNavMenuItems = (menusData, parent) => {
        if (!menusData) {
            return [];
        }
        return menusData
            .filter(item => item.name && !item.hideInMenu)
            .map(item => {
                // make dom
                const ItemDom = this.getSubMenuOrItem(item, parent);
                return this.checkPermissionItem(item.authority, ItemDom);
            })
            .filter(item => item);
    };

    // Get the currently selected menu
    getSelectedMenuKeys = () => {
        const {
            location: { pathname },
        } = this.props;
        return urlToList(pathname).map(itemPath => getMenuMatches(this.flatMenuKeys, itemPath).pop());
    };

    /**
     * get SubMenu or Item
     */
    getSubMenuOrItem = item => {
        // doc: add hideChildrenInMenu
        if (item.children && !item.hideChildrenInMenu && item.children.some(child => child.name)) {
            const name = item.name;
            return (
                <SubMenu
                    title={
                        item.subIcon ? (
                            <span style={{ position: 'relative' }}>
                                {getIcon(item.subIcon)}
                                <span>{name}</span>
                            </span>
                        ) : (
                            name
                        )
                    }
                    key={item.path}
                >
                    <div className={styles.antMenuTriangle}></div>
                    {this.getNavMenuItems(item.children)}
                </SubMenu>
            );
        }
        return <Menu.Item key={item.path}>{this.getMenuItemPath(item)}</Menu.Item>;
    };

    /**
     * 判断是否是http链接.返回 Link 或 a
     * Judge whether it is http link.return a or Link
     * @memberof SiderMenu
     */
    getMenuItemPath = item => {
        const name = item.name;
        const itemPath = this.conversionPath(item.path);
        const icon = getIcon(item.subIcon);
        const { target } = item;
        const procurementAuth = localStorage.getItem('procurementAuth')
        // Is it a http link
        if (/^https?:\/\//.test(itemPath)) {
            return (
                <a href={itemPath} target={target}>
                    {icon}
                    <span>{name}</span>
                </a>
            );
        }
        const { location, menuCollapsed } = this.props;
        return (
            <Link
                style={menuCollapsed ? {
                    padding: '0px 8px',
                    margin: 0
                } : null}
                to={(
                    itemPath == '/arrailSchool'
                    || itemPath == '/findDentist'
                    || (itemPath == '/procurementsystem' && procurementAuth == 1)   // 跳转到采购中心
                    || itemPath == '/consumableShop'  // 跳转到商城
                ) ? null : itemPath}
                target={target}
                replace={itemPath === location.pathname}

                onClick={() => {
                    console.log('123123213 :: ', itemPath);
                    if (itemPath == '/arrailSchool') {
                        window.open('https://apptmq2ocpm6396.pc.xiaoe-tech.com/', '_blank');
                    } else if (itemPath == '/findDentist') {
                        window.open('https://clinic.jwsmed.com/', '_blank');
                    } else if (itemPath == '/procurementsystem' && procurementAuth == 1) { // 跳转到采购中心
                        const { dispatch } = this.props
                        dispatch({
                            type: 'global/setVisibleByModalByGoJwsmed',
                            payload: {
                                visibleByModalByGoJwsmed: true,
                                moduleIdByModalByGoJwsmed: null,
                            },
                        })
                    } else if (itemPath == '/consumableShop') { // 跳转到商城
                        const { dispatch } = this.props
                        dispatch({
                            type: 'global/setVisibleByModalByConsumablesMall',
                            payload: { visibleByModalByConsumablesMall: true },
                        })
                    }

                }}
            >
                {icon}
                <span>{name}</span>
            </Link>
        );
    };

    // permission to check
    checkPermissionItem = (authority, ItemDom) => {
        const { Authorized } = this.props;
        if (Authorized && Authorized.check) {
            const { check } = Authorized;
            return check(authority, ItemDom);
        }
        return ItemDom;
    };

    conversionPath = path => {
        if (path && path.indexOf('http') === 0) {
            return path;
        }
        return `/${path || ''}`.replace(/\/+/g, '/');
    };

    isMainMenu = key => {
        let RouterMenus = getMenuData(this.props);
        return RouterMenus.some(item => {
            if (key) {
                return item.key === key || item.path === key;
            }
            return false;
        });
    };
    // 菜单点击可以 展开收起项的事件
    handleOpenChange = openKeys => {
        const moreThanOne = openKeys.filter(openKey => this.isMainMenu(openKey)).length > 1;
        this.setState({
            openKeys: moreThanOne ? [openKeys.pop()] : [...openKeys],
        });
        // this.resetMenuHeight();
    };
    // 菜单选中事件：解决不是展开项时收起 所有展开项问题
    onSubMenuSelect = (menukeys) => {
        let RouterMenus = getMenuData(this.props);
        let openKeys = RouterMenus.filter(item => item.path == menukeys.key).length > 0 ?
            [] : menukeys.item.props.openKeys;
        this.setState({
            openKeys
        })
    }

    // 展开收起菜单
    onCollapse = () => {
        const { dispatch, menuCollapsed } = this.props;
        this.setState({
            openKeys: getDefaultCollapsedSubMenus(this.props),
        }, () => {
            dispatch({
                type: 'global/changeLayoutMenuCollapsed',
                payload: !menuCollapsed,
            });
        })
    }

    render() {
        const { theme, style, menuCollapsed } = this.props;
        const { openKeys } = this.state;
        let selectedKeys = this.getSelectedMenuKeys();
        if (!selectedKeys.length && openKeys) {
            selectedKeys = [openKeys[openKeys.length - 1]];
        }
        let props = {};
        if (openKeys) {
            props = {
                openKeys,
            };
        }
        return (
            <Sider
                trigger={null}
                collapsible
                collapsed={!menuCollapsed}
                breakpoint="lg"
                width={180}
                collapsedWidth={56}
                theme={theme}
            >
                <Row type='flex' justify='space-between' style={{ margin: menuCollapsed ? '20px 16px 14px 24px' : '20px 16px 14px 20px' }}>
                    {
                        menuCollapsed && <img src={SubmenuLogo}
                            style={{ width: 106, height: 23, cursor: 'pointer' }}
                            onClick={() => {
                                router.push('/home/<USER>');
                            }}
                        />
                    }

                    <img src={menuCollapsed ? SubmenuClose : SubmenuOpen}
                        style={{ width: 16, height: 16, cursor: 'pointer', marginTop: 4 }}
                        onClick={this.onCollapse}
                    />
                </Row>
                <div style={{height: "calc(100vh - 50px - 58px)", overflow: 'auto', paddingBottom: '50px', boxSizing: 'border-box'}}>
                <Menu
                    key="Menu"
                    mode={'inline'}
                    theme={theme}
                    onOpenChange={this.handleOpenChange}
                    selectedKeys={selectedKeys}
                    style={style}
                    {...props}
                >
                    {this.getNavMenuItems(getMenuData(this.props))}
                </Menu>
                </div>

            </Sider >
        );
    }
}
