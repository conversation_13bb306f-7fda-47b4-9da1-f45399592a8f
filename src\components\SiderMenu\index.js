import React from 'react';
import { Drawer } from 'antd';
import SiderMenu from './SiderMenu';
import styles from './index.less';
import { pushHistoryStack, cleanHistoryStack } from '@/utils/utils'

import Submenu from './Submenu/Submenu';

/**
 * Recursively flatten the data
 * [{path:string},{path:string}] => {path,path2}
 * @param  menus
 */
const getFlatMenuKeys = menuData => {
  let keys = [];
  menuData.forEach(item => {
    if (item.children) {
      keys = keys.concat(getFlatMenuKeys(item.children));
    }
    keys.push(item.path);
  });
  return keys;
};

const SiderMenuWrapper = props => {
  const { isMobile, menuData, collapsed, onCollapse, menuCollapsed } = props;

  const menuPlanData = [
    "/customerPlan",
  ]

  return isMobile ? (
    <>
      <Drawer
        className={styles.box}
        visible={!collapsed}
        placement="left"
        onClose={() => onCollapse(true)}
        height={"100%"}
        destroyOnClose={true}
        closable={false}
        maskStyle={{ backgroundColor: 'rgba(0,0,0,0)' }}
        style={{
          padding: 0,
          background: 'rgba(0,0,0,0)',
        }}
        bodyStyle={{
          background: 'rgba(0,0,0,0)',
          height: '100%'
        }}
      >
        <SiderMenu
          {...props}
          click={() => {
            onCollapse(true)
            cleanHistoryStack()
          }}
          flatMenuKeys={getFlatMenuKeys(menuData)}
          collapsed={isMobile ? false : collapsed}
        />
      </Drawer >

      {!menuPlanData.includes(props.location.pathname) && <div style={{ width: menuCollapsed ? 180 : 56 }} className={styles.subMenu}>
        <Submenu
          {...props}

        />
      </div>}
    </>

  ) :
    null
};

export default SiderMenuWrapper;
