import { findByUserIdToday,findHistoryPatients } from '@/services/todayVisit'; // 医生管理列表 单个医生详情
//引入接诊，医生列表，今日预约，专业，等级，保存转诊，昨日待写病历，病历详情，更新病历，用户面板，更新面板，设置面板，今日预约排序等相关接口
import { notification } from 'antd';

const Model = {
  namespace: 'todayVisit',
  state: {
      PendingInfo: [], //今日就诊
      historyPatientsList:[] ,// 历史患者
  },
  effects: {
     /**今日就诊和获取就诊记录
     * 参数：病历号，医生id，机构id，分页，就诊状态，排序 平台标识 当前登录医生标识 预约日期  患者标识 就诊状态 等等，**/
    *todaySeePending({ payload, callback }, { call, put }) {
      const response = yield call(findByUserIdToday, payload);
      yield put({
        type: 'getTodaySeePending',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
     /**历史患者列表
     * 参数：平台标识 当前登录医生标识 机构id，筛选条件 分页， 等等，**/
    *historyPatients({ payload, callback }, { call, put }) {
      const response = yield call(findHistoryPatients, payload);
      yield put({
        type: 'getHistoryPatients',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
  },
  reducers: {
     getTodaySeePending(state, action) {
      return {
        ...state,
        PendingInfo: action.payload || {},
      };
    },
    getHistoryPatients(state, action) {
      return {
        ...state,
        historyPatientsList: action.payload || {},
      };
    },
  },
};

export default Model;
