@import '~antd/lib/style/themes/default.less';

.downloadContent{
  padding: 16px;
  .title{
    font-size: 16px;
    font-family: PingFang SC;
    color: rgba(0, 0, 0, 0.85);
  }
  .content{
    display: flex;
    flex-wrap: wrap;
    margin-top: 16px;
    .cardContent{
      padding: 16px;
      margin-right: 12px;
      margin-bottom: 16px;
      &:hover {
        transform: translate3d(0, -3px, 0);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
        cursor: pointer;
      }
    }
    .cardContentTop{
      display: flex;
      .imgStyle{
        width: 100px;
        height: 120px;
      }
      .contentStyle{
        margin-left: 12px;
        .title{
          font-size: 16px;
          font-family: PingFang SC;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
        }
        .TopcardContent{
          margin-top: 8px;
          width: 196px;
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.45);
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;/* 这里是超出几行省略 */
          line-clamp: 2;
          -webkit-box-orient: vertical;
          &:hover{
            color:rgba(0, 0, 0, 0.3);
          }
        }
      }
    }
  }
  .downBtn{
    border: 0;
    margin-top: 16px;
    width: 100%;
    height: 37px;
    background: #4292FF;
    border-radius: 2px;
    font-size: 14px;
  }
  .already{
    border: 0;
    margin-top: 16px;
    width: 100%;
    height: 37px;
    background: #F5F5F5;
    border-radius: 2px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
  }
  .loading{
    border: 0;
    margin-top: 16px;
    width: 100%;
    height: 37px;
    background: rgba(0, 180, 42, 0.06);
    border-radius: 2px;
    font-size: 14px;
    color: #00B42A;
  }
  .errBtn{
    border: 0;
    margin-top: 16px;
    width: 100%;
    height: 37px;
    background: rgba(245, 63, 63, 0.06);
    border-radius: 2px;
    font-size: 14px;
    color: #F53F3F;
  }
}
.free{
  margin-top: 8px;
  height: 20px;
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #F53F3F;
  line-height: 20px;
}
.cardMsgContent{
  margin-top: 16px;
  display: flex;
  .imgStyle{
    width:140px;
    height: 180px;
  }
  .contentStyle{
    margin-left: 16px;
    .title{
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
    .TopcardContent{
      margin-top: 8px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.45);
    }
  }
}
.contentImg{
  width: 100%;
}
.Listborder{
  height: 400px;
  overflow: scroll;
  margin-top: 16px;
  border:1px solid #f0f0f0;
  .contentList{
    display: flex;
    font-size: 16px;
    .title{
      color: rgba(0, 0, 0, 0.45);
      width: 200px;
    }
  }
  :global{
    .ant-list-item{
      padding: 6px 24px;
    }
  }
}
.check{
  margin-top: 8px;
  height: 20px;
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  color:rgba(24,144,255,1);
  line-height: 20px;
  &:hover{
    color:rgba(24,144,255,.85)
  }
}
