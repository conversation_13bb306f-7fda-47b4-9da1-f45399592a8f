import request from '@/utils/request';

//获取标准诊断列表
export async function findDiags(params) {
  return request(`/api/emr/system/findDiags`, {
    method: 'POST',
    data: params,
  });
}
/** 获取单个标准诊断详情 **/
export async function diagInfo(params) {
  return request(`/api/emr/system/getDiagInfoById`, {
    method: 'POST',
    data: params,
  });
}
/** 新增编辑诊断 **/
export async function saveDiag(params) {
  return request(`/api/emr/system/saveDiag`, {
    method: 'POST',
    data: params,
  });
}
/**获取筛选类型的类型列表数据**/
export async function findDiag(params) {
  return request(`/api/emr/system/findClassNames`, {
    method: 'POST',
    data: params,
  });
}
/** 删除词条 **/
export async function deleteDiag(params) {
  return request(`/api/emr/system/deleteDiag`, {
    method: 'POST',
    data: params,
  });
}




