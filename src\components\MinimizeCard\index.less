.rightPositionBox{
  position: fixed;
  bottom: 0;
  right: 0;
  height: 40px;
  z-index: 100;
  .itemBox{
    width: 125px;
    height: 40px;
    border-radius: 2px;
    overflow: hidden;
    padding: 10px 16px;
    display: inline-block;
    cursor: pointer;
    img{
      width: 16px;
      height: 16px;
      float: right;
      margin-top: 2px;
    }
  }
  .visit{
    background: linear-gradient(136deg, #18D8D9 0%, #0ACBCC 100%);
  }
  .settlement{
    background: linear-gradient(136deg, #FFD34D 0%, #F6C122 100%);
  }
  .settlementInfo{
    background: linear-gradient(136deg, #5CC1FF 0%, #2796F3 100%);
  }
  .refund{
    background: linear-gradient(136deg, #98CF5E 0%, #52C41A 100%);
  }
  .payDebt {
    background: linear-gradient(136deg, #FF8A46 0%, #FA640C 100%);
  }
  .doctorWorkbench {
    background: linear-gradient(136deg, #98A1FF 0%, #737EF2 100%);
  }
}
