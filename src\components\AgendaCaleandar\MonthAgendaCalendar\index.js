import React, {Component} from 'react';
import {Card,Radio,Popover,message,Spin,Icon} from 'antd';
import $ from 'jquery';
import IScroll from 'iscroll/build/iscroll-probe.js'

import moment from 'moment';
import FullCalendar from '@fullcalendar/react'
import dayGridPlugin from '@fullcalendar/daygrid'
import resourceTimeGridPlugin from '@fullcalendar/resource-timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import resourceTimelinePlugin from '@fullcalendar/resource-timeline';
import timelinePlugin from '@fullcalendar/timeline';
import { Calendar } from '@fullcalendar/core';
import InfiniteScroll from 'react-infinite-scroller';

import './index2.less' // webpack must be configured to do this
import classNames from 'classnames';
import styles from './TimeAgendaCalendar.less'
import commonStyles from './AgendaCaleander.less'
import PropTypes from 'prop-types';
import Popper from 'popper.js';
import Tooltip from 'tooltip.js';
import {findDOMNode} from 'react-dom'
import { connect } from 'dva'
import './Popper.less'

import AppointmentParticulars from '@/pages/Appointment/appointmentParticulars/index'
import MonthEventElement from '../Elements/MonthEventElement'
import ReactDOM from 'react-dom';
import {
  getScrollbarWidth,
  minTime,
  maxTime,
  getDateClickTime,
  eventBus,
  CLOSE_AppointDetailsHungWindow,
} from '@/utils/CalendarUtils';
import { getUrlParam,serilizeURL } from '@/utils/utils'
import Immutable from 'immutable'

const workStatusForDayOff =  2        // 休息日
const workStatusForOther  =  3        // 走诊医生
const workStatusForNot    =  4        // 未排班医生
const workStatusForOrganizationStatus  =  5             // 走诊机构
const rowTotal = 7                    // 列表项总列数
const lableClickClassName = 'labelTh' // 表头时间行点击className
const slotWidth = 40                  // 格子宽度

/**
 * @type 1:老患者预约 2:新患者预约 3:会诊 4:个人时间占用 5:已到诊 6:上班时间
 * @NewType 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
 */
const typeClassName = {
  1:'oldPatientClass',
  2:'newPatientClass',
  3:'consultationClass',
  4:'personalTimeClass',
  5:'diagnosisClass',
  6:'officeHoursClass',
}

const typeEventCommonName = {
  1:'notClinicType.', // 未到诊
  2:'comeClinicType', // 已到诊
  3:'lateType',       // 过预约时间未到诊
  4:'finishType'      // 已结算
}

import {
  getEventElement,
  getEventElementClass, // 更新Event结构方法
  getChairNum,
  newCustomerEventType,
  oldCustomerEventType,
  otherEventType,
  backgroundType,
  LarageHeight,
  MediumHeight,
  SmallHeight,
  tittleHight,
  slotLabelFormat,
  schedulerLicenseKey,
  eventPatientNameEllipsis // 患者名称添加省略号
} from '@/utils/CalendarUtils'
import AppointmentInfoSmallWindow from '@/components/AgendaCaleandar/TimeAgendaCalendar/AppointmentInfoSmallWindow';

// 防抖
function debounce(callback,delay){
  let timerId = null;
  return function(args){
    let that = this;
    clearTimeout(timerId)
    timerId = setTimeout(function(){
      callback.call(that,args)
    },delay)
  }
}

/**
 * 时间制 预约组件
 * 全屏或半屏 使用时间制度 预约组件
 */
@connect(({Getscreen,TimeCalendar,loading}) => ({
  Getscreen,
  TimeCalendar,
  loading
}))
export default class TimeAgendaCalendar extends Component {

  static propTypes = {
    onRemoveEvent:PropTypes.func,     // 点击删除事件方法
    onEventClick:PropTypes.func,      // 点击Event预约事件的回调方法
    onTimeBlankClick:PropTypes.func,  // 点击空白区域回调事件的方法
    goNextIcon:PropTypes.func,        // 点击下一页的回调方法
    goLastIcon:PropTypes.func,        // 点击上一页的回调方法
    calendarId:PropTypes.string,      // 当前预约组件绑定的ID名称
    Option:PropTypes.object,          // 需要扩展的预约组件配置字段
    calendarTableList:PropTypes.array, // 预约组件表格表头格式
    EventList:PropTypes.array,         // 预约组件预约事件集合
    onRef:PropTypes.func,
    AgendaHeadType: PropTypes.string,  // 当前组件是姓名头 还是 时间头
    appointmentPageInfo:PropTypes.object, // 预约头列表分页信息
    consultationId:PropTypes.any,         // 会诊医生id
    onEventDrop:PropTypes.any,            // 回调函数，当拖动结束且日程移动另一个时间时触发
    onEventResize:PropTypes.any,          // 当日程事件调整（resize）结束并且事件被改变时触发
    onClickTitleForDoctor:PropTypes.func, // 点击表格头中的医生回调
    onClickTitleForData:PropTypes.func,    // 点击表格头中的日期回调
    onEventDragStart:PropTypes.func,       // 开始预约拖拽的回调
    showResourceName:PropTypes.bool,
    appointmentCountAfterWorkTime:PropTypes.any,
    appointmentCountBeforeWorkTime:PropTypes.any,
  };
  static defaultProps = {
    onRemoveEvent:()=>{},
    onEventClick:()=>{},
    onTimeBlankClick:()=>{},
    goNextIcon:()=>{},
    goLastIcon:()=>{},
    consultationId:null,
    calendarId:'calendar',   // 默认组件ID
    Option:{},               // 默认组件公共配置项目
    calendarTableList:[],
    EventList:[],
    onRef:()=>{},
    AgendaHeadType:'1',       // 姓名展示:1  事件展示:2
    appointmentPageInfo:{},   // 分页信息初始化参数
    onEventDrop:()=>{},       // 回调函数，当拖动结束且日程移动另一个时间时触发
    onEventResize:()=>{},      // 当日程事件调整（resize）结束并且事件被改变时触发
    onClickTitleForDoctor:()=>{},
    onClickTitleForData:()=>{},
    onEventDragStart:()=>{},   // 开始预约拖拽的回调
    showResourceName:false,    // 左侧title是否展示
    appointmentCountAfterWorkTime:null,
    appointmentCountBeforeWorkTime:null
  };

  /**
   * 删除Event事件
   * @param eventId
   */
  onRemoveEvent=(taskId)=>{
    this.props.onRemoveEvent(taskId)
  }
  /**
   * 点击Event事件
   * @param calEvent
   */
  onEventClick=(calEvent)=>{
    //type 1:老患者预约   2:新患者预约  3:会诊   4:个人时间占用  5:已到诊  6:上班时间
    // @NewType 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊

    const {
      eventExtendedProps,
      id,
      newResourceId,
    } = calEvent || {}

    const {
      appointmentCount,
      appointmentInfoOfTableDto,
      checked,
      consultationInfoOfTableDto,
      operationType,
      remarks,
      reservationRule,
      resourceIdValue,
      titleInfo,
      type,
    } = eventExtendedProps || {}

    /**
     * 获取event 组件中的内容
     */
    if (type == 1 || type == 2 || type == 4){
      this.props.onEventClick(calEvent);
    }
  }
  /**
   * 点击空白事件
   * @param date
   */
  onTimeBlankClick=({dateMonent,resourceObj:res,info})=>{
    let resourceObj = getChairNum(res.id,this.props.calendarTableList);
    this.props.onTimeBlankClick({dateMonent,resourceObj,info})
  }

  /**
   * 点击下一页
   */
  /*goNextIcon=()=>{
    let { current,pageSize,total,pageCount} = this.props.appointmentPageInfo;
    current = current || 1
    pageSize = pageSize || 0
    total = total || 0
    pageCount = pageCount || 0
    let nextPage = current >= pageCount ? pageCount : ++current
    this.props.goNextIcon(nextPage);
  }*/


  /**
   * 获取resources列表项目数据
   * 遍历列表结构的同时添加椅位资源列事件
   * @returns {Array}
   */
  getResources=(calendarTableList)=>{
    let resources = [];                         // 椅位资源列
    if(calendarTableList) {
      let groupTotal = calendarTableList.length;  // 医生总数
      let EventListPlue = [];
      calendarTableList.forEach((val, idx) => {
        if (val.resources) {
          val.resources.forEach((res, resIdx) => {
            let rowspan = null
            if (resIdx == 0) {
              rowspan = val.resources.length
            }
            const {
              staffStatus,
              chairTotal,
              type,
              workStatus,
              workTimeEnd,
              workTimeStart,
              date,
              doctorId,
              groupId,
              name,
              organizationName,
              operationType,
              doctorUserId,
            } = val || {}

            resources.push({
              ...res,
              chairTotal,
              workStatus: res.organizationStatus == 2 ? 5 : workStatus,
              workTimeEnd,
              workTimeStart,
              staffStatus,
              type,
              date,
              doctorId,
              groupId,
              name,
              organizationName: res.organizationStatus == 2 ? res.organizationName : organizationName,
              operationType,
              doctorUserId,
              rowspan,
              resLength: val.resources.length,
            });
          })
        }
      })
    }
    return resources;
  }

  /**
   * 列表中 补位处理
   */
  setSurplusEvent=(resources,nextProps)=>{
    let {
      current,
      pageCount,
      pageSize,
      total,
    } = nextProps.appointmentPageInfo || {}

    let surplusNum = 0;
    if(pageCount && pageSize) {
      surplusNum = 10 - resources.length;
    }

    let surplusObj = null;
    if(surplusNum > 0){
      let surplusReslist= []
      for (let x = 1;x <= surplusNum; x++){
        surplusReslist.push({
          chairNum: x,
          id: `surplusNum_${x}`,
          title: null,
        })
      }
      surplusObj = {
        date: null,
        doctorId: null,
        doctorUserId: null,
        groupId: 'suplus',
        name: null,
        operationType: 1,
        organizationName: null,
        resources: surplusReslist,
        staffStatus: "0",
        type: 1,
        workStatus: 5,
        workTimeEnd: null,
        workTimeStart: null,
      }
    }

    let surplusBackageEvent = []
    surplusObj && surplusObj.resources.forEach((val)=>{
      surplusBackageEvent.push({
        rendering: 'background',
        id: 'surplusEvent' + val.id,
        start: moment('00:00:00','HH:mm:ss').format(),
        end: moment('24:00:00','HH:mm:ss').format(),
        titleInfo: '',
        title: '',
        resourceId:val.id,
        resourceIdValue: val.id,
        backgroundColor: '#DCDCDC',
        className:'surplusBackgroundEvent'
      })
    })

    /**
     * 补位列和Event添加
     */
    /*let surplusList = nextProps.calendarTableList
    let surplusEventList = nextProps.EventList
    if (surplusObj && nextProps.calendarTableList){
      surplusList = [...nextProps.calendarTableList,surplusObj]
      surplusEventList = [...nextProps.EventList,...surplusBackageEvent]
    }*/

    return {
      surpCalendarTableList: surplusObj ? surplusObj : [],
      surpEventList: surplusBackageEvent ? surplusBackageEvent : []
    }
  }

  /**
   * 列表中 休息日 走诊 event事件添加
   */
  setDayOffEvent=(list)=>{
    let eventListPlue = []
    list.forEach((res)=>{
      let {
        workStatus,
        workTimeEnd,
        workTimeStart,
        staffStatus,
        type,
        date,
        doctorId,
        groupId,
        name,
        organizationName,
        id,
        chairNum,
        title
      } = res || {}

      if(workStatus == workStatusForOrganizationStatus) { // 其他诊所椅位出诊
        eventListPlue.push({
          start: moment('00:00:00','HH:mm:ss').format(),
          end: moment('24:00:00','HH:mm:ss').format(),
          title: organizationName,
          resourceId:res.id,
          resourceIdValue: res.id,
          backgroundColor: '#DCDCDC',
          className:'surplusBackgroundEvent',
          //backgroundColor: "#5f5c6b",
          rendering: "background",
          titleInfo: organizationName,
          type: 7,
          workStatus,
          color: null,
          id: `dayOff${res.id}`
        })
      }
    })
    return eventListPlue
  }

  closeTooltip=(isStoreEventId = false)=>{
    // 由于IScroll(滚动容器组件)会屏蔽当前页面所有的滚动事件,所以再日历组件内部的预约详情悬窗中Y轴滚动就失效了
    // 所以(this.myScroll.enabled = false) 当预约详情悬窗打开时,暂时性让IScroll失效,已释放默认的滚动事件
    // 当悬窗关闭后 再将此属性重置(this.myScroll.enabled = true)
    if (this.myScroll) { this.myScroll.enabled = true }
    if (this.tooltip) {
      if(isStoreEventId){
        this.tooltipStoreEventId =  this.tooltip.evnetId
      }else {
        this.tooltipStoreEventId = null
      }
      this.AppointmentParticulars&&this.AppointmentParticulars.clearTooltipDATA&&this.AppointmentParticulars.clearTooltipDATA()
      this.tooltip.hide();
      this.tooltip.dispose();
      this.tooltip = null
      // 广播: 关闭预约详情时发送关闭详情窗口事件的广播,此广播用于将详情弹窗组件中的状态都重置为默认
      // 以及详情弹窗组件中的子组件状态全部清空
      // 接收组件: AppointmentInfoSmallWindow.js  (搜索CLOSE_AppointDetailsHungWindow)
      eventBus.emit(CLOSE_AppointDetailsHungWindow)
    }
  }

  /**
   * 绑定外部点击事件方法
   */
  bindClickForCalendar=()=>{
    const _this = this
    $('body').on("click",function(event){
      let target = $(event.target);
      let thisTargetState = target.attr('class') && target.attr('class').indexOf('fc-event') != -1
      let parentsTarState = target.parents('.fc-event').attr('class') && target.parents('.fc-event').attr('class').indexOf('fc-event') != -1
      if (!(thisTargetState || parentsTarState)) {
        let _con = $('#patientAppointmentById');   // 设置目标区域
        let DomBycloseByAppointmentInfoSmallWindow = _con.find('#closeByAppointmentInfoSmallWindow');   // 设置目标区域
        console.log('bindClickForCalendar123 :: ',DomBycloseByAppointmentInfoSmallWindow.has(event.target).length);
        if (    (DomBycloseByAppointmentInfoSmallWindow
            && DomBycloseByAppointmentInfoSmallWindow.is(event.target))
            ||  (DomBycloseByAppointmentInfoSmallWindow
            && DomBycloseByAppointmentInfoSmallWindow.has(event.target).length != 0)) {
          _this.closeTooltip()
        }
        if (!_con.is(event.target) && _con.has(event.target).length === 0) {
          _this.closeTooltip()
        };
      }
    })
  }

  /**
   * 过滤事件方法
   * @param eventList
   * @returns {*}
   */
  filterEventList=(eventList)=>{
    let doctorIdentification = localStorage.getItem('doctorIdentification') == 1
    eventList = eventList.filter((val)=>{
      if((val.type != 6)){
        if((val.workStatus == workStatusForDayOff && doctorIdentification)) {
        }else {
          return val
        }
      }
    })
    return eventList
  }

  /**
   * 刷新列表组件方法
   */
  refreshBespeakComponent=(nextProps)=>{
    //处理resources列表项目数据

    const resources = this.getResources(nextProps.calendarTableList);
    let resList = this.setDayOffEvent(resources)
    /**
     surpCalendarTableList: surplusObj ? surplusObj : [],
     surpEventList
     */
    let { surpCalendarTableList,surpEventList } = this.setSurplusEvent(resources,nextProps)

    this.setState({
      commonOption: {
        ...this.state.commonOption,
        ...nextProps.Option,
      },
      option: nextProps.Option,
      calendarTableList: [...nextProps.calendarTableList,surpCalendarTableList],
      EventList: [...nextProps.EventList,...resList,...surpEventList],
      AgendaHeadType: nextProps.AgendaHeadType,
      appointmentPageInfo: nextProps.appointmentPageInfo,
    },()=>{
      this.buildCalendar()
      if(this.pageInfoChange){
        this.setPageScrollTopByYIndex(0)
        this.onClickOtherUp()
      }
    })
  };

  /**
   * 确定是否允许拖拽和调整大小的事件可以互相重叠
   */
  eventOverlap=(stillEvent, movingEvent)=>{
    const { extendedProps:stillEventExtendedProps } = stillEvent;
    const { extendedProps:movingEventExtendedProps } = movingEvent;
    const { type:typeFoStill } = stillEventExtendedProps;
    const { type:typeFoMoving } = movingEventExtendedProps;
    return true
  }


  // 展示悬窗的样式
  applyReactStyle=(data)=>{
    this.setState({
      visibleAppointmentParticularsStyle:{
        position:'absolute',
        top:'1px',
        left:'1px'
      },
      visibleAppointmentParticulars:true,
    })
  }


  onPopperShowById=(EventId)=>{
    /*this.setTimeout(()=>{

    })*/
    if (this.tooltipStoreEventId) {
      let changeEvent = this.calendar.getEventById(this.tooltipStoreEventId)
      const {
        extendedProps:eventExtendedProps, // event事件中的扩展信息
        start,                            // 事件开始时间
        end,                              // 事件结束时间
        id:EventId,                       // 事件ID
        groupId,                          // 事件组ID
        title,                            // 事件标题
      } = changeEvent || {}

      const {
        resourceIdValue:newResourceId,     // 选中newResourceId
        operationType,                     // 当前用户是否可编辑此条预约 1:可以编辑 0:不可编辑
        type,                              // 当前点击Event的类型
      } = eventExtendedProps || {}

      let eventClickInfo = {event:changeEvent};
      let resources = this.getResources(this.state.calendarTableList)
      let thisResource = resources.find((res)=>{
        return res.id == newResourceId
      })
      eventClickInfo.thisResource = thisResource

      this.onPopperShow(eventClickInfo,()=>{
        this.AppointmentParticulars.updateCurrentClickEvent(eventClickInfo)
      })
    }
  }

  onPopperShow=({event,el},callBack)=> {
    const {
      extendedProps: eventExtendedProps, // event事件中的扩展信息
      start,                            // 事件开始时间
      end,                              // 事件结束时间
      id: EventId,                       // 事件ID
      groupId,                          // 事件组ID
      title,                            // 事件标题
    } = event || {}
    const {
      type,
      appointmentInfoOfTableDto
    } = eventExtendedProps || {}


    if(this.tooltip){
      const { popperInstance } = this.tooltip;
      const { popper } = popperInstance || {}
      const { id } = popper || {}
      //let myElement = document.getElementById(id+'');
      //myElement && myElement.removeNode(true);
      this.AppointmentParticulars&&this.AppointmentParticulars.clearTooltipDATA&&this.AppointmentParticulars.clearTooltipDATA()
      $('#' + id).remove()
      $('body').remove('.tooltip')
      //this.tooltip.hide()
      //this.tooltip.dispose();
      this.tooltip = null
    }
    if (!this.tooltip) {
      // 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
      if (type == 1 || type == 4) {
        //let calendarApi = this.calendar.current.getApi()
        let changeEvent = this.calendar.getEventById(EventId)
        let elByEvnetId = $(`#event_${EventId}`).parents('.fc-event');

        let pageEnd = $('#calendarWarp').height();       // 页面总内容高度
        let pageShowHeight = $('#scrollBox').height();   // 可视范围高度
        let pageShowWidth = $('#scrollBox').width();     // 可视范围宽度

        let offsetTop = $(elByEvnetId.parents('.fc-widget-content')[0]).offset().top - 200;        // 当前Event在内容区域的位置
        let offsetBottom = $(elByEvnetId.parents('.fc-widget-content')[0]).offset().top - 200;  // 当前Event在内容区域的位置
        let eventOffsetLeft = Math.abs(elByEvnetId.css('left').replace('px',''))
        let eventOffsetRight = Math.abs(elByEvnetId.css('right').replace('px',''))


        let topVisibleContent =  Math.abs(this.myScroll.y)              // 上至区域
        let buttomVisibleContent = topVisibleContent + pageShowHeight;  // 下至区域
        let leftVisibleContent = Math.abs(this.fcScroll.x)              // 左
        let rightVisibleContent =  leftVisibleContent + pageShowWidth   // 右

        /**
         * 计算当前Evnet是否展示在可视区域内
         * @type {string}
         *
         * Event上边界  要小于 可视范围的 下边界
         * Event下边界  要小于 可视范围的 上边界

         * Event右边界 - 50  要小于 可视范围的 左边界
         * Event左边界 要
         */

        let EventShow = true
        // Event超出可视范围上线
        if (offsetBottom < 0){ EventShow = false }
        // Event超出可视范围下线
        if (offsetTop > pageShowHeight){ EventShow = false }
        // Event左右距离可视范围 Event超出左线
        if (eventOffsetRight < leftVisibleContent) {EventShow = false}
        // Event超出右线
        if(rightVisibleContent < eventOffsetLeft){ EventShow = false }

        console.log("------边距判断------");
        console.log(offsetBottom < 0,offsetTop,offsetBottom);
        console.log(offsetTop < pageShowHeight,offsetTop,pageShowHeight);
        console.log(eventOffsetRight < leftVisibleContent);
        console.log(rightVisibleContent < eventOffsetLeft);
        console.log("------边距判断END------");

        if(EventShow){
          let placement = 'left-start'
          if (pageShowHeight - 350  < offsetTop) {
            placement = 'left-end'
          }
          // 非个人占用时间弹出弹窗
          // 非个人占用时间弹出弹窗
          if(type == 1 || type == 4) {
            /* 展示弹窗 */
            if (type == 1 && !this.props.isfullScreen) {
              return
            }
            const {
              appointmentId,
              appointmentStatus
            } = appointmentInfoOfTableDto || {}

            $('.fc-event').removeClass('checkedEvent')
            if(appointmentStatus != 10) { $(el).addClass('checkedEvent') }
            this.clickAppintmentId = appointmentId // 记录当前点击的Event预约id
            let elByEvnetId = $(el);
            // let fc_body =  elByEvnetId.parents('.fc-body')
            let fc_body =  elByEvnetId.parents('#scrollBox')
            console.log('elByEvnetIdelByEvnetId :: ',fc_body);
            let popperContent = document.getElementById('droppableById')
            this.tooltip = new Tooltip(elByEvnetId, {
              html: true,
              container: fc_body[0],
              trigger: 'click',
              popperOptions: {
                modifiers: {
                  flip: {behavior: ['left', 'right']},
                }
              },
              placement: 'left-start',
            });
            this.tooltip.updateTitleContent(findDOMNode(this.refs.foo))

            // 由于IScroll(滚动容器组件)会屏蔽当前页面所有的滚动事件,所以再日历组件内部的预约详情悬窗中Y轴滚动就失效了
            // 所以(this.myScroll.enabled = false) 当预约详情悬窗打开时,暂时性让IScroll失效,已释放默认的滚动事件
            // 当悬窗关闭后 再将此属性重置(this.myScroll.enabled = true)
            if (this.myScroll) { this.myScroll.enabled = false }
            this.tooltip.show();
            this.tooltip.evnetId = EventId; // 对悬窗对象添加唯一标识
            // this.AppointmentParticulars && this.AppointmentParticulars.showModelVisible()
            /*this.setState({
              //modelVisible: true,
              clickEvent:appointmentId,
            }, () => {*/
            // callBack && callBack()
            //})
            // return\
            console.log('tooltiptooltip :: ',this.tooltip);
          }
          // if(type == 1) {
          //   /* 展示弹窗 */
          //   const {
          //     appointmentId
          //   } = appointmentInfoOfTableDto || {}
          //
          //   $('.fc-event').removeClass('checkedEvent')
          //   $(el).addClass('checkedEvent')
          //   this.clickAppintmentId = appointmentId // 记录当前点击的Event预约id
          //   //this.checkedEventId =  EventId
          //   this.tooltip = new Tooltip(elByEvnetId, {
          //     title: '',
          //     html: true,
          //     placement: placement,
          //     trigger: 'click',
          //     container: 'body',
          //   });
          //   this.tooltip.updateTitleContent(findDOMNode(this.refs.details))
          //   this.tooltip.show();
          //   this.tooltip.evnetId = EventId; // 对悬窗对象添加唯一标识
          //   this.tooltipStoreEventId = null;
          // }else {
          //   let popperContent = document.getElementById('droppableById')
          //   this.tooltip = new Tooltip(elByEvnetId, {
          //     title: '',
          //     html: true,
          //     placement: placement,
          //     trigger: 'click',
          //     container: 'body',
          //   });
          //   this.tooltip.updateTitleContent(findDOMNode(this.refs.foo))
          //   this.tooltip.show();
          //   this.tooltip.evnetId = EventId; // 对悬窗对象添加唯一标识
          //   this.tooltipStoreEventId = null;
          // }
        }else {
          this.tooltipStoreEventId = null
        }
      }
    }
    callBack && callBack()
  }

  /**
   * 当点击日历上面的某一时间触发
   */
  /*dateClick=(info)=>{

    let { dateStr,resource } = info
    let origindateStr = dateStr; // 保存记录当前点击时间

    const {
      id,
      title,
    } = resource;

    const {
      chairNum,
      date,
      doctorId,
      groupId,
      name,
      organizationName,
      organizationStatus,
      staffStatus,
      type,
      workStatus,
      workTimeEnd,
      workTimeStart,
      operationType,
      doctorUserId,
    } = resource.extendedProps || {}



    let resourceObj = {
      id,
      title,
      chairNum,
      date,
      doctorId,
      groupId,
      name,
      organizationName,
      staffStatus,
      type,
      workStatus,     // 是否上班 1:上班 2:休息
      workTimeEnd,
      workTimeStart,
      operationType,  // 判断当前列表是否可操作 1可操作 0不可操作
      doctorUserId,
    }

    /!**
     * 处理点击区域所在时间边界
     * @type {Array}
     *
     * 00 15 30 45 边界
     *!/
    dateStr = getDateClickTime(dateStr)


    let eventList = this.state.EventList;
    let eventFilter = eventList.filter((res)=>{
      if (res.resourceIdValue == id) {
        if (moment(dateStr).isBetween(res.start, res.end, 'minutes')) {
          // @NewType 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
          //return res
          if (res.type == 7) {
            return res
          }
        }
      }
    })

    // 获取当前点击事件包含的Event 判断当前点击时间是否包含预约或占用
    let eventFilterTypeUPTiem = this.getBackEvent({
      eventList,id,type:[1,2,4],dateStr
    })
    if (eventFilterTypeUPTiem.length != 0) {
      dateStr = moment(dateStr).add(15,'minute').format()
    }
    let eventFilterTypeDownTiem = this.getBackEvent({
      eventList,id,type:[1,2,4],dateStr
    })
    if (eventFilterTypeDownTiem.length != 0) {
      dateStr = moment(origindateStr).format()
    }

    let dateTimeText = moment(dateStr).format('HH:mm:ss')
    let dataDayText = moment(date).format('YYYY-MM-DD')
    let dataText = dataDayText + ' ' + dateTimeText;
    let dateMonent = moment(dataText,'YYYY-MM-DD HH:mm:ss')


    //当前已过日期 点击空不展示弹框回调
    let resourceMonent = moment(resourceObj.date,'YYYY-MM-DD')
    let isBefore = false
    isBefore = moment().isSame(resourceMonent,'day')
    if(!isBefore) {
      isBefore = moment().isBefore(resourceMonent,'day')
    }

    // 禁用外诊机构椅位点击事件
    if(organizationStatus == 2 || workStatus == 5){
      return
    }

    if (eventFilter.length == 0) {
      const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1  // 1医生  2非医生
      if (doctorIdentificationState) { // 医生登录
        if (operationType == 1) {
          if (isBefore) {
            this.onTimeBlankClick({dateMonent, resourceObj, info});
          }else {
            //message.warning('当前时间已过')
          }
        } else {
          message.warning('当前医生无操作权限', 1)
        }
      } else { // 客服登录
        if (workStatus == 1) {
          //message.warning('当前医生')
          if (isBefore) {
            this.onTimeBlankClick({dateMonent, resourceObj, info});
          }else {
            //message.warning('当前时间已过')
          }
          //this.onTimeBlankClick({dateMonent, resourceObj, info});
        } else {
          //message.warning('当前医生无操作权限', 1)
        }
      }
    }
  }*/

  onWindowResize=()=>{
    this.windowResize()
  }

  /**
   * 组件窗口改变大小时触发w
   */
  windowResize=()=>{
    // eventPatientNameEllipsis()
    //this.buildCalendar(false)
    //this.tit.destroy();
    this.stopScroll()
    this.startScroll()            //启用滚动
    this.buildScrollByTitle()
  }



  /**
   * 获取
   */
  getResourcesItem=(id)=>{
    let resList = this.getResources(this.state.calendarTableList)
    if (id) {
      return resList.find((res) => {
        return res.id == id
      })
    }else {
      return null
    }
  }


  /**
   * 从外部事件拖动回调
   * @param info
   */
  eventReceive=(info)=>{

    const { event } = info || {}
    const { _def:def } = event || {}
    const { resourceIds } = def
    const resourceId = Array.isArray(resourceIds) && resourceIds.length != 0 ? resourceIds[0] : null
    let resourcesItem = this.getResourcesItem(resourceId)
    this.props.onEventReceive && this.props.onEventReceive({
      info,
      resourcesItem
    })
  }

  /**
   * 开始拖拽
   * @param info
   */
  eventDragStart=(info)=>{
    this.props.onEventDragStart && this.props.onEventDragStart(info)
    if(this.tooltip){
      this.tooltip.dispose();
      this.tooltip = null
    }
  }

  /**
   * 拖拽是否允许回调
   * @param dropInfo
   * @param draggedEvent
   */
  eventAllow=(dropInfo, draggedEvent)=>{
    const { resource } = dropInfo || {};
    const { extendedProps } = resource || {}
    const { operationType,workStatus,type,doctorId,organizationStatus,chairStatus } = extendedProps || {};
    const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1  // 1医生  2非医生

    const { extendedProps:draggedEventExtendedProps } = draggedEvent;
    const { type:draggedEventExtendedPropsType } = draggedEventExtendedProps;

    // 外部椅子位列禁止拖拽 空列禁止拖拽
    if(organizationStatus == 2 || workStatus == 5){
      return false
    }

    if (doctorIdentificationState) { // 医生登录
      return operationType == 1
    }else { // 客服登录  客服登录不可以拖拽个人占用时间 && draggedEventExtendedPropsType != 4
      if(draggedEventExtendedPropsType == 4){
        // 拖拽个人占用时间 不可拖拽到其他医生下 只可拖拽到当前医生下的所有椅位置
        const { occupyTimeDto } = draggedEventExtendedProps || {}
        const { resourceId:EventResourceId } = occupyTimeDto || {};
        return operationType == 1 && chairStatus == 1 && EventResourceId == doctorId
      }
      return operationType == 1 && chairStatus == 1
    }
  }

  upDataCalendar=()=>{
    let calendarApi = this.calendarRef.current.getApi()
    calendarApi.render()
    calendarApi.rerenderEvents()
    let changeEvent = calendarApi.getEventById('changeEvent')
    changeEvent && changeEvent.remove()
  }

  clearTooltip=()=>{
    this.closeTooltip()
    /* if (this.tooltip) {
       this.tooltip.hide()
       this.tooltip.dispose();
       this.tooltip = null
     }*/
  }

  /**
   * 滚动到指定位置y轴
   */
  setPageScrollTopByYIndex=(scrollTopNum)=>{
    setTimeout(()=>{
      this.myScroll && this.myScroll.scrollTo(scrollTopNum,0,0);
      let absY = Math.abs(scrollTopNum)
      $('#leftOtherUpContent').css({top:absY}) // 将额外时间栏重置位置
      $('#rightOtherUpContent').css({top:absY}) // 将额外时间栏重置位置
    },200)
  }

  /**
   * 滚动到指定位置x轴
   */
  setPageScrollTopByXIndex=(scrollTopNum)=>{
    if(scrollTopNum == 'end') {
      setTimeout(()=> {
        let that = this;
        // let TimeTitleViweWidth = $('#TimeTitleNum').width()
        // let Timewidth = this.getTitleTimewidth() - TimeTitleViweWidth - 71;

        // let TimeViweWidth = $('#MonthAgendaCalendar').width()
        // let TimeMaxWidth = $('#MonthAgendaCalendar').css('maxWidth')

        // 获取内容宽度
        let TimeContentWidth = $('#MonthAgendaCalendar .fc-view-container .fc-scroller-canvas').last().width()
        let TimeViweWidth = $('#monthAgendaCalendarWarp').width();

        let TimeMaxWidth = this.getTitleTimewidth();
        let scrollX = TimeContentWidth - TimeViweWidth;
        if(scrollX > 0){
          this.fcScroll.scrollTo(0 - (scrollX), 0, 0);
          this.Timetitle.scrollTo(0 - scrollX, 0, 0);
        }else {
          this.fcScroll.scrollTo(0, 0, 500);
          this.Timetitle.scrollTo(0, 0, 500);
        }
      },200)
      return
    }

    setTimeout(()=>{
      this.fcScroll.scrollTo(scrollTopNum,0,200);
      this.Timetitle.scrollTo(scrollTopNum,0,200)
    },500)
  }

  /**
   * 点击展示营业外时间 上时间
   */
  onClickOtherUp=()=>{
    // this.buildCalendar(false);
    // 获取内容宽度
    let TimeContentWidth = $('#MonthAgendaCalendar .fc-view-container .fc-scroller-canvas').last().width()
    let TimeViweWidth = $('#monthAgendaCalendarWarp').width();

    let TimeMaxWidth = this.getTitleTimewidth();
    let scrollX = TimeContentWidth - TimeViweWidth;
    console.log('scrollXscrollX :: ',TimeContentWidth,TimeViweWidth);
    // 默认定位到8点
    let XIndex = 0 - (120 * 9 + 200)
    this.setPageScrollTopByXIndex(XIndex);
  }

  /**
   * 点击展示营业外时间 下时间
   */
  onClickOtherDown=()=> {
    this.buildCalendar(false)
    this.setPageScrollTopByXIndex('end')
  }

  getUrlParamsActiveKey=()=>{
    let href = window.location.href;
    let regArr = ['/subscribe/therearepatient','/customerservice/subscribe/appointment']
    let urlState = false
    regArr.forEach((res)=>{if (href.indexOf(res) != -1){urlState = true}})
    if (urlState) {
      // 首先判断当前页面是否输入半屏改约页面
      //let urlIndex = href.indexOf('therearepatient')
      let urlIndexParams = href.indexOf('?')
      let urlParamsActiveKey = serilizeURL(href).activeKey
      return urlParamsActiveKey
    }else {
      return null
    }
  }

  /**
   * ---------------------------------------------生命周期---------------------------------------------
   * @param props
   */
  constructor(props) {
    super(props);
    this.windowResize = debounce(this.windowResize,500);
    this.onEndMousewheelByUpdata = debounce(this.onEndMousewheelByUpdata,100);

    const userAgent = window.navigator.userAgent.toLowerCase();
    const isChrome = userAgent.indexOf('chrome') > -1;
    this.state = {
      calendarTableList:[...props.calendarTableList],
      EventList:[...props.EventList],
      visibleAppointmentParticulars :false,           // 弹窗状态是否展示
      visibleAppointmentParticularsStyle:{},          // 弹窗定位样式
      currentClickEvent:{},                           // 当前点击的预约数据
      showFullCalendar:true,                          // 展示预约数据,
      AgendaHeadType:null,                            // 回显列表头类型数据
      commonOption:{},                                // 时间颗粒度改变
      appointmentPageInfo:{},
      calendarY:0,                                    // 记录最后滚动的位置Y轴
      calendarX:0,                                    // 记录最后滚动的位置X轴
      OtherUp:false,                                  // 是否展示预约时间之外的上时间
      OtherDown:false,                                // 是否展示预约时间之外的下时间
      modelVisible:false,                             // 弹窗展示
      isChrome,                                       // 是否是使用chrome浏览器
      clickEvent:null,                                // 记录点击的Event
    };
    //this.calendarRef = React.createRef();
  }

  updateStatus = () =>{
    this.props.updateStatuss && this.props.updateStatuss()
  };

  /**
   * 获取表格头数据区间(已废除)
   * */
  _getCalendarTimeTitleOld=()=>{
    //moment(minTime(),'HH:MM')

    /*
    //开始时间
    let start_date = moment(start,"YYYY-MM-DD HH:mm:ss");
    // 结束时间
    let end_date = moment(end,"YYYY-MM-DD HH:mm:ss");   //秒
    // 分钟差
    let diff_minutes = end_date.diff(start_date,"minutes");  //分钟*/

    let oldTime = moment(json[i].startTime,"YYYY-MM-DD HH:mm:ss");
    let diff_minutes = oldTime.diff(moment(),"minutes");
    moment().diff(oldTime,"minutes");
    /**
     let start_date = moment(start,"YYYY-MM-DD HH:mm:ss");
     let end_date = moment(end,"YYYY-MM-DD HH:mm:ss");     //秒
     let seconds = end_date.diff(start_date,"seconds");    //分钟
     let mintus = (seconds/60);
     minTime:this.state.OtherUp ? '00:00:00' : this.props.Option.minTime,
     maxTime:this.state.OtherDown ? '24:00:00' : this.props.Option.maxTime

     * */
    // let minTimeValue = this.state.OtherUp ? '00:00:00' : minTime();
    // let maxTimeValue = this.state.OtherDown ? '23:59:59' : maxTime();
    let minTimeValue = '00:00:00';
    let maxTimeValue = '23:59:59';
    let start_date = moment(minTimeValue,"HH:mm:ss"); //
    let end_date = moment(maxTimeValue,"HH:mm:ss");   // 秒

    let startMM = start_date.minute();
    let endMM = end_date.minute();

    let startHH = start_date.hours();
    let endHH = end_date.hours();


    let hours = end_date.diff(start_date, "hours");        // 分钟

    let endhours = moment(maxTimeValue,"HH:mm:ss").endOf('hour').minutes(0).seconds(0).milliseconds(0)
    hours = endhours.diff(start_date, "hours");            // 分钟

    let mintus = parseInt(hours) * 2 - (endMM == 0 ? 2 : 1);                  // 计算时间区间 去除开头和结尾时间



    let span = [start_date.format('HH:mm')];
    for(let i = 0; i <= mintus; i++){
      span.push(start_date.add('30', 'minute').format('HH:mm'))
    }
    return span
  }

  /**
   * 获取表格头数据区间(正在使用)
   * */
  getCalendarTimeTitle=()=>{
    // let minTimeValue = this.state.OtherUp ? '00:00:00' : minTime();
    // let maxTimeValue = this.state.OtherDown ? '24:00:00' : maxTime();
    let minTimeValue = '00:00:00';
    let maxTimeValue = '23:59:59';
    let start_date = moment(minTimeValue,"HH:mm:ss");
    let end_date = moment(maxTimeValue,"HH:mm:ss");  // 秒

    let startMM = start_date.minute();
    let endMM = end_date.minute();

    let startHH = start_date.hours();
    let endHH = end_date.hours();

    let span = [start_date.format('HH:mm')];
    /*for(let i = 0; i <= mintus; i++){
      span.push(start_date.add('30', 'minute').format('HH:mm'))
    }*/
    while (start_date.diff(end_date,'minute') < 0){
      span.push(start_date.add('30', 'minute').format('HH:mm'))
    }

    if (endMM == 0) {
      span = span.map((val,idx)=>{
        if (span.length - 1 != idx){
          return val
        }
      })
    }

    return span
  }


  /**
   * 搜索结果定位添加
   * */
  setPageScrollTop=()=> {
    let checkEvent = this.state.EventList.find((event)=>{return event.checked == 1})
    let checkEvnetOffset = $($('.checkedEvent').parents('.fc-widget-content')[0]).position()
    let checkEvnetByPosition =  $('.checkedEvent').position()

    if(checkEvnetOffset){

      let scrollBoxViewHeight = $('#scrollBox').height();     // 滚动视口高度
      let scrollBoxViewWidth  = $('#scrollBox').width();      // 滚动视口宽度
      let contentHeight =  $('#calendarWarp').height();       // 滚动区域总高度
      let contentWidth  =  $($('.checkedEvent').parents('.fc-widget-content')[0]).width();  // 滚动区域总宽度

      let checkTop = checkEvnetOffset.top - 20                // 需要滚动到的高度
      let checkLeft = checkEvnetByPosition.left               // 需要滚动到的X轴位置

      let maxScrollTop   = contentHeight - scrollBoxViewHeight  // Y轴可滚动定位的最大高度
      //let maxScrollWidth = contentWidth - scrollBoxViewWidth    // X轴可滚动定位的最大宽度

      console.log('contentWidth :: ',contentWidth);

      // 计算到Y轴的位置
      if(checkTop <= 0) {
        checkTop = 0
      }else if(checkTop >= maxScrollTop){
        checkTop = maxScrollTop
      }

      // 计算到X轴的位置
      /*if(checkLeft <= 0) {
        checkLeft = 0
      }else if(checkLeft >= maxScrollWidth ){
        checkLeft = maxScrollWidth
      }*/

      console.log('checkLeft :: ',checkLeft);

      //this.myScroll.scrollBy(0,(-checkTop))  // 定位到Y轴的位置
      this.myScroll.scrollTo(0,(-checkTop),1000)  // 定位到Y轴的位置
      //this.fcScroll.scrollTo((-checkLeft),0,500);

    }
  }

  getTitleTimewidth=()=>{
    // let minTimeValue = this.state.OtherUp ? '00:00:00' : minTime();
    // let maxTimeValue = this.state.OtherDown ? '24:00:00' : maxTime();
    let minTimeValue = '00:00:00';
    let maxTimeValue = '23:59:59';
    let start_date = moment(minTimeValue,"HH:mm:ss");
    let end_date = moment(maxTimeValue,"HH:mm:ss");  // 秒

    let minuteDiff = end_date.diff(start_date,'minute')

    let width = (parseInt(minuteDiff / 15) * slotWidth) + 73
    //let width = timeTitleList.length * 60 + 71
    return width
  }

  /**
   * 获取最大宽度
   */
  /*getMaxWidth=()=>{




  }*/

  /**
   * 关闭详情弹窗方法
   * */
  closeInfoModel=()=>{
    this.setState({
      modelVisible:false
    })
  }

  // 点击患者 详情中 加入等待列表 床底数据

  lineUpListModal = (patientInfoDto,complaintList,objAppointment,type,that) =>{
    this.props.TimeAgendaCalendarLineUpListModal&&this.props.TimeAgendaCalendarLineUpListModal(patientInfoDto, complaintList,objAppointment,type,that)
  };

  render(){
    let href = window.location.href;
    //let customerserviceState = href.indexOf('customerservice') != -1
    let resourceList = this.getResources(this.state.calendarTableList)
    let timeTitleList = this.getCalendarTimeTitle();
    const {current, pageSize, total, pageCount } = this.props.appointmentPageInfo

    return (
      <Spin spinning={!!this.props.loading.effects['TimeCalendar/appointmentTimeLengthAlter']}>
        <div id="AppintmentCalendar" className={ classNames('AppintmentCalendar',styles.AppintmentCalendar)}>
          <div className={styles.TimeTitleWarp}>
            <div className={styles.titleLeftWarp}>
              <div className={styles.titleLeftbox}>
                <div className={styles.pageControlWarp}>
                  <div className={styles.pageControl}>
                    {/*上一页按钮 current > 1  current < pageCount*/}
                    <div
                      onClick={()=>{if (current > 1) { this.goLastIcon()}}}
                      className={classNames({
                        [styles.circleIcon]:true,
                        [styles.circleBox]:true,
                        [styles.circleAllow]: current > 1 ? true : false,
                        [styles.circleUnallow]: current > 1 ? false : true
                      })}
                    >
                      <Icon className={styles.circleUP} type="left-circle"/>
                      {/*<span className={styles.circleSpan}>上一页</span>*/}
                    </div>
                    {/*下一页*/}
                    <div
                      onClick={()=>{if (current < pageCount) {this.goNextIcon()}}}
                      className={classNames({
                        [styles.circleIconleft]:true,
                        [styles.circleIcon]:true,
                        [styles.circleBox]:true,
                        [styles.circleAllow]   : current < pageCount ? true  : false,
                        [styles.circleUnallow] : current < pageCount ? false : true
                      })}
                    >
                      <Icon className={styles.circleDown} type="right-circle"/>
                      {/*<span className={styles.circleSpan}>下一页</span>*/}
                    </div>
                  </div>
                </div>
              </div>

              <table className={styles.TimeTitleTable}>
                <tr>
                  <td> </td>
                  <td> </td>
                </tr>
              </table>
            </div>
            <div  id="TimeTitleWrap"  className={styles.titleRigthWarp}>
              {/*<div id="TimeTitleWrap" className={styles.TimeTitleWrap}>*/}
              <div id="TimeTitleNum">

                <div id="TimeTitleScroll" style={{width:this.getTitleTimewidth()}} className={styles.TimeTitleScroll}>
                  <div  className={styles.TimeTitleNum}>
                    {timeTitleList.map((res,idx)=>{
                      if (idx == 0){
                        return (<div>{res}</div>)
                      }else {
                        if (moment(res,'HH:mm:ss').format('mm') == 30) {
                          return (<div></div>)
                        } else {
                          if(timeTitleList.length - 1 == idx && res == "00:00") {
                            return (<div></div>)
                          }else {
                            return (<div>{res}</div>)
                          }
                        }
                      }
                    })}
                    <div className={styles.timeLastBox}></div>
                  </div>
                </div>
              </div>
              {/*</div>*/}
            </div>
          </div>
          <div id="scrollBox" style={{
            maxWidth:this.getTitleTimewidth() + 130,
          }} className={commonStyles.scrollBox}>
            {/*className={commonStyles.scroll} style={{width: `calc(100% + ${this.scrollbarWidth}px)`}}*/}
            <div id="scrollContent" className={commonStyles.scroll} style={{width: `calc(100%)`}}>
              { Array.isArray(resourceList) && resourceList.length != 0 &&
              <div id="dataUp" onClick={this.goLastIcon} className={commonStyles.scrollSpan}>
                <div className={commonStyles.scrollSpanContent}>
                  <span><span className={commonStyles.spanContentTitle}>加载上一页</span><Spin/></span>
                </div>
              </div>}
              <div id="calendarWarp" className={commonStyles.scrollContentInfo}>
                <div className={commonStyles.scrollClear}>
                  {/*侧边栏*/}
                  <div className={styles.resourceTableWarp}>
                    {resourceList.length != 0 &&
                    <table id='resourceListTitle' className={classNames({
                      [styles.resourceTable]:true,
                      //deskBoxMarginTop
                      [styles.resourceMarginTop]:true,
                    })}>
                      {/*资源列*/}
                      {resourceList.map((res,index)=>{
                        return (
                          <tr>
                            {res.rowspan &&
                            <td className={classNames({
                              [styles.titleTitleRow]:true,
                              [styles.rowTitile]:true,
                            })} rowSpan={res.rowspan}>
                              {res.groupId != 'suplus' &&
                              <>
                                {this.props.showResourceName ?
                                  <div>{res.name}</div>
                                  :
                                  <div>
                                    <div>{moment(res.date, 'YYYY-MM-DD').format('MM-DD')}</div>
                                    <div>{moment(res.date, 'YYYY-MM-DD').format('dddd')}</div>
                                  </div>
                                }
                              </>
                              }
                            </td>}
                            <td className={classNames({
                              'resourceListName':true,
                              [styles.resSuplus]:res.groupId == 'suplus'
                            })}>
                              {res.groupId != 'suplus' &&
                              <>
                                {/*<div>
                                        <i className={styles.chairIcon}></i>
                                        { res.organizationStatus != 2 && res.chairStatus == 2 &&
                                          <span className={classNames(styles.chiarText, styles.restDay)}>休</span>
                                        }
                                      </div>*/}

                                {res.organizationStatus == 2 &&
                                <div>
                                  <span className={styles.chiarText}>外</span>
                                </div>
                                }

                                {/*{res.organizationStatus != 2 && res.chairStatus == 2 &&
                                      <div>

                                        <span className={styles.chiarText}>休</span>
                                      </div>
                                      }*/}

                                {res.organizationStatus != 2  &&
                                <div>
                                  {res.chairTotal == 1 ?
                                    <span className={styles.chiarText}><span>{res.chairTotal}</span></span> :
                                    <span className={styles.chiarText}><span
                                      // className={styles.redChiarNum}
                                    >{res.chairNum}</span><span>/</span><span>{res.chairTotal}</span></span>}
                                  {/*{res.title}*/}
                                </div>
                                }
                              </>
                              }
                            </td>
                          </tr>
                        )
                      })}
                    </table>
                    }

                    {resourceList.length == 0 &&
                    <div className={styles.monthAgendaBox}>
                      &nbsp;
                    </div>
                    }


                  </div>
                  {/*预约组件*/}
                  <div id="monthAgendaCalendarWarp" style={{
                    maxWidth:this.getTitleTimewidth() - 71,
                  }} className={styles.calendarBox}>
                    <div className={styles.calendarWarp}>
                      <div id="MonthAgendaCalendar" style={{
                        maxWidth:this.getTitleTimewidth() - 71,
                      }} className={styles.MonthAgendaCalendar}></div>
                    </div>
                  </div>
                </div>
              </div>
              { Array.isArray(resourceList) && resourceList.length != 0 &&
              <div id="dataDown" onClick={this.goNextIcon} className={commonStyles.scrollSpanBottom}>
                <div className={commonStyles.scrollSpanContent}>
                  <span><span className={commonStyles.spanContentTitle}>加载下一页</span><Spin/></span>
                </div>
              </div> }
            </div>
            {/*</InfiniteScroll>*/}
          </div>

          {/*<div className={styles.pageControlDownWarp}>*/}
          {/*  <div className={styles.pageControl}>*/}
          {/*    /!*上一页按钮 current > 1  current < pageCount*!/*/}
          {/*    <div*/}
          {/*      onClick={()=>{if (current > 1) { this.goLastIcon()}}}*/}
          {/*      className={classNames({*/}
          {/*        [styles.circleIcon]:true,*/}
          {/*        [styles.circleBox]:true,*/}
          {/*        [styles.circleAllow]: current > 1 ? true : false,*/}
          {/*        [styles.circleUnallow]: current > 1 ? false : true*/}
          {/*      })}*/}
          {/*    >*/}
          {/*      <Icon className={styles.circleUP} type="left-circle"/>*/}
          {/*      /!*<span className={styles.circleSpan}>上一页</span>*!/*/}
          {/*    </div>*/}
          {/*    /!*下一页*!/*/}
          {/*    <div*/}
          {/*      onClick={()=>{if (current < pageCount) {this.goNextIcon()}}}*/}
          {/*      className={classNames({*/}
          {/*        [styles.circleIconleft]:true,*/}
          {/*        [styles.circleIcon]:true,*/}
          {/*        [styles.circleBox]:true,*/}
          {/*        [styles.circleAllow]   : current < pageCount ? true  : false,*/}
          {/*        [styles.circleUnallow] : current < pageCount ? false : true*/}
          {/*      })}*/}
          {/*    >*/}
          {/*      <Icon className={styles.circleDown} type="right-circle"/>*/}
          {/*      /!*<span className={styles.circleSpan}>下一页</span>*!/*/}
          {/*    </div>*/}
          {/*  </div>*/}
          {/*</div>*/}


          {/* 全屏预约点击详情功能弹窗 */}
          {
            <div className={classNames({[styles.poppperWarp]:true})}>
              <div ref='foo'>
                <AppointmentParticulars
                  className={'eventShowPopper'}
                  visible={this.state.modelVisible}
                  currentClickEvent={this.state.currentClickEvent}
                  onRef={(that)=>{this.AppointmentParticulars = that}}
                  hideButtom={false} //隐藏操作按钮
                  useForMonthMode={true}
                  upDataEventById={this.upDataEventById}
                  updateStatus={()=>{this.updateStatus()}}
                  clearTooltip={()=>{this.clearTooltip()}}
                  handleCancel={()=>{this.clearTooltip()}}
                  closeInfoModel={()=>{this.closeInfoModel()}}
                  lineUpListModal={this.lineUpListModal}
                  arrival={this.props.arrival} // 点击到诊
                  cancelModalClick={this.props.cancelModalClick}
                  maximizeAppointmentDetails={this.props.maximizeAppointmentDetails}
                  changeContract={this.props.changeContract} // 改约点击
                >
                </AppointmentParticulars>
              </div>
              <div ref='details' className={'AppointmentInfoSmallWindow'}>
                <AppointmentInfoSmallWindow

                />
              </div>
            </div>
          }
        </div>
      </Spin>
    )
  }

  upDataOption=()=>{

  }

  componentDidMount() {
    const _this = this;
    this.props.onRef && this.props.onRef(this);
    this.bindClickForCalendar()
    this.scrollbarWidth = getScrollbarWidth()
    this.refreshBespeakComponent(this.props) // 更新方法
    //this.buildCalendar();
    window.addEventListener('resize',this.onWindowResize);
    // this.onMousewheel()
  }

  onMousewheel=()=>{
    $('#MonthAgendaCalendar').bind('wheel',(event)=>{
      var wheel = event.originalEvent.wheelDelta;
      var detal = event.originalEvent.deltaY;
      var isFirefox = navigator.userAgent.indexOf("Firefox") != -1;
      if (isFirefox){

        if (event.originalEvent.deltaY) {  //Firefox滚轮事件
          if (detal > 0) { //当滑轮向下滚动时
            this.onStartMousewheelByUpdata('down')
            this.onEndMousewheelByUpdata('down')
          }
          if (detal < 0) { //当滑轮向上滚动时
            this.onStartMousewheelByUpdata('up')
            this.onEndMousewheelByUpdata('up')
          }
        }

      }else {
        if (event.originalEvent.wheelDelta) { //判断浏览器IE,谷歌滚轮事件
          if (wheel > 0) { //当滑轮向上滚动时
            this.onStartMousewheelByUpdata('up')
            this.onEndMousewheelByUpdata('up')
          }
          if (wheel < 0) { //当滑轮向下滚动时
            this.onStartMousewheelByUpdata('down')
            this.onEndMousewheelByUpdata('down')
          }
        }
      }
    })
  }

  /**
   * 滚动开始
   * delta = up
   * delta = down
   */
  onStartMousewheelByUpdata=(delta)=>{
    if(!this.mousewheelUnderway){
      this.mousewheelUnderway = true
      const {current, pageSize, total, pageCount } = this.props.appointmentPageInfo
      console.log('onStartMousewheelByUpdata 滚动方向 :: ' + delta + ' : ' + this.y);
      if (this.myScroll) {
        if (this.y == this.myScroll.maxScrollY && delta == 'down') {
          if (current < pageCount) {
            console.log('触发下一页 :: down');
            //this.myScroll
            //$('#dataDown').show();
            this.myScroll.scrollTo(0, (this.myScroll.maxScrollY - 60), 200);
          } else {
            console.log('没有下一页了');
          }
        }
        if (this.y == 0 && delta == 'up') {
          if (current > 1) {
            console.log('触发上一页 :: up');
            //$('#dataUp').show();
            this.myScroll.scrollTo(0, 40, 200);
          } else {
            console.log('没有上一页了');
          }
        }
      }
    }else {
      console.log('正在滚动中');
    }
  }

  /**
   * 滚动结束
   * 加载上一页或者下一页
   * delta = up
   * delta = down
   */
  onEndMousewheelByUpdata=(delta)=>{
    // 滚动结束
    this.mousewheelUnderway = false
  }

  eventRender=(info)=>{
    const { event, el, view} = info
    const {
      extendedProps,
      id,
      start,
      end,
      durationEditable,
    } = event || {} //changeEvent
    const {
      appointmentInfoOfTableDto,
      type,
      operationType,
      checked,
      EventData,
    } = extendedProps || {}
    let {
      appointmentId,
      isComeVisit,  // 0未到诊 1已到诊
      isLate,       // 过预约时间未到诊 0没有迟到  1迟到
      isSettlement, // 结算状态 0:未结算 1:已结算
    } = appointmentInfoOfTableDto || {}

    const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1  // 1医生  2非医生

    let element = $(el)

    if(type == 2){
      let diffMinutes = 0;
      if(start && end){
        let startMoment = moment(start);
        let endMoment = moment(end);
        diffMinutes = endMoment.diff(startMoment,'minutes');
      }
      if(diffMinutes <= 5) {
        element.addClass('unset')
      }
    }

    element.addClass(typeClassName[type])
    if(checked == 1){element.addClass('checkedEvent')} // 圈住当前搜索到的预约

    if(id != 'changeEvent') {
      //ReactDOM.render(<EventElement/>)
      ReactDOM.render(<MonthEventElement eventObj={info}/>, el)
      //ReactDOM.render()
      //ReactDOM.render(<div></di1v>, el)
    }
    if (type == 1 && appointmentId) {
      let activeKey = this.getUrlParamsActiveKey()
      if(activeKey == appointmentId){ // 圈住改约中当前预约的逻辑
        element.addClass('checkedEvent')
      }
      if(this.clickAppintmentId && this.clickAppintmentId == appointmentId){
        element.addClass('checkedEvent')
      }
      if (durationEditable){
        element.append(`<div class='fc-resizer fc-end-resizer'></div>`)
      }
    }

    // 其他占用时间展示
    if(type == 4){
      element.addClass('eventOtherEvent')
      //ReactDOM.render(<MonthEventElement eventObj={info}/>, el)
      //if(doctorIdentificationState) {  // 客服登录不可以下拉拖拽占用时间
      //客服或医生登录都可下拉拖动
      element.append(`<div class='fc-resizer fc-end-resizer'></div>`)
      //}
    }
  }

  /**
   * 当点击日历中某个事件的时候触发回调
   */
  eventClick=(eventClickInfo)=>{
    const {
      el,
      event,
      jsEvent,
      view,
    } = eventClickInfo

    const {
      extendedProps:eventExtendedProps, // event事件中的扩展信息
      start,                            // 事件开始时间
      end,                              // 事件结束时间
      id:EventId,                       // 事件ID
      groupId,                          // 事件组ID
      title,                            // 事件标题
    } = event || {}

    /*eventExtendedProps.appointmentInfoOfTableDto.patientInfoDto.name = 'hahahaha'
    this.upDataEventById('3',eventExtendedProps)*/

    if(EventId == 'checkedBackEvent'){return}

    const {
      resourceIdValue:newResourceId,     // 选中newResourceId
      operationType,                     // 当前用户是否可编辑此条预约 1:可以编辑 0:不可编辑
      type,                              // 当前点击Event的类型
      appointmentInfoOfTableDto,
    } = eventExtendedProps || {}

    const {
      appointmentStatus // 预约状态 预约状态 1正常 2已改约 3已取消 10预约占用
    } = appointmentInfoOfTableDto || {}

    // 预约状态 预约状态 1正常 2已改约 3已取消 10预约占用   预约占用不记录
    if(appointmentStatus == 10){
      const { chairCode,appointmentDate,timeStart,appointmentId } = appointmentInfoOfTableDto || {}
      let chairInfo = this.getResources(this.state.calendarTableList).find(item=> item.id == newResourceId)
      let dateStr = `${appointmentDate}T${timeStart}`
      const { target } = jsEvent
      let classNameTest = $(target).prop("className");
      const isCancel = classNameTest && classNameTest.indexOf('ShutDownBtn') != -1
      /**
       isCancel:false
       appointmentId: 6852
       appointmentInfoOfTableDto: {appointmentId: 6852, taskId: null, taskType: 3, appointmentStatus: 10, chairCode: 1, …}
       chairInfo: {id: 1, chairNum: 1, title: '椅位1', organizationStatus: 1, organizationName: null, …}
       dateStr: "2022-03-25T15:00"
       */

      this.props.onTemporaryStorageEventClick({
        isCancel:isCancel,                                   // 是否是取消暂存
        dateStr:dateStr,                                     // 时间
        chairInfo:chairInfo,                                 // 椅位信息
        appointmentId:appointmentId,                         // 预约id
        appointmentInfoOfTableDto:appointmentInfoOfTableDto, // 原始预约信息
      })
      return;
    }


    let stateMoment = moment(start) // 获取新event的开始时间
    let endMoment = moment(end)     // 获取新event的结束时间

    let eventObj = {
      start:stateMoment,        // 开始时间
      end:endMoment,            // 结束时间
      id:EventId,               // 事件id
      newResourceId,            // event 事件中对应的列资源id
      eventExtendedProps,       // event 事件中扩展信息
      el,                       // event 中的Dom
    }

    let resources = this.getResources(this.state.calendarTableList)
    let thisResource = resources.find((res)=>{
      return res.id == newResourceId
    })


    if(appointmentStatus == 10){ return }


    eventClickInfo.thisResource = thisResource
    const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1  // 1医生  2非医生\
    console.log('doctorIdentificationState :: ',doctorIdentificationState,eventClickInfo);
    if (doctorIdentificationState) { // 医生登录
      if (type == 4){  // 当前点击事件为个人占用时间事件
        this.onPopperShow(eventClickInfo, () => {
          this.AppointmentParticulars.updateCurrentClickEvent(eventClickInfo)
        });
        return
      }
      if (operationType == 1) {
        this.onPopperShow(eventClickInfo, () => {
          this.AppointmentParticulars.updateCurrentClickEvent(eventClickInfo)
        });
        this.onEventClick(eventObj);
      } else {
        /*if (eventExtendedProps.type == 1) {
          message.warning('当前医生无操作权限', 1)
        }*/
        this.onPopperShow(eventClickInfo, () => {
          this.AppointmentParticulars.updateCurrentClickEvent(eventClickInfo)
        });
        //this.onEventClick(eventObj);
      }
    }else { // 客服登录
      //if (type != 4) {
      this.onPopperShow(eventClickInfo, () => {
        this.AppointmentParticulars.updateCurrentClickEvent(eventClickInfo)
      });
      this.onEventClick(eventObj);
      //}
    }
  };

  /**
   * 获取空白区域覆盖的event
   */
  getBackEvent=({eventList,id,type,dateStr})=>{
    let eventFilterWork = eventList.filter((res)=> {  //资源的上班时间
      if (res.resourceIdValue == id) {
        if (moment(dateStr).isBetween(res.start, res.end, 'minutes') || moment(dateStr).isSame(res.start,'minutes')) {
          if (Array.isArray(type)) {
            let typeList = type.filter((val)=>{
              return val == res.type
            })
            return typeList.length != 0
          }else {
            if (res.type == type) {
              return res
            }
          }
        }
      }
    })
    return eventFilterWork
  }

  /**
   * 当点击日历上面的某一时间触发
   */
  dateClick=(info,value)=>{
    let { dateStr,resource,jsEvent } = info
    const { target } = jsEvent
    let origindateStr = dateStr; // 保存记录当前点击时间
    let patientAppointmentById = $(target).parents('.tooltip').prop("className")
    if(patientAppointmentById == 'tooltip'){ return }

    const {
      id,
      title,
    } = resource;

    const {
      chairNum,
      date,
      doctorId,
      groupId,
      name,
      organizationName,
      staffStatus,
      type,
      workStatus,
      workTimeEnd,
      workTimeStart,
      operationType,
      doctorUserId,
      chairStatus
    } = resource.extendedProps || {}



    let resourceObj = {
      id,
      title,
      chairNum,
      date,
      doctorId,
      groupId,
      name,
      organizationName,
      staffStatus,
      type,
      workStatus,     // 是否上班 1:上班 2:休息
      workTimeEnd,
      workTimeStart,
      operationType,  // 判断当前列表是否可操作 1可操作 0不可操作
      doctorUserId,
      chairStatus
    }


    /**
     * 处理点击区域所在时间边界
     * @type {Array}
     *
     * 00 15 30 45 边界
     */
    dateStr = getDateClickTime(dateStr)
    let dateMonent = moment(dateStr)


    let eventList = this.state.EventList;
    let eventFilter = eventList.filter((res)=>{
      if (res.resourceIdValue == id) {
        if (moment(dateStr).isBetween(res.start, res.end, 'minutes')) {
          // @NewType 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
          //return res
          if (res.type == 7) {
            return res
          }
        }
      }
    })


    let eventFilterTypeUPTiem = this.getBackEvent({
      eventList,id,type:[1,2,4],dateStr
    })
    if (eventFilterTypeUPTiem.length != 0) {
      dateStr = moment(dateStr).add(15,'minute').format()
    }
    let eventFilterTypeDownTiem = this.getBackEvent({
      eventList,id,type:[1,2,4],dateStr
    })

    if (eventFilterTypeDownTiem.length != 0) {
      dateStr = moment(origindateStr).format()
    }

    let dateTimeText = moment(dateStr).format('HH:mm:ss')
    let dataDayText = moment(date).format('YYYY-MM-DD')
    let dataText = dataDayText + ' ' + dateTimeText;
    dateMonent = moment(dataText,'YYYY-MM-DD HH:mm:ss')
    info.dateStr = dateStr;

    //当前已过日期 点击空不展示弹框回调
    let resourceMonent = moment(resourceObj.date,'YYYY-MM-DD')
    let isBefore = false;
    isBefore = moment().isSame(resourceMonent,'day')
    if(!isBefore) {
      isBefore = moment().isBefore(resourceMonent,'day')
    }

    // 判断当前医生是否离职 离职医生不可预约
    if(staffStatus == 1){
      message.warning('当前医生已离职,不可新建预约')
      return
    }

    if (eventFilter.length == 0) {
      const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1  // 1医生  2非医生
      if (doctorIdentificationState) { // 医生登录
        if (operationType == 1) {
          if (isBefore) {
            this.onTimeBlankClick({dateMonent, resourceObj, info});
          }else {
            //message.warning('当前时间已过')
          }
        } else {
          message.warning('当前医生无操作权限', 1)
        }
      } else { // 客服登录
        if (isBefore) {
          if (chairStatus == 2){
            message.warning('当前椅位状态为休息不可新建预约')
            return
          }
          if(chairStatus == 1) {
            this.onTimeBlankClick({dateMonent, resourceObj, info});
          }
        }else {
          //message.warning('当前时间已过')
        }
        /*if (workStatus == 1) {
          //message.warning('当前医生')
          if (isBefore) {
            if (eventFilterOffDay.length != 0) {
              message.warning('选中椅位状态为休息不可新建预约')
              return
            }
            this.onTimeBlankClick({dateMonent, resourceObj, info});
          }else {
            //message.warning('当前时间已过')
          }
          //this.onTimeBlankClick({dateMonent, resourceObj, info});
        } else {
          //message.warning('当前医生无操作权限', 1)
        }*/
      }
    }
  }

  /**
   * 回调函数，当拖动结束且日程移动另一个时间时触发：
   * @param info
   */
  eventDrop=(info)=>{
    //_this.props.onEventDrop && _this.props.onEventDrop(event,delta)
    /*const {
      appointmentInfoDto, // 主诉信息
      end,                // 结束时间
      start,              // 开始时间
      resourceId,         // 改变的垂直资源列id
      id,                 // 当前事件ID
    } = event*/

    const {
      el,
      event,
      oldEvent,
      view,
      delta,
      newResource,
      oldResource
    } = info || {}
    /* extendedProps 事件扩展中包含信息
        appointmentCount: null
        appointmentInfoDto: {appointmentId: 73426, patientId: 1115583, patientName: "大闹天宫", sex: 1, sexDescribe: "先生"}
        appointmentInfoDtoList: null
        canceled: false
        operationType: 1
        reservationRule: null
        titleInfo: "Event Title1"
        type: 5
     */
    const {
      extendedProps:eventExtendedProps, // event事件中的扩展信息
      start,         // 事件开始时间
      end,           // 事件结束时间
      id:EventId,    // 事件ID
      groupId,       // 事件组ID
      title,         // 事件标题
      extendedProps
    } = event || {}
    const {
      appointmentInfoDto,
      resourceIdValue
    } = eventExtendedProps || {};
    /*
     newResourceExtendedProps 资源列中包含信息
     chairNum: 1
     date: "2019-05-07"
     doctorId: 405
     groupId: 2
     name: "王医生小组测试别动"
     organizationName: null
     staffStatus: "0"
     type: 1
     workStatus: 4
     workTimeEnd: null
     workTimeStart: null
     */
    const {
      extendedProps:newResourceExtendedProps,  // 新资源列信息
      id:newResourceId,                        // 新资源列ID
      title:newResourceTitle,                  // 新资源列标题
    } = newResource || {}


    let stateMoment = moment(start) // 获取新event的开始时间
    let endMoment = moment(end)     // 获取新event的结束时间


    let eventObj = {
      start:stateMoment,        // 开始时间
      end:endMoment,            // 结束时间
      id:EventId,               // 事件id
      eventExtendedProps,       // 扩展Event携带参数
      newResource,              // 移动到的资源列对象
      newResourceId,            // 资源列ID
      newResourceTitle,         // 资源列title
      newResourceExtendedProps, // 资源列扩展对象
    }

    const { _def:def } = event || {}
    let resources = this.getResources(this.state.calendarTableList)
    let thisResource = resources.find((res)=>{
      if(res.id == resourceIdValue){
        return res
      }
    })

    let infoment = {...info,thisResource}
    //info.revert()
    // 抛出拖拽事件回调
    this.props.onEventDrop && this.props.onEventDrop({...info,thisResource})
  }

  /**
   * 以到诊预约预约 修改预约时长
   * appointmentId [long]	是	预约ID
   appointmentDate [date]	是	预约日期
   timeStart [date]	是	预约起始时间
   timeLength [int]	是	预约时长
   chairCode [int]	是	椅位
   */
  appointmentTimeLengthAlter=(info)=>{
    const { event,thisResource } = info
    const { end,start,extendedProps } = event
    const { appointmentInfoOfTableDto,resourceIdValue } = extendedProps || {}
    const { appointmentId,appointmentDate,chairCode,appointmentDatetimeStr } = appointmentInfoOfTableDto || {}
    const { dispatch } = this.props
    let timeLength = moment(end).diff(moment(start),'minute');
    let timeStart = moment(start).format('HH:mm')


    let params = {
      appointmentId,
      appointmentDate,
      chairCode,
      timeLength,
      timeStart,
      doctorIdOfAppointment:thisResource && thisResource.doctorId,
    }


    dispatch({
      type:'TimeCalendar/appointmentTimeLengthAlter',
      payload:{...params}
    }).then((res)=>{
      if(res && res.code == 200){
        message.success('修改预约时长成功')
      }else {
        let msg = res && res.msg ? res.msg : '修改预约时长失败'
        message.error(msg)
        info.revert();
      }
    })
  }


  /**
   * 开始拖拽
   */
  eventDragStart=(e)=>{
    this.stopScroll()      //关闭滚动
  }
  eventDragStop=()=>{
    this.startScroll(true) //启用滚动
  }
  /**
   * 改变预约时长
   */
  eventResizeStart=(e)=>{
    this.stopScroll()      //关闭滚动
  }
  eventResizeStop=()=>{
    this.startScroll(true) //启用滚动
  }
  eventResize=(info)=>{
    if(this.tooltip){
      this.tooltip.dispose();
      this.tooltip = null
    }

    const {
      el,
      endDelta,
      event,
      jsEvent,
      prevEvent,
      revert,
      startDelta,
      view
    } = info || {}

    const {
      extendedProps:eventExtendedProps, // event事件中的扩展信息
      start,                            // 事件开始时间
      end,                              // 事件结束时间
      id:EventId,                       // 事件ID
      groupId,                          // 事件组ID
      title,                            // 事件标题
      _def:def,
    } = event || {}

    const {
      resourceIdValue:newResourceId
    } = eventExtendedProps || {}


    let stateMoment = moment(start) // 获取新event的开始时间
    let endMoment = moment(end)     // 获取新event的结束时间

    let eventObj = {
      start: stateMoment,        // 开始时间
      end: endMoment,            // 结束时间
      id: EventId,               // 事件id
      newResourceId,            // event 事件中对应的列资源id
      eventExtendedProps,       // event事件中扩展信息
    }

    let resources = this.getResources(this.props.calendarTableList)
    let thisResource = resources.find((res) => { return res.id == newResourceId })
    info.thisResource = thisResource

    const { appointmentInfoOfTableDto } = eventExtendedProps;
    const { isComeVisit } = appointmentInfoOfTableDto || {}
    if (isComeVisit == 1) {
      this.appointmentTimeLengthAlter(info)
      return
    }
    info.revert();
    this.props.onEventResize && this.props.onEventResize(info)
  }

  /**
   * 关闭滚动
   */
  stopScroll=()=>{
    if (this.fcScroll) {
      this.fcScroll.destroy();
      this.fcScroll = null
    }

    if (this.myScroll) {
      this.myScroll.destroy();
      this.myScroll = null
    }

    if(this.Timetitle) {
      this.Timetitle.destroy()
      this.Timetitle = null
    }
  }

  /**
   * 启用滚动条
   * @params isLastLocation 是否定位到上一次存储的位置 true定位
   */
  startScroll=(isLastLocation=false)=>{
    let that = this;
    this.buildScrollByX()
    this.buildScrollByY()
    this.buildScrollByTitle();
    /*if (!this.pageInfoChange) {
      // 重新构建时 获取上一次记录的滚动Y轴值 定位
      this.myScroll.scrollBy(0, this.state.calendarY);
      // 重写构建时 获取上一次记录的滚动X轴值 定位
      this.fcScroll.scrollBy(this.state.calendarX, 0);
      // 重写构建时 获取上一次记录的滚动x轴 定位title

      this.pageInfoChange = false
    }*/

    let TimeTitleViweWidth = $('#TimeTitleNum').width()
    let Timewidth = this.getTitleTimewidth() - TimeTitleViweWidth - 71;
    let TimeViweWidth = $('#MonthAgendaCalendar').width()
    let TimeMaxWidth = this.getTitleTimewidth();


    /*if(TimeMaxWidth > TimeViweWidth){
      let scrollX = TimeMaxWidth - TimeViweWidth;
      this.fcScroll.scrollTo(0 - (scrollX + 71), 0, 0);
      this.Timetitle.scrollTo(0 - (scrollX), 0, 0);
      this.fcScroll.scrollTo(0,0,0);
      this.Timetitle.scrollTo(0,0,0);
    }*/



    if(!this.fcScroll || !this.Timetitle || !this.myScroll){
      return
    }

    if(isLastLocation){
      // 重新构建时 获取上一次记录的滚动Y轴值 定位
      if(that.y){this.myScroll.scrollBy(0, that.y);}
      // 重新构建时 获取上一次记录的左右营业时间固定位置
      if (that.y <= 0){
        let absY = Math.abs(this.y)
        $('#leftOtherUpContent').css({top:absY})
        $('#rightOtherUpContent').css({top:absY})
      }
      // 重写构建时 获取上一次记录的滚动X轴值 定位
      if(that.x){
        this.fcScroll.scrollBy(that.x, 0);
        // 重写构建时 获取上一次记录的滚动x轴 定位title
        this.Timetitle.scrollBy(that.x, 0);
      }
    }

    // 滚动时关闭悬浮窗口
    this.myScroll.on('scrollStart',function(){
      // that.closeTooltip(true)
    })
    this.fcScroll.on('scrollStart',function(){
      // that.closeTooltip(true)
    })
    this.Timetitle.on('scrollStart',function(){
      // that.closeTooltip(true)
    })
  }

  /**
   * 构建X轴
   */
  buildScrollByX=()=>{
    if(this.fcScroll){
      this.fcScroll.destroy()
      this.fcScroll = null
    }
    // X轴构建
    let scr =  $('.fc-body .fc-time-area .fc-scroller-clip .fc-scroller')[0] //.fc-scroller fc-scroller-canvas
    if(!scr){ return }
    //if (!scr) { return }
    if(scr) { $(scr).scrollLeft(0); }
    this.fcScroll = new IScroll(scr,{
      //scrollbars: false,        // 不显示滚动条
      preventDefault:false,
      scrollX : true,             //滚动方向（水平）
      scrollY : false,            //滚动方向（垂直）
      bounce: false,
      probeType:3,
      momentum:false,
      mouseWheel: false,
      //snap: true,
      //scrollbarClass: 'myScrollbar' ,
      //scrollbars:true,
      onBeforeScrollStart: function ( e ) {
        e.preventDefault();
      },
    });

    if(!this.fcScroll){
      return
    }

    let that = this;
    let TimeTitleViweWidth = $('#TimeTitleNum').width()
    let Timewidth = this.getTitleTimewidth() - TimeTitleViweWidth - 71;


    let TimeViweWidth = $('#MonthAgendaCalendar').width()
    let TimeMaxWidth = this.getTitleTimewidth();



    // 记录最后滚动的位置 用于重新构建时定位
    this.fcScroll.on('scrollEnd', function(e){
      that.x = this.x
      clearTimeout(that.scrollXtimer);

      that.scrollXtimer = setTimeout(()=>{
        //that.goNextIcon && that.goNextIcon()
        /*that.setState({
          calendarX:this.x
        },()=>{*/
        that.onPopperShowById()
        /*})*/
      },300)
    });
  }

  /**
   * 构建Y轴
   */
  buildScrollByY=()=>{
    if(this.myScroll){
      this.myScroll.destroy()
      this.myScroll = null
    }
    // Y轴构建
    let wrapper = document.getElementById('scrollBox');
    if (!wrapper) { return }
    this.myScroll = new IScroll(wrapper,{
      preventDefault:false,
      bounce: true,                // 有回弹效果
      mouseWheel:true,             // 鼠标滚轮
      // scrollbars: "custom",        // 开启滚动条支持
      scrollX : false,             // 滚动方向（水平）
      scrollY : true,              // 滚动方向（垂直）
      probeType:3,                 //1,scroll执行缓慢,惯性的滚动不执行 //2,scroll执行较快,惯性的滚动不执行 //3,scroll执行较快,惯性的滚动执行
      // eventPassthrough:true,
      onBeforeScrollStart: function ( e ) {
        console.log('onBeforeScrollStart :: ',e);
        //e.preventDefault();
      },
    });



    if(!this.myScroll){
      return
    }

    // 上拉上一页  下拉下一页监听触发
    let that = this;
    let TimeTitleViweWidth = $('#TimeTitleNum').width()
    let Timewidth = this.getTitleTimewidth() - TimeTitleViweWidth - 71;

    // 上一页 下一页
    this.myScroll.on('scroll', function(e) {
      console.log('scrollscrollscrollscroll :: ',e);
      if(this.y >= 40) {
        $('#dataUp').show();
        clearTimeout(that.timerLast);
        that.timerLast = setTimeout(()=>{
          that.goLastIcon && that.goLastIcon()
          //$('#leftOtherUpContent').css({top:40}) // 将额外时间栏重置位置
          //$('#rightOtherUpContent').css({top:40}) // 将额外时间栏重置位置
        },800)
      }
      if(this.y < (this.maxScrollY - 40)){
        $('#dataDown').show();
        clearTimeout(that.timerNext);
        that.timerNext = setTimeout(()=>{
          that.goNextIcon && that.goNextIcon()
          //$('#leftOtherUpContent').css({top:0}) // 将额外时间栏重置位置
          //$('#rightOtherUpContent').css({top:0}) // 将额外时间栏重置位置
        },800)
      }

      if (this.y <= 0){
        let absY = Math.abs(this.y)
        $('#leftOtherUpContent').css({top:absY})
        $('#rightOtherUpContent').css({top:absY})
      }
    });

    // 记录最后滚动的位置 用于重新构建时定位
    this.myScroll.on("scrollEnd",function(){
      that.y = this.y
      clearTimeout(that.scrollYtimer);
      that.scrollYtimer = setTimeout(()=>{
        /*that.setState({
          calendarY:this.y
        },()=>{*/
        that.onPopperShowById()
        /*})*/
      },300)
    })
  }

  /**
   * 构建时间栏标题轴
   */
  buildScrollByTitle=()=>{
    // 构建Titile轴
    //let Timetitle = $('#TimeTitleNum')
    if(this.Timetitle) {
      this.Timetitle.destroy()
      this.Timetitle = null
    }


    let TimeTitleWrap = document.getElementById('TimeTitleNum');
    if (!TimeTitleWrap){ return }
    this.Timetitle = new IScroll(TimeTitleWrap,{
      preventDefault:false,
      scrollX : true,             //滚动方向（水平）
      scrollY : false,            //滚动方向（垂直）
      bounce: false,
      probeType:3,
      momentum:false,
      mouseWheel: false,
      //snap: true,
      scrollbarClass: 'TimeTitleWrapbar' ,
      scrollbars:true,
      interactiveScrollbars:true,
      onBeforeScrollStart: function ( e ) {
        e.preventDefault();
      },
    })

    if(!this.Timetitle){
      return
    }

    let that = this;
    let TimeTitleViweWidth = $('#TimeTitleNum').width()
    let Timewidth = this.getTitleTimewidth() - TimeTitleViweWidth - 71 + 35 + 24;



    // 添加时间列跟随滚动效果
    this.Timetitle.on('scroll', function(e){
      if(Timewidth > 0) {
        if (Math.abs(this.x) >= Timewidth) {
          that.Timetitle.scrollTo(-Timewidth, 0)
        }
        that.fcScroll.scrollTo(this.x, 0)
      }
    });
    // 添加时间列跟随滚动效果
    this.fcScroll.on('scroll', function(e){
      //$('#TimeTitleNum').scrollLeft(Math.abs(this.x));
      that.Timetitle.scrollTo(this.x,0)
    });

  }




  refreshCalendar=()=>{
    // 更新列表修改属性
    this.calendar.setOption('events',[...this.state.EventList])
    this.calendar.setOption('resources',this.getResources(this.state.calendarTableList))

    //this.calendar.render()
    this.myScroll.refresh()
    this.fcScroll.refresh()
  }

  upDataEventById=(id,extendedProps)=>{
    //let calendarApi = this.calendarRef.current.getApi()
    let changeEvent = this.calendar && this.calendar.getEventById(id)

    if (changeEvent) {
      //extendedProps&&extendedProps.appointmentInfoOfTableDto.patientInfoDto.isFirstVisit,
      const {appointmentInfoOfTableDto} = extendedProps || {}
      const {appointmentIconDto, patientInfoDto} = appointmentInfoOfTableDto || {}
      const {vipClient} = appointmentIconDto || {}
      const {vipGrade} = patientInfoDto || {}
      if (vipClient != vipGrade) {
        if (extendedProps && appointmentInfoOfTableDto && patientInfoDto) {
          extendedProps.appointmentInfoOfTableDto.patientInfoDto.vipGrade = vipClient
        }
      }
      changeEvent.setProp('extendedProps', {
        ...extendedProps
      })

    }
  }

  /**
   * 处理获取月模式列表中的Event
   */
  getEventList=(EventList)=>{
    /*let event = EventList.map((res)=>{
      res.editable = false
      return res
    })*/





    return [...EventList]
  }

  /**
   * 计算获取补位table资源
   */
  getSurplusNumTableResources=()=>{
    let calendarTableList = [...this.state.contrastEventList];
    let resources = this.getResources(this.state.calendarTableList);
    let tableList = Array.isArray(calendarTableList) ? [...calendarTableList] : []
    let {
      current,
      pageCount,
      pageSize,
      total,
    } = this.props.appointmentPageInfo || {}

    let surplusNum = 0;
    if(pageCount && pageSize) {
      surplusNum = 8 - resources.length > 0 ? 8 - resources.length : 0;
    }
  }

  /**
   * 计算获取补位list Event
   */
  getSurplusNumEventList=()=>{

  }

  /**
   * 构建Calendar组件
   * @params isScrollerIndex // 是否不定位到上一次滚动位置 默认定位
   */
  buildCalendar=(isScrollerIndex = true)=>{
    let calendarEl = document.getElementById('MonthAgendaCalendar');
    if (this.calendar) {
      this.calendar.destroy()
      this.calendar = null;

      $('#scrollContent').unbind('scroll')
      $('.fc-scroller').unbind('scroll')
      $('#TimeTitleNum').unbind('scroll')
      $('#leftOtherUpContent').css({top:0}) // 将额外时间栏重置位置
      $('#rightOtherUpContent').css({top:0}) // 将额外时间栏重置位置
    }

    /**
     defaultDate: "2019-08-15"
     eventDurationEditable: true
     eventResizableFromStart: true
     maxTime: "22:00:00"
     minTime: "07:00:00"
     slotDuration: "00:15:00"
     slotLabelInterval: undefined
     */

    let Option = {
      ...this.props.Option,
      minTime:'00:00:00',
      maxTime:'23:59:59',
    }
    console.log('OptionOption123123  :: ',this.props.Option.minTime,this.props.Option.maxTime);

    // fullCalendar构建
    this.calendar = new Calendar(calendarEl, {
      ...Option,
      slotDuration:'00:15:00',
      events:this.getEventList(this.state.EventList),
      resources: this.getResources(this.state.calendarTableList),
      plugins: [ resourceTimelinePlugin, interactionPlugin ],
      //aspectRatio: 0.5,
      //slotWidth:30,
      slotWidth:slotWidth,
      height:"auto",
      defaultView: 'resourceTimelineDay',
      resourceAreaWidth: '0%',
      schedulerLicenseKey: 'GPL-My-Project-Is-Open-Source',
      header:false,
      resourceColumns: [],
      slotLabelFormat:{
        hour: 'numeric',
        minute: '2-digit',
        hour12: false,
        omitZeroMinute: false,
        meridiem: false
      },
      eventTimeFormat:{
        hour: 'numeric',
        minute: '2-digit',
        meridiem: false,
        hour12: false,
      },
      editable: true,
      slotEventOverlap:true,
      aspectRatio:1.6,
      eventRender:this.eventRender,
      eventClick:this.eventClick,
      dateClick:this.dateClick,
      eventDragStart:this.eventDragStart,
      eventDragStop:this.eventDragStop,      // 拖拽结束
      eventResizeStart:this.eventResizeStart,
      eventResizeStop:this.eventResizeStop,
      eventResize:this.eventResize,
      eventDrop:this.eventDrop,
      eventOverlap:this.eventOverlap,
      eventAllow:this.props.eventAllow ? this.props.eventAllow : this.eventAllow,
      /*resourceRender:function({el, resource,view}){
        el.style.backgroundColor = 'blue';

      },*/
      datesRender:function({el,view}){
        $(el).find('.fc-body').find('.fc-time-area').find('.fc-content').find('td.fc-widget-content').each((idx,res)=>{
          $(res).find('div').addClass('resColl');

          // let height = $(res).find('div').css('height');
          let height = $(res).parents('tr').height()

          if (height > 0) {
            //let heightPx = parseInt(height.replace('px',''))

            $('.resourceListName').eq(idx).css({
              height:`${(height)}px`,
              'box-sizing': 'border-box'
            })
          }
        })
      },
      dayRender:function({date,el,view}){
        let datemm = moment(date, 'HH:mm:ss').format('mm')
        if (datemm == '00') {
          $(el).css({
            'border-left-color':'#DBDBDB',
            'border-left-width':'1px',
            'border-left-style':'solid',
            'border-right-style':'dotted',
          })
        }else {
          $(el).css({
            borderLeftColor:'#DBDBDB',
            borderLeftWidth:'1px',
            borderLeftStyle:'dotted',
          })
        }
      }
    });
    this.calendar.render();
    this.startScroll(isScrollerIndex);
  }





  /**
   * 点击下一页
   */
  goNextIcon=()=>{
    let { current,pageSize,total,pageCount} = this.props.appointmentPageInfo;
    current = current || 1
    pageSize = pageSize || 0
    total = total || 0
    pageCount = pageCount || 0
    let nextPage = current >= pageCount ? pageCount : ++current
    if (pageCount >= current) {
      this.props.goNextIcon(nextPage);
    }
    $('#dataUp').hide();
    $('#dataDown').hide();
    this.y = 1
    this.myScroll && this.myScroll.refresh()
  }

  /**
   * 点击上一页
   */
  goLastIcon=()=>{
    let { current,pageSize,total,pageCount} = this.props.appointmentPageInfo;
    current = current || 1
    pageSize = pageSize || 0
    total = total || 0
    pageCount = pageCount || 0
    let lastPage = current <= 1 ? 1 : --current
    this.props.goLastIcon(lastPage);

    $('#dataUp').hide();
    $('#dataDown').hide();
    this.y = 1
    this.myScroll && this.myScroll.refresh()
  }

  setScrollTopAnimate(scrollTop){
    $('#scrollContent').animate({
      scrollTop:scrollTop
    })
  }

  // 绑定纵向下拉加载更多数据滚动
  fcScrollerBind=()=>{
    let scrollBoundary = false;
    $('#scrollContent').unbind('scroll').scroll(()=>{
      let pageEnd = $('#calendarWarp').height() + 100; // 页面的高度 加100上拉更多
      let scrollBottom = $(event.target).scrollTop() + $('#scrollContent').height();
      let scrollTop = $(event.target).scrollTop();
      if (scrollTop < 100) {
        $(event.target).scrollTop(100 - 20)
        scrollBoundary = true
      }
      if (scrollBottom > pageEnd + 4) {
        let scrollEndIndexTop = pageEnd - $('#scrollContent').height() + 4
        $(event.target).scrollTop(scrollEndIndexTop)
        scrollBoundary = true
      }
    })

    /*$('#scrollContent').unbind('mousewheel').bind('mousewheel',(event)=>{
      const { detail, target } = event

      //$(event.target).scrollTop()
    })*/
  }


  shouldWillReceive=(nextProps,nextState)=>{
    let stateDlend = false
    // 判断如果预约list相同无需更新
    if (Array.isArray(nextProps.EventList)) {
      let nextPropsList = nextProps.EventList
      let stateEventList = this.props.EventList;

      let map1 = Immutable.fromJS(nextPropsList);
      let map2 = Immutable.fromJS(stateEventList);

      let appointmentPageInfoImmutable = nextProps.appointmentPageInfo;
      let appointmentPageInfoStateImmutable = this.props.appointmentPageInfo;

      stateDlend = !Immutable.is(map1, map2);
      let pageInfoChange = !Immutable.is(appointmentPageInfoImmutable, appointmentPageInfoStateImmutable);
      stateDlend = pageInfoChange
      this.pageInfoChange = false
      if(appointmentPageInfoImmutable && appointmentPageInfoStateImmutable){
        if (appointmentPageInfoImmutable.current != appointmentPageInfoStateImmutable.current) {
          this.pageInfoChange = true
        }
      }
    }

    // 判断如果头部列表相同无需更新
    // 判断如果切换了事件颗粒度则需要更新
    let nextslotLabel = nextProps.Option.slotLabelInterval
    let currentSlotLabel = this.state.commonOption.slotLabelInterval

    // stateDlend = this.state.clickEvent != nextProps.clickEvent
    // if (this.state.clickEvent != nextProps.clickEvent){stateDlend = true}
    if(nextslotLabel != currentSlotLabel){ stateDlend = true }

    /*if (nextProps.AgendaHeadType != this.state.AgendaHeadType){
      stateDlend = true
    }*/


    return stateDlend
  }

  componentWillReceiveProps(nextProps,nextState){
    // 判断是否需要刷新
    let stateDlend = this.shouldWillReceive(nextProps,nextState)
    if(stateDlend) {
      this.refreshBespeakComponent(nextProps)
    }
    /*if(stateDlend) {
      this.setState({
        commonOption: {
          ...this.state.commonOption,
          ...nextProps.Option,
        },
        calendarTableList: [...nextProps.calendarTableList],
        option: nextProps.Option,
        EventList: [...nextProps.EventList],
        AgendaHeadType: nextProps.AgendaHeadType,
        appointmentPageInfo: nextProps.appointmentPageInfo,
      }, () => {
        this.refreshBespeakComponent();
      })
    }*/
  }

  //组件销毁回调
  componentWillUnmount(){
    const { dispatch } = this.props
    if (this.tooltip) {
      this.tooltip.hide()
      this.tooltip.dispose();
      this.tooltip = null
    }
    dispatch({
      type:'TimeCalendar/save',
      payload:{
        appointmentInfoEventDtoList:[],        // ①预约列表展示 ,预约事件集合
        calendarTableDtoList:[],               // ①预约列表展示  , table列表椅位集合
        appointmentPageInfo:{},                //  预约列表分页信息
      }
    })
    $('body').unbind()
    $('#MonthAgendaCalendar').unbind();
    window.removeEventListener('resize', this.onWindowResize);
    this.setState = (state,callback)=>{
      return;
    };
  }

}
