import {Card, Image, Button, Modal, List, Spin} from 'antd';
import React, {Component} from 'react';
import {GridContent} from '@ant-design/pro-layout';
import styles from './style.less';//引入样式
//引入图标
import img1 from '@/assets/<EMAIL>'
import stdWordImg from '@/assets/download/stdWord.jpg'
import stdMrcImg from '@/assets/download/stdMrc.png'
import stdInputTempImg from '@/assets/download/stdInputTemp.png'
import ICD9Img from '@/assets/download/ICD9.png'
import ICD10Img from '@/assets/download/ICD10.png'
import ICD11Img from '@/assets/download/ICD11.png'
import {connect} from "dva";
//下载内容
const diag = [
  {
    contentType: 4,
    title: "ICD-9",
    desc: "近年来，国家卫健委大力加强医疗机构病案管理，先后明确要求医疗机构在病案书写中统一使用ICD-9、ICD-10。推广使用世界卫生组织最新修订公布的ICD-9中文版，对于提高医疗服务标准化水平和医疗管理效率，促进诊疗信息有效互联互通具有积极意义。",
    descInfo: "疾病分类与代码、手术操作分类与代码、病案首页、医学名词术语等是推进医疗服务规范化、标准化管理的重要基础。近年来，国家卫健委大力加强医疗机构病案管理，先后明确要求医疗机构在病案书写中统一使用ICD-9、ICD-10。推广使用世界卫生组织修订的ICD-9中文版，对于提高医疗服务标准化水平和医疗管理效率，促进诊疗信息有效互联互通具有积极意义。",
    stdDiagImg:ICD9Img
  },
  {
    contentType: 5,
    title: "ICD-10",
    desc: "ICD-10即疾病和有关健康问题的国际统计分类(第10次修订本)，或国际疾病与相关健康问题统计分类第十版，是世界卫生组织（WHO）依据疾病的某些特征，按照规则将疾病分门别类，并用编码的方法来表示的系统。",
    descInfo: "国际疾病分类（International Classification of Diseases ,ICD），是WHO制定的国际统一的疾病分类方法，它根据疾病的病因、病理、临床表现和解剖位置等特性，将疾病分门别类，使其成为一个有序的组合，并用编码的方法来表示的系统。全世界通用的是第10次修订本《疾病和有关健康问题的国际统计分类》，仍保留了ICD的简称，并被统称为ICD-10。\n" +
      "\n" +
      "疾病分类与代码、手术操作分类与代码、病案首页、医学名词术语等是推进医疗服务规范化、标准化管理的重要基础。近年来，国家卫健委大力加强医疗机构病案管理，先后明确要求医疗机构在病案书写中统一使用ICD-9、ICD-10。推广使用世界卫生组织修订的ICD-10中文版，对于提高医疗服务标准化水平和医疗管理效率，促进诊疗信息有效互联互通具有积极意义。",
    stdDiagImg:ICD10Img
  },
  {
    contentType: 6,
    title: "ICD-11",
    desc: "近年来，国家卫健委大力加强医疗机构病案管理，先后明确要求医疗机构在病案书写中统一使用ICD-9、ICD-10。推广使用世界卫生组织最新修订公布的ICD-11中文版，对于提高医疗服务标准化水平和医疗管理效率，促进诊疗信息有效互联互通具有积极意义。",
    descInfo: "为落实《国务院办公厅关于促进“互联网医疗健康’发展的意见》（国办发〔2018〕26号），健全统一规范的医疗数据标准体系，进一步规范医疗机构疾病分类管理，卫健委组织世界卫生组织国际分类家族中国合作中心、中华医学会及有关医疗机构专家对世界卫生组织公布的《国际疾病分类第十一次修订本（ICD-11 ）》进行了编译，形成了《国际疾病分类第十一次修订本（ICD-11）中文版》（以下简称ICD-11中文版）并提出以下要求：（1）充分认识统一疾病分类与代码的重要意义。疾病分类与代码、手术操作分类与代码、病案首页、医学名词术语等是推进医疗服务规范化、标准化管理的重要基础。（2）积极推进ICD-11中文版全面使用。各级各类医疗机构要认真组织做好培训，结合新版疾病分类与代码特点，修订完善病案首页填写等相关管理制度，更新电子病历系统，做好ICD-11中文版和原有疾病分类与代码之间的衔接（3）加大ICD-11中文版应用管理和监督指导力度。地方各级卫生健康行政部门要加大宣传贯彻和监督指导力度，指导辖区内医疗机构做好相关工作\n" +
      "\n" +
      "疾病分类与代码、手术操作分类与代码、病案首页、医学名词术语等是推进医疗服务规范化、标准化管理的重要基础。近年来，国家卫健委大力加强医疗机构病案管理，先后明确要求医疗机构在病案书写中统一使用ICD-9、ICD-10。推广使用世界卫生组织修订的ICD-11中文版，对于提高医疗服务标准化水平和医疗管理效率，促进诊疗信息有效互联互通具有积极意义。",
    stdDiagImg:ICD11Img
  }
];
//内容下载中心页
class DownLoad extends Component {
  constructor(props) {
    super(props);
    this.state = {
      MsgStatus: false,//弹窗显示隐藏状态
      synStatus: null,//获取内容同步状态
      title: "",//获取系统诊断标题
      diagDescInfo: "",
      stdDiagImg:""//获取系统诊断图片
    };
  }
  //初始化
  componentDidMount() {
    const {history} = this.props;
    this.getSynStatus(); //获取内容同步状态
  }

  /**获取内容同步状态**/
  getSynStatus = () => {
    const {dispatch} = this.props;
    this.setState({loading: true})
    let params = {
      tenantId: localStorage.getItem("tenantId")
    }
    if (dispatch) {
      dispatch({
        type: 'synContent/findSynStatusService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              loading: false,
              synStatus: res.content,
            });
          }
        },
      });
    }
  };
  //下载文件
  synSystemEmrTmpt = ()=>{
    const {dispatch} = this.props;
    this.setState({emrTmptLoading:true})
    let params = {
      tenantId: localStorage.getItem("tenantId"),
      userId: localStorage.getItem("userId"),
      userName: localStorage.getItem("userName"),
    }
    if (dispatch) {
      dispatch({
        type: 'synContent/synSystemEmrTmptService',
        payload: params,
        callback: (res) => {
          this.setState({
            emrTmptLoading: false,
          });
          this.getSynStatus();
        },
      });
    }
  }
  //同步同意书
  synSystemMrcTmpt = ()=>{
    const {dispatch} = this.props;
    this.setState({mrcTmptLoading:true})
    let params = {
      tenantId: localStorage.getItem("tenantId"),
      userId: localStorage.getItem("userId"),
      userName: localStorage.getItem("userName"),
    }
    if (dispatch) {
      dispatch({
        type: 'synContent/synSystemMrcTmptService',
        payload: params,
        callback: (res) => {
          this.setState({
            mrcTmptLoading: false,
          });
          this.getSynStatus();
        },
      });
    }
  }

  //标准诊断查看详情
  synSystemStdDiag = (type) => {
    const {dispatch} = this.props;
    let params = {
      tenantId: localStorage.getItem("tenantId"),
      userId: localStorage.getItem("userId"),
      userName: localStorage.getItem("userName"),
      contentType: type
    }
    let loadingName = "diagLoading" + type;
    this.setState({[loadingName]: true})
    if (dispatch) {
      dispatch({
        type: 'synContent/synSystemStdDiagService',
        payload: params,
        callback: (res) => {
          this.setState({
            [loadingName]: false,
          });
          this.getSynStatus();
        },
      });
    }
  };
  //词条查看详情
  synSystemStdWord = () => {
    const {dispatch} = this.props;

    let params = {
      tenantId: localStorage.getItem("tenantId"),
      userId: localStorage.getItem("userId"),
      userName: localStorage.getItem("userName"),
    }
    this.setState({wordLoading: true})
    if (dispatch) {
      dispatch({
        type: 'synContent/synSystemStdWordsService',
        payload: params,
        callback: (res) => {
          this.setState({
            wordLoading: false,
          });
          this.getSynStatus();
        },
      });
    }
  };
  //获取系统诊断
  findSystemDiags = (item) => {
    // const {dispatch} = this.props;
    // let params = {
    //   contentType: item.contentType
    // }
    // if (dispatch) {
    //   dispatch({
    //     type: 'synContent/findSystemDiagsService',
    //     payload: params,
    //     callback: (res) => {
    //       if (res.code === 200) {
            this.setState({
              // sysDiag: res.rows,
              title: item.title,
              descInfo: item.descInfo,
              diagStatus: true,
              stdDiagImg:item.stdDiagImg
            })
      //     }
      //
      //   },
      // });
    // }
  };

  //查看系统词条
  findSysStdWord = () => {
    this.setState({
      worldStatus: true,
      title: "病历词条-2022版",
      descInfo: '丰富的病历书写词条，能有效简化病历书写实践，提高病历书写效率与质量',
    });
  };
  //查看系统词条
  findSystemMrcs = () => {
    this.setState({
      mrcStatus: true,
      title: "知情同意书模板-2022版",
      descInfo: "包含9大分类45份知情同意书模板，模板提供中/英双语。",
    });
  };
  //查看病历模板
  findSystemInputTmpts = () => {
    // const {dispatch} = this.props;
    // if (dispatch) {
    //   dispatch({
    //     type: 'synContent/findSysStdMrcsService',
    //     //payload: ,
    //     callback: (res) => {
          this.setState({
            inputTempStatus: true,
            title: "病历书写模板-2022版",
            descInfo: "9大分类300多套病历书写模板，应用价值较高,能有效简化病历书写实践.通过科学高效化的电子病历模板有助于提升病历应用价值,提高工作质量，改善病历质量。",
            // mrcInfo: res.rows
          });
    //     },
    //   });
    // }
  };

  //查看详情弹窗关闭
  CancelDiagMsg = () => {
    this.setState({
      diagStatus: false,
    })
  }
  //查看详情弹窗关闭
  CancelWoldMsg = () => {
    this.setState({
      worldStatus: false,
    })
  }
  //查看详情弹窗关闭
  CancelMrcMsg = () => {
    this.setState({
      mrcStatus: false,
    })
  }
  //查看详情弹窗关闭
  CancelInputTempMsg = () => {
    this.setState({
      inputTempStatus: false,
    })
  }

  render() {
    const {
      inputTempStatus,
      mrcStatus,
      diagStatus,
      worldStatus,
      loading,
      synStatus,
      descInfo,
      stdDiagImg,
      title
    } = this.state;
    return (
      <GridContent>
        <Spin spinning={loading}>
          <div className={styles.downloadContent}>
            <div className={styles.title}>内容下载中心</div>
            <div className={styles.content}>

              {diag.map((item,index) =>
                <Card
                  style={{width:342,height: 205}}
                  key={index}
                  className={styles.cardContent}
                >
                  <div className={styles.cardContentTop}>
                    <img
                      className={styles.imgStyle}
                      src={img1}
                    />
                    <div className={styles.contentStyle}>
                      <div className={styles.title}>{item.title}</div>
                      <div className={styles.TopcardContent}>{item.desc}</div>
                      <div style={{display: 'flex'}}>
                        <div className={styles.free}>免费</div>
                        <div
                          onClick={() => this.findSystemDiags(item)}
                          className={`${styles.check}`}
                          style={{marginLeft: 16,}}>查看详情
                        </div>
                      </div>
                    </div>
                  </div>
                  {!synStatus ?
                    <Button disabled type="primary" className={styles.downBtn}>立即下载</Button> :
                    !synStatus[item.contentType] ? <Button type="primary" className={styles.downBtn}
                                                           loading={this.state["diagLoading" + item.contentType]}
                                                           onClick={() => this.synSystemStdDiag(item.contentType)}>立即下载</Button> :
                      synStatus[item.contentType] === 0 ? <Button className={styles.loading}>正在下载</Button> :
                        synStatus[item.contentType] === -1 ?
                          <Button className={styles.errBtn} loading={this.state["diagLoading" + item.contentType]}
                                  onClick={() => this.synSystemStdDiag(item.contentType)}>下载错误，点击重新下载</Button> :
                          <Button disabled className={styles.already}>已拥有</Button>
                  }
                </Card>
              )}
              <Card
                style={{width:342,height: 205}}
                className={styles.cardContent}
              >
                <div className={styles.cardContentTop}>
                  <img
                    className={styles.imgStyle}
                    src={img1}
                  />
                  <div className={styles.contentStyle}>
                    <div className={styles.title}>知情同意书模板-2022版</div>
                    <div className={styles.TopcardContent}>包含9大分类45份知情同意书模板，模板提供中/英双语</div>
                    <div style={{display: 'flex'}}>
                      <div className={styles.free}>免费</div>
                      <div
                        onClick={() => this.findSystemMrcs()}
                        className={`${styles.check}`}
                        style={{marginLeft: 16,}}>查看详情
                      </div>
                    </div>
                  </div>
                </div>
                {!synStatus ?
                  <Button disabled type="primary" className={styles.downBtn}>立即下载</Button> :
                  !synStatus[3] ? <Button type="primary" className={styles.downBtn} loading={this.state.mrcTmptLoading}
                                          onClick={() => this.synSystemMrcTmpt()}>立即下载</Button> :
                    synStatus[3] === 0 ? <Button className={styles.loading}>正在下载</Button> :
                      synStatus[3] === -1 ? <Button className={styles.errBtn} loading={this.state.mrcTmptLoading}
                                                    onClick={() => this.synSystemMrcTmpt()}>下载错误，点击重新下载</Button> :
                        <Button disabled className={styles.already}>已拥有</Button>
                }
              </Card>
              <Card
                style={{width:342,height: 205}}
                className={styles.cardContent}
              >
                <div className={styles.cardContentTop}>
                  <img
                    className={styles.imgStyle}
                    src={img1}
                  />
                  <div className={styles.contentStyle}>
                    <div className={styles.title}>病历书写模板-2022版</div>
                    <div
                      className={styles.TopcardContent}>9大分类300多套病历书写模板，应用价值较高,能有效简化病历书写实践.通过科学高效化的电子病历模板有助于提升病历应用价值,提高工作质量，改善病历质量.
                    </div>
                    <div style={{display: 'flex'}}>
                      <div className={styles.free}>免费</div>
                      <div
                        onClick={() => this.findSystemInputTmpts()}
                        className={`${styles.check}`}
                        style={{marginLeft: 16,}}>查看详情
                      </div>
                    </div>
                  </div>
                </div>
                {!synStatus ?
                  <Button disabled type="primary" className={styles.downBtn}>立即下载</Button> :
                  !synStatus[2] ? <Button type="primary" className={styles.downBtn} loading={this.state.emrTmptLoading}
                                          onClick={() => this.synSystemEmrTmpt()}>立即下载</Button> :
                    synStatus[2] === 0 ? <Button className={styles.loading}>正在下载</Button> :
                      synStatus[2] === -1 ? <Button className={styles.errBtn} loading={this.state.emrTmptLoading}
                                                    onClick={() => this.synSystemEmrTmpt()}>下载错误，点击重新下载</Button> :
                        <Button disabled className={styles.already}>已拥有</Button>
                }
              </Card>
              <Card
                style={{width:342,height: 205}}
                className={styles.cardContent}
              >
                <div className={styles.cardContentTop}>
                  <img
                    className={styles.imgStyle}
                    src={img1}
                  />
                  <div className={styles.contentStyle}>
                    <div className={styles.title}>病历词条-2022版</div>
                    <div className={styles.TopcardContent}>丰富的病历书写词条，能有效简化病历书写实践，提高病历书写效率与质量</div>
                    <div style={{display: 'flex'}}>
                      <div className={styles.free}>免费</div>
                      <div
                        onClick={() => this.findSysStdWord()}
                        className={`${styles.check}`}
                        style={{marginLeft: 16}}>查看详情
                      </div>
                    </div>
                  </div>
                </div>
                {!synStatus ?
                  <Button disabled type="primary" className={styles.downBtn}>立即下载</Button> :
                  !synStatus[1] ? <Button type="primary" className={styles.downBtn} loading={this.state.wordLoading}
                                          onClick={() => this.synSystemStdWord()}>立即下载</Button> :
                    synStatus[1] === 0 ? <Button className={styles.loading}>正在下载</Button> :
                      synStatus[1] === -1 ? <Button className={styles.errBtn} loading={this.state.wordLoading}
                                                    onClick={() => this.synSystemStdWord()}>下载错误，点击重新下载</Button> :
                        <Button disabled className={styles.already}>已拥有</Button>
                }
              </Card>
            </div>
          </div>
        </Spin>

        <Modal
          title="详情介绍"
          visible={diagStatus}
          destroyOnClose={true}
          onCancel={this.CancelDiagMsg}
          footer={[]}
          width={1000}
          maskClosable={false}
        >
          <div className={styles.cardMsgContent}>
            <img
              className={styles.imgStyle}
              src={img1}
            />
            <div className={styles.contentStyle}>
              <div className={styles.title}>{title}</div>
              <div className={styles.TopcardContent}>
                {descInfo}
              </div>
              <div style={{display: 'flex'}}>
                <div className={styles.free}>免费</div>
              </div>
            </div>
          </div>
          <div className={styles.Listborder}>
            <img className={styles.contentImg} src={stdDiagImg}/>
          </div>
        </Modal>
        <Modal
          title="详情介绍"
          visible={worldStatus}
          destroyOnClose={true}
          onCancel={this.CancelWoldMsg}
          footer={[]}
          width={1000}
          maskClosable={false}
        >
          <div className={styles.cardMsgContent}>
            <img
              className={styles.imgStyle}
              src={img1}
            />
            <div className={styles.contentStyle}>
              <div className={styles.title}>{title}</div>
              <div className={styles.TopcardContent}>
                {descInfo}
              </div>
              <div style={{display: 'flex'}}>
                <div className={styles.free}>免费</div>
              </div>
            </div>
          </div>
          <div className={styles.Listborder}>
            <img className={styles.contentImg} src={stdWordImg}/>
          </div>
        </Modal>

        <Modal
          title="详情介绍"
          visible={mrcStatus}
          destroyOnClose={true}
          onCancel={this.CancelMrcMsg}
          footer={[]}
          width={1000}
          maskClosable={false}
        >
          <div className={styles.cardMsgContent}>
            <img
              className={styles.imgStyle}
              src={img1}
            />
            <div className={styles.contentStyle}>
              <div className={styles.title}>{title}</div>
              <div className={styles.TopcardContent}>
                {descInfo}
              </div>
              <div style={{display: 'flex'}}>
                <div className={styles.free}>免费</div>
              </div>
            </div>
          </div>
          <div className={styles.Listborder}>
            <img className={styles.contentImg} src={stdMrcImg}/>
          </div>
        </Modal>
        <Modal
          title="详情介绍"
          visible={inputTempStatus}
          destroyOnClose={true}
          onCancel={this.CancelInputTempMsg}
          footer={[]}
          width={1000}
          maskClosable={false}
        >
          <div className={styles.cardMsgContent}>
            <img
              className={styles.imgStyle}
              src={img1}
            />
            <div className={styles.contentStyle}>
              <div className={styles.title}>{title}</div>
              <div className={styles.TopcardContent}>
                {descInfo}
              </div>
              <div style={{display: 'flex'}}>
                <div className={styles.free}>免费</div>
              </div>
            </div>
          </div>
          <div className={styles.Listborder}>
            <img className={styles.contentImg} src={stdInputTempImg}/>
          </div>
        </Modal>

      </GridContent>
    );
  }
}

export default connect(
  ({DownLoad,}) => ({
    DownLoad, //病历首页- 患者信息
  }),
)(DownLoad);
