import React,{ Component } from "react";
import classNames from "classnames";
import styles from "./index.less";
import Ellipsis from "@/components/Ellipsis";
import maximizeIcon from "@/assets/AppointmentRules/maximizeIcon.png";
import PropTypes from "prop-types";

export default class MinimizeCard extends Component {
  static propsType = {
    minimizeVisitList: PropTypes.array,           // 最小化数组
    rightMaximizeClick: PropTypes.func,           // 点击卡片打开方法
  }

  static defaultProps = {
    minimizeVisitList: [],
    rightMaximizeClick: () => {}
  }
  // 打开最小化卡片
  rightMaximizeClick = (item,index) => {
    const { rightMaximizeClick } = this.props
    rightMaximizeClick && rightMaximizeClick(item,index)
  }
  render() {
    // 最小化卡片数据
    const { minimizeVisitList } = this.props
    return (
      <div className={styles.rightPositionBox}>
        {
          minimizeVisitList && minimizeVisitList.map((item,index) => {
            return (
              <div
                key={index}
                className={classNames(styles.itemBox,{
                  [styles.visit]: !item.typeStatus,
                  [styles.settlement]: item.typeStatus == 1, // 结算卡片
                  [styles.settlementInfo]: item.typeStatus == 2, // 结算详情卡片
                  [styles.refund]: item.typeStatus == 3, // 退款卡片
                  [styles.payDebt]: item.typeStatus == 4, // 欠款补缴卡片
                  [styles.doctorWorkbench]: item.typeStatus == 5, // 接诊卡片
                })}
                onClick={this.rightMaximizeClick.bind(this,item,index)}
              >
                            <span style={{maxWidth: 77, display: 'inline-block', color: '#ffffff'}}>
                              <Ellipsis lines={1}>{item.name}</Ellipsis>
                            </span>
                <img src={maximizeIcon} alt='' />
              </div>
            )
          })
        }
      </div>
    );
  }
}
