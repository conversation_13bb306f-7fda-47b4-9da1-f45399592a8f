import {Col, Row, Input, Form, Modal, Popconfirm, message, Spin} from 'antd';
import {ExclamationCircleOutlined} from '@ant-design/icons';
import React, {Component} from 'react';
import {GridContent} from '@ant-design/pro-layout';
//引入样式
import styles from './style.less';
import commonStyle from "@/pages/common.less";
//引入图标
import Unfold from "@/assets/<EMAIL>";
import Fold from "@/assets/<EMAIL>";
import Folder from "@/assets/<EMAIL>";
import FileSelected from "@/assets/<EMAIL>";
import FileUnselected from "@/assets/<EMAIL>";
import FolderSelected from "@/assets/<EMAIL>";
import NewFile from "@/assets/<EMAIL>";
import Delete from "@/assets/<EMAIL>";
import Edit from "@/assets/<EMAIL>";
import NewFolder from "@/assets/<EMAIL>";
import noData from "@/assets/<EMAIL>";

import {connect} from 'dva';
//公共验证
import {StringUtils} from "@/utils/StringUtils";

const {Search} = Input;
const {TextArea} = Input;
const {confirm} = Modal;
//表单布局
const layout = {
  labelCol: {
    span: 2,
  },
  wrapperCol: {
    span: 22,
  },
};

class MedicalModel extends Component {
  constructor(props) {
    super(props);
    this.state = {
      firstStatus: false,
      show1: null,//点击一级文件夹状态
      show2: null,//点击二级文件夹状态
      show3: null,//点击三级文件夹状态
      editorStatus: false,//编辑显示状态
      addFileBorderStatus: false,//点击...弹窗
      title: "",
      addFileBorderStatus2: false,//新建文件夹目录菜单状态
      hover1: false,//一级文件夹hover状态
      setVisible: false,//删除气泡框
      addFileBorderStatus3: false,
      hover2: false,//二级文件夹hover状态
      newModel: false,//新建模板弹窗状态
      editorName: false,//修改模板名称弹窗
      addFileBorderStatus4: false,//编辑名称菜单状态
      clientHeight: document.documentElement.clientHeight, // 屏幕高度
      EmrMsgs: {},//病历详情数据
      editorLoading: false,//编辑loading
      modelListLoading: false,//文件列表loading
      EmrTmp: [],//病历模板分组列表
      addFolder: {
        className: "",//分类夹名称
        parentCode: "",//父级标识
      },//新增二级文件夹参数
      addFile: {
        typeCodes: "",//classCode
        className: "",//分类夹名称
        parentCode: "",//父级标识
      },//新增三级文件夹参数
      fileLists: [],//模板列表
      oneClassCode:"",//单独存的一级文件夹的closscode
      twoClassCode: "",//单独存的二级文件夹的closscode
      editorNames: false,
      fetchmodel: [],//循环出的已有的自建文件夹
      searchEmrTmp: [],//搜索后的数据
      searchStatus: false,//搜索input框是否有数据状态
      reminderId: "",//模板id
      saveBtnLoading:false,//新建文件夹按钮loading状态
      saveModelLoading:false,//新建模板按钮loading状态
      editorModelLoading:false,//编辑模板按钮loading状态
      editornameLoading:false,//编辑名称按钮loading状态
      saveStatus:false,//保存文字状态
      searchValue:""//搜索名称
    };
    this.resize = this.resize.bind(this);//监听屏幕高度
  }
  //初始化
  componentDidMount() {
    this.getModelList()//获取左侧目录列表
    // this.getEmrMsg()//病历详情数据
    // 点击其他地方隐藏输入框
    document.addEventListener('click', (e) => {
      if (this.state.addFileBorderStatus2) {
        this.setState({
          addFileBorderStatus2: false,
        })
      }
      if (this.state.addFileBorderStatus3) {
        this.setState({
          addFileBorderStatus3: false
        })
      }
      if (this.state.addFileBorderStatus4) {
        this.setState({
          addFileBorderStatus4: false
        })
      }
    })
    window.addEventListener("resize", this.resize); //监听屏幕高度
  }
  //在组件卸载及销毁之前直接调用
  componentWillUnmount() {
    window.removeEventListener("resize", this.resize); //监听屏幕高度
  }
  //监听屏幕高度
  resize() {
    this.setState({clientHeight: document.documentElement.clientHeight}); //监听
  }

  //获取左侧目录列表
  getModelList = () => {
    const {dispatch} = this.props
    this.setState({
      modelListLoading: true
    })
    let params = {
      tenantId: localStorage.getItem('tenantId')//平台标识
    };
    if (dispatch) {
      dispatch({
        type: 'medicalModel/findEmrTmptService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              EmrTmp: res.rows,
              modelListLoading: false,
            })
            if (res.rows[0].children && this.state.show1 == null) {
              let classCode = res.rows[0].children[0].classCode;
              this.ModelLists(classCode)
              this.setState({
                show1: res.rows[0].classCode,
                show2: res.rows[0].children ? res.rows[0].children[0].classCode : null,
                twoClassCode: res.rows[0].children ? res.rows[0].children[0].classCode : ""
              })
            }
          } else {
            this.setState({
              modelListLoading: false
            })
          }
        }
      });
    }
  }
  // 搜索菜单
  onSearch = (value) => {
    if (StringUtils.isNotBlank(value)) {
      this.setState({
        searchStatus: true
      })
      this.searchList(value)
    } else {
      this.setState({
        searchStatus: false,
        show2: null,
      })
      this.getModelList()
    }
  }
  //有内容的搜索
  searchList = (value) => {
    this.state.searchValue=value;
    const {dispatch} = this.props
    this.setState({
      modelListLoading: true
    })
    let params = {
      tenantId: localStorage.getItem('tenantId'),
      tmptName: value
    };
    if (dispatch) {
      dispatch({
        type: 'medicalModel/ClassSearchService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              modelListLoading: false,
              searchEmrTmp: res.rows,
            })
            let arr = res.rows;
            let arr1 = []
            arr.forEach((key, index) => {
              arr1.push({
                id: key.id,
                classCode: key.code,
                className: key.name,
                children: key.children
              })
            })
          } else {
            this.setState({
              modelListLoading: false
            })
          }
        }
      });
    }
  }
  //获取病历详情
  getEmrMsg = (id) => {
    const {dispatch} = this.props
    let params = {
      id: id,//模板代码
      tenantId: localStorage.getItem('tenantId')//平台标识
    };
    this.setState({
      editorLoading: true
    })
    if (dispatch) {
      dispatch({
        type: 'medicalModel/EmrMessageService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              EmrMsgs: res.content,
              editorLoading: false,
              show3: res.content.id,
              reminderId: ""
            })
          }
        }
      });
    }
  }
  // 点击编辑
  editor = () => {
    this.setState({
      editorStatus: true
    })
  }
  //编辑保存
  saveEmrMsg = () => {
    const {dispatch} = this.props;
    const {EmrMsgs, addFile} = this.state;
    let params = {
      id: EmrMsgs.id,
      tenantId: localStorage.getItem('tenantId'),
      tmptCode: EmrMsgs.tmptCode,
      tmptName: EmrMsgs.tmptName,
      classCode: EmrMsgs.classCode?EmrMsgs.classCode:"0",
      illnessDesc: EmrMsgs.illnessDesc,
      preIllnessDesc: EmrMsgs.preIllnessDesc,
      pastHistDesc: EmrMsgs.pastHistDesc,
      genCondDesc: EmrMsgs.genCondDesc,
      examineDesc: EmrMsgs.examineDesc,
      auxiExamDesc: EmrMsgs.auxiExamDesc,
      diagDesc: EmrMsgs.diagDesc,
      treatPlanDesc: EmrMsgs.treatPlanDesc,//本次治疗
      treatDesc: EmrMsgs.treatDesc,
      adviceDesc: EmrMsgs.adviceDesc,
      updateName: localStorage.getItem('userName'),//更新者
      updateId: localStorage.getItem('userId')//更新者id
    };
    if(!params.tmptName){
      message.warning({
        content: '模板名称不能为空',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    this.setState({
      editorLoading:true
    })
    if (dispatch) {
      dispatch({
        type: 'medicalModel/saveEmrTmptService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '修改成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              editorLoading: false,
              editorStatus: false,
              editornameLoading:false,
              saveStatus:false,
              editorNames: false,
            })
            this.getEmrMsg(this.state.reminderId ? this.state.reminderId : EmrMsgs.id)
            this.ModelLists(this.state.twoClassCode);
            if(this.state.searchValue){
              this.searchList(this.state.searchValue)
            }
          } else {
            message.error({
              content: '修改失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              editorLoading: false,
              editornameLoading:false,
              saveStatus:false
            })
          }
        }
      });
    }
  }
  //删除模板
  deleteModel = () => {
    confirm({
      title: '确定删除此模板?',
      icon: <ExclamationCircleOutlined/>,
      onOk: () => {
        this.DeleteMedical();
      },
      onCancel() {
      },
    });
  }
  //删除病历模板
  DeleteMedical = (id, tmptCode) => {
    const {dispatch} = this.props;
    const {EmrMsgs} = this.state;
    this.setState({
      editorLoading: true,
      deleteBtnLoading:true
    })
    let params = {
      id: id ? id : EmrMsgs.id,
      tenantId: localStorage.getItem("tenantId")
    };
    if (dispatch) {
      dispatch({
        type: 'medicalModel/deleteEmrTmptService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '删除成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              editorLoading: false,
              EmrMsgs: {},
              show3: null,
              deleteBtnLoading:false
            })
            this.ModelLists(this.state.twoClassCode)
            if(this.state.searchValue){
              this.searchList(this.state.searchValue)
            }
          } else {
            message.error({
              content: '删除失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              editorLoading: true,
              deleteBtnLoading:false
            })
          }
        }
      });
    }
  }
  //取消编辑
  CancelEditor = () => {
    const {EmrMsgs} = this.state;
    this.setState({
      editorStatus: false
    })
    this.getEmrMsg(EmrMsgs.id)
  }
  // 第一级鼠标hover菜单事件
  hoverFirst = (hoverFirst, id) => {
    this.setState({
      ['hoverFirst' + id]: hoverFirst
    })
  }
  // 第一级鼠标点击菜单事件
  openFirst = (item, index, id) => {
    this.setState({
      oneClassCode: item.classCode?item.classCode:item.code,
      fileLists: []
    })
    if (this.state.show1 == id) {
      this.setState({
        show1: null,
        show2: null,
        show3: null
      })
    } else {
      this.setState({
        show1: id,
        show2:null,
        show3: null
      })
    }
  }
  // 第二级鼠标hover菜单事件
  hoverTwo = (hoverTwo, id) => {
    this.setState({
      ['hoverTwo' + id]: hoverTwo
    })
  }
  //病历模板列表
  ModelLists = (classCode) => {
    const {dispatch} = this.props;
    let param;
    if(this.state.oneClassCode=="0"){
      param = {
        classCode: classCode,//分类编码
        tenantId: localStorage.getItem('tenantId'),//平台标识
        isSystem:2
      }
    }else{
      param = {
        classCode: classCode,//分类编码
        tenantId: localStorage.getItem('tenantId'),//平台标识
      }
    }

    if (dispatch) {
      dispatch({
        type: 'medicalModel/findEmrTmptNameService',
        payload: param,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              fileLists: res.rows
            })
            if (res.rows.length > 0 && res.rows[0].id && this.state.show3 == null) {
              this.getEmrMsg(res.rows[0].id)
            }
          }
        }
      });
    }
  }
  // 第二级鼠标点击菜单事件
  opentwo = (key, item1, classCode) => {
    this.state.addFile.typeCodes = classCode;
    this.setState({
      twoClassCode: classCode
    })
    if (key !== 'search') {
      this.ModelLists(classCode)
    }
    if (this.state.show2 == classCode) {
      this.setState({
        fileLists: [],
        show2: null
      })
    } else {
      this.setState({
        fileLists: [],
        show2: classCode
      })
    }
  }
  // 第三级鼠标hover菜单事件
  hoverThree = (hoverThree, id) => {
    this.setState({
      ['hoverThree' + id]: hoverThree
    })
  }
  // 第三级鼠标点击菜单事件
  openthree = (item2, index2, id, tmptCode) => {
    if (this.state.editorStatus && this.state.show3 !== id) {
      this.showConfirm(id)
    } else {
      if (this.state.show3 == id) {
        this.setState({
          show3: null
        })
      } else {
        this.setState({
          show3: id
        })
      }
      this.getEmrMsg(id)
    }
  }
  //提示框
  showConfirm = (id) => {
    confirm({
      title: '当前模板未保存，是否保存?',
      icon: <ExclamationCircleOutlined/>,
      onOk: () => {
        this.setState({
          editorStatus: false,
          reminderId: id,
        })
        this.saveEmrMsg();
        if (this.state.show3 == id) {
          this.setState({
            show3: null
          })
        } else {
          this.setState({
            show3: id
          })
        }
      },
      onCancel: () => {
        this.setState({
          editorStatus: false,
        })
        if (this.state.show3 == id) {
          this.setState({
            show3: null
          })
        } else {
          this.setState({
            show3: id
          })
        }
        this.getEmrMsg(id)
      },
    });
  };
  //新建文件
  addFile = (e, id) => {
    e.nativeEvent.stopImmediatePropagation()
    if (this.state.addFileBorderStatus2) {
      this.setState({
        addFileBorderStatus2: false,
        addFileBorderStatus3: false,
        addFileBorderStatus4: false
      })
    } else {
      this.setState({
        hover1: id,
        addFileBorderStatus2: true,
        addFileBorderStatus3: false,
        addFileBorderStatus4: false
      })
    }

  }
  //新建模板
  addModel = (e, id) => {
    e.nativeEvent.stopImmediatePropagation()
    if (this.state.addFileBorderStatus3) {
      this.setState({
        addFileBorderStatus3: false,
        addFileBorderStatus2: false,
        addFileBorderStatus4: false,
      })
    } else {
      this.setState({
        hover2: id,
        addFileBorderStatus3: true,
        addFileBorderStatus2: false,
        addFileBorderStatus4: false,
      })
    }
  }
  //文件操作
  fileModel = (e, id) => {
    e.nativeEvent.stopImmediatePropagation()
    if (this.state.addFileBorderStatus4) {
      this.setState({
        addFileBorderStatus3: false,
        addFileBorderStatus2: false,
        addFileBorderStatus4: false,
      })
    } else {
      this.setState({
        hover3: id,
        addFileBorderStatus3: false,
        addFileBorderStatus2: false,
        addFileBorderStatus4: true,
      })
    }
  }
  //新建文件弹窗
  newFile = (classCode) => {
    this.state.addFolder.parentCode = classCode;
    this.setState({
      newFile: true,
      ['hoverFirst' + classCode]: false
    })
  }
  //新建二级文件夹
  newFileOk = () => {
    const {dispatch} = this.props;
    const {addFolder} = this.state;
    let param;
    if(addFolder.parentCode=="0"){
       param = {
        className: addFolder.className,//分类夹名称
        parentCode:null,//父级标识
        isSystem : 0,
        classRank: 2,//分类等级 1/2/3
        classType: 1,//模板类型-0系统分类；1病历模板；2基础治疗；3知情同意书
        tenantId: localStorage.getItem('tenantId'),//平台标识
        createName: localStorage.getItem('userName'),
        createId: localStorage.getItem('userId'),
      }
    }else{
       param = {
        className: addFolder.className,//分类夹名称
        parentCode: addFolder.parentCode,//父级标识
        classRank: 2,//分类等级 1/2/3
        classType: 1,//模板类型-0系统分类；1病历模板；2基础治疗；3知情同意书
        tenantId: localStorage.getItem('tenantId'),//平台标识
        createName: localStorage.getItem('userName'),
        createId: localStorage.getItem('userId'),
      }
    }
    if (!param.className) {
      message.warning({
        content: '文件夹名称不能为空',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return false;
    }
    this.setState({
      saveBtnLoading:true
    })
    if (dispatch) {
      dispatch({
        type: 'medicalModel/saveContentService',
        payload: param,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              newFile: false,
              saveBtnLoading:false
            })
            this.state.addFolder.className="";
            this.getModelList();
          } else {
            this.state.addFolder.className="";
            this.setState({
              saveBtnLoading:false
            })
            message.error({
              content: res.msg,
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
          }
        }
      });
    }
  }
  //新建二级文件夹取消
  newFileCancel = () => {
    this.setState({
      newFile: false,
      saveBtnLoading:false
    })
  }
  //删除文件
  showPopconfirm = (e) => {
    e.nativeEvent.stopImmediatePropagation()
    this.setState({
      setVisible: true,
    })
  }
  //新建模板
  newModel = (item1) => {
    this.state.addFile.parentCode = item1.classCode?item1.classCode:item1.code;
    this.setState({
      newModel: true,
      ['hoverTwo' + item1.classCode]: false
    })
  }
  //新建三级文件
  newModelOk = (key) => {
    const {dispatch} = this.props;
    const {addFile} = this.state;
    let param;
    if(this.state.oneClassCode=="0"){
      param = {
        tenantId: localStorage.getItem('tenantId'),//平台标识
        tmptName: addFile.className,//模板名称
        classCode: addFile.parentCode,//分类编码
        illnessDesc: "",//主诉
        preIllnessDesc: "",//现病史
        pastHistDesc: "",//既往史
        genCondDesc: "",//全身情况
        examineDesc: "",//检查
        auxiExamDesc: "",//辅助检查
        diagDesc: "",//诊断
        treatPlanDesc: "",//本次治疗
        treatDesc: "",//处置
        adviceDesc: "",//医嘱
        createName: localStorage.getItem('userName'),//创建者
        createId: localStorage.getItem('userId'),//创建者id
        isSystem:2
      }
    }else{
      param = {
        tenantId: localStorage.getItem('tenantId'),//平台标识
        tmptName: addFile.className,//模板名称
        classCode: addFile.parentCode,//分类编码
        illnessDesc: "",//主诉
        preIllnessDesc: "",//现病史
        pastHistDesc: "",//既往史
        genCondDesc: "",//全身情况
        examineDesc: "",//检查
        auxiExamDesc: "",//辅助检查
        diagDesc: "",//诊断
        treatPlanDesc: "",//本次治疗
        treatDesc: "",//处置
        adviceDesc: "",//医嘱
        createName: localStorage.getItem('userName'),//创建者
        createId: localStorage.getItem('userId'),//创建者id
      }
    }
    if (!param.tmptName) {
      message.warning({
        content: '模板名称不能为空',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return false;
    }
    this.setState({
      saveModelLoading:true
    })
    if (dispatch) {
      dispatch({
        type: 'medicalModel/saveEmrTmptService',
        payload: param,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              newModel: false,
              show2: addFile.parentCode,
              saveModelLoading:false
            })
            this.state.addFile.className="";
            this.ModelLists(addFile.parentCode);
            if(this.state.searchValue){
              this.searchList(this.state.searchValue)
            }
          } else {
            this.state.addFile.className="";
            message.error({
              content: res.msg,
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              saveModelLoading:false
            })
          }
        }
      });
    }
  }
  //编辑模板
  EditorModel = () => {
    const {dispatch} = this.props;
    const {addFile} = this.state;
    const {addFolder} = this.state;
    let param = {
      id: addFile.id,
      tenantId: localStorage.getItem('tenantId'),//平台标识
      tmptCode: addFile.parentCode,//模板编码
      tmptName: addFile.className,//模板名称
      classCode: addFile.typeCodes,//分类编码
      illnessDesc: "",//主诉
      preIllnessDesc: "",//现病史
      pastHistDesc: "",//既往史
      genCondDesc: "",//全身情况
      examineDesc: "",//检查
      auxiExamDesc: "",//辅助检查
      diagDesc: "",//诊断
      treatPlanDesc: "",//本次治疗
      treatDesc: "",//处置
      adviceDesc: "",//医嘱
      updateName: localStorage.getItem('userName'),//更新者
      updateId: localStorage.getItem('userId')//更新者id
    }
    if (!param.tmptName) {
      message.warning({
        content: '请填文件名称',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if (dispatch) {
      dispatch({
        type: 'medicalModel/saveEmrTmptService',
        payload: param,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              newModel: false,
            })
            this.ModelLists(addFile.typeCodes);
          } else {
            message.error({
              content: '操作失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
          }
        }
      });
    }
  }
  //编辑文件夹
  EditorFolder = () => {
    const {dispatch} = this.props;
    const {addFolder} = this.state;
    let param = {
      id: addFolder.id,
      className: addFolder.className,//分类夹名称
      tenantId: localStorage.getItem('tenantId'),//平台标识
      updateName: localStorage.getItem('userName'),//更新者
      updateId: localStorage.getItem('userId')//更新者id
    }
    if (!param.className) {
      message.warning({
        content: '请填文件名称',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    this.setState({
      editorModelLoading:true
    })
    if (dispatch) {
      dispatch({
        type: 'medicalModel/updateContentClassService',
        payload: param,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              editorName: false,
              newModel: false,
              editorModelLoading:false
            })
            this.getModelList()
            if(this.state.searchValue){
              this.searchList(this.state.searchValue)
            }
          } else {
            message.error({
              content: res.msg,
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              editorModelLoading:false
            })
          }
        }
      });
    }
  }
  //新建模板取消
  newModelCancel = () => {
    this.setState({
      newModel: false,
      saveModelLoading:false
    })
  }
  //修改模板名称
  editorFile = (fileName, item1) => {
    if (fileName == 'file') {
      this.state.addFolder.parentCode = item1.classCode ? item1.classCode : item1.code;
      this.state.addFolder.className = item1.className ? item1.className : item1.name;
      this.state.addFolder.id = item1.id;
      this.setState({
        modelName: item1.tmptName,
        editorName: true,
        ['hoverTwo' + item1.fileid]: false,
        ['hoverThree' + item1.contentid]: false,
      })
    }
  }
  //编辑模板名
  editorFileName = (item2) => {
    this.state.addFile.parentCode = item2.tmptCode ? item2.tmptCode : item2.code;
    this.state.addFile.className = item2.tmptName ? item2.tmptName : item2.name;
    this.setState({
      editorNames: true,
      ['hoverTwo' + item2.fileid]: false,
      ['hoverThree' + item2.contentid]: false,
    })
    this.getEmrMsg(item2.id)
  }
  //保存编辑点击事件
  editorNameOks = () => {
    this.saveEmrMsg()
  }
  //取消状态事件
  editorNameCancels = () => {
    this.setState({
      editorNames: false,
      editornameLoading:false
    })
  }
  //编辑文件夹名称
  editorNameOk = () => {
    // this.EditorModel();
    this.EditorFolder();

  }
  //取消点击事件
  editorNameCancel = () => {
    this.setState({
      editorName: false,
      editorModelLoading:false
    })
  }
  //删除模板确定
  DeletehandleOk = (item) => {
    this.setState({
      setVisible: false,
      ['hoverFirst' + item.id]: false,
      ['hoverTwo' + item.fileid]: false,
      ['hoverThree' + item.contentid]: false,
    })
    this.DeleteMedical(item.id, item.tmptCode)
  }
  //删除自建文件夹
  DeleteOk = (item) => {
    const {dispatch} = this.props;
    this.setState({
      editorLoading: true
    })
    let params = {
      id: item.id,
    };
    if (dispatch) {
      dispatch({
        type: 'medicalModel/deleteContentClassService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '删除成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              editorLoading: false,
              setVisible:false,
            })
            this.getModelList()
          } else {
            message.error({
              content: '删除失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              editorLoading: true
            })
          }
        }
      });
    }
  }
  //取消点击事件
  DeletehandleCancel = (item) => {
    this.setState({
      setVisible: false,
      ['hoverFirst' + item.id]: false,
      ['hoverTwo' + item.fileid]: false,
      ['hoverThree' + item.contentid]: false,
    })
  }

  render() {
    const {
      editorStatus,
      newFile,
      clientHeight,
      EmrMsgs,
      modelListLoading,
      editorLoading,
      EmrTmp,
      addFolder,
      addFile,
      fileLists,
      searchEmrTmp,
      searchStatus,
      saveBtnLoading,
      saveModelLoading,
      editorModelLoading,
      editornameLoading,
      deleteBtnLoading
    } = this.state;
    return (
      <GridContent>
        <Row>
          <Col span={5}>
            <div className={styles.leftMenu} style={{height: clientHeight - 25}}>
              <div className={styles.MenuTitle}>病历模板</div>
              <div style={{margin: 12}}>
                <Search
                  placeholder="搜索"
                  onSearch={this.onSearch}
                  style={{
                    width: '100%',
                  }}
                />
              </div>
              <Spin spinning={modelListLoading} delay={300}>
                <div className={styles.modelContent}>
                  {searchEmrTmp && searchStatus ?
                    <>
                      {searchEmrTmp.length > 0 ?
                        <>
                          {searchEmrTmp.map((item, index) => (
                            <div className={styles.menuFirst} key={index}>
                              <div
                                className={this.state['hoverFirst' + item.code] ? (`${styles.pointer} ${styles.chooseBgcolor}`) : (`${styles.pointer}`)}
                                style={{cursor: 'pointer', marginTop: 12, position: 'relative'}}
                                onMouseEnter={() => this.hoverFirst(true, item.code)}
                                onMouseLeave={() => this.hoverFirst(false, item.code)}
                              >
                                <div
                                  onClick={() => this.openFirst(item, index, item.code)}
                                  style={{display: 'flex', width: '80%'}}>
                                  {this.state.show1 == item.code ?
                                    <img
                                      onMouseEnter={() => this.hoverFirst(true, item.code)}
                                      onMouseLeave={() => this.hoverFirst(false, item.code)}
                                      src={Unfold}
                                      className={styles.arrows} alt=""/> :
                                    <img
                                      onMouseEnter={() => this.hoverFirst(true, item.code)}
                                      onMouseLeave={() => this.hoverFirst(false, item.code)}
                                      src={Fold}
                                      className={styles.arrows} alt=""/>
                                  }
                                  {this.state['hoverFirst' + item.code] ?
                                    <img
                                      onMouseEnter={() => this.hoverFirst(true, item.code)}
                                      onMouseLeave={() => this.hoverFirst(false, item.code)}
                                      src={FolderSelected}
                                      className={styles.fileIcon} alt=""/> :
                                    <img
                                      onMouseEnter={() => this.hoverFirst(true, item.code)}
                                      onMouseLeave={() => this.hoverFirst(false, item.code)}
                                      src={Folder}
                                      className={styles.fileIcon}
                                      alt=""/>
                                  }
                                  <div
                                    className={this.state['hoverFirst' + item.code] ? (`${styles.filetitle} ${styles.chooseFontcolor}`) : (`${styles.filetitle}`)}
                                    style={{marginLeft: 8}}>{item.name}</div>
                                </div>
                                {this.state['hoverFirst' + item.code] ?
                                  <>
                                      <div className={styles.addBtns}
                                           onClick={e => this.addFile(e, item.code)}
                                      >...</div>
                                  </>

                                  :
                                  ""
                                }
                                {this.state.addFileBorderStatus2 && this.state.hover1 == item.code ?
                                  <>
                                    <div className={styles.addFile} style={{top: 25, height: 35}}>
                                      <div
                                        onClick={() => this.newFile(item.code)}
                                        className={styles.addFileLine}>
                                        <img src={NewFolder} className={styles.iconStyle} alt=""/>
                                        <span className={styles.addFileName}>新建文件夹</span>
                                      </div>
                                    </div>
                                  </>
                                  :
                                  null}
                              </div>
                              {item.children ?
                                <>
                                  {item.children.map((item1, index1) => (
                                        <>
                                          <div style={{marginLeft: 15}} key={index1}
                                               className={this.state.show1 == item.code ? (styles.show) : (styles.hidden)}>
                                            <div
                                              className={this.state['hoverTwo' + item1.code] ? (`${styles.pointer} ${styles.chooseBgcolor}`) : (`${styles.pointer}`)}
                                              style={{cursor: 'pointer', marginTop: 12}}
                                              onMouseEnter={() => this.hoverTwo(true, item1.code)}
                                              onMouseLeave={() => this.hoverTwo(false, item1.code)}
                                            >
                                              <div
                                                onClick={() => this.opentwo('search', item1, item1.code)}
                                                style={{display: 'flex', width: '80%'}}>
                                                {this.state.show2 == item1.code ?
                                                  <img src={Unfold} className={styles.arrows} alt=""/> :
                                                  <img src={Fold} className={styles.arrows} alt=""/>
                                                }
                                                {this.state['hoverTwo' + item1.code] ?
                                                  <img
                                                    onMouseEnter={() => this.hoverTwo(true, item1.code)}
                                                    onMouseLeave={() => this.hoverTwo(false, item1.code)}
                                                    src={FolderSelected}
                                                    className={styles.fileIcon} alt=""/> :
                                                  <img
                                                    onMouseEnter={() => this.hoverTwo(true, item1.code)}
                                                    onMouseLeave={() => this.hoverTwo(false, item1.code)}
                                                    src={Folder}
                                                    className={styles.fileIcon}
                                                    alt=""/>
                                                }
                                                <div
                                                  className={this.state['hoverTwo' + item1.code] ? (`${styles.filetitle} ${styles.chooseFontcolor}`) : (`${styles.filetitle}`)}
                                                  style={{marginLeft: 8}}>{item1.name}</div>
                                              </div>
                                              {this.state['hoverTwo' + item1.code] ?
                                                <div className={styles.addBtns}
                                                     onClick={e => this.addModel(e, item1.code)}>...</div> :
                                                ""
                                              }
                                              {this.state.addFileBorderStatus3 && this.state.hover2 == item1.code ?
                                                <div className={styles.addFile} style={{top: 25, height: 85}}>
                                                  <div
                                                    onClick={() => this.newModel(item1)}
                                                    className={styles.addFileLine}>
                                                    <img src={NewFile} className={styles.iconStyle} alt=""/>
                                                    <span className={styles.addFileName}>新建模板</span>
                                                  </div>
                                                  <div
                                                    onClick={() => this.editorFile('file', item1)}
                                                    className={styles.addFileLine}>
                                                    <img src={Edit} className={styles.iconStyle} alt=""/>
                                                    <span className={styles.addFileName}>编辑名称</span>
                                                  </div>
                                                  <Popconfirm
                                                    title="确定删除？"
                                                    visible={this.state.setVisible}
                                                    onConfirm={() => this.DeleteOk(item1)}
                                                    // okButtonProps={{
                                                    //   loading: confirmLoading,
                                                    // }}
                                                    onCancel={() => this.DeletehandleCancel(item1)}
                                                  >
                                                    <div
                                                      onClick={this.showPopconfirm.bind(this,)}
                                                      className={styles.addFileLine}>
                                                      <img src={Delete} className={styles.iconStyle} alt=""/>
                                                      <span className={styles.addFileName}>删除</span>
                                                    </div>
                                                  </Popconfirm>
                                                </div> :
                                                null}
                                            </div>
                                            {item1.children ?
                                              <>
                                                {item1.children.map((item2, index2) => (
                                                  <div style={{marginLeft: 15}} key={index2}
                                                       className={this.state.show2 == item1.code ? (styles.show) : (styles.hidden)}>
                                                    <div
                                                      className={this.state['hoverThree' + item2.id] ? (`${styles.fileName} ${styles.chooseBgcolor}`) : (`${styles.fileName}`)}
                                                      style={{
                                                        justifyContent: 'space-between',
                                                        marginTop: 12,
                                                        position: 'relative'
                                                      }}
                                                      onMouseEnter={() => this.hoverThree(true, item2.id)}
                                                      onMouseLeave={() => this.hoverThree(false, item2.id)}
                                                    >
                                                      <div
                                                        onClick={() => this.openthree(item2, index2, item2.id, item2.tmptCode)}
                                                        style={{display: 'flex', cursor: 'pointer', width: '85%'}}
                                                      >
                                                        {this.state.show3 == item2.id || this.state['hoverThree' + item2.id] ?
                                                          <img src={FileSelected} className={styles.Unselected}
                                                               alt=""/> :
                                                          <img src={FileUnselected} className={styles.Unselected}
                                                               alt=""/>
                                                        }
                                                        <div
                                                          className={this.state.show3 == item2.id || this.state['hoverThree' + item2.id] ? (styles.SelectedStyle) : (styles.UnselectedStyle)}>{item2.name}</div>
                                                      </div>
                                                      {this.state['hoverThree' + item2.id] ?
                                                        <div className={styles.addBtns}
                                                             onClick={e => this.fileModel(e, item2.id)}>...</div> :
                                                        ""
                                                      }
                                                      {this.state.addFileBorderStatus4 && this.state.hover3 == item2.id ?
                                                        <div className={styles.addFile} style={{top: 25, height: 62}}>
                                                          <div
                                                            onClick={() => this.editorFileName(item2)}
                                                            className={styles.addFileLine}>
                                                            <img src={Edit} className={styles.iconStyle} alt=""/>
                                                            <span className={styles.addFileName}>编辑名称</span>
                                                          </div>
                                                          <Popconfirm
                                                            title="确定删除？"
                                                            visible={this.state.setVisible}
                                                            onConfirm={() => this.DeletehandleOk(item2)}
                                                            // okButtonProps={{
                                                            //   loading: confirmLoading,
                                                            // }}
                                                            onCancel={() => this.DeletehandleCancel(item2)}
                                                          >
                                                            <div
                                                              onClick={this.showPopconfirm.bind(this,)}
                                                              className={styles.addFileLine}>
                                                              <img src={Delete} className={styles.iconStyle} alt=""/>
                                                              <span className={styles.addFileName}>删除</span>
                                                            </div>
                                                          </Popconfirm>
                                                        </div> :
                                                        null}
                                                    </div>
                                                  </div>
                                                ))}
                                              </> : ""
                                            }
                                          </div>
                                        </>

                                  ))}
                                </> :
                                <>

                                  {item.code == "0" ?
                                    <>
                                      {fileLists ?
                                        <>
                                          {fileLists.map((item2, index2) => (
                                            <div style={{marginLeft: 15}} key={index2}
                                                 className={this.state.show1 == item.code ? (styles.show) : (styles.hidden)}>
                                              <div
                                                className={this.state['hoverThree' + item2.id] ? (`${styles.fileName} ${styles.chooseBgcolor}`) : (`${styles.fileName}`)}
                                                style={{
                                                  justifyContent: 'space-between',
                                                  marginTop: 12,
                                                  position: 'relative'
                                                }}
                                                onMouseEnter={() => this.hoverThree(true, item2.id)}
                                                onMouseLeave={() => this.hoverThree(false, item2.id)}
                                              >
                                                <div
                                                  onClick={() => this.openthree(item2, index2, item2.id, item2.tmptCode)}
                                                  style={{display: 'flex', cursor: 'pointer', width: '85%'}}
                                                >
                                                  {this.state.show3 == item2.id || this.state['hoverThree' + item2.id] ?
                                                    <img src={FileSelected} className={styles.Unselected} alt=""/> :
                                                    <img src={FileUnselected} className={styles.Unselected} alt=""/>
                                                  }
                                                  <div
                                                    className={this.state.show3 == item2.id || this.state['hoverThree' + item2.id] ? (styles.SelectedStyle) : (styles.UnselectedStyle)}>{item2.tmptName}</div>
                                                </div>
                                                {this.state['hoverThree' + item2.id] ?
                                                  <div className={styles.addBtns}
                                                       onClick={e => this.fileModel(e, item2.id)}>...</div> :
                                                  ""
                                                }
                                                {this.state.addFileBorderStatus4 && this.state.hover3 == item2.id ?
                                                  <div className={styles.addFile} style={{top: 25, height: 62}}>
                                                    <div
                                                      onClick={() => this.editorFileName(item2)}
                                                      className={styles.addFileLine}>
                                                      <img src={Edit} className={styles.iconStyle} alt=""/>
                                                      <span className={styles.addFileName}>编辑名称</span>
                                                    </div>
                                                    <Popconfirm
                                                      title="确定删除？"
                                                      visible={this.state.setVisible}
                                                      onConfirm={() => this.DeletehandleOk(item2)}
                                                      // okButtonProps={{
                                                      //   loading: confirmLoading,
                                                      // }}
                                                      onCancel={() => this.DeletehandleCancel(item2)}
                                                    >
                                                      <div
                                                        onClick={this.showPopconfirm.bind(this,)}
                                                        className={styles.addFileLine}>
                                                        <img src={Delete} className={styles.iconStyle} alt=""/>
                                                        <span className={styles.addFileName}>删除</span>
                                                      </div>
                                                    </Popconfirm>
                                                  </div> :
                                                  null}
                                              </div>
                                            </div>
                                          ))}
                                        </> : ""
                                      }
                                    </> : null
                                  }


                                </>
                              }
                            </div>
                          ))}
                        </>
                        :
                        <div className={commonStyle.nodataContent} style={{marginTop: '45%'}}>
                          <img src={noData} className={commonStyle.imgStyle} alt=""/>
                          <div className={commonStyle.fontStyle}>暂无数据</div>
                        </div>
                      }
                    </> :
                    <>
                      {EmrTmp ?
                        <>
                          {EmrTmp.map((item, index) => (
                            <div className={styles.menuFirst} key={index}>
                              <div
                                className={this.state['hoverFirst' + item.classCode] ? (`${styles.pointer} ${styles.chooseBgcolor}`) : (`${styles.pointer}`)}
                                style={{cursor: 'pointer', marginTop: 12, position: 'relative'}}
                                onMouseEnter={() => this.hoverFirst(true, item.classCode)}
                                onMouseLeave={() => this.hoverFirst(false, item.classCode)}
                              >
                                <div
                                  onClick={() => this.openFirst(item, index, item.classCode)}
                                  style={{display: 'flex', width: '80%'}}>
                                  {this.state.show1 == item.classCode ?
                                    <img
                                      onMouseEnter={() => this.hoverFirst(true, item.classCode)}
                                      onMouseLeave={() => this.hoverFirst(false, item.classCode)}
                                      src={Unfold}
                                      className={styles.arrows} alt=""/> :
                                    <img
                                      onMouseEnter={() => this.hoverFirst(true, item.classCode)}
                                      onMouseLeave={() => this.hoverFirst(false, item.classCode)}
                                      src={Fold}
                                      className={styles.arrows} alt=""/>
                                  }
                                  {this.state['hoverFirst' + item.classCode] ?
                                    <img
                                      onMouseEnter={() => this.hoverFirst(true, item.classCode)}
                                      onMouseLeave={() => this.hoverFirst(false, item.classCode)}
                                      src={FolderSelected}
                                      className={styles.fileIcon} alt=""/> :
                                    <img
                                      onMouseEnter={() => this.hoverFirst(true, item.classCode)}
                                      onMouseLeave={() => this.hoverFirst(false, item.classCode)}
                                      src={Folder}
                                      className={styles.fileIcon}
                                      alt=""/>
                                  }
                                  <div
                                    className={this.state['hoverFirst' + item.classCode] ? (`${styles.filetitle} ${styles.chooseFontcolor}`) : (`${styles.filetitle}`)}
                                    style={{marginLeft: 8}}>{item.className}</div>
                                </div>
                                {this.state['hoverFirst' + item.classCode] ?
                                  <>
                                    <div className={styles.addBtns}
                                         onClick={e => this.addFile(e, item.classCode)}
                                    >...</div>
                                  </>

                                  :
                                  ""
                                }
                                {this.state.addFileBorderStatus2 && this.state.hover1 == item.classCode ?
                                  <>
                                    <div className={styles.addFile} style={{top: 25, height: 35}}>
                                      <div
                                        onClick={() => this.newFile(item.classCode)}
                                        className={styles.addFileLine}>
                                        <img src={NewFolder} className={styles.iconStyle} alt=""/>
                                        <span className={styles.addFileName}>新建文件夹</span>
                                      </div>
                                    </div>
                                  </>
                                  :
                                  null}
                              </div>
                              {item.children ?
                                <>
                                  {item.children.map((item1, index1) => (
                                    <div style={{marginLeft: 15}} key={index1}
                                         className={this.state.show1 == item.classCode ? (styles.show) : (styles.hidden)}>
                                      <div
                                        className={this.state['hoverTwo' + item1.classCode] ? (`${styles.pointer} ${styles.chooseBgcolor}`) : (`${styles.pointer}`)}
                                        style={{cursor: 'pointer', marginTop: 12}}
                                        onMouseEnter={() => this.hoverTwo(true, item1.classCode)}
                                        onMouseLeave={() => this.hoverTwo(false, item1.classCode)}
                                      >
                                        <div
                                          onClick={() => this.opentwo('orginal', item1, item1.classCode)}
                                          style={{display: 'flex', width: '80%'}}>
                                          {this.state.show2 == item1.classCode ?
                                            <img src={Unfold} className={styles.arrows} alt=""/> :
                                            <img src={Fold} className={styles.arrows} alt=""/>
                                          }
                                          {this.state['hoverTwo' + item1.classCode] ?
                                            <img
                                              onMouseEnter={() => this.hoverTwo(true, item1.classCode)}
                                              onMouseLeave={() => this.hoverTwo(false, item1.classCode)}
                                              src={FolderSelected}
                                              className={styles.fileIcon} alt=""/> :
                                            <img
                                              onMouseEnter={() => this.hoverTwo(true, item1.classCode)}
                                              onMouseLeave={() => this.hoverTwo(false, item1.classCode)}
                                              src={Folder}
                                              className={styles.fileIcon}
                                              alt=""/>
                                          }
                                          <div
                                            className={this.state['hoverTwo' + item1.classCode] ? (`${styles.filetitle} ${styles.chooseFontcolor}`) : (`${styles.filetitle}`)}
                                            style={{marginLeft: 8}}>{item1.className}</div>
                                        </div>
                                        {this.state['hoverTwo' + item1.classCode] ?
                                          <div className={styles.addBtns}
                                               onClick={e => this.addModel(e, item1.classCode)}>...</div> :
                                          ""
                                        }
                                        {this.state.addFileBorderStatus3 && this.state.hover2 == item1.classCode ?
                                          <div className={styles.addFile} style={{top: 25, height: 85}}>
                                            <div
                                              onClick={() => this.newModel(item1)}
                                              className={styles.addFileLine}>
                                              <img src={NewFile} className={styles.iconStyle} alt=""/>
                                              <span className={styles.addFileName}>新建模板</span>
                                            </div>
                                            <div
                                              onClick={() => this.editorFile('file', item1)}
                                              className={styles.addFileLine}>
                                              <img src={Edit} className={styles.iconStyle} alt=""/>
                                              <span className={styles.addFileName}>编辑名称</span>
                                            </div>
                                            <Popconfirm
                                              title="确定删除？"
                                              visible={this.state.setVisible}
                                              onConfirm={() => this.DeleteOk(item1)}
                                              // okButtonProps={{
                                              //   loading: confirmLoading,
                                              // }}
                                              onCancel={() => this.DeletehandleCancel(item1)}
                                            >
                                              <div
                                                onClick={this.showPopconfirm.bind(this,)}
                                                className={styles.addFileLine}>
                                                <img src={Delete} className={styles.iconStyle} alt=""/>
                                                <span className={styles.addFileName}>删除</span>
                                              </div>
                                            </Popconfirm>
                                          </div> :
                                          null}
                                      </div>
                                      {fileLists ?
                                        <>
                                          {fileLists.map((item2, index2) => (
                                            <div style={{marginLeft: 15}} key={index2}
                                                 className={this.state.show2 == item1.classCode ? (styles.show) : (styles.hidden)}>
                                              <div
                                                className={this.state['hoverThree' + item2.id] ? (`${styles.fileName} ${styles.chooseBgcolor}`) : (`${styles.fileName}`)}
                                                style={{
                                                  justifyContent: 'space-between',
                                                  marginTop: 12,
                                                  position: 'relative'
                                                }}
                                                onMouseEnter={() => this.hoverThree(true, item2.id)}
                                                onMouseLeave={() => this.hoverThree(false, item2.id)}
                                              >
                                                <div
                                                  onClick={() => this.openthree(item2, index2, item2.id, item2.tmptCode)}
                                                  style={{display: 'flex', cursor: 'pointer', width: '85%'}}
                                                >
                                                  {this.state.show3 == item2.id || this.state['hoverThree' + item2.id] ?
                                                    <img src={FileSelected} className={styles.Unselected} alt=""/> :
                                                    <img src={FileUnselected} className={styles.Unselected} alt=""/>
                                                  }
                                                  <div
                                                    className={this.state.show3 == item2.id || this.state['hoverThree' + item2.id] ? (styles.SelectedStyle) : (styles.UnselectedStyle)}>{item2.tmptName}</div>
                                                </div>
                                                {this.state['hoverThree' + item2.id] ?
                                                  <div className={styles.addBtns}
                                                       onClick={e => this.fileModel(e, item2.id)}>...</div> :
                                                  ""
                                                }
                                                {this.state.addFileBorderStatus4 && this.state.hover3 == item2.id ?
                                                  <div className={styles.addFile} style={{top: 25, height: 62}}>
                                                    <div
                                                      style={{cursor: 'pointer'}}
                                                      onClick={() => this.editorFileName(item2)}
                                                      className={styles.addFileLine}>
                                                      <img src={Edit} className={styles.iconStyle} alt=""/>
                                                      <span className={styles.addFileName}>编辑名称</span>
                                                    </div>
                                                    <Popconfirm
                                                      title="确定删除？"
                                                      visible={this.state.setVisible}
                                                      onConfirm={() => this.DeletehandleOk(item2)}
                                                      // okButtonProps={{
                                                      //   loading: confirmLoading,
                                                      // }}
                                                      onCancel={() => this.DeletehandleCancel(item2)}
                                                    >
                                                      <div
                                                        style={{cursor: 'pointer'}}
                                                        onClick={this.showPopconfirm.bind(this,)}
                                                        className={styles.addFileLine}>
                                                        <img src={Delete} className={styles.iconStyle} alt=""/>
                                                        <span className={styles.addFileName}>删除</span>
                                                      </div>
                                                    </Popconfirm>
                                                  </div> :
                                                  null}
                                              </div>
                                            </div>
                                          ))}
                                        </> : ""
                                      }
                                    </div>
                                  ))}
                                </> :
                                <>

                                  {/*{item.classCode == "0" ?*/}
                                  {/*  <>*/}
                                  {/*    {fileLists ?*/}
                                  {/*      <>*/}
                                  {/*        {fileLists.map((item2, index2) => (*/}
                                  {/*          <div style={{marginLeft: 15}} key={index2}*/}
                                  {/*               className={this.state.show1 == item.classCode ? (styles.show) : (styles.hidden)}>*/}
                                  {/*            <div*/}
                                  {/*              className={this.state['hoverThree' + item2.id] ? (`${styles.fileName} ${styles.chooseBgcolor}`) : (`${styles.fileName}`)}*/}
                                  {/*              style={{*/}
                                  {/*                justifyContent: 'space-between',*/}
                                  {/*                marginTop: 12,*/}
                                  {/*                position: 'relative'*/}
                                  {/*              }}*/}
                                  {/*              onMouseEnter={() => this.hoverThree(true, item2.id)}*/}
                                  {/*              onMouseLeave={() => this.hoverThree(false, item2.id)}*/}
                                  {/*            >*/}
                                  {/*              <div*/}
                                  {/*                onClick={() => this.openthree(item2, index2, item2.id, item2.tmptCode)}*/}
                                  {/*                style={{display: 'flex', cursor: 'pointer', width: '85%'}}*/}
                                  {/*              >*/}
                                  {/*                {this.state.show3 == item2.id || this.state['hoverThree' + item2.id] ?*/}
                                  {/*                  <img src={FileSelected} className={styles.Unselected} alt=""/> :*/}
                                  {/*                  <img src={FileUnselected} className={styles.Unselected} alt=""/>*/}
                                  {/*                }*/}
                                  {/*                <div*/}
                                  {/*                  className={this.state.show3 == item2.id || this.state['hoverThree' + item2.id] ? (styles.SelectedStyle) : (styles.UnselectedStyle)}>{item2.tmptName}</div>*/}
                                  {/*              </div>*/}
                                  {/*              {this.state['hoverThree' + item2.id] ?*/}
                                  {/*                <div className={styles.addBtns}*/}
                                  {/*                     onClick={e => this.fileModel(e, item2.id)}>...</div> :*/}
                                  {/*                ""*/}
                                  {/*              }*/}
                                  {/*              {this.state.addFileBorderStatus4 && this.state.hover3 == item2.id ?*/}
                                  {/*                <div className={styles.addFile} style={{top: 25, height: 62}}>*/}
                                  {/*                  <div*/}
                                  {/*                    onClick={() => this.editorFileName(item2)}*/}
                                  {/*                    className={styles.addFileLine}>*/}
                                  {/*                    <img src={Edit} className={styles.iconStyle} alt=""/>*/}
                                  {/*                    <span className={styles.addFileName}>编辑名称</span>*/}
                                  {/*                  </div>*/}
                                  {/*                  <Popconfirm*/}
                                  {/*                    title="确定删除？"*/}
                                  {/*                    visible={this.state.setVisible}*/}
                                  {/*                    onConfirm={() => this.DeletehandleOk(item2)}*/}
                                  {/*                    // okButtonProps={{*/}
                                  {/*                    //   loading: confirmLoading,*/}
                                  {/*                    // }}*/}
                                  {/*                    onCancel={() => this.DeletehandleCancel(item2)}*/}
                                  {/*                  >*/}
                                  {/*                    <div*/}
                                  {/*                      onClick={this.showPopconfirm.bind(this,)}*/}
                                  {/*                      className={styles.addFileLine}>*/}
                                  {/*                      <img src={Delete} className={styles.iconStyle} alt=""/>*/}
                                  {/*                      <span className={styles.addFileName}>删除</span>*/}
                                  {/*                    </div>*/}
                                  {/*                  </Popconfirm>*/}
                                  {/*                </div> :*/}
                                  {/*                null}*/}
                                  {/*            </div>*/}
                                  {/*          </div>*/}
                                  {/*        ))}*/}
                                  {/*      </> : ""*/}
                                  {/*    }*/}
                                  {/*  </> : null*/}
                                  {/*}*/}


                                </>
                              }
                            </div>
                          ))}
                        </>
                        : ""}
                    </>
                  }
                </div>
              </Spin>
            </div>
          </Col>
          <Col span={19}>
            {EmrMsgs.id ?
              <Spin spinning={editorLoading} delay={300}>
                <div className={styles.Rightcontent}>
                  <div className={styles.flexContent}>
                    <div className={styles.contentTitle}>{EmrMsgs.tmptName ? EmrMsgs.tmptName : ""}</div>
                    {editorStatus ?
                      <div className={styles.editorBtn}>
                        <span onClick={this.CancelEditor}>取消</span>
                        {this.state.saveStatus?
                          <span style={{color: '#4292FF'}}>保存</span>:
                          <span style={{color: '#4292FF'}} onClick={this.saveEmrMsg}>保存</span>
                        }
                      </div>
                      :
                      <div className={styles.editorBtn}>
                        <span onClick={this.deleteModel}>删除</span>
                        <span style={{color: '#4292FF'}} onClick={this.editor}>编辑</span>
                      </div>
                    }
                  </div>
                  <Form {...layout} name="nest-messages">
                    <Form.Item
                      label="主诉"
                    >
                      {editorStatus ?
                        <TextArea
                          autoSize={{minRows: 1, maxRows: 10}}
                          onChange={e => {
                            EmrMsgs.illnessDesc = e.target.value
                          }}
                          defaultValue={EmrMsgs.illnessDesc ? EmrMsgs.illnessDesc : ""}
                        />
                        :
                        <span>{EmrMsgs.illnessDesc ? EmrMsgs.illnessDesc : ""}</span>
                      }
                    </Form.Item>
                    <Form.Item
                      label="现状史"
                    >
                      {editorStatus ?
                        <TextArea
                          autoSize={{minRows: 1, maxRows: 10}}
                          onChange={e => {
                            EmrMsgs.preIllnessDesc = e.target.value
                          }}
                          defaultValue={EmrMsgs.preIllnessDesc ? EmrMsgs.preIllnessDesc : ""}
                        />
                        :
                        <span>{EmrMsgs.preIllnessDesc ? EmrMsgs.preIllnessDesc : ""}</span>
                      }
                    </Form.Item>
                    <Form.Item
                      label="既往史"
                    >
                      {editorStatus ?
                        <TextArea
                          autoSize={{minRows: 1, maxRows: 10}}
                          onChange={e => {
                            EmrMsgs.pastHistDesc = e.target.value
                          }}
                          defaultValue={EmrMsgs.pastHistDesc ? EmrMsgs.pastHistDesc : ""}
                        />
                        :
                        <span>{EmrMsgs.pastHistDesc ? EmrMsgs.pastHistDesc : ""}</span>
                      }
                    </Form.Item>
                    <Form.Item
                      label="全身状况"
                    >
                      {editorStatus ?
                        <TextArea
                          autoSize={{minRows: 1, maxRows: 10}}
                          onChange={e => {
                            EmrMsgs.genCondDesc = e.target.value
                          }}
                          defaultValue={EmrMsgs.genCondDesc ? EmrMsgs.genCondDesc : ""}
                        />
                        :
                        <span>{EmrMsgs.genCondDesc ? EmrMsgs.genCondDesc : ""}</span>
                      }
                    </Form.Item>
                    <Form.Item
                      label="检查"
                    >
                      {editorStatus ?
                        <TextArea
                          autoSize={{minRows: 1, maxRows: 10}}
                          onChange={e => {
                            EmrMsgs.examineDesc = e.target.value
                          }}
                          defaultValue={EmrMsgs.examineDesc ? EmrMsgs.examineDesc : ""}
                        />
                        :
                        <span>{EmrMsgs.examineDesc ? EmrMsgs.examineDesc : ""}</span>
                      }
                    </Form.Item>
                    <Form.Item
                      label="辅助检查"
                    >
                      {editorStatus ?
                        <TextArea
                          autoSize={{minRows: 1, maxRows: 10}}
                          onChange={e => {
                            EmrMsgs.auxiExamDesc = e.target.value
                          }}
                          defaultValue={EmrMsgs.auxiExamDesc ? EmrMsgs.auxiExamDesc : ""}
                        />
                        :
                        <span>{EmrMsgs.auxiExamDesc ? EmrMsgs.auxiExamDesc : ""}</span>
                      }
                    </Form.Item>
                    <Form.Item
                      label="诊断"
                    >
                      {editorStatus ?
                        <TextArea
                          autoSize={{minRows: 1, maxRows: 10}}
                          onChange={e => {
                            EmrMsgs.diagDesc = e.target.value
                          }}
                          defaultValue={EmrMsgs.diagDesc ? EmrMsgs.diagDesc : ""} type="text"/>
                        :
                        <span>{EmrMsgs.diagDesc ? EmrMsgs.diagDesc : ""}</span>
                      }
                    </Form.Item>
                    <Form.Item
                      label="本次治疗"
                    >
                      {editorStatus ?
                        <TextArea
                          autoSize={{minRows: 1, maxRows: 10}}
                          onChange={e => {
                            EmrMsgs.treatPlanDesc = e.target.value
                          }}
                          defaultValue={EmrMsgs.treatPlanDesc ? EmrMsgs.treatPlanDesc : ""} type="text"/>
                        :
                        <span>{EmrMsgs.treatPlanDesc ? EmrMsgs.treatPlanDesc : ""}</span>
                      }
                    </Form.Item>
                    <Form.Item
                      label="处置"
                    >
                      {editorStatus ?
                        <TextArea
                          autoSize={{minRows: 1, maxRows: 10}}
                          onChange={e => {
                            EmrMsgs.treatDesc = e.target.value
                          }}
                          defaultValue={EmrMsgs.treatDesc ? EmrMsgs.treatDesc : ""}
                        />
                        :
                        <span>{EmrMsgs.treatDesc ? EmrMsgs.treatDesc : ""}</span>
                      }
                    </Form.Item>
                    <Form.Item
                      label="医嘱"
                    >
                      {editorStatus ?
                        <TextArea
                          autoSize={{minRows: 1, maxRows: 10}}
                          onChange={e => {
                            EmrMsgs.adviceDesc = e.target.value
                          }}
                          defaultValue={EmrMsgs.adviceDesc ? EmrMsgs.adviceDesc : ""}
                        />
                        :
                        <span>{EmrMsgs.adviceDesc ? EmrMsgs.adviceDesc : ""}</span>
                      }
                    </Form.Item>
                  </Form>
                </div>
              </Spin>
              :
              <div className={commonStyle.nodataContent} style={{marginTop: '20%'}}>
                <img src={noData} className={commonStyle.imgStyle} alt=""/>
                <div className={commonStyle.fontStyle}>暂无数据</div>
              </div>
            }

          </Col>
        </Row>
        {/*新建文件夹*/}
        <Modal
          title="新建文件夹"
          visible={newFile}
          destroyOnClose={true}
          onOk={this.newFileOk}
          onCancel={this.newFileCancel}
          okText="创建"
          cancelText="取消"
          width={660}
          maskClosable={false}
          confirmLoading={saveBtnLoading}
        >
          <TextArea
            showCount
            maxLength={100}
            autoSize={{minRows: 1, maxRows: 10}}
            onChange={e => {
              addFolder.className = e.target.value
            }}
            placeholder="请输入文件夹名称"/>
        </Modal>
        {/*新建模板文件*/}
        <Modal
          title="新建模板"
          visible={this.state.newModel}
          destroyOnClose={true}
          onOk={() => {
            this.newModelOk(2)
          }}
          onCancel={this.newModelCancel}
          okText="创建"
          cancelText="取消"
          width={660}
          maskClosable={false}
          confirmLoading={saveModelLoading}
        >
          <TextArea
            showCount
            maxLength={100}
            autoSize={{minRows: 1, maxRows: 10}}
            onChange={e => {
              addFile.className = e.target.value
            }}
            placeholder="请输入模板名称"/>
        </Modal>
        {/*编辑文件夹名称*/}
        <Modal
          title="编辑文件夹名称"
          visible={this.state.editorName}
          destroyOnClose={true}
          onOk={this.editorNameOk}
          onCancel={this.editorNameCancel}
          okText="确定"
          cancelText="取消"
          width={660}
          maskClosable={false}
          confirmLoading={editorModelLoading}
        >
          <TextArea
            showCount
            maxLength={100}
            autoSize={{minRows: 1, maxRows: 10}}
            onChange={e => {
              addFolder.className = e.target.value
            }}
            defaultValue={addFolder.className}
            placeholder="请输入模板名称"/>
        </Modal>
        {/*编辑文件名称*/}
        <Modal
          title="编辑名称"
          visible={this.state.editorNames}
          destroyOnClose={true}
          onOk={this.editorNameOks}
          onCancel={this.editorNameCancels}
          okText="确定"
          cancelText="取消"
          width={660}
          maskClosable={false}
          confirmLoading={editornameLoading}
        >
          <TextArea
            showCount
            maxLength={100}
            autoSize={{minRows: 1, maxRows: 10}}
            onChange={e => {
              EmrMsgs.tmptName = e.target.value
            }}
            defaultValue={addFile.className}
            placeholder="请输入模板名称"/>
        </Modal>
      </GridContent>
    );
  }
}

export default connect(({EmrMessageData}) => ({
  EmrMessageData,
}))(MedicalModel);
