const getnurse = (req, res) =>
  res.json([
    {
      id: '000000001',
      name: '张三',
      sex: '男',
      age: '35',
      Doctor: '无菌',
      Department:"正畸科",
      type: '1',
      Adept:'种植、修复',
      service:'张柏芝、张靓颖',
      time:10,
      num:99
    },
    {
      id: '000000002',
      name: '李丽',
      sex: '女',
      age: '24',
      Doctor: '无菌',
      Department:"正畸科",
      type: '1',
      Adept:'种植、修复',
      service:'张柏芝、张靓颖',
      time:10,
      num:100,

    },
    {
      id: '000000003',
      name: '李丽',
      sex: '女',
      age: '24',
      Doctor: '无菌',
      Department:"正畸科",
      type: '1',
      Adept:'种植、修复',
      service:'',
      time:10,
      num:1,
    },
    {
      id: '000000004',
      name: '李丽',
      sex: '女',
      age: '24',
      Doctor: '无菌',
      Department:"正畸科",
      type: '1',
      service:'张柏芝、张靓颖',
      Adept:'种植、修复',
      time:10,
      num:1,

    },
  ]);

export default {
  'GET /api/datanurse': getnurse,
};
