import {Row, Col, Cascader, Radio, Button, Upload, message, TreeSelect, Image, Modal, Spin,Popconfirm } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import React, { useState, useRef, Component } from 'react';
import { useIntl, FormattedMessage } from 'umi';
import { GridContent } from '@ant-design/pro-layout';
//引入样式
import styles from './style.less';
import commonStyle from '@/pages/common.less';
//引入图片
import img from '@/assets/img1.png';
import boy from '@/assets/<EMAIL>';
import Preview from "@/assets/<EMAIL>";
import Delete from "@/assets/<EMAIL>";
import tys from "@/assets/<EMAIL>";
import narrow from "@/assets/narrow.png";
import enlarge from "@/assets/enlarge.png";
import rotate from "@/assets/rotate.png";
import noData from "@/assets/<EMAIL>";

import {connect} from "dva";

// 关联知情同意书
class InforConsent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: undefined,
      BookStatus:false,//预览知情同意书状态
      language:1,//编辑时选择语言的状态
      language1:1,//预览时的选择语言状态
      InforList:[],////模板分组列表
      MrcMessage:{},//知情同意书详情
      MrcMessageLeft:{},//知情同意书详情，区分右侧
      agreId:"",//知情同意书模板id暂存
      fileList:[],//获取文件数据
      Imgvisible:false//查看大图状态
    };
  }
  //初始化事件
  componentDidMount() {
    this.props.onRef(this)
    this.InforList()
  }
  //模板分组列表
  InforList=()=>{
    const {dispatch} = this.props
    let params={
      tenantId:localStorage.getItem('tenantId')
    }
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/findMrcTmptsService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            // console.log("%%%%%",JSON.stringify(res))
            let arr=res.rows;
            let arr1=[];
            arr.forEach((key,index)=>{
              arr1.push({
                title:key.className,
                value:key.classCode,
                selectable:false,
                children:(key.tmptList||[]).map(item => {
                  return {...item, title:item.tmptName,value:item.id}
                })
              })
            })
            // console.log("arr1====",JSON.stringify(arr1))
            this.setState({
              InforList:arr1,
            })
          }
          // console.log("模板分组列表==",JSON.stringify(res.rows))
        }
      });
    }
  }
  //下载同意书类型选择
  onChange=(value)=> {
    this.mrcsMessage(value,1)
  }
  //上传同意书类型选择
  uploadonChange=(value)=>{
    this.mrcsMessage(value,2)
  }
  //知情同意书详情
  mrcsMessage=(value,key)=>{
    const {dispatch} = this.props
    this.state.agreId=value;
    let params={
      id:value,
      tenantId:localStorage.getItem('tenantId'),
    }
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/getMrcTempInfoService',
        payload: params,
        callback: (res) => {
          // console.log("详情==",JSON.stringify(res))
          if (res.code == 200) {
            if(key==1){
              this.setState({
                MrcMessageLeft:res.content,
                language:1
              })
            }
            if(key==2){
              this.setState({
                MrcMessage:res.content,
              })
            }
          }
        }
      });
    }
  }
  // 鼠标移入移出影像图片
  onMouseIn = (mouseIn,uid) => {
    this.setState({
      ['mouseIn'+uid]: mouseIn
    })
  }
  //鼠标是进入进出事件调用
  preview= (previewStatus) => {
    this.setState({
      ['previewStatus']: previewStatus
    })
  }
  //预览知情同意书
  previewBook=()=>{
    this.setState({
      BookStatus:true,
      language1:1
    })
  }
  //知情同意书预览确定点击事件
  ModalOK=()=>{
    this.setState({
      BookStatus:false
    })
  }
  //知情同意书预览取消点击事件
  ModalCancel=()=>{
    this.setState({
      BookStatus:false,
      // language:1
    })
  }
  // 选择语言
  LanguageChange=(e)=>{
    // console.log("==",e.target.value)
    this.setState({
      language:e.target.value
    })
  }
  // 中英切换
  tabChinese=(tabChinese)=>{
    this.setState({
      language1:1
    })
    document.getElementById('pageView').scrollTop = 0;
  }
  //英文点击事件
  tabEnglish=(tabEnglish)=>{
    this.setState({
      language1:2,
    })
    document.getElementById('pageView').scrollTop = 0;
  }
  //下载知情同意书模板
  downMrcByCode = () => {
    const { dispatch } = this.props;
    let params = {
      id:this.state.agreId, //模板id
      language:this.state.language==1?'cn':this.state.language==2?'en':null
    };
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/downMrcByCodeService',
        payload: params,
        callback: (blob) => {
          let fileName = this.state.MrcMessageLeft.tmptName + '.zip';
          if (window.navigator.msSaveOrOpenBlob) {
            navigator.msSaveBlob(blob, fileName);
          } else {
            const link = document.createElement('a');
            const evt = document.createEvent('MouseEvents');
            link.style.display = 'none';
            link.href = window.URL.createObjectURL(blob);
            link.download = fileName;
            document.body.appendChild(link); // 此写法兼容可火狐浏览器
            evt.initEvent('click', false, false);
            link.dispatchEvent(evt);
            document.body.removeChild(link);
          }
          this.setState({
            language:this.state.language
          })
        },
      });
    }
  };
  //上传
  handleBeforeUploadPDF = file => {
    const isSize = file.size / 1024 / 1024 < 15;
    if (!isSize) {
      Modal.error({
        title: '超过15M限制，不允许上传~',
      });
      return false;
    }
    const isType = file.type.indexOf("image")!=-1;
    if (!isType) {
      Modal.error({
        title: '只能上传图片文件~',
      });
      return false;
    }
    return isType;
  };
  //上传文件移除
  fileRemove = (file)=>{
    let fileList = this.state.fileList;
    for (let i in fileList) {
      if (file.uid === fileList[i].uid) {
        fileList.splice(i, 1);
        this.setState({
          fileList: fileList,
        });
        return;
      }
    }
    // console.log("&&&&&删除",this.state.fileList)
  }
  //上传文件
  handleChangeImg = info => {
    let fileList = info.fileList;
    // console.log("哈哈哈哈哈==",JSON.stringify(fileList))
    if (info.file.status === 'done') {
      if (info.file.response.code === 200) {
        info.file.thumbUrl = info.file.response.content.fileUrlView;
        message.success({
          content: '上传成功',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
      } else {
        fileList.forEach((text,index)=>{
          if(info.file.uid==text.uid){
            fileList.splice(index, 1);
            this.setState({
              fileList: fileList,
            });
          }
        })
        message.error({
          content: '上传失败，请上传png、jpg图片文件',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
      }
    }
    if(!info.file.status){
      fileList.forEach((text,index)=>{
        if(info.file.uid==text.uid){
          fileList.splice(index, 1);
          this.setState({
            fileList: fileList,
          });
        }
      })
    }
    this.setState({ fileList });
  };
  //查看大图
  LookImg=(url)=>{
    this.setState({
      Imgvisible:true,
      bigImgurl: url,
    });
  }
  render() {
    const {BookStatus,InforList,MrcMessage,MrcMessageLeft,fileList,Imgvisible,bigImgurl}=this.state;
    //上传参数数据
    const props = {
      action: `/api/medical/ossFile/uploadFile?tenantId=`+localStorage.getItem('tenantId')+`&fileType=3`,//接口路径
      multiple: false,//支持多个文件
      headers: {
        access_token: localStorage.getItem('access_token'),
        client: 'PC',
        username:localStorage.getItem("username")
      },
    }
    return (
      <GridContent>
        <Row gutter={40}>
          <Col className="gutter-row" span={12}>
            <div>
              <div className={commonStyle.font_16}>1.下载知情同意书模板</div>
              <div className={styles.AllStyle}>
                <div className={styles.titleStyle}>同意书类型</div>
                <div className={styles.chooseStyle}>
                  <TreeSelect
                    treeDefaultExpandAll={false}
                    style={{ width: '270px' }}
                    value={this.state.value}
                    dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                    dropdownClassName={styles.resource_tree}
                    treeData={InforList}
                    placeholder="请选择"
                    onChange={this.onChange}
                  />
                </div>
              </div>
              <div className={styles.AllStyle}>
                <div className={styles.titleStyle} style={{textAlign:'right',paddingRight:'5.5%',lineHeight:'28px'}}>语言</div>
                <div className={styles.chooseStyle}>
                  <Radio.Group
                    key={this.state.language}
                    defaultValue = {this.state.language}
                    onChange={this.LanguageChange}
                  >
                    <Radio value={1}>中文</Radio>
                    <Radio value={2}>英文</Radio>
                  </Radio.Group>
                </div>
              </div>

              {MrcMessageLeft.tmptCode?<>
                <div
                  style={{position:'relative'}}
                  onMouseOver={()=>this.preview(true)}
                  onMouseOut={()=>this.preview(false)}
                  className={styles.cardBorder}>
                  <img
                    className={styles.imgStyle}
                    src={tys}
                  />
                  <div
                    hidden={!this.state['previewStatus']}
                    className={styles.ctimgdelete}
                  >
                    <div style={{marginTop:'20%'}}>
                      <div style={{cursor:'pointer'}} onClick={this.previewBook}>
                        <img src={Preview} className={styles.icon_delete} style={{marginLeft:8}} alt=""/>
                        <span className={styles.deleteFont}>预览</span>
                      </div>
                    </div>
                  </div>
                  <Button
                    onClick={this.downMrcByCode.bind(this,)}
                    type="primary"
                    className={styles.btnStyle}>下载模板</Button>
                </div>
              </>:""}


            </div>
          </Col>
          <Col className="gutter-row" span={12}>
            <div>
              <div className={commonStyle.font_16}>2.上传知情同意书</div>
              <div className={styles.AllStyle}>
                <div className={styles.titleStyle}>同意书类型</div>
                <div className={styles.chooseStyle}>
                  <TreeSelect
                    style={{ width: '270px' }}
                    value={this.state.value}
                    dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                    dropdownClassName={styles.resource_tree}
                    treeData={InforList}
                    placeholder="请选择"
                    // treeDefaultExpandAll
                    onChange={this.uploadonChange}
                  />
                </div>
              </div>
              <div className={styles.uploadStyle}>
                <Upload
                  {...props}
                  fileList={this.state.fileList}
                  beforeUpload={this.handleBeforeUploadPDF}
                  onRemove={this.fileRemove}
                  multiple={true}
                  maxCount={10}
                  onChange={this.handleChangeImg}
                  showUploadList={false}
                >
                  <Button icon={<UploadOutlined/>}>上传文件</Button>
                </Upload>
              </div>
              <div>
              {(fileList || []).map((item, index) => (
                <div key={index}>
                  <Spin spinning={item.status === 'uploading'}>
                    <div
                      className={styles.cardBorderRight}
                      style={{height:152,position:'relative'}}
                      onMouseOver={()=>this.onMouseIn(true,item.uid)}
                      onMouseOut={()=>this.onMouseIn(false,item.uid)}
                    >
                      <img src={item.thumbUrl} className={styles.imgStyle} alt=""/>
                      <div
                        hidden={!this.state['mouseIn'+item.uid]}
                        className={styles.ctimgdelete}
                      >
                        <div style={{marginTop:'20%',display:'flex'}}>
                          <div style={{cursor:'pointer'}} onClick={this.LookImg.bind(this,item.thumbUrl)}>
                            <img src={Preview} className={styles.icon_delete} style={{marginLeft:60}} alt=""/>
                            <span className={styles.deleteFont}>预览</span>
                          </div>
                          <Popconfirm
                            title="确定删除?"
                            onConfirm={() => this.fileRemove(item)}
                            okText="是"
                            cancelText="否"
                          >
                            <div style={{cursor:'pointer'}}>
                              <img src={Delete} className={styles.icon_delete} style={{marginLeft:10}} alt=""/>
                              <span
                                className={styles.deleteFont}>删除</span>
                            </div>
                          </Popconfirm>
                        </div>
                      </div>
                    </div>
                  </Spin>
                </div>

              ))}
              </div>
            </div>
          </Col>
        </Row>
        <Image
          width={200}
          style={{
            display: 'none',
          }}
          preview={{
            visible:Imgvisible,
            src:bigImgurl,
            onVisibleChange: (value) => {
              this.setState({
                Imgvisible:false
              })
            },
          }}
        />
        <Modal
          width={790}
          title="知情同意书预览"
          visible={BookStatus}
          onOk={this.ModalOK}
          onCancel={this.ModalCancel}
          cancelText="取消"
          maskClosable={false}
          footer={[]}
        >
          <div>
            <div className={styles.contentTop}>
              <div>{MrcMessageLeft.tmptName?MrcMessageLeft.tmptName:""}</div>
              <div className={styles.Intertranslation}>
                {this.state.language1==1?
                  <div
                    onClick={()=>this.tabChinese(false)}
                    className={styles.Choose}>中</div>:
                  <div
                    onClick={()=>this.tabChinese(true)}
                    className={styles.notChoose}>中</div>
                }
                {this.state.language1==2?
                  <div
                    onClick={()=>this.tabEnglish(false)}
                    className={styles.Choose}>英</div>:
                  <div
                    onClick={()=>this.tabEnglish(true)}
                    className={styles.notChoose}>英</div>
                }
              </div>
            </div>
            <div
              id="pageView"
              className={styles.contentBook}
            >
              { this.state.language1==1 && MrcMessageLeft.tmptCont&& MrcMessageLeft.tmptCont!==" "?
                <div
                  dangerouslySetInnerHTML = {{ __html:MrcMessageLeft.tmptCont}}
                ></div>:this.state.language1==2 && MrcMessageLeft.tmptContEn&& MrcMessageLeft.tmptContEn!==" "?
                  <div
                    dangerouslySetInnerHTML = {{ __html:MrcMessageLeft.tmptContEn}}
                  ></div>:
                    <div className={commonStyle.nodataContent} style={{marginTop:'25%'}}>
                      <img src={noData} className={commonStyle.imgStyle} alt=""/>
                      <div className={commonStyle.fontStyle}>暂无数据</div>
                    </div>
              }
            </div>
          </div>
        </Modal>
      </GridContent>
    );
  }
}
export default connect(({ }) => ({}))(InforConsent);
