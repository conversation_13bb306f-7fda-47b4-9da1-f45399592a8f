import React, {Component} from 'react';
import {Card,Radio,Popover,Tooltip} from 'antd';
import PropTypes from 'prop-types';
import $ from 'jquery';
import moment from 'moment';
import styles from './index.less'
import classNames from 'classnames';
/**
 * comfortTreatment,       // 舒适治疗
   crown,                  // 戴冠
 */
const colorField = {
  toothExtraction:   { color:'#CD3700',textIcon:'拔牙' },
  implantSurgery:    { color:'#800080',textIcon:'种植手术' },
  implantTwice:      { color:'#0000C6',textIcon:'种植二期修复'},
  orthodontic:       { color:'#800080',textIcon:'正畸' },
  repairCrown:       { color:'#800080',textIcon:'修复' },
  rootCanal:         { color:'#800080',textIcon:'根管' },
  teethWhitening:    { color:'#CD3700',textIcon:'冷光美白' },
  lab:               { color:'#22605F',textIcon:'LAB' },
  emergency:         { color:'#F62929',textIcon:'急诊' },
  hitSupport:        { color:'#800080',textIcon:'打支抗' },
  firstOrthodontics: { color:'#800080',textIcon:'正畸初戴' },
  platinumCard:      { color:'#F03BF0',textIcon:'招白金' },
  bankPlatinumCard:  { color:'#F03BF0',textIcon:'银行白金卡' },
  policyHolder:      { color:'#F62929',textIcon:'保险' },
  microscopicRootCanal: {color:'#0000C6',textIcon:'显微镜根管'},
  childrenBound:        {color:'#800080',textIcon:'儿童束缚'},
  toothWhitening:       {color:'#CD3700',textIcon:'牙齿美白'},
  regularCustomerVisit: {color:'#4BA2FF',textIcon:'老客户回访'},
  regularInspection:    {color:'#0000C6',textIcon:'三个月定检'},
  comfortTreatment:     {color:'#F03BF0',textIcon:'舒适治疗'},
  crown:       {color:'#800080',textIcon:'戴冠'},
  majorTreatment:       {color:'#F62929',textIcon:'重大治疗'},
  basicTreatment:       {color:'#4200FF',textIcon:'基础治疗'},

};

/*let {
      basicTreatment,    //  指定客服大客户
      bigCustomer,       //  指定客服大客户
      emergency,         //  急诊
      implantSurgery,    //  种植手术
      implantTwice,      //  种植二期
      lab,               //  lab
      majorTreatment,    //  重大治疗
      orthodontic,       //  正畸
      platinumCard,      //  招商银行白金卡
      policyHolder,      //  MSH保险客户
      repairCrown,       //  修复戴冠
      rootCanal,         //  根管
      teethWhitening,    //  冷光美白
      vipClient,         //  VIP客户
      hitSupport,        //  打支抗
      toothExtraction,   //  拔牙
      firstOrthodontics, //  正畸初戴
      voucher,
} = appointmentIconDto || {}*/


/**
 * 手工标签组件
 */
export default class EventElement extends Component {
  static propTypes = {
    isSelect:PropTypes.bool,
    color:PropTypes.string,
    textIcon:PropTypes.string,
    shortcutField:PropTypes.string,
  }

  static defaultProps = {
    isSelect:true,
    color:'#CCC',
    textIcon:'',
    shortcutField:null
  }

  render() {
    let styleObj = this.props.isSelect ? {
      borderColor:this.props.color,
      background:this.props.color,
      color:'#fff'
    } : {
      borderColor:this.props.color,
      color:'#444'
    }

    // 是否有快捷字段颜色 如果没有沿用传入color手工标签颜色
    if(colorField[this.props.shortcutField]){
      styleObj = this.props.isSelect ? {
        borderColor:colorField[this.props.shortcutField].color,
        background:colorField[this.props.shortcutField].color,
        color:'#fff'
      } : {
        borderColor:colorField[this.props.shortcutField].color,
        color:'#444'
      }
    }

    return (
      <>
          <div
            className={classNames({
              [styles.Icon]: true,
              [styles.selectIcon]: this.props.isSelect
            })}
            style={styleObj}
          >
            {colorField[this.props.shortcutField] ? colorField[this.props.shortcutField].textIcon : this.props.textIcon}
          </div>

      </>
    )
  }
}
