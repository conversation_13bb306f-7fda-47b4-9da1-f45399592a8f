import { StringUtils } from "@/utils/StringUtils";

const { GridContent } = require('@ant-design/pro-layout');

import React, { Component, useState } from 'react';
import {
  Row,
  Col,
  Collapse,
  Tooltip,
  Form,
  Input,
  Select,
  Radio,
  Modal,
  DatePicker,
  Checkbox,
  InputNumber,
  Button,
  message, Spin
} from 'antd';
import { PrinterOutlined } from '@ant-design/icons';
//引入样式
import styles from './style.less';
import commonStyle from "@/pages/common.less";

import { connect } from 'dva';
import moment from 'moment';
import { hisOptionType } from '@/utils/common.js';
import pathToRegexp from 'path-to-regexp';
import noData from "@/assets/<EMAIL>";//图标

const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;

// 国籍列表
const nationalityList = [
  {
    label: '中国',
    value: '中国',
  },
  {
    label: '英国',
    value: '英国',
  }, {
    label: '法国',
    value: '法国',
  }, {
    label: '美国',
    value: '美国',
  }, {
    label: '俄国',
    value: '俄国',
  }, {
    label: '德国',
    value: '德国',
  }, {
    label: '日本',
    value: '日本',
  }, {
    label: '韩国',
    value: '韩国',
  }, {
    label: '意大利',
    value: '意大利',
  },
  {
    label: '其他',
    value: '其他',
  },
]
/**客户信息编辑form表单控制布局**/
const modalLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 18 },
};
// 既往病史form表单布局
const indexLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 20 },
};

//时间格式
const dateFormat = "YYYY-MM-DD";
class MedicalRecordHomePage extends React.Component {
  infoFormRef = React.createRef();

  constructor(props) {
    super(props);
    this.state = {
      disabledBtn: false,// 提交按钮是否置灰
      loading: false,
      baseInfoLonding: false,//客户信息加载
      tabsAll: true, //全部病历
      fileModal: false, // 归档弹窗
      infoModalVisible: false, //客户信息弹窗
      historyModalVisible: false, // 历史记录弹窗
      healthyModalVisible: false, //健康状态弹窗
      // value: 'male', //所选性别

      modalForm: {  //客户信息表单信息
      },
      editHealthConditionParams: { //编辑健康状况数据及回显内容 的参数
        tenantId: localStorage.getItem('tenantId'), //平台标识
        customerId: props.patientInfo ? props.patientInfo.patientId : '1645',  //患者
        organizationId: localStorage.getItem('organizationId') //机构id
      },
      healthConditionParams: {
        //健康状况参数
        tenantId: localStorage.getItem('tenantId'), //平台标识
        organizationId: localStorage.getItem('organizationId'), //机构标识
        customerId: props.patientInfo ? props.patientInfo.patientId : null, //客户标识
      },
      editHistoryData: [], // 回显健康状况的数据
      newEditHistoryData: [], //修改需保存的健康状况数据
      saveHealthConditionParams: {  //保存健康数据的参数
      },
      // defaultActiveKey:[],// 历史记录的折叠面板的key
      historyOptionsList: [], //历史操作记录
      historyOptionsParams: { //历史操作的入参
        tenantId: localStorage.getItem('tenantId'),
        organizationId: localStorage.getItem('organizationId'),
        customerId: props.patientInfo ? props.patientInfo.patientId : null,//患者id
        userId: localStorage.getItem('userId'),
        userName: localStorage.getItem('userName')
      },
      basicByIdInfo: {},//客户信息
      patientInfo: props.patientInfo, //患者信息
      rightPatientInfos: props.rightPatientInfos, //患者信息
      healthCondition: {}//健康状况
    };
  }
  // 初始化
  componentDidMount() {
    if (this.props.patientInfo) {
      this.findCustomerBasicById();//病历首页-患者信息
      this.getPatHealthCondition();// 请求病历首页--患者健康状况
    }

  }
  //监听数据变化
  // eslint-disable-next-line react/no-deprecated
  componentWillReceiveProps(props) {
    if (props.patientInfo && (!this.props.patientInfo || props.patientInfo.patientId != this.props.patientInfo.patientId)) {
      this.refreshShoeList(props);
    }
  }
  //刷新数据变化
  refreshShoeList = (props) => {
    this.setState({
      loading: false,
      baseInfoLonding: false,//客户信息加载
      patientInfo: props.patientInfo,//患者信息
      rightPatientInfos: props.rightPatientInfos,//患者信息
      patientInfoDto: props.patientInfoDto,//患者信息
      basicByIdInfo: {},//病历首页-患者信息
      historyOptionsParams: { //历史操作的入参
        tenantId: localStorage.getItem("tenantId"),
        organizationId: localStorage.getItem("organizationId"),
        customerId: props.patientInfo ? props.patientInfo.patientId : null,//患者id
        userId: localStorage.getItem("userId"),
        userName: localStorage.getItem("userName")
      },
      modalForm: {  //客户信息表单信息
        customerId: props.rightPatientInfos.patientId,//患者id
        name: props.rightPatientInfos.name,//患者名称
        sex: props.rightPatientInfos.sex,//患者性别
        telPhone: props.rightPatientInfos.telPhone,//患者电话
        birthday: props.rightPatientInfos.birthday,//患者生日
        nationality: props.patientInfoDto.nationality,//患者国际
      }
    }, () => this.findCustomerBasicById(), this.getPatHealthCondition());

  }
  // 打开编辑客户信息弹窗
  editInfo = () => {
    const { basicByIdInfo } = this.state;
    this.setState({
      infoModalVisible: true,
      modalForm: {
        ...basicByIdInfo,
        customerId: basicByIdInfo.id
      }
    }, () => {
      // 设置form表单的值，防止使用规则校验时出错
      this.infoFormRef.current.setFields([{name: 'oftenTel', value: basicByIdInfo.oftenTel}])
    });
  };


  //病历首页-患者信息
  findCustomerBasicById = () => {
    const { dispatch } = this.props;
    let { historyOptionsParams, patientInfo } = this.state;
    historyOptionsParams.customerId = patientInfo.patientId;

    this.setState({
      baseInfoLonding: true
    });
    if (dispatch) {
      dispatch({
        type: 'homePage/findCustomerBasicByIdService',
        payload: historyOptionsParams,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              baseInfoLonding: false,
              basicByIdInfo: res.content,
            });
          }
        },
      });
    }
  };


  // 请求病历首页--患者健康状况
  getPatHealthCondition = () => {
    const { dispatch } = this.props;
    let params = this.state.healthConditionParams;
    this.setState({
      loading: true
    });
    if (dispatch) {
      dispatch({
        type: 'homePage/patHealthCondition',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              loading: false,
              healthCondition: res.content,
            });
          }
        },
      });
    }
  };

  // 保存客户信息
  saveHideModal = () => {
    // 保存数据再关闭弹窗
    const { modalForm } = this.state;
    if (!StringUtils.isNotEmpty(modalForm.name)) {
      message.warning({
        content: '请输入姓名',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if (!StringUtils.isNotEmpty(modalForm.sex)) {
      message.warning({
        content: '请选择性别',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if (!StringUtils.isNotEmpty(modalForm.birthday)) {
      message.warning({
        content: '请选择出生日期',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if (!StringUtils.isNotEmpty(modalForm.oftenTel)) {
      message.warning({
        content: '请输入电话',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    // if (!StringUtils.isNotEmpty(modalForm.nationality)) {
    //   message.warning({
    //     content: '请选择国籍',
    //     className: 'custom-class',
    //     style: {
    //       marginTop: '20vh',
    //     },
    //   });
    //   return;
    // }
    if (!StringUtils.isNotEmpty(modalForm.oftenTel)) {
      message.warning({
        content: '请输入电话',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    // 电话格式校验
    if (modalForm.telType == 1 && !/^1[3456789]\d{9}$/.test(modalForm.oftenTel)) {// 中国大陆
      message.warning({
        content: '请输入正确的手机号',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }

    if(modalForm.telType == 2 && !/^\d{7,12}$/.test(modalForm.oftenTel)) {// 小灵通
      message.warning({
        content: '请输入7-12位数字',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }

    if(modalForm.telType == 3 && !/^\d{6,20}$/.test(modalForm.oftenTel)) { // 其他类型手机号
      message.warning({
        content: '请输入6-20位数字',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }

    // if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(modalForm.oftenTel) && (!/^\d+-\d+$/.test(String(modalForm.oftenTel)) && modalForm.oftenTel.length > 16)) {
    //   message.warning({
    //     content: '电话格式错误！',
    //     className: 'custom-class',
    //     style: {
    //       marginTop: '20vh',
    //     },
    //   });
    //   return;
    // }
    // 保存数据方法
    this.formInfoFinish();
  };

  // 关闭客户信息弹层
  hideModal = (modal) => {
    this.setState({
      infoModalVisible: false,
    });
    this.onReset()
  };

  // 表单重置
  onReset = () => {
    this.infoFormRef.current.resetFields();
  };
  // 关闭历史记录弹窗
  historyHideModal = () => {
    // const { historyModalVisible, } = this.state;
    this.setState({
      historyModalVisible: false,
    });
  };
  // 查看历史操作弹窗
  seeHistoryOptions = () => {
    const { historyOptionsParams } = this.state;
    this.setState({
      historyModalVisible: true,
    });

    const { dispatch } = this.props;
    if (dispatch) {
      dispatch({
        type: 'homePage/historyByCustomerId',
        payload: historyOptionsParams,
        callback: (res) => {
          if (res.code == 200) {
            // 返回成功
            res.content.map(item => {
              let sort = 0
              // 为每项数据添加序号
              item.record && item.record.map(recordItem => {
                recordItem.details && recordItem.details.map(detailItem => {
                  detailItem.sort = ++sort
                })
              })
            })

            // console.log(res.content,'00000000---')
            this.setState({
              historyOptionsList: res.content
            });
          } else {
            message.error({
              content: '请求失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
          }
          this.setState({
            // loading: false
          })
        }
      })
    }
  };
  // 编辑健康状况弹窗
  editHealthy = () => {
    // const { healthyModalVisible } = this.state;
    this.setState({
      healthyModalVisible: true,
      editHistoryData: []
    });
    this.editHealthCondition();
  };
  // 获取健康状况接口
  editHealthCondition = () => {
    const { dispatch } = this.props
    const { editHealthConditionParams, } = this.state;
    this.setState({
      loading: true
    })
    if (dispatch) {
      dispatch({
        type: 'homePage/editHealthCondition',
        payload: editHealthConditionParams,
        callback: (res) => {
          if (res.code == 200) {
            let dataHaveChild = JSON.parse(JSON.stringify(res.content.primaryCustomerArchivesDtos)); // []
            // console.log(res.content.primaryCustomerArchivesDtos, dataHaveChild, '99999-----------')
            // 将多选项中含可手动输入的项添加到多选项的对象中
            dataHaveChild.map(item => {
              let associateArchivesDictList = []
              // // 单选
              // if (item.classification == 1) {
              // if (item.associateArchivesDictList != null) {
              //   // let childItem = []
              //   // let newData = []
              //   const { editHistoryData, newEditHistoryData } = this.state;
              //   // newData = JSON.parse(JSON.stringify(res.content.primaryCustomerArchivesDtos))
              //   // 将子集加到最外层
              //   item.associateArchivesDictList.map(childItem => {
              //     // delete item.associateArchivesDictList
              //     dataHaveChild.push(childItem)
              //     // console.log(childItem, newData,' newData999----=')
              //   })
              // }
              if (item.id == 22) {
                // 补充您是否还有任何疾病
                dataHaveChild.map(newItem => {
                  if (newItem.id == 23) {
                    associateArchivesDictList.push(newItem)
                    item.associateArchivesDictList = associateArchivesDictList
                  }
                })

              } else if (item.id == 25) {
                // 补充您是否还有过敏史
                dataHaveChild.map(newItem => {
                  if (newItem.id == 26) {
                    associateArchivesDictList.push(newItem);
                    item.associateArchivesDictList = associateArchivesDictList
                  }
                })
              } else if (item.id == 27) {
                // 补充您是否还有在服药物
                dataHaveChild.map(newItem => {
                  if (newItem.id == 28) {
                    associateArchivesDictList.push(newItem);
                    item.associateArchivesDictList = associateArchivesDictList
                  }
                })
              }
              // dataHaveChild.map()
              if (item.classification == 1) {
                if (item.selectStatus == 2) {
                  // 做了修改  // 选了是
                  // console.log(item, item.secondaryCustomerArchivesDtoList, '单选的项999----')

                  if (item.secondaryCustomerArchivesDtoList.length > 0) {
                    // for (let childItem of item.secondaryCustomerArchivesDtoList) {
                    // console.log(childItem, '000000---')
                    // 是
                    if (item.secondaryCustomerArchivesDtoList[0].selectStatus == 2) {
                      item.selectStatus = 2
                      item.secondaryId = item.secondaryCustomerArchivesDtoList[0].id
                    } else if (item.secondaryCustomerArchivesDtoList[1].selectStatus == 2) {
                      // 否
                      item.selectStatus = 1
                      item.secondaryId = item.secondaryCustomerArchivesDtoList[1].id
                    }

                    // }
                  }

                } else {
                  // 默认是否
                  if (item.secondaryCustomerArchivesDtoList.length > 0) {
                    for (let childItem of item.secondaryCustomerArchivesDtoList) {
                      // console.log(childItem, '000000---')
                      if (item.secondaryCustomerArchivesDtoList[1].selectStatus == 2) {
                        item.selectStatus = 1
                        item.secondaryId = item.secondaryCustomerArchivesDtoList[1].id
                      }

                    }
                  }
                }
              }
            })
            // console.log(dataHaveChild, '99999-----充足的editHistoryData')
            this.setState({
              editHistoryData: dataHaveChild, //res.content.primaryCustomerArchivesDtos, // []
            }, () => {
              let newData = []
              const { editHistoryData, newEditHistoryData } = this.state;
              newData = JSON.parse(JSON.stringify(res.content.primaryCustomerArchivesDtos))
              // newData = JSON.parse(JSON.stringify(editHistoryData))
              newData.map(item => {
                // if (item.classification ==2){
                //   item.associateArchivesDictList && item.associateArchivesDictList.map(child => {
                //     newData.push(child)
                //   })
                // }

                // 获取到健康状况的多选项
                if (item.secondaryCustomerArchivesDtoList != null) {
                  let secondaryId = [];
                  item.secondaryCustomerArchivesDtoList.map(childItem => {
                    if (childItem.selectStatus == 2) {
                      secondaryId.push(childItem.id)
                      item.secondaryId = secondaryId.toString()
                      return item
                    }
                  })
                }
                if (item.classification == 1) {
                  // console.log(item, '单选的项999----')
                  if (item.selectStatus == 2) {
                    // 做了修改  // 选了是
                    if (item.secondaryCustomerArchivesDtoList.length > 0) {
                      // 是
                      if (item.secondaryCustomerArchivesDtoList[0].selectStatus == 2) {
                        item.selectStatus = 2
                        item.secondaryId = item.secondaryCustomerArchivesDtoList[0].id
                      } else if (item.secondaryCustomerArchivesDtoList[1].selectStatus == 2) {
                        // 否
                        item.selectStatus = 1
                        item.secondaryId = item.secondaryCustomerArchivesDtoList[1].id
                      }
                    }
                  } else {
                    // 默认是否
                    if (item.secondaryCustomerArchivesDtoList.length > 0) {
                      for (let childItem of item.secondaryCustomerArchivesDtoList) {

                        item.selectStatus = 1
                        item.secondaryId = item.secondaryCustomerArchivesDtoList[1].id
                      }
                    }
                  }
                }
              })
              // console.log(newData, '99999-----充足的newnewnewHistoryData')

              this.setState({
                newEditHistoryData: newData
              },
                // () => console.log(this.state.newEditHistoryData, this.state.editHistoryData, 'newEditHistoryData11111')
              )
            })
            this.setState({
              loading: false
            })
          } else {
            message.error({
              content: '获取健康状况失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              loading: false
            })
          }

        }
      });
    }

  };
  // 保存健康状况
  savehealthyHideModal = () => {  //editHealthCondition
    const { dispatch } = this.props
    let customerArchivesFroms = [];

    let dataHaveChild = JSON.parse(JSON.stringify(this.state.newEditHistoryData));
    // 将多选项中含可手动输入的项添加到多选项的对象中
    dataHaveChild.map(item => {
      let associateArchivesDictList = []
      if (item.id == 22) {
        // 补充您是否还有任何疾病
        dataHaveChild.map((newItem, newIndex) => {
          if (newItem.id == 23) {
            associateArchivesDictList.push(newItem)
            item.associateArchivesDictList = associateArchivesDictList;
            dataHaveChild.splice(newIndex, 1)
          }
        })
      } else if (item.id == 25) {
        // 补充您是否还有过敏史
        dataHaveChild.map((newItem, newIndex) => {
          if (newItem.id == 26) {
            associateArchivesDictList.push(newItem);
            item.associateArchivesDictList = associateArchivesDictList
            dataHaveChild.splice(newIndex, 1)
          }
        })
      } else if (item.id == 27) {
        // 补充您是否还有在服药物
        dataHaveChild.map((newItem, newIndex) => {
          if (newItem.id == 28) {
            associateArchivesDictList.push(newItem);
            item.associateArchivesDictList = associateArchivesDictList
            dataHaveChild.splice(newIndex, 1)
          }
        })
      }
    })


    // console.log(dataHaveChild, '99999要保存的数据')
    for (let from of dataHaveChild) {
      // 选中“是”或者多选的项
      if (from.classification == 2 && from.id > 21) {
        if (from.selectStatus == 2) {
          if (StringUtils.isNotBlank(from.secondaryId)) {
            //选了多选项的是 并且有多选
            let secondaryIds = from.secondaryId.split(',');
            for (let id of secondaryIds) {
              customerArchivesFroms.push({
                type: (from.type).toString(),
                primaryId: (from.id).toString(),
                secondaryId: id,
                customerId: this.props.patientInfo.patientId,
              })
              from.secondaryCustomerArchivesDtoList && from.secondaryCustomerArchivesDtoList.map(item=>{
                // 选中多选项后输入框中输入信息
                if(item.id == id && item.inputText){
                  customerArchivesFroms.map(child=>{
                    child.secondaryId == id ? child.inputText = item.inputText : child
                  })
                }
              })
            }
            // console.log('9999999999999多选项的数据', customerArchivesFroms)
          } else {
            // 选了是，但是没有选择多选
            customerArchivesFroms.push({
              type: (from.type).toString(),
              primaryId: (from.id).toString(),
              secondaryId: '',
              customerId: this.props.patientInfo.patientId,
            })
          }
          // 子集中是否有额外输入的信息
          if (from.associateArchivesDictList) {
            for (let childItem of from.associateArchivesDictList) {
              // console.log(from.secondaryId, '>>>>>>没有输入')
              // 子集中是否有额外输入的信息
              if (childItem.classification == 4 && (childItem.inputText != null && childItem.inputText != '' && childItem.inputText != undefined)) {
                customerArchivesFroms.push({
                  type: (childItem.type).toString(),
                  primaryId: (childItem.id).toString(),
                  customerId: this.props.patientInfo.patientId,
                  inputText: childItem.inputText
                })
              }

              // else if (StringUtils.isBlank(from.secondaryId)) {
              //   // 选了是,但是没有多选 也没有输入信息的提示
              //   return message.warning({
              //     content: `${from.dictCount}如没有，请选择无或否`,
              //     className: 'custom-class',
              //     style: {
              //       marginTop: '20vh',
              //     },
              //   })
              // }
            }
          }
        }
      }
      else if (from.classification == 1) {
        // 有更新 选中
        if (from.selectStatus == 2) {
          customerArchivesFroms.push({
            type: (from.type).toString(),
            primaryId: (from.id).toString(),
            customerId: this.props.patientInfo.patientId,
            secondaryId: from.secondaryId  //(secondaryItem[0].id).toString()  //from.secondaryItem
          })
          // 单选项并且有其他内容
          if (from.associateArchivesDictList != null) {
            from.associateArchivesDictList.map(assoItem => {
              // console.log(assoItem, 'assoItem999------')
              if ((assoItem.inputText != null && assoItem.inputText != '' && assoItem.inputText != undefined)) {

                // console.log('from----bushi 选中的', from )
                // 多选项中额外输入的信息
                customerArchivesFroms.push({
                  type: (from.type).toString(),
                  primaryId: (assoItem.id).toString(),
                  customerId: this.props.patientInfo.patientId,
                  inputText: assoItem.inputText
                })
              }
            })
          }
        }
        // else {
        //   // 选中‘否’
        //   customerArchivesFroms.push({
        //     type: (from.type).toString(),
        //     primaryId: (from.id).toString(),
        //     customerId: this.props.patientInfo.patientId,
        //     secondaryId: from.secondaryId  //(secondaryItem[1].id).toString()
        //   })
        // }
      }
    }


    // console.log('要保存的数据newEdit->', this.state.newEditHistoryData, 'customerArchivesFroms-->', customerArchivesFroms)

    // return
    this.state.saveHealthConditionParams = {
      tenantId: localStorage.getItem('tenantId'),
      organizationId: localStorage.getItem('organizationId'),
      userId: localStorage.getItem('userId'),
      userName: localStorage.getItem('userName'),
      content: {
        customerId: this.props.patientInfo.patientId,
        customerArchivesFroms: customerArchivesFroms
      }
    };
    const { saveHealthConditionParams } = this.state;
    this.setState({
      loading: true,
      disabledBtn: true
    })
    if (dispatch) {
      dispatch({
        type: 'homePage/saveHealthCondition',
        payload: saveHealthConditionParams,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '编辑成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.getPatHealthCondition();

          } else {
            message.error({
              content: '编辑失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              loading: false,
              disabledBtn: false,
            })
          }
          this.setState({
            loading: false,
            disabledBtn: false
          })
        }
      });
    }
    this.healthyHideModal();
  };
  // 关闭健康状况弹窗
  healthyHideModal = (modal) => {
    // const { healthyModalVisible, } = this.state;
    this.setState({
      healthyModalVisible: false,
    });
    // this.onReset();
  };


  // 一级分类 是否 单选按钮
  medicalHistoryChange = (e, type, id,) => {
    console.log(e.target.value, id, '-------------+++++++=')
    const { newEditHistoryData, editHistoryData } = this.state;

    this.setState({
      editHistoryData: editHistoryData.map((item, index) => {
        if (item.classification == 1) {
          // d单选按钮 选了否
          if (e.target.value == 1 && item.id == id) {
            item.selectStatus = 1
            item.associateArchivesDictList && item.associateArchivesDictList.map(assoItem => {
              assoItem.inputText = null
            })
          }
          return item.id == id ? {
            ...item,
            primaryId: id,
            selectStatus: e.target.value,
          } : item
        }
        else {
          return item.id == id ? {
            ...item,
            primaryId: id,
            selectStatus: e.target.value,
            inputText: e.target.value == 1 ? '' : item.inputText
          } : item
        }
      }),
      newEditHistoryData: newEditHistoryData.map((item, index) => {
        if (item.classification == 1) {
          // d单选按钮
            // 点击单选项时获取是 否的id
            if (item.id == id) {
              item.secondaryCustomerArchivesDtoList.map(secondItem => {
                if (e.target.value == 2) {
                  item.secondaryId = item.secondaryCustomerArchivesDtoList[0].id.toString()
                } else {
                  item.associateArchivesDictList && item.associateArchivesDictList.map(assoItem => {
                    assoItem.inputText = null
                  })
                  item.secondaryId = item.secondaryCustomerArchivesDtoList[1].id.toString()
                }
              })
            }

          return item.id == id ? {
            ...item,
            primaryId: id,
            selectStatus: 2,
          } : item
        } else {
          return item.id == id ? {
            ...item,
            primaryId: id,
            selectStatus: e.target.value,
            inputText: e.target.value == 1 ? '' : item.inputText,
            secondaryId: e.target.value == 1 ? "" : item.secondaryId
          } : item
        }
      })
    },
      () => console.log('单选选择完后111',
      // this.state.editHistoryData,
       this.state.newEditHistoryData)
    );

  };

  // 选择病史
  changeMedicalHistory = (checkedMedicalHistoryValue, id, type) => {
    // console.log('checked =选择病史 ', id, type, checkedMedicalHistoryValue);

    const { newEditHistoryData, editHistoryData } = this.state;
    this.setState({
      editHistoryData: editHistoryData.map((item, index) => {
        // console.log(item.id, '9999---')
        return item.id == id ? {
          ...item,
          secondaryId: checkedMedicalHistoryValue.toString(),
        } : item
      }),
      newEditHistoryData: newEditHistoryData.map((item, index) => {
        // console.log(item.id, '9999---')
        return item.id == id ? {
          ...item,
          secondaryId: checkedMedicalHistoryValue.toString(),
        } : item
      })
    },
      // () => console.log('选择病史后多选算则111', this.state.newEditHistoryData,)
    );

  };

  // 补充填写事件
  changeInput = (value, id) => {
    const { editHistoryData, newEditHistoryData } = this.state;

    // console.log(value, id, newEditHistoryData, typeof value, '多久啊分离焦虑------')

    this.setState({
      // medicalValue: e.target.value,
      editHistoryData: editHistoryData.map((item, index) => {
        // console.log(item.id, '9999---')
        return item.id == id ? {
          ...item,
          selectStatus: value ? 2 : 1,
          inputText: value ? value.toString() : '',  //防止怀孕周数 value为null
        } : item
      }),
      newEditHistoryData: newEditHistoryData.map((item) => {
        // console.log(item.id, '9999---')
        if (item.classification == 1) {
          // 从里边的自己中 修改
          // d单选按钮
          if (item.associateArchivesDictList != null) {
            // for (let childItem of item.associateArchivesDictList) {
            // 点击单选项时获取是 否的id
            item.associateArchivesDictList.map(secondItem => {
              if (secondItem.id == id) {
                secondItem.inputText = value ? value.toString() : ''
              }
            })
            // }
          }
          return item
        } else if(item.classification ==2){
          item.secondaryCustomerArchivesDtoList && item.secondaryCustomerArchivesDtoList.map(child=>{
            if(child.id == id){
              child.inputText = value ? value.toString() : '';
              // console.log(id,'999999999>>>>')
            }
          })
          return item
        }

        else{
          return item.id == id ? {
            ...item,
            selectStatus: value ? 2 : 1,
            inputText: value ? value.toString() : '',
            // selectStatus: e.target.value,
          } : item
        }
      })
    },
      // () => console.log('输入完后111', this.state.newEditHistoryData, this.state.editHistoryData)
    );
  }
  // 不可选择的日期
  disabledDate = (current) => {
    // Can not select days after today
    return current && current > moment().endOf('day');
  }
  // 保存客户信息
  formInfoFinish = (value) => {
    const { modalForm } = this.state;
    let params = {
      ...modalForm,
      tenantId: localStorage.getItem("tenantId"),
      organizationId: localStorage.getItem("organizationId"),
      userId: localStorage.getItem("userId"),
      userName: localStorage.getItem("userName"),
    }
    this.setState({
      disabledBtn: true
    })
    const { dispatch } = this.props
    if (dispatch) {
      dispatch({
        type: 'homePage/updateCustomerBasic',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            // console.log("保存客户信息===", JSON.stringify(res))
            // 调用查询客户信息接口
            this.findCustomerBasicById();//重新查询本页面的客户信息

            this.props.getTopPatientInfo(); //右上方信息重新请求
            // setTimeout(()=>{
            this.props.getTodayWaitInfo(); //今日就诊列表,数据有延迟，延迟两秒请求

            // },2000)
            // this.props.getPatientOverview();
            message.success({
              content: '编辑成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.hideModal();
          }
          else {
            message.error({
              content: '编辑失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
          }
          this.setState({
            disabledBtn: false
          })
        }
      });
    }
  };

  // 手机号校验规则
  phoneNumberValidator = (_, value) => {
    const { modalForm } = this.state;
    const getTelType = modalForm.telType;

    if(!value) return Promise.resolve();

    if(getTelType == 1 && value) { // 中国大陆
      if (/^1[3456789]\d{9}$/.test(value)) {
        return Promise.resolve();
      } else {
        return Promise.reject('请输入正确的手机号')
      }
    } else if(getTelType == 2 && value) { // 小灵通
      if(/^\d{7,12}$/.test(value)) {
        return Promise.resolve();
      } else {
        return Promise.reject('请输入7-12位数字');
      }
    } else if(getTelType == 3 && value) { // 其他
      if(/^\d{6,20}$/.test(value)) {
        return Promise.resolve();
      } else {
        return Promise.reject('请输入6-20位数字');
      }
    } else {
      return Promise.resolve();
    } 
  }

  // 健康状况
  healthFinish = (values) => {
    // console.log(values, '健康状况999');
  };
  render() {
    const { notOptions
    } = this.props;
    const {
      modalForm,
      infoModalVisible,
      historyModalVisible,
      healthyModalVisible,
      haveSurgeryDate,
      loading,
      disabledBtn,
      editHistoryData,
      historyOptionsList,
      basicByIdInfo, baseInfoLonding, healthCondition,
    } = this.state;

    // 手机号码类型模版
    const prefixSelector = 
      <Select 
       style={{ width: 100 }}
       defaultValue={modalForm.telType || 1}
       onChange={(value)=>{
        modalForm.telType = value;
        setTimeout(() => {
          this.infoFormRef.current.validateFields(['oftenTel']);
        })
      }}
      >
        <Option value={1}>中国大陆</Option>
        <Option value={2}>小灵通</Option>
        <Option value={3}>其他</Option>
      </Select>;
    
    // console.log(modalForm, basicByIdInfo, 'modalForm999999------')
    return (
      <GridContent>
        {/* 病历首页 */}
        <Spin spinning={baseInfoLonding} key={"baseInfoLonding"} tip={"加载中..."}>
          <Row className={styles.indexTop}>
            <p className={`${styles.topTitle} ${styles.width100}`}>{localStorage.getItem("organizationName")}</p>
            <p className={`${styles.secondTitle} ${styles.width100}`}>病历首页</p>
            <p className={`${styles.date} ${styles.width100}`}>
              <span style={{ paddingRight: '20px' }}>病历号：{basicByIdInfo.fileNumber}</span>
              <span>日期：{basicByIdInfo.createdGmtAt}</span>
            </p>
          </Row>
          {/* 客户信息 */}
          <Row className={styles.userInfo}>
            <Row className={`${styles.displayFlex} ${styles.width100}`}>
              <Col span={20}>
                <h3 style={{ fontWeight: 900 }}> 客户信息</h3>
              </Col>
              {notOptions != false ? (
                <Col style={{ float: 'right' }}>
                  <span className={styles.txt11} onClick={this.editInfo}>
                    编辑
                  </span>
                </Col>
              ) : (
                <></>
              )}
            </Row>

            <Row className={`${styles.width100} ${styles.padding70}`}>
              <Col span={8} className={styles.col8}>
                <div className={styles.names}>姓名：
                  <Tooltip placement="top" title={basicByIdInfo.name}>
                    <div className={`${styles.userName}`}>{basicByIdInfo.name || ""}</div>
                  </Tooltip>
                </div>
              </Col>
              <Col span={8} className={styles.col8}>
                <Form.Item labelAlign="right">性别：
                  <span>{basicByIdInfo.sex == 1 ? "男" : basicByIdInfo.sex == 2 ? '女' : ''}</span>
                </Form.Item>
              </Col>
              <Col span={8} className={styles.col8}>
                <Form.Item labelAlign="right">出生日期：
                  <span>
                    {basicByIdInfo.birthday}
                  </span>
                </Form.Item>
              </Col>
              <Col span={8} className={styles.col8}>
                <div className={styles.names}>国籍：
                  <Tooltip placement="bottom" title={basicByIdInfo.nationality}>
                    <div className={`${styles.userName}`}>{basicByIdInfo.nationality || ""}</div>
                  </Tooltip>
                </div>
              </Col>
              <Col span={8} className={styles.col8}>
                <Form.Item labelAlign="right">电话：
                  <span>{basicByIdInfo.oftenTel}</span>
                </Form.Item>
              </Col>
              {/* </Form> */}
            </Row>
          </Row>

        </Spin>
        {/* 健康状况 */}
        <Spin spinning={loading} key={"loading"} tip={"加载中..."}>
          <Row className={styles.userInfo}>
            <Row className={`${styles.width100} ${styles.displayFlex}`}>
              <Col span={20}>
                <h3 style={{ fontWeight: 900 }}> 健康状况</h3>
              </Col>
              {notOptions != false ? (
                <Col style={{ float: 'right' }}>
                  <span className={styles.txt11} onClick={this.seeHistoryOptions}>
                    历史操作
                  </span>
                  <span className={styles.txt11} onClick={this.editHealthy}>
                    编辑
                  </span>
                </Col>
              ) : (
                <></>
              )}
            </Row>
            <Row className={`${styles.width100} ${styles.padding20}`}>
              <Form {...indexLayout} className={`${styles.width100}`} onFinish={this.healthFinish}>
                <Form.Item
                  // name={['user', 'name']}
                  className={styles.contentinner}
                  labelAlign="right"
                  label="既往病史"
                >
                  <span>
                    {healthCondition.previousHistory ? healthCondition.previousHistory : ' 无'}
                  </span>
                </Form.Item>
                <Form.Item
                  // name={['user', 'name']}
                  className={styles.contentinner}
                  labelAlign="right"
                  label="家族史"
                >
                  <span>{healthCondition.familyHistory ? healthCondition.familyHistory : ' 无'}</span>
                </Form.Item>
                <Form.Item
                  // name={['user', 'name']}
                  className={styles.contentinner}
                  labelAlign="right"
                  label="过敏史"
                >
                  <span>
                    {healthCondition.allergicHistory ? healthCondition.allergicHistory : '  无'}
                  </span>
                </Form.Item>
                <Form.Item
                  // name={['user', 'name']}
                  className={styles.contentinner}
                  labelAlign="right"
                  label="在服药物"
                >
                  <span>{healthCondition.inTaking ? healthCondition.inTaking : '无'}</span>
                </Form.Item>
                <Form.Item
                  // name={['user', 'name']}
                  className={styles.contentinner}
                  labelAlign="right"
                  label="怀孕"
                >
                  <span>{healthCondition.pregnant ? healthCondition.pregnant : '无'}</span>
                </Form.Item>
                <Form.Item
                  // name={['user', 'name']}
                  className={styles.contentinner}
                  labelAlign="right"
                  label="关节置换手术"
                >
                  <span>{healthCondition.jointReplacement ? healthCondition.jointReplacement : '否认'}</span>
                </Form.Item>
                <Form.Item
                  className={styles.contentinner}
                  labelAlign="right"
                  label="其他"
                >
                  <span>{healthCondition.other ? healthCondition.other : ''}</span>
                </Form.Item>
              </Form>
            </Row>
          </Row>
        </Spin>

        <Modal
          className={styles.userModal}
          maskClosable={false}
          title="编辑客户信息"
          width={960}
          confirmLoading={disabledBtn}
          visible={infoModalVisible}
          onOk={this.saveHideModal}
          onCancel={this.hideModal}
          okText="保存"
          cancelText="取消"
        >
          <Form
            {...modalLayout}
            className={styles.width100}
            ref={this.infoFormRef}
          // initialValues={{
          //   name: basicByIdInfo.name,
          //   sex: basicByIdInfo.sex,
          //   // birthday: moment(modalForm.birthday),
          //   nationality: basicByIdInfo.nationality,
          //   oftenTel: basicByIdInfo.oftenTel

          // }}
          >
            <Row className={styles.displayFlex}>
              <Col span={10} className={styles.col8}>
                <Form.Item name='name' labelAlign="right" label="姓名"
                  getValueFromEvent={e => {
                    return e.target.value.replace(/\s+/g, "")
                  }}
                  rules={[{ required: true }]}
                >
                  <Input
                    maxLength={50}
                    showCount
                    placeholder="请输入姓名"
                    defaultValue={modalForm.name}
                    onChange={(e) => modalForm.name = e.target.value}
                  />
                </Form.Item>
              </Col>
              <Col span={10} className={styles.col8}>
                <Form.Item name='sex' labelAlign="right" label="性别" rules={[{ required: true }]}>
                  <Radio.Group
                    defaultValue={modalForm.sex}
                    onChange={e => { modalForm.sex = e.target.value }} >
                    <Radio value={1}>男</Radio>
                    <Radio value={2}>女</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>
            <Row className={styles.displayFlex}>
              <Col span={10} className={styles.col8}>
                <Form.Item name='birthday' labelAlign="right" label="出生日期"
                  rules={[{ required: true }]}
                >
                  <DatePicker
                    placeholder="请选择出生日期"
                    disabledDate={this.disabledDate}
                    style={{ width: '100%' }}
                    defaultValue={modalForm.birthday ? moment(modalForm.birthday, dateFormat) : null}
                    format={dateFormat}
                    onChange={(date, dateString) => modalForm.birthday = dateString}
                  />
                </Form.Item>
              </Col>

              <Col span={10} className={styles.col8}>
                <Form.Item name='nationality' labelAlign="right" label="国籍">
                  <Select placeholder="请选择" style={{ width: '100%' }}
                    defaultValue={modalForm.nationality}
                    onChange={(value) => { modalForm.nationality = value }}
                  >
                    {nationalityList.map((item, index) => (
                      <Option key={index} value={item.value}>{item.label}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>

            </Row>
            <Row className={styles.displayFlex}>
              <Col span={10} className={styles.col8}>
                <Form.Item name='oftenTel' labelAlign="right" label="电话" rules={[{ required: true }, {validator: this.phoneNumberValidator}]}
                >
                  <Input
                    className={styles.oftenTel}
                    placeholder="请输入电话"
                    addonBefore={prefixSelector}
                    onChange={(e) => modalForm.oftenTel = e.target.value}
                    defaultValue={modalForm.oftenTel}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>

        <Modal
          title="查看历史操作记录"
          width={960}
          height={492}
          visible={historyModalVisible}
          onOk={this.historyHideModal}
          onCancel={this.historyHideModal}
          footer={[
            <Button key="submit" type="primary" loading={loading} onClick={this.historyHideModal}>
              关闭
            </Button>]}
        >
          <div style={{ overflowY: 'auto', height: 400 }}>
            {
              historyOptionsList.length == 0 || historyOptionsList == null ? (
                <div>
                  {/*暂无数据*/}
                  <div className={commonStyle.nodataContent} style={{ marginTop: '20%' }}>
                    <img src={noData} className={commonStyle.imgStyle} alt="" />
                    <div className={commonStyle.fontStyle}>暂无数据</div>
                  </div>
                </div>
              ) : (
                <Row className={`${styles.block9} `}>
                  {historyOptionsList && historyOptionsList.map((item, index) => (
                    <Collapse
                      defaultActiveKey={[`${index}`]}
                      // expandIconPosition="end"
                      bordered={true}
                      key={index}
                      style={{ width: '100%', marginBottom: '10px' }}
                      ghost
                    >
                      <Panel
                        showArrow={false}
                        header={
                          <div
                            style={{
                              color: '#595959',
                              width: '100%',
                              lineHeight: '28px',
                              height: '28px',
                            }}
                          >
                            <Row className={`${styles.rowStyle} `}>
                              <span>{item.createdGmtAt.substring(0, 10)}</span>
                              <span style={{ marginLeft: 30 }}>{item.createdGmtAt.substring(11)}</span>
                              <span style={{ marginLeft: 30 }}>{item.userName}</span>
                              <Tooltip placement="bottom" title={`${item.title}更新`} >
                                <span className={styles.healthEllipse} style={{ marginLeft: 30 }}>{item.title}更新</span>
                              </Tooltip>
                            </Row>
                          </div>
                        }
                        key={index}
                      >
                        {
                          item.record && item.record.length > 0 ? item.record.map((childItem, childIndex) => (
                            <div key={childIndex}>
                              {
                                (childItem.details) && (childItem.details).map((childItem1, childIndex1) => (<p className={styles.pLineHeight} key={childIndex1}>
                                  {childItem1.sort}、{childItem1.infoType}：{childItem1.label}（{hisOptionType[childItem1.type]}）
                                </p>))
                              }
                              {/* {
                                (item.record[0].details) && (item.record[0].details).map((childItem, childIndex) => (<p className={styles.pLineHeight} key={childIndex}>
                                  {childIndex += 1} 、{childItem.infoType}：{childItem.label}{hisOptionType[childItem.type]}
                                </p>))
                              } */}
                            </div>
                          )) : <></>
                        }
                      </Panel>
                    </Collapse>
                  ))
                  }

                </Row>
              )
            }
          </div>
        </Modal>
        <Modal
          maskClosable={false}
          title="编辑健康状况"
          width={1145}
          confirmLoading={disabledBtn}
          visible={healthyModalVisible}
          onOk={this.savehealthyHideModal}
          onCancel={this.healthyHideModal}
          okText="保存"
          cancelText="取消"
        >
          <Spin spinning={loading} key={"loading"} tip={"加载中..."}>
            <div style={{ overflowY: 'auto', maxHeight: '450px', minHeight: '200px' }}>
              {editHistoryData && editHistoryData.map((item, index) => (
                <div key={index}>
                  {item.classification == 2 ?
                    <Row style={{ marginBottom: '12px' }}>
                      <div>
                        {item.dictCount || "您是否有以下病史？"}
                        <Radio.Group
                          defaultValue={item.selectStatus}
                          style={{ marginLeft: '16px' }}
                          // options={medicalHistoryOptions}
                          onChange={(e) => this.medicalHistoryChange(e, item.type, item.id)}
                          value={item.selectStatus}
                        >
                          <Radio value={1}>否</Radio>
                          <Radio value={2}>是</Radio>
                        </Radio.Group>
                      </div>
                      {/*   展示全部  selectStatus =1 ：否 2：是*/}
                      {item.selectStatus != 1 ?
                        (<Row style={{ width: '100%' }}>
                          <Checkbox.Group
                            style={{ width: '100%' }}
                            defaultValue={(item.secondaryCustomerArchivesDtoList || []).map(param => {
                              if (param.selectStatus == 2) {
                                return param.id
                              }
                            })
                            }
                            onChange={(checkedValue) => this.changeMedicalHistory(checkedValue, item.id, item.type)}>
                            <Row>
                              {item.secondaryCustomerArchivesDtoList && item.secondaryCustomerArchivesDtoList.map((childItem, childIndex) =>
                              (<Col key={childIndex} span={childItem.id == '138' || childItem.id == '223' || childItem.id == '226' ? 8 : 4} style={{ marginTop: '12px', height: '32px',lineHeight:'32px' }}>
                                <Checkbox
                                  checked={childItem.selectStatus == 2}
                                  value={childItem.id}>
                                    {childItem.dictZxCount}
                                  {childItem.id == 138 ? <Input maxLength={50} style={{ marginLeft: '5px', width: '280px'}} placeholder="请输入过敏食物"
                                    defaultValue={childItem.inputText}
                                    onChange={e => this.changeInput(e.target.value, 138)}></Input> : <></>}
                                  {childItem.id == 223  ? <Input maxLength={50} style={{ marginLeft: '5px', width: '280px' }} placeholder="请输入其他过敏物"
                                    defaultValue={childItem.inputText}
                                    onChange={e => this.changeInput(e.target.value, 223)}></Input> : <></>}

                                  {childItem.id == 226 ? <Input maxLength={50} style={{ marginLeft: '5px', width: '280px'}} placeholder="请输入其他在服药物"
                                    defaultValue={childItem.inputText}
                                    onChange={e => this.changeInput(e.target.value, childItem.id)}></Input> : <></>}
                                  </Checkbox>

                              </Col>)
                              )
                            // }
                              }
                            </Row>
                          </Checkbox.Group>
                          {/* 既往病史补充 输入 */}
                          {item.id == 22 && item.selectStatus == 2 && index ? (<Row className={styles.width100} style={{ marginTop: '12px' }}>
                            {
                              (item.associateArchivesDictList || []).map((childItem, childIndex) => (

                                <Row key={childIndex} style={{ width: '100%' }} className={styles.textInputStyle}>
                                  {childItem.dictCount || "您是否还有任何疾病、情况未包含在上述问题里，您认为我应该知道吗？"}
                                  <TextArea
                                    maxLength={200}
                                    showCount
                                    autoSize={{ minRows: 3, maxRows: 10 }}
                                    placeholder="小于200个字符"
                                    defaultValue={childItem.inputText}
                                    style={{ height: 'auto', width: '100%', marginTop: '12px' }}
                                    onChange={e => this.changeInput(e.target.value, item.id + 1)}
                                  // value={}
                                  />
                                </Row>
                              ))
                            }
                          </Row>) : <></>}

                        </Row>) : (
                          // 隐藏病症
                          <Row></Row>
                        )}
                    </Row> : <></>
                  }
                </div>))
              }

              <Row>
                <Col offset={8}>
                  <span style={{ marginRight: '28px', marginLeft: '62px' }}>是</span>
                  <span>否</span>
                </Col>
              </Row>
              {
                editHistoryData && editHistoryData.map((item, index) => (
                  item.classification == 1 ? <div key={index}>
                    <Row style={{ marginBottom: '12px' }}>
                      <Col className={styles.healthSpan} span={9}>{item.dictCount || "您是否怀孕？"}
                        <span style={{ float: 'right' }}>
                          {item.id == 29 || item.id == 32 || item.id == 33 ? "-------------------------------------------------" : item.id == 31 ? '-----------------------------------' : item.id == 32 ? '---------------------------------------------------' : item.id == 34 ? '-------' : item.id == 37 ? '----------------------------------------------' : ''}
                        </span>
                      </Col>
                      <Radio.Group
                        style={{ marginLeft: '16px' }}
                        onChange={e => this.medicalHistoryChange(e, item.type, item.id)}
                        // onChange={(e) => this.haveBabyChange(e, item.id)}
                        value={item.selectStatus}
                      >
                        <Radio value={2} style={{ marginRight: '24px' }}></Radio>
                        <Radio value={1}></Radio>

                      </Radio.Group>
                      {item.associateArchivesDictList && item.associateArchivesDictList.map((childItem, childIndex) => (
                        item.selectStatus != 1 && childItem.id == 30 ? (
                          <Col key={childIndex} span={12} style={{ marginTop: '-6px' }}>
                            {childItem.dictCount || "您怀孕的周数是？"}
                            <InputNumber style={{ width: '245px' }}
                              placeholder="怀孕周数"
                              defaultValue={childItem.inputText}
                              min={1}
                              max={40}
                              // autoFocus=
                              // {true}
                              // {(childItem.inputText == '' || childItem.inputText==null) && item.selectStatus != 1 ? true : false}
                              parser={value => {
                                if (value.length < 1) {
                                  return 1
                                } else {
                                  return value
                                }
                              }}
                              onChange={value => this.changeInput(value, childItem.id)} />
                          </Col>
                        ) : (
                          item.selectStatus != 1 && childItem.id == 35 ? (
                            <>
                              <Col span={12} style={{ marginTop: '-6px' }}>
                                {childItem.dictCount || "您置换手术日期是？"}
                                <DatePicker
                                  placeholder="手术日期"
                                  disabledDate={this.disabledDate}
                                  defaultValue={childItem.inputText ? moment(childItem.inputText, dateFormat) : undefined}
                                  style={{ width: '245px' }}
                                  onChange={(date, dateString) => this.changeInput(dateString, childItem.id)}
                                ></DatePicker>
                              </Col>
                            </>
                          ) : <></>
                        )

                      ))
                      }
                    </Row>
                    <Row span={24} >
                      {item.associateArchivesDictList && item.associateArchivesDictList.map((childItem, childIndex) =>
                      (item.selectStatus != 1 && childItem.id == 36 ? (
                        <Row style={{ width: '100%' }} key={childIndex} className={styles.textInputStyle}>
                          {childItem.dictCount || "如果您做过关节置换手术，是否导致您有其他并发症？"}
                          <TextArea
                            maxLength={200}
                            showCount
                            defaultValue={childItem.inputText}
                            autoSize={{ minRows: 3, maxRows: 10 }}
                            placeholder="小于200个字符"
                            style={{ height: 'auto', width: '100%', marginTop: '12px' }}
                            onChange={e => this.changeInput(e.target.value, childItem.id)}
                          />
                        </Row>
                      ) : <></>
                      )
                      )}
                    </Row>

                  </div>
                    : <></>
                ))
              }
            </div>
          </Spin>
        </Modal>
      </GridContent>
    );
  }
}

export default connect(({ Classification, loading }) => ({

}))(MedicalRecordHomePage);
