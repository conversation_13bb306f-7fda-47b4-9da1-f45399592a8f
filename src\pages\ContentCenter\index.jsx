import React,{Component} from 'react';
import ProLayout from '@ant-design/pro-layout';
import {Link,  history} from 'umi';
import complexMenu from './components/complexMenu'//引入菜单目录
import styles from './style.less';//引入样式
class ContentCenter extends Component {
 constructor(prop) {
   super(prop);
 }
  //初始化
  componentDidMount() {
    const urlParams = new URL(window.location.href);
    const pathname = urlParams?.pathname;
    if(pathname==='/emr/ContentCenter'){
      this.openFirstMenu('/emr/ContentCenter/DownLoad');
    }
  }
  componentWillUnmount() {
  }

  //打开第一个菜单
  openFirstMenu=(url)=>{
    history.push(url)
  }
  render() {
    return (
      <ProLayout
        className={styles.Typecontent}
        navTheme="light"
        collapsedButtonRender={false}//禁用收起
        route={{routes: complexMenu}} // 内嵌菜单数据
        menuHeaderRender={false}
        fixSiderbar={true} //固定左侧栏
        menuItemRender={(item, dom) => ( //切记：跳转方式，如果不声明pro是不知道如何跳转的
          <Link to={item.path}>{dom}</Link>
        )}
      >
        <div>{this.props.children}</div>
      </ProLayout>

    );
  }
}

export default ContentCenter;
