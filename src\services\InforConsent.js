import request from '@/utils/request';

//获取知情同意书模板分组列表
export async function findCheckClasses(params) {
  return request('/api/emr/system/findMrcTempClass', {
    method: 'POST',
    data: params,
  });
}

//根据知情同意书模板分类获取模板名
export async function findMrcTmptNamesByClass(params) {
  return request('/api/emr/system/findMrcTmptNamesByClass', {
    method: 'POST',
    data: params,
  });
}

//新增同意书模板
export async function saveMrcTmpt(params) {
  return request('/api/emr/system/saveMrcTmpt', {
    method: 'POST',
    data: params,
  });
}

//获取知情同意书模板详情
export async function getMrcTempInfoById(params) {
  return request('/api/emr/system/getMrcTempInfoById', {
    method: 'POST',
    data: params,
  });
}

//编辑同意书模板
export async function editMrcTmpt(params) {
  return request('/api/emr/system/editMrcTmpt', {
    method: 'POST',
    data: params,
  });
}

//删除知情同意书模板
export async function deleteMrcTmpt(params) {
  return request('/api/emr/system/deleteMrcTmpt', {
    method: 'POST',
    data: params,
  });
}

//知情同意书模板分组列表搜索
export async function findMrcTempClassSearch(params) {
  return request('/api/emr/system/findMrcTempClassSearch', {
    method: 'POST',
    data: params,
  });
}

//下载指定知情同意书
export async function downMrcByCode(params) {
  return request('/api/emr/system/downMrcByCode', {
    method: 'POST',
    data: params,
    responseType: 'blob',
  });
}

//获取同意书模板分组列表
export async function findMrcTmpts(params) {
  return request('/api/emr/mrc/findMrcTmpts', {
    method: 'POST',
    data: params,
  });
}
