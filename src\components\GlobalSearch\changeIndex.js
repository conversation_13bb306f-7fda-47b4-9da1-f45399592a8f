import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { Input, Icon, AutoComplete,Form } from 'antd';
import classNames from 'classnames';
import Debounce from 'lodash-decorators/debounce';
import Bind from 'lodash-decorators/bind';
import styles from './index.less';
import { postSeanchData, getSeanchData } from '@/services/api';

const Item = Form.Item;
const Option = AutoComplete.Option;
const Search = Input.Search;

function searchResult(value) {
  return value.map((item, idx) => {
    return item.item;
  });
}
function renderOption(item, idx) {
  return (
    <Option key={item} value={item} text={item}>
      {item}
    </Option>
  );
}
function renderOption2(item, idx) {
  return (
    <Option key={item.id} value={item.id+''} text={item.diagnosisName}>
      {item.diagnosisName}
    </Option>
  );
}

const validate = {
  validateStatus: 'error',
  errorMsg: 'The prime between 8 and 12 is 11!',
}

export default class HeaderSearch extends PureComponent {
  static propTypes = {
    className: PropTypes.string,
    placeholder: PropTypes.string,
    onSearch: PropTypes.func,
    onPressEnter: PropTypes.func,
    defaultActiveFirstOption: PropTypes.bool,
    dataSource: PropTypes.array,
    defaultOpen: PropTypes.bool,
    keywordsUrl: PropTypes.string,
    query: PropTypes.object,
    keyName: PropTypes.string,
    method: PropTypes.string,
    searchValidateStatus:PropTypes.object
  };

  static defaultProps = {
    defaultActiveFirstOption: false,
    onPressEnter: () => { },
    onSearch: () => { },
    className: '',
    placeholder: '',
    dataSource: [],
    defaultOpen: false,
    keywordsUrl: '',
    method: '',
    query: {},
    keyName: 'GlobalSearch',
    searchValidateStatus:{
      validateStatus: 'success',
      errorMsg: null,
    }
  };

  constructor(props) {
    super(props);
    this.state = {
      value: '',
      dataSource: [],
    };
  }

  componentWillUnmount() {
    clearTimeout(this.timeout); // 组件销毁时释放
  }

  onSearch = (value) => {
    // 去掉首尾空格
    const ValueEnds= value.replace(/(^\s*)|(\s*$)/g, "");
    const { onSearch } = this.props;
    this.timeout = setTimeout(() => {
      if (this.selectValue) {
        onSearch(this.selectValue, this.selectName); // 异步输出input中的值
      } else {
        onSearch(ValueEnds, this.selectName); // 异步输出input中的值
      }
      this.selectValue = null;
      if (this.props.type) {
        this.setState({
          value: ''
        })
      }
    }, 200);
  };

  onChange = value => {
    const { onChange, query } = this.props;
    const { keywordsUrl,method } = this.props;
    // 去掉首尾空格
    const ValueEnds= value.replace(/(^\s*)|(\s*$)/g, "");
    let valuedata = {
      queryText: ValueEnds,
      ...query,
    };
    if (keywordsUrl) {
      if (method == "GET") {
        getSeanchData(`${keywordsUrl}?search=${ValueEnds}&name=${ValueEnds}`).then((re) => {
          let data = [];
          if (re && re.content) {
            data = re.content.filter(item => item);
          }
          this.setState({
            dataSource: data
          })
        })
      } else {
        postSeanchData(keywordsUrl, valuedata).then((re) => {
          let data = [];
          if (re && re.content) {
            data = re.content.filter(item => item);
          }
          this.setState({
            dataSource: data
          })
        })
      }
    }

    this.setState({ value:ValueEnds });
    if (onChange) {
      onChange(ValueEnds, this.selectName);
    }
  };

  /**
   * 关联联想数据
   */
  handleSearch = value => {
    //this.onSearch(value)
  };

  render() {
    const { className, placeholder, keyName, source, ...restProps } = this.props;
    const { value, dataSource } = this.state;
    delete restProps.defaultOpen;
    const {validateStatus,errorMsg} = this.props.searchValidateStatus
    // console.log('validateStatus',validateStatus);
    // console.log('errorMsg',errorMsg);

    return (
      <span className={classNames(className, styles.headerSearch)}>
        <Form>
          <Item
            validateStatus={validateStatus}
            help={errorMsg}
          >
            <AutoComplete
              key={keyName}
              {...restProps}
              value={value}
              backfill={true}
              onChange={this.onChange}
              onSearch={this.handleSearch}
              onSelect={(value, option) => {
                this.selectValue = value;
                this.selectName = option.props.text;
              }}
              dataSource={dataSource.map(source === 'mengmeng' ? renderOption2 : renderOption)}
            >
              <Search
                ref={node => {
                  this.input = node;
                }}
                aria-label={placeholder}
                placeholder={placeholder}
                onSearch={this.onSearch}
              />
            </AutoComplete>
          </Item>
        </Form>
      </span>
    );
  }
}
