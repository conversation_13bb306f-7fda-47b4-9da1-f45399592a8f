@import '~antd/lib/style/themes/default.less';
.Typecontent{
  :global{
    .ant-menu-item{
      text-align: center;
      box-shadow: 0 1px 0 0 #EEE;
      margin-bottom: 0 !important;
      margin-top: 0 !important;
      height: 48px;
      line-height: 48px;
    }
    .ant-menu-item-selected{
      border-left: 4px solid #4292FF;
    }
    .ant-pro-grid-content{
      padding: 0;
    }
    .ant-menu-inline.ant-menu-root .ant-menu-item, .ant-menu-inline.ant-menu-root .ant-menu-submenu-title{
      padding-left: 32px;
    }
    .ant-menu-vertical .ant-menu-item::after, .ant-menu-vertical-left .ant-menu-item::after, .ant-menu-vertical-right .ant-menu-item::after, .ant-menu-inline .ant-menu-item::after{
      border-right: 0;
    }
  }
}

