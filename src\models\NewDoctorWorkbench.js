import {
  getappointmentCacheShow, // 获取主诉
  getcomplaintDtoCacheSave, // 编辑主诉
  cancelVisiting, // 退回前台修改
  processEndToUpdateVisitStatus, // 流程结束
  getAllStatus, // 获取页面接诊状态
  getConsultantAndPlanCount,      // 查询咨询师与治疗方案总数
  updateAppointmentVisitStatus, // 修改接诊状态
} from '@/services/NewDoctorWorkbench';

export default {
  namespace: 'NewDoctorWorkbench',
  state: {
    doctorWorkbenchVisible: false, // 医生工作台弹窗状态
    maximizeIndex: null, // 最小化下标
    appointmentId: null, // 预约Id
    patientId: null, // 患者Id
    patientName: null, // 患者名称
    settlementId: null, // 结算Id
  },
  effects: {
    // 查询主诉
    *getappointmentCacheShow({ payload }, { call }) {
      const res = yield call(getappointmentCacheShow, payload);
      return res;
    },
    // 编辑主诉
    *getcomplaintDtoCacheSave({ payload }, { call }) {
      const res = yield call(getcomplaintDtoCacheSave, payload);
      return res;
    },
    // 退回前台修改
    *cancelVisiting({ payload }, { call }) {
      const res = yield call(cancelVisiting, payload);
      return res;
    },
    // 流程结束
    *processEndToUpdateVisitStatus({ payload }, { call }) {
      const res = yield call(processEndToUpdateVisitStatus, payload);
      return res;
    },
    // 获取页面接诊状态
    *getAllStatus({ payload }, { call }) {
      const res = yield call(getAllStatus, payload);
      return res;
    },
    // 查询咨询师与治疗方案总数
    *getConsultantAndPlanCount({ payload }, { call }) {
      const res = yield call(getConsultantAndPlanCount, payload);
      return res;
    },
    // 获取页面接诊状态
    *updateAppointmentVisitStatus({ payload }, { call }) {
      const res = yield call(updateAppointmentVisitStatus, payload);
      return res;
    },
  },
  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    // 清空数据
    clean(state, { payload }){
      return {
        ...state,
        doctorWorkbenchVisible: false, // 医生工作台弹窗状态
        maximizeIndex: null, // 最小化下标
        appointmentId: null, // 预约Id
        patientId: null, // 患者Id
        patientName: null, // 患者名称
        settlementId: null, // 结算Id
      }
    }
  },
  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, search }) => {
        if (pathname.indexOf('/allTasks') === -1) {
          dispatch({
            type: "clean"
          })
        }
      });
    }
  }
};
