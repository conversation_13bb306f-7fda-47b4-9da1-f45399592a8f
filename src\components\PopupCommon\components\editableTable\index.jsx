import React from 'react';
import { Select, Input,Tooltip } from 'antd';
import styles from './index.less';

// 口腔检查
export default class EditableTable extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      consultationProjectOption: ['种植', '填充', '修复', '检查'],
      statusOption: ['待开发', '未完成', '已完成'],
      nextStep: ['方案A', '方案B', '方案C'],
    };
  }
  //删除牙位信息
  deleteResult = (index) => {
    const tmp = Array.from(this.props.checkResult);
    tmp.splice(index, 1);
    this.props.update(tmp);
  }
  //编辑牙位信息
  openEditDialog = (t) => {
    this.props.openEditDialog(t);
  }

  render() {
    return (
      <div className={styles.tableHome}>
        <div className={styles.siteBox}>
          <span>口腔检查</span>
        </div>
        <div className={styles.tableData}>
          <div className={styles.tablenav}>
            <span>牙位</span>
            <span>检查项目</span>
            <span style={{width:258}}>备注</span>
            <span>操作</span>
          </div>
          {
            this.props.checkResult.map((element, index) =>
                // <>

                <div className={styles.datasoure} key={index}>
                  {/*{(element.medExamInfoList||[]).map((ele, number) =>*/}
                  {/*  <span key={index}>{ele.toothPosition}</span>*/}
                  {/*)}*/}
                  <span>{element.toothPosition}</span>
                  <span>
                    <Tooltip title={<span>{element.examName}
                      { element.toothDesc?<span>{element.toothDesc.BSelected==true?",颊侧":""}
                        {element.toothDesc.MSelected==true?",近中":""}
                        {element.toothDesc.DSelected==true?",远中":""}
                        {element.toothDesc.OSelected == true ?",颌面":""}
                        {element.toothDesc.LSelected==true?",舌侧":""}</span>:null}
                    </span>}>
                       <span>{element.examName}
                         { element.toothDesc?<span>{element.toothDesc.BSelected==true?",颊侧":""}
                           {element.toothDesc.MSelected==true?",近中":""}
                           {element.toothDesc.DSelected==true?",远中":""}
                           {element.toothDesc.OSelected == true ?",颌面":""}
                           {element.toothDesc.LSelected==true?",舌侧":""}</span>:null}
                         {/*  {element.toothCode?element.toothCode.map((i) => {*/}
                         {/*  return  i=='B'?"颊侧,":i=='M'?"近中,":i=='D'?"远中,":i=='O'?"颌面,":i=='L'?"舌侧,":""*/}
                         {/*}):null}*/}
                    </span>
                    </Tooltip>

                    {/*</Tooltip>*/}
                  </span>
                  <span style={{width:258}}>
                    <Input placeholder="治疗建议" type="text" value={element.examRemark} style={{ width: '100%' }} onChange={this.inputChange} />
                  </span>
                  <span>
                    <span onClick={() => this.openEditDialog(element.toothPosition)}>编辑</span>  <span onClick={() => this.deleteResult(index)}>删除</span>
                  </span>
                </div>

              // </>
            )
          }
        </div>

      </div>
    );
  }
}
