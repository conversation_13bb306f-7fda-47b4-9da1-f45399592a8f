import React, { useState } from 'react';
import { Tooltip } from 'antd';
import { ExclamationCircleTwoTone, ClockCircleTwoTone, CheckCircleTwoTone } from '@ant-design/icons';
import styles from './style.less';//样式
//牙位
const EditTooth = (props) => {
  const [hoverPosition, setHoverPosition] = useState('');
  //待完成 未完成
  const getchildData = (position) => {
    return () => {
      // props.click(position);
    };
  };
  //颜色
  const colors = ['white',];
  //检查结果数据 牙面 状态等
  const text = (
    <div className={styles.process}>
      {
        props.checkResult.filter((item) => {
          return item.toothPosition === hoverPosition.position || item.toothPosition === hoverPosition.milkToothPosition;
        }).map((element,number) => {
          return (
            <div className={styles.list} key={number}>
              <span>{element.toothPosition}</span>
              <span>{element.checkDate}</span>
              <div className={styles.list_examName} style={{}}>

                <span>{element.examName}</span>
                {element.toothDesc?
                  <span>
                    <span>{element.toothDesc.BSelected==true?",颊侧":""}</span>
                    <span> {element.toothDesc.MSelected==true?",近中":""}</span>
                    <span>{element.toothDesc.DSelected==true?",远中":""}</span>
                    <span> {element.toothDesc.OSelected == true ?",颌面":""}</span>
                    <span> {element.toothDesc.LSelected==true?",舌侧":""}</span>
                  </span>:null}


                {/*  {element.toothCode?element.toothCode.map((i) => {*/}
                {/* return  i=='B'?"颊侧,":i=='M'?"近中,":i=='D'?"远中,":i=='O'?"颌面,":i=='L'?"舌侧,":""*/}
                {/*}):null}*/}
              </div>
              <span>{element.status}</span>
            </div>
          );
        })
      }
    </div>
  );
  //获取牙位
  const getlposition = (position) => {
    return () => {
      props.click(position);
    };
  };
  //鼠标进入
  const handlerMouseOver = (e, position) => {
    setHoverPosition(position);
  };
  //鼠标移出
  const handlerMouseLeave = () => {
    setHoverPosition('');
  };
  return (
    <div className={styles.toothBox}>
      {/* 上排牙齿 */}
      <div className={styles.topBox}>
        {
          props.teeth.above.map((item, index) => {
            return (
              <Tooltip key={index}
                       placement="bottom"
                       icon={false}
                       title={text}
                       trigger={props.checkResult.filter((it) => { return it.toothPosition === item.position || it.toothPosition === item.milkToothPosition; }).length > 0 ? 'hover' : ''}
                       color={colors}
                       className={(styles.customSelect)}
                       style={{ display: 'none' }}
                       overlayInnerStyle={{ color: '#130c0c', padding: '0px 0px', width: '495px' }}
              >
                <div className={styles.inul} key={item.position} onMouseOver={e => handlerMouseOver(e, item, index)} onMouseLeave={handlerMouseLeave}>
                  <div onClick={getchildData(item.position)}>
                    {
                      item.toothImgs.length > 1
                      && (
                        <span className={styles.status}>
                        {props.checkResult.filter(res => res.toothPosition === item.position && res.status === '待完成').length
                          ? <ClockCircleTwoTone />
                          : props.checkResult.filter(res => res.toothPosition === item.position && res.status === '未完成').length
                            ? <ExclamationCircleTwoTone />
                            : <CheckCircleTwoTone />}
                      </span>
                      )
                    }
                    <span className={styles.inli}>
                      {
                        item.toothImgs.map((it, inx) => {
                          let code=  Array.isArray(it)?it.map((i) => {
                            return <img key={i} className={[styles.bigimg, inx === 0 ? '' : styles.projectimg].join(' ')}
                                        src={i} alt="tooth" onError={(e) => { e.target.onerror = null; e.target.src = '/assets/blank.png'; }} />
                          }): <img key={inx} className={[styles.bigimg, inx === 0 ? '' : styles.projectimg].join(' ')}
                                   src={it} alt="tooth" onError={(e) => { e.target.onerror = null; e.target.src = '/assets/blank.png'; }} />
                          return (code);
                        })
                      }
                      {
                        (hoverPosition.position === item.position || hoverPosition.milkToothPosition === item.position) && <img className={styles.upimg} src={`/assets/highlight/${item.position}.png`} alt="tooth" />
                      }
                    </span>
                    <span className={[(styles.seat), props.checkResult.filter((it) => { return it.toothPosition === item.position; }).length > 0 ? (styles.bgRed) : ''].join(' ')}>{item.index}</span>
                  </div>
                  <div onClick={getlposition(item.milkToothPosition)}><span className={[index < 3 || index > 12 ? (styles.seatm) : (styles.seat), props.checkResult.filter((it) => { return it.toothPosition === item.milkToothPosition; }).length > 0 ? (styles.bgRed) : ''].join(' ')}>{item.milkToothPosition[1]}</span></div>
                </div>
              </Tooltip>
            );
          })
        }
      </div>
      <div className={styles.interval}>
        <span />
        <span>右</span>
        <span />
        <span />
        <span>左</span>
        <span />
      </div>
      {/* 下排牙齿 */}
      <div className={styles.bottBox}>
        {
          props.teeth.below.map((item, index) => {
            return (
              <Tooltip
                key={index}
                icon={false}
                title={text}
                placement="top"
                trigger={props.checkResult.filter((it) => { return it.toothPosition === item.position || it.toothPosition === item.milkToothPosition; }).length > 0 ? 'hover' : ''}
                color={colors}
                overlayInnerStyle={{ color: '#130c0c', padding: '0px 0px', width: '495px' }}
              >
                <div className={styles.bottinul} key={item.position} onMouseOver={e => handlerMouseOver(e, item)} onMouseLeave={handlerMouseLeave} onClick={getchildData(item.position)}>
                  <span className={[index < 3 || index > 12 ? (styles.seatm) : (styles.seat), props.checkResult.filter((it) => {
                    return it.toothPosition === item.milkToothPosition; }).length > 0 ? (styles.bgRed) : ''].join(' ')}>{item.milkToothPosition[1]
                  }
                  </span>
                  <span className={[(styles.seat), props.checkResult.filter((it) => {
                    return it.toothPosition === item.position; }).length > 0 ? (styles.bgRed) : ''].join(' ')}>{item.index}</span>
                  <span style={{ position: 'relative' }}>
                  {
                    item.toothImgs.map((it, inx) => {
                      let code =  Array.isArray(it)?it.map((i) => {
                        return <img key={i} className={[styles.bigimg, inx === 0 ? '' : styles.projectimg].join(' ')}
                                    src={i} alt="tooth" onError={(e) => { e.target.onerror = null; e.target.src = '/assets/blank.png'; }} />
                      }): <img key={inx} className={[styles.bigimg, inx === 0 ? '' : styles.projectimg].join(' ')}
                               src={it} alt="tooth" onError={(e) => { e.target.onerror = null; e.target.src = '/assets/blank.png'; }} />
                      return (code);

                    })
                  }
                    {
                      (hoverPosition.position === item.position || hoverPosition.milkToothPosition === item.position) && <img className={styles.upimg} src={`/assets/highlight/${item.position}.png`} alt="tooth" />
                    }
                </span>
                  {
                    item.toothImgs.length > 1
                    && (
                      <span className={styles.statusbottom}>
                    {props.checkResult.filter(res => res.toothPosition === item.position && res.status === '待完成').length
                      ? <ClockCircleTwoTone />
                      : props.checkResult.filter(res => res.toothPosition === item.position && res.status === '未完成').length
                        ? <ExclamationCircleTwoTone />
                        : <CheckCircleTwoTone />}
                  </span>
                    )
                  }
                </div>
              </Tooltip>
            );
          })
        }
      </div>
    </div>
  );
};

export default EditTooth;
