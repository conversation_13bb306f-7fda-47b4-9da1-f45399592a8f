@import '~antd/es/style/themes/default.less';

.writeHeight{
  //border-left:1px solid #000c17;
  //height: 560px;
  position:relative;
}
.formStyle{
  //border:1px solid #000c17;
  //height:445px;
  overflow-y: scroll;
}
.peopleInfo_border{
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #E5E6EB;
  padding: 0 10px 10px 10px;
  margin-bottom: 16px;
}
.peopleInfo{
  width: 50%;
  display: flex;
}
//已收费(绿色框)
.group6 {
  width: 44px;
  background-color: rgba(0, 180, 42, 0.1);
  border-radius: 4px;
  height: 22px;
  border: 1px solid rgba(0, 180, 42, 0.3);
  line-height: 18px;
  text-align: center;
  margin-left: 8px;
}
.word6{
  color: rgba(0, 180, 42, 1);
  font-size: 12px;
  font-family: PingFang SC;
  white-space: nowrap;
}
//病历(红色框)
.group7 {
  width: 44px;
  background-color: rgba(245, 63, 63, 0.1);
  border-radius: 4px;
  height: 22px;
  border: 1px solid rgba(245, 63, 63, 0.3);
  line-height: 18px;
  text-align: center;
  margin-left: 8px;
}
.txt8 {
  color: rgba(245, 63, 63, 1);
  font-size: 12px;
  font-family: PingFang SC;
  white-space: nowrap;
}
.fontgray{
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  font-family: PingFang SC;
}
.assistant{
  color: #4292FF;
  font-size: 14px;
  font-family: PingFang SC;
  margin-left: 5%;
  cursor: pointer;
}
.videoImg{
  display: flex;
  flex-wrap: wrap;
  .videomargin{
    margin-right: 10px;
    margin-top: 5px;
  }
}
.images{
  width: 290px;
  display: flex;
  border: 1px solid rgba(0, 0, 0, 0.06);
  .imgborder{
    position: relative;
  }
  .ctimgStyle{
    width: 118px;
    height: 88px;
  }
  .ctimgInfo{
    padding-left: 8px;
    padding-top: 5px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
  }
  .ctimgdelete {
    color: #F0F0F0;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, 0.3);
    text-align: center;
    align-items: center;
  }
  .icon_delete{
    width: 20px;
    height: 20px;
  }
  .deleteFont{
    font-size: 14px;
    margin-left: 4px;
    margin-top: 1px;
  }
}
.agressBook{
  display: flex;
  flex-wrap: wrap;
  .ctimgStyle{
    width: 128px;
    height: 88px;
  }
  .agressMargin{
    margin-right: 10px;
    margin-top: 5px;
  }
  .agressImg{
    width: 132px;
    height: 105px;
  }
}
:global{
  .ant-form-item-label > label {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
    font-family: PingFang SC;
  }
  .ant-form-item{
    margin-bottom: 12px;
  }
  .ant-form-item-with-help{
    margin-bottom: 0;
  }
}

//牙位显示
.table_row{
  width: 120px;
  height: 50px;
}
.line_th_row,.line_th_col{
  width: 33px;
  text-align: center;
  border-bottom: 1px solid #DEDEDE;
}
.line_th_row{
  border-right: 1px solid #DEDEDE;
}
.line_row,.line_col{
  text-align: center;
}
.line_th_row,.line_th_col,
.line_row,.line_col{
  font-size: 12px;
  height: 16px;
  color: #333;
  font-weight: 500;
}
.line_row{
  border-right: 1px solid #DEDEDE;
}

.inputStyle{
  width: 100%;
  line-height: 45px;
  margin-left: 5px;
}
.AllchooseLine{
  display: flex;
  position: relative;
}
.operation{
  position: absolute;
  right: -28px;
  .add{
    width:20px;
    height: 20px;
    margin-top: 12px;
    cursor: pointer;
  }
}
.bottomBtns{
  position:absolute;
  bottom: 0;
  right:0;
  left:0;
  width: 100%;
  height: 56px;
  background: #FFF;
  box-shadow: 0 -2px 6px -2px rgba(0, 0, 0, 0.25);
  display: flex;
  justify-content: space-between;
  padding-left: 10px;

  .btn_position{
    float: right;
    margin-right: 12px;
    margin-top: 12px;
  }
  .checked{
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
  }
}
.mL_16{
  margin-left: 16px;
}
.examineLookAll{
  display: flex;
  flex-wrap: wrap;
}
.examineLooks{
  margin: 8px 24px 8px 0;
  width: 300px;
  .examines{
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  .right{
    color:#4292FF;
    span{
      cursor: pointer;
    }
  }
  .ctimgStyle{
    width: 118px;
    height: 88px;
  }
  .examineBottom{
    margin-top: 5px;
    border:1px solid rgba(0, 0, 0, 0.06);
    width: 100%;
    display: flex;
  }
  .examineContent{
    margin-left: 8px;
  }

}
.ctimgdelete {
  color: #F0F0F0;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  text-align: center;
  align-items: center;
}
.icon_delete{
  width: 20px;
  height: 20px;
}
.deleteFont{
  font-size: 14px;
  margin-left: 4px;
  margin-top: 1px;
}
//显示一行，超出...
.fontLineStyle{
  width: 152px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.nameMaxwidth{
  max-width: 32%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.rightContent{
  display: flex;
}
.build{
  :global{
    .ant-modal-footer{
      position:absolute;
      top:0;
      right: 0;
    }
  }
}
.lineHeightStyle{
  line-height: 24px;
}
.inputStyle{
  :global{
    .rc-virtual-list{
      max-height: 100px !important;
    }
    .rc-virtual-list-holder{
      max-height: 100px !important;
    }
    .rc-virtual-list-holder-inner{
      max-height: 100px !important;
    }
  }
}

.left_boxBtns {
  width: calc(100% - 430px);
  height: 100%;
  // overflow: auto;
  word-break: break-all;

  .AppointmentOpinionWarp {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .AppointmentOpinionTitle {
      font-weight: 700;
      width: 80px;
    }

    .AppointmentOpinionContent {
      width: calc(100% - 80px);
      display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
      -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
      -webkit-line-clamp: 2; /* 2行，只有 webkit内核支持 */
      word-break: break-all; /* 纯英文换行 */
      overflow: hidden;
    }
  }
}
