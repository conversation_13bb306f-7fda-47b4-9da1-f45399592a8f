@import '~@/utils/utils.less';

@keyframes hoverBtn
{
  0%   {background: #fff;}
  100% {background: #f4f4f9;}
}

@keyframes DownToLeft {
  0%   {  transform:rotate(0deg); }
  100% {  transform:rotate(180deg); }
}

@keyframes UpToRight {
  0%   {  transform:rotate(-180deg);  }
  100% {  transform:rotate(0deg);  }
}

.calendar {
  :global {
    font-size: 14px;
    background: #fff;

    .fc-widget-content:not(.fc-axis .fc-time) {
      cursor:pointer;
    }

    .eventElent  .patientName{
      margin-right: 10px;
    }

    .eventElent .womanIcon {
      margin-right: 7px;
    }

    .eventElent .manIcon {
      margin-right: 7px;
    }

    .eventElent  .age{
      //margin-left: 7px;
    }

    /*.fc th, .fc td {
      border-style: solid;
      border-width: 1px;
      padding: 0;
      vertical-align: top;
    }*/

    .fc-time-grid .fc-slats td {
      height: 3.5em;
      //border-color: #dddddd;
    }
    .fc-unthemed tbody {
      //background: #fff;
    }
    .fc-v-event{
      //border: 1px solid #ccd;
      min-height: 16px;
      overflow: unset;
    }

    .fc-view .fc-head-container tr {
      background: #E0E2E7;
      height: 60px;
      line-height: 60px;
      font-family: MicrosoftYaHei;
      font-weight: normal;
      border-bottom: 1px solid #000000;
      font-size: 14px;
      color: #444444;
    }

    .fc-body .fc-axis {
      background: #E0E2E7;
      border-color: #ccc;
    }


    .fc-head .fc-widget-content {
      //background: #fff;
      //border-color: #ddd;
    }

    .fc-unthemed th,
    .fc-unthemed td,
    .fc-unthemed thead,
    .fc-unthemed tbody,
    .fc-unthemed .fc-divider,
    .fc-unthemed .fc-row,
    .fc-unthemed .fc-content,
    .fc-unthemed .fc-popover,
    .fc-unthemed .fc-list-view,
    .fc-unthemed .fc-list-heading td{
      border-color: #ccc;
    }

    .fc-unthemed  .fc-resource-cell {
      background: #ECECF1;
    }

    .eventElent {
      font-size: 14px;
      color: #444444;
      font-family: 'MicrosoftYaHei';
      padding-left: 3%;
      position: absolute;
      width: 100%;
      top: 50%;
      transform: translate(0, -50%);

      p {
        margin-top: 0;
        margin-bottom: 0;
      }

    }

    .doctorName {
      margin-left: 10px;
    }

    .onterEventTile {
      text-align: center;
    }



    .removeIcon {
      position: absolute;
      z-index: 99;
      right: -5px;
      top: -6px;
      width: 14px;
      height: 14px;
      display: inline-block;
      background-image: url('../../../../src/assets/calendar/guanbi.png');
      background-size: 14px 14px;
    }
    .manIcon {
      width: 12px;
      height: 12px;
      display: inline-block;

      background: url('../../../../src/assets/calendar/man.png');
    }
    .womanIcon {
      width: 8px;
      height: 14px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/woman.png');
    }
    .doctorIcon {
      width: 14px;
      height: 18px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/doctorIcon.png')
    }
    .bigDoctorIcon {
      width: 22px;
      height: 28px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/bigDoctorIcon.png')
    }
    .insuranceIcon {
      width: 18px;
      height: 18px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/InsuranceIcon.png');
    }
    .inIcon {
      width: 18px;
      height: 18px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/IN.png');
    }
    .goNextIcon {
      width: 22px;
      height: 22px;
      display: inline-block;
      position:relative;
      left:10px;
      vertical-align: text-bottom;
      background: url('../../../../src/assets/calendar/next.png');
      cursor: pointer;
    }

    .goDisableNextIcon {
      width: 22px;
      height: 22px;
      display: inline-block;
      position:relative;
      left:10px;
      vertical-align: text-bottom;
      background: url('../../../../src/assets/calendar/disableNext.png');
    }

    .goLastIcon {
      width: 22px;
      height: 22px;
      position:relative;
      right: 10px;
      display: inline-block;
      vertical-align: text-bottom;
      transform:scaleX(-1);
      background: url('../../../../src/assets/calendar/next.png');
      cursor: pointer;
    }

    .goDisableLastIcon {
      width: 22px;
      height: 22px;
      position:relative;
      right: 10px;
      display: inline-block;
      vertical-align: text-bottom;
      transform:scaleX(-1);
      background: url('../../../../src/assets/calendar/disableNext.png');
    }
    .labelTdsTitle{
      line-height: 0px;
      margin-top: 20px;
      font-weight: 400;
      color: #444444;
    }

    .titleLable {
      line-height:0px;
      margin-top: 20px;
      color: #444444;
      font-weight: 600;
    }

    /*实现样式*/
    .fc-event-container .fc-v-event.eventTypeClass {
      background: #002140;
    }
  }
}

.scrollClear {
  .clearfix();
}

.chiarText {
  font-size:12px;
  font-weight:bold;
  color:rgba(0,0,0,0.45);
}

.restDay {
  margin-left: 4px;
  /* margin-top: -1px; */
  position: relative;
  top: -4px;
}

.redChiarNum {
  color: #E24C4D;
}

/**
 月日历样式修改
*/
.MonthAgendaCalendar {
  //height: 50vh;
  border-top: 1px solid #ccc;
  background: #fff;
  .clearfix();
  :global {
    .fc-timeline .fc-head > tr > .fc-divider{
      display: none;
    }
    .fc-timeline .fc-body > tr > .fc-divider{
      display: none;
    }

    .fc-scroller {
      overflow: hidden!important;
    }

    .fc-resource-area {
      display: none;
    }
    .fc-head {
      display: none;
    }
    .fc-time-grid .fc-slats td {
      height: 3.5em;
      border-color: #999;
    }
    .fc-timeline th, .fc-timeline td {
      //min-height: 60px;
    }
    .fc-rows .fc-widget-content {
      //min-height: 60px;
      /*min-height: 60px;*/
      /*min-height: 60px;*/
      box-sizing: border-box;
    }

    .fc-rows .fc-widget-content div.resColl {
      min-height: 60px;
      box-sizing: border-box;
    }

    .fc-time-area .fc-event-container{
      padding-bottom: 0px;
      top:0px;
    }

    .fc-rows .fc-widget-content .eventElent div {
      height: auto;
    }

    .fc-unthemed th,
    .fc-unthemed td,
    .fc-unthemed thead,
    .fc-unthemed tbody,
    .fc-unthemed .fc-divider,
    .fc-unthemed .fc-row,
    .fc-unthemed .fc-content,
    .fc-unthemed .fc-popover,
    .fc-unthemed .fc-list-view,
    .fc-unthemed .fc-list-heading td{
      border-color: #bbb;
    }


    .fc-timeline-event{
      //height: 58px;
      padding: 0px 0;
      height: 55px;
      box-sizing: border-box;
      margin-bottom: 2px;
      margin-top: 2px;
      margin-left: 2px;
      margin-right: 2px;
      border-radius: 10px;
      overflow: hidden;
    }

    .fc-event-container .fc-event.unset .eventElent {
      //overflow: unset;
      padding-left: 0px;
    }

    /*.fc-event-container .fc-event.unset .antd-pro-components-agenda-caleandar-elements-event-element-patientEventicon{
      margin-top: -7px;
    }*/

    /*已结算*/
    /*.fc-event-container .fc-event.finishType:not(.unset):before {
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #9E9E9E;
    }
    !*已经迟到*!
    .fc-event-container .fc-event.lateType:not(.unset):before {
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #F28A19;
    }
    !*未结算 未迟到 未到诊*!
    .fc-event-container .fc-event.notClinicType:not(.unset):before {
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #27AE6B;
    }
    !*未结算 未迟到 已经到诊*!
    .fc-event-container .fc-event.comeClinicType:not(.unset):before {
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #2796F3;
    }*/

    /*搜索到的event*/
    .fc-event-container .fc-event.checkedEvent{
      border:2px solid blue!important;
    }

    .eventOtherEvent{
      border-radius: 10px;
      border: 1px solid #5B6AFF;
      margin: 1px;
      text-align: center;
      opacity: 1;
    }

    .otherWalkingTime.fc-bgevent{
      border-radius: 10px;
      border: 1px solid #5B6AFF;
      margin: 1px;
      text-align: center;
      opacity: 1;
    }

    .temporaryStorageBackground.fc-event{
      border-radius: 4px;
      border: 1px #9F9f9f dashed;
      margin: 1px;
      overflow: unset;
      // opacity: 0.5;
    }

    .surplusBackgroundEvent {
      opacity: 1!important;
    }
    .inverseWorkbackground {
      opacity: 0.6!important;
    }
  }
}

.caleaderTable {
  height: 100%;
}

.resourceMarginTop{
  margin-top: 1px;
}

.resourceTable {
  //height: 100vh;
  width: 100%;
  //width:ca;
  border: 1px solid #E3E3E3;
  :global {
    tr {
      height: 60px;
      box-sizing: border-box;
    }
    tr td {
      border: 1px solid #E3E3E3;
      height: 61px;
      text-align: center;
    }
  }
}

.resSuplus {
  background: #E0E2E7;
}

.AppintmentCalendar {
  .clearfix();
}

.TimeTitleWarp {
  .clearfix();
  height: 37px;
  background: #f7f7f7;
}

.TimeTitleTable {
  width:100%;
  height:100%;
  .clearfix();
}

.resourceTableWarp {
  width: 128px;
  height: 100%;
  float: left;
}

.leftOtherUp {
  float: left;
  width: 35px;
  position: relative;
}

.leftOtherUpContent {
  //width: 100%;
  top: 0px;
  position: fixed;
  height: 70vh;
  max-height: 616px;
  width: 35px;
  z-index: 10;
  background: #4ca2ff;
  background:rgba(255,255,255,1);
  box-shadow:2px 0px 9px 2px rgba(0, 0, 0, 0.34);
  padding-top: 10px;
  cursor: pointer;
  user-select: none;
}

.leftOtherUpContent:hover {
  animation: hoverBtn 0.4s;
  background: #f4f4f9;
}




.content {
  writing-mode:horizontal-tb;
  width: 15px;
  margin: 0 auto;
  text-align: center;
  line-height: 16px;
  font-weight:700;
}

.contentNum {
  position: relative;
  line-height: 10px;
  left: -2px;
}
.contentTimeSpan {
  margin-top: 0px;
  margin-bottom: 7px;
}

.split {
  transform:rotate(90deg);
  margin-left: 7px;
}

.redContent {
  color: red;
}

.rightOtherUp {
  float: left;
  width: 35px;
  position: relative;
}

.rightOtherUpContent {
  top: 0px;
  position: fixed;
  height: 70vh;
  max-height: 616px;
  width: 35px;
  z-index: 10;
  background: #4ca2ff;
  background:rgba(255,255,255,1);
  box-shadow:2px 0px 9px 2px rgba(0, 0, 0, 0.34);
  border-right: 1px solid #ccc;
  padding-top: 10px;
  cursor: pointer;
  user-select: none;
}

.rightOtherUpContent:hover {
  animation: hoverBtn 0.4s;
  background: #f4f4f9;
}

.rightOtherUp {
  float: left;
  width: 35px;
  position: relative;
}

.rightOtherUpContent {
  //width: 100%;
  top: 0px;
  position: fixed;
  height: 70vh;
  width: 35px;
  z-index: 10;
}


.calendarBox {
  width: calc(100% - 129px);
  max-width: 1980px;
  height: 95%;
  float: left;
}

.calendarWarp {
  width: 100%;
  height: 100%;
}

.titleLeftWarp {
  width: 100px;
  height: 100%;
  float: left;
  // padding: 0 11px;
  // border: 1px solid #f4f4f4;
  .titleLeftbox {
    width: 78px;
    padding-left: 15px;
  }

  .clearfix()
}


.titleRigthWarp {
  //width: 92%;
  width: calc(100% - 107px - 0px);
  //max-width: calc(1980px - 107px - 35px);
  height: 100%;
  overflow: hidden;
  position: relative;
  float: left;
  cursor: pointer;
  -moz-user-select:none;/*火狐*/
  -webkit-user-select:none;/*webkit浏览器*/
  -ms-user-select:none;/*IE10*/
  -khtml-user-select:none;/*早期浏览器*/
  user-select:none;
  //border: 1px solid #f4f4f4;
  background: #f7f7f7;
  .clearfix()
}

.timeLastBox {
  width: 71px!important;
  height: 100%;
}

.TimeTitleScroll {
  cursor: pointer;
}

.TimeTitleNum {
  /*overflow-x:scroll;
  width: 100%;*/
  width: auto;
  white-space: nowrap;

  :global {
    div {
      display: inline-block;
      width: 60px;
      margin-right:20px;
      height: 24px;
      text-align: center;
      font-size: 16px;
      font-family: MicrosoftYaHei-Bold;
      font-weight: bold;
    }
  }
}

:global {
  .iScrollLoneScrollbar {
    //left: 27px !important;
    z-index: 1 !important;
  }
}

.chairIcon {
  width: 24px;
  height: 20px;
  display: inline-block;
  background: url('../../../../src/assets/registerAndArrival/chair.png') no-repeat;
  background-size: 24px 20px;
}

.titleTitleRow {
  background: #F7F7F7;
  font-weight: 700;
}

.rowTitile {
  max-width: 68px;
}


.poppperWarp {
  display: none;
}

.demo {
  width: 100px;
  height: 100px;
  background: #22ff22;
}

.contentDoubleIcon {
  margin-top: 15px;
  margin-bottom: 15px;
  text-align: center;
  font-size: 16px;
  font-weight: 700;
}

.doubleDownToLeft {
  animation: DownToLeft 0.4s;
  transform: rotate(180deg);
}

.doubleUpToRigth {
  animation: UpToRight 0.4s;
  transform: rotate(0deg);
}

.contentTop {
  display: inline-block;
  margin-bottom: 8px;
}

.monthAgendaBox {
  display: inline-block;
  height: 100%;
  width:100%;
  //background: #bbb;
}

.leftOtherUpContentActive {
  background: #f4f4f9;
}

.circleBox {
  font-size: 20px;
  //width: 30px;
  height: 30px;
  vertical-align:middle;
}

.circleSpan {
  font-size: 14px;
}

.circleAllow {
  color: #4ba2ff;
  cursor: pointer;
}
.circleAllow:hover {
  color: #437ed1;
}
.circleAllow:active {
  color: #416bbc;
}

.circleUnallow {
  color: #DCDCDC;
  cursor: not-allowed;
}

.circleIcon {
  display: inline-block;
  margin-right: 9px;
  font-size: 14px;
}
.pageControlWarp {
  //background: #4ca2ff;
  width: 130px;
  height: 30px;
  margin-top: 3px;
}

.pageControlDownWarp {
  //background: #4ca2ff;
  //margin-top: -3px;
  width: 130px;
  height: 30px;
}

.pageControl {
  //margin-left: 10px;
}

.circleIconleft {
  //margin-left: 5px;
}








