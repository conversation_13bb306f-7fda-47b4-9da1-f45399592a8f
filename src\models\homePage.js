import {
  findByUserIdToday,
  findAllMedicalRecords,
  ermPatientInfo,
  findMedicalRecordDetail,
  toFile,
  updateCustomerBasicInfo,
  findCustomerBasicById,
  ermPatHealthCondition,
  editHealth,
  saveHealth,
  findHistoryByCustomerId,
  findMedicalRecords,
  findCheckImgsByClass,
  findCheckImgsByDate,
  findCheckById, // 预览影像
  deleteCheckImg,
  findLinkEmrs,
  saveCheckImgs,
} from '@/services/homePage'; //引入接诊，医生列表，今日预约，专业，等级，保存转诊，昨日待写病历，病历详情，更新病历，用户面板，更新面板，设置面板，今日预约排序等相关接口
import { notification } from 'antd';

const Model = {
  namespace: 'homePage',
  state: {
    PendingInfo: [], //今日就诊
    allMedicalRecords: [], //全部病历
    medicalRecordDetail: {}, //病例详情
    tobeFile: {}, // 归档操作
    rightTopPatientInfo: {}, //右上方患者信息
    patientInfo: {}, //患者信息
    savePatientInfo: {}, //保存患者信息
    healthCondition: {}, // 健康状况
    patHealthConditionInfo: {}, ///编辑时回显的健康状况
    savePatHealthConditionInfo: {}, //保存健康状况
    findHistoryList: [], //健康状况历史记录
    medicalRecords: [], //病历记录
    imageByClass: [], //影像资料-按分类
    imageByDate: [], //影像资料-按时间
    previewImageInfo: {}, //预览影像数据
    deleteCheckImgRes: {}, //删除影像及其关联
    linkEmrsList: [], //查询影像关联
    saveCheckImgsRes: {}, //保存影像数据
    basicByIdInfo:{},//患者信息
    loading: false,
    loadTip: '加载中',
  },
  // 异步
  effects: {
    /**今日就诊和获取就诊记录
     * 参数：病历号，医生id，机构id，分页，就诊状态，排序 平台标识 当前登录医生标识 预约日期  患者标识 就诊状态 等等，**/
    *todaySeePending({ payload, callback }, { call, put }) {
      const response = yield call(findByUserIdToday, payload);
      yield put({
        type: 'getTodaySeePending',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**
     * 获取全部病历
     * 参数
     * 当前患者标识,大病历号,状态：1编辑中；2已完成；3归档,就诊时间筛选开始时间,就诊时间筛选结束时间,排序：正序是ASC 倒叙是DESC,平台标识
     *
     * **/
    *allMedicalRecords({ payload, callback }, { call, put }) {
      const response = yield call(findAllMedicalRecords, payload);
      yield put({
        type: 'getAllMedicalRecords',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**
     * 病历页面右上方患者信息
     * @param {*tenantId}  平台标识
     * @param {*patientId} 患者标识
     * organizationId  机构ID
     */

    *rightPatientInfo({ payload, callback }, { call, put }) {
      const response = yield call(ermPatientInfo, payload);
      yield put({
        type: 'getRightPatientInfos',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**获取单个病例详情
     * 参数：
     * "tenantId": "平台标识",
        "emrId": "大病历号",
        "emrSubId": "小病历号   //保存病历时电子病历系统自动生成的标识"，**/
    *medicalRecordDetail({ payload, callback }, { call, put }) {
      const response = yield call(findMedicalRecordDetail, payload);
      yield put({
        type: 'getMedicalRecordDetail',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**归档操作
     * 参数：
     * "更新人标识",
        更新人姓名,
        "小病历号   //保存病历时电子病历系统自动生成的标识"，**/
    *BeFile({ payload, callback }, { call, put }) {
      const response = yield call(toFile, payload);
      yield put({
        type: 'toBeFile',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**获取患者信息
     * 参数：
     * 患者标识,平台标识,机构ID**/
    *patientOverview({ payload, callback }, { call, put }) {
      const response = yield call(ermPatientInfo, payload);
      yield put({
        type: 'getPatientOverview',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**获取患者信息
     * 参数：
     * 患者标识,平台标识,机构ID**/
    *findCustomerBasicByIdService({ payload, callback }, { call, put }) {
      const response = yield call(findCustomerBasicById, payload);
      yield put({
        type: 'findCustomerBasicByIdInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    // 编辑保存患者信息
    /**
     * @param {"customerId": 3810,
            "name": "曹盟盟编辑",
            "sex": 1,
            "oftenTel": "13522607093",
            "birthday": "2011-10-04",
            "nationality": "中国"}
     */
    *updateCustomerBasic({ payload, callback }, { call, put }) {
      const response = yield call(updateCustomerBasicInfo, payload);
      yield put({
        type: 'getUpdateCustomerBasic',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**获取患者健康状况
     * 参数：
     * "患者标识,平台标识 **/
    *patHealthCondition({ payload, callback }, { call, put }) {
      const response = yield call(ermPatHealthCondition, payload);
      yield put({
        type: 'getPatHealthCondition',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },

    /***
     * 编辑健康状况
     */
    *editHealthCondition({ payload, callback }, { call, put }) {
      const response = yield call(editHealth, payload);
      yield put({
        type: 'editPatHealthCondition',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },

    /***
     * 保存健康状况
     */
    *saveHealthCondition({ payload, callback }, { call, put }) {
      const response = yield call(saveHealth, payload);
      yield put({
        type: 'savePatHealthCondition',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**
     * 查看健康状况编辑操作记录
     * @param {*} param0
     * @param {*} param1
     */
    *historyByCustomerId({ payload, callback }, { call, put }) {
      const response = yield call(findHistoryByCustomerId, payload);
      yield put({
        type: 'getHistoryByCustomerId',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**获取病历记录
     * 参数：
     * "患者标识,大病历号， 平台标识 **/
    *medicalRecords({ payload, callback }, { call, put }) {
      const response = yield call(findMedicalRecords, payload);
      yield put({
        type: 'getMedicalRecords',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**获取影像资料--按分类
     * 参数：
     * "分类标识,大病历号， 平台标识  pageSize pageNum**/
    *checkImgByClass({ payload, callback }, { call, put }) {
      const response = yield call(findCheckImgsByClass, payload);
      yield put({
        type: 'getCheckImgByClass',
        payload: response,
      });
      if (response.code == 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**获取影像资料--按时间
     * 参数：
     * "大病历号， 平台标识 pageSize pageNum**/
    *checkImgByDate({ payload, callback }, { call, put }) {
      const response = yield call(findCheckImgsByDate, payload);
      yield put({
        type: 'getCheckImgByDate',
        payload: response,
      });
      if (response.code == 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },

    /**影像资料--影像预览
     * 参数：
     * id 和平台标识 **/
    *previewImage({ payload, callback }, { call, put }) {
      const response = yield call(findCheckById, payload);
      yield put({
        type: 'getPreviewImage',
        payload: response,
      });
      if (response.code == 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**删除影像及其关联 是否关联都是这个接口
     * 参数：
     * "影像标识  std_check表中的id**/
    *deleteCheckImg({ payload, callback }, { call, put }) {
      const response = yield call(deleteCheckImg, payload);
      yield put({
        type: 'getDeleteCheckImg',
        payload: response,
      });
      if (response.code == 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },

    /**查询影像关联
     * 参数：
     * 影像标识  std_check表中的id 、平台标识**/
    *findLinkEmrs({ payload, callback }, { call, put }) {
      const response = yield call(findLinkEmrs, payload);
      yield put({
        type: 'findLinkEmrsList',
        payload: response,
      });
      if (response.code == 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**保存影像资料
     * 参数：
     * 影像标识  std_check表中的id 、平台标识**/
    *saveCheckImgs({ payload, callback }, { call, put }) {
      const response = yield call(saveCheckImgs, payload);
      yield put({
        type: 'saveImgs',
        payload: response,
      });
      if (callback && typeof callback === 'function') {
        callback(response);
      }
      if (response.code != 200) {
        notification.error({
          message: response.msg,
        });
      }
    },
  },
  // 同步
  reducers: {
    getTodaySeePending(state, action) {
      return {
        ...state,
        PendingInfo: action.payload || {},
      };
    },
    getAllMedicalRecords(state, action) {
      return {
        ...state,
        allMedicalRecords: action.payload || {},
      };
    },
    getRightPatientInfos(state, action) {
      return {
        ...state,
        rightTopPatientInfo: action.payload || {},
      };
    },
    getMedicalRecordDetail(state, action) {
      return {
        ...state,
        medicalRecordDetail: action.payload || {},
      };
    },
    // 归档
    toBeFile(state, action) {
      return {
        ...state,
        tobeFile: action.payload || {},
      };
    },
    findCustomerBasicByIdInfo(state, action) {
      return {
        ...state,
        basicByIdInfo: action.payload || {},
      };
    },
    // 患者信息
    getPatientOverview(state, action) {
      return {
        ...state,
        patientInfo: action.payload || {},
      };
    },
    //编辑更新患者信息
    getUpdateCustomerBasic(state, action) {
      return {
        ...state,
        savePatientInfo: action.payload || {},
      };
    },
    // 患者健康状况
    getPatHealthCondition(state, action) {
      return {
        ...state,
        healthCondition: action.payload || {},
      };
    },
    // 编辑健康状况
    editPatHealthCondition(state, action) {
      return {
        ...state,
        patHealthConditionInfo: action.payload || {},
      };
    },
    // 保存健康状况
    savePatHealthCondition(state, action) {
      return {
        ...state,
        savePatHealthConditionInfo: action.payload || {},
      };
    },
    // 查看健康状况编辑操作记录
    getHistoryByCustomerId(state, action) {
      return {
        ...state,
        findHistoryList: action.payload || {},
      };
    },

    // 病历记录
    getMedicalRecords(state, action) {
      return {
        ...state,
        medicalRecords: action.payload || {},
      };
    },
    // 影像资料--按分类
    getCheckImgByClass(state, action) {
      return {
        ...state,
        imageByClass: action.payload || {},
      };
    },
    // 影像资料--按时间
    getCheckImgByDate(state, action) {
      return {
        ...state,
        imageByDate: action.payload || {},
      };
    },
    // 预览影像
    getPreviewImage(state, action) {
      return {
        ...state,
        previewImageInfo: action.payload || {},
      };
    },
    // 删除影像及其关联
    getDeleteCheckImg(state, action) {
      return {
        ...state,
        deleteCheckImgRes: action.payload || {},
      };
    },

    // 查询影像关联
    findLinkEmrsList(state, action) {
      return {
        ...state,
        linkEmrsList: action.payload || {},
      };
    },
    // 保存影像数据
    saveImgs(state, action) {
      return {
        ...state,
        saveCheckImgsRes: action.payload || {},
      };
    },
  },
};
export default Model;
