import {
  getList,
  getFilterDict,
} from '@/services/caseQualityCheck';


export default {
  namespace: 'caseQualityCheck',
  state: {
    searchKey: '',       // 搜索关键字
    submitTimeFrom: '',  // 日期--开始
    submitTimeTo: '',      // 日期--结束
    doctorUserId: '',      // 医生
  },

  effects: {
    // 列表
    *getList({ payload }, { call }) {
      const response = yield call(getList, payload);
      return response;
    },
    // 筛选典项
    *getFilterDict({ payload }, { call }) {
      const response = yield call(getFilterDict, payload);
      return response;
    },
  },

  reducers: {
    // 更新状态值数据
    setScreenState(state, { payload }) {
      return {
        ...state,
        ...payload
      }
    },
    // 清空数据
    clean(state){
      return {
        ...state,
        searchKey: '',       // 搜索关键字
        submitTimeFrom: '',  // 日期--开始
        submitTimeTo: '',      // 日期--结束
        doctorUserId: '',      // 医生
      }
    }
  },
  subscriptions: {
    setup({ dispatch, history }) {
      // 监听路由变化
      return history.listen(({ pathname }) => {
        if (!pathname.includes('CaseQualityInspection/CaseQualityCheck')) {
          dispatch({
            type: 'clean'
          });
          localStorage.removeItem('caseQualityCheckDateCleared');
        }
      });
    }
  }
};
