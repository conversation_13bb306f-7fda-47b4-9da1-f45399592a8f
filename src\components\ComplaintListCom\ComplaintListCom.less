.zhusuInput{
  input{
    margin-right: 30px;
  }
  .zhusuInputbtn{
    //margin-bottom: 10px;
    background: #fff;
    //border:1px solid #CBCBCB;
    border-radius: 3px;
    padding: 11px 16px;
    min-height: 80px;
  }
  :global {
    .antd-pro-components-complaint-list-com-complaint-list-com-complaintNameBoxSelected {
      margin: 0px;
    }
  }
}
.grayborder {
  border: 1px solid #CBCBCB;
}

.redborder {
  border: 1px solid #F43D3D;
}

.zhusuIBox{
  // background:rgba(224,226,231,1);
  // padding: 0px 13px;
}

.complaintBox {
  display: inline-block;
}
.contentItem{
  display: flex;
  //padding: 12px;
  font-size: 14px;
  .contentItemTitle{
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    line-height: 32px;
    //min-width: 30px;
    //margin-right: 16px;
  }
  .contentItemContent{
    flex: 1;
    color: rgba(0, 0, 0, 0.85);
  }
}

.complaintNameBox {
  display: inline-block;
  font-size:14px;
  font-weight:400;
  color:#434343;
  cursor: pointer;
  // margin: 3px;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 3px;
  user-select: none;
}

.complaintNameBox:hover {
  //margin: 3px;
  color: #4BA1FF;
  //border: 1px solid #4BA1FF;
  border-radius: 3px;
}

.complaintNameBoxTelephoneCustomerService {
  display: inline-block;
  font-size:14px;
  font-weight:400;
  color:#434343;
  cursor: pointer;
  //margin: 3px;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 3px;
  user-select: none;
}

.complaintNameBoxTelephoneCustomerService:hover {
  //margin: 3px;
  color: #EE6E52;
  //border: 1px solid #4BA1FF;
  border-radius: 3px;
}


.complaintNameBoxSelected {
  display: inline-block;
  font-size:14px;
  font-weight:400;
  cursor: pointer;
  //margin: 3px;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 3px;
  user-select: none;
  background:#4BA1FF;
  color:#fff;
}

.complaintNameBoxSelected:hover {
  color:#fff;
}

.complaintNameBoxSelectedTelephoneCustomerService {
  display: inline-block;
  font-size:14px;
  font-weight:400;
  cursor: pointer;
  //margin: 3px;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 3px;
  user-select: none;
  background:#EE6E52;
  color:#fff;
}

.complaintNameBoxSelectedTelephoneCustomerService:hover {
  color:#fff;
}

.comlaintContent {
  display: inline-block;
  margin-left: -5px;
}

.complaintContentBox {
  display: inline-block;
  padding: 1px 6px;
  user-select: none;
  cursor: pointer;
  border-radius: 3px;
  margin: 5px 4px;
  //font-weight: 700;
}


.complaintContentBoxDefault {
  //background:#E4ECF5;
  color:#444444;
  margin-bottom: 3px;
}

.complaintContentBoxDefault:hover {
  //color: #3e72c2;
  background: #F2F2F2;
  margin-bottom: 3px;
}

.complaintContentBoxDefaultTelephoneCustomerService {
  background: #f9e6e4;
  color:#444444;
  margin-bottom: 3px;
}

.complaintContentBoxDefaultTelephoneCustomerService:hover {
  color: #EE6E52;
  background: #e5d2d0;
  margin-bottom: 3px;
}

.complaintContentBoxSelect {
  color: #ffffff;
  //background: #c6ced7;
  background:#4292FF;
  //font-weight: 700;
  margin-bottom: 3px;
}

.complaintContentBoxSelectTelephoneCustomerService {
  color: #EE6E52;
  //background: #c6ced7;
  background:#e5d2d0;
  //font-weight: 700;
  margin-bottom: 3px;
}

.titleBox {
  height: 10px;
}

.redTextComlaintList {
  color: #F43D3D;
  font-size: 14px;
}

.ModalContentDisable {
  opacity:0.5
}

.appointmentVMListStateText {
  text-align: center;
  margin-top: 27px;
  font-size: 14px;
  color: #666;
}


