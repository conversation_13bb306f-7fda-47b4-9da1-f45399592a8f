import React, {Component} from 'react';
import {Card,Radio,Calendar,Popover} from 'antd';
import { connect } from 'dva';
import $ from 'jquery';
import router from 'umi/router';
import { getOrganizationInfo } from '@/utils/utils';
import { getTiemDeskInfo } from  '@/utils/CalendarUtils.js'
import calendarDeskStyles from './CalendarDesk.less'
import moment from 'moment';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import 'moment/locale/zh-cn'

moment.locale('zh-cn');

/**
 * 预约组件 左右Desk 时间栏
 * */
export default class CalendarDesk extends Component {

  static propTypes = {
    Option:PropTypes.object,
    isRight:PropTypes.bool,
    heightDesk:PropTypes.any
  }
  static defaultProps = {
    Option:{},
    isRight:false,
    heightDesk:null
  }

  constructor(props) {
    super(props);
    const userAgent = window.navigator.userAgent.toLowerCase();
    const isChrome = userAgent.indexOf('chrome') > -1;
    this.state = {
      isChrome,
    }
  }

  render() {
    let DeskInfoList = getTiemDeskInfo(this.props.Option);
    DeskInfoList = Array.isArray(DeskInfoList) ? DeskInfoList : []
    let heightByDesk = this.props.heightDesk ? this.props.heightDesk : 32;
    let heightCellByDesk = heightByDesk
    //let heightCellByDesk = 56

    return (
      <div>
        {!this.props.isRight && <table
          className={classNames({
            [calendarDeskStyles.deskBox]:true,
            [calendarDeskStyles.deskBoxMarginTop]:this.state.isChrome,
          })}><tbody>
          {DeskInfoList.map((res,idx)=>{
            let dataTime = res.subsetMin
            let mm = moment(dataTime, 'HH:mm:ss').minute()
            return (<tr key={`deskInfo_${idx}`}>
              {res.rowspan &&
              <td
                data-time-desk={dataTime}
                className={classNames({
                  [calendarDeskStyles.deakTd]:true,
                  [calendarDeskStyles.deakTdhour]:true,
                  [calendarDeskStyles.hourTimeBorder]:mm == 0 && idx != 0 && DeskInfoList.length-1 != idx,
                  'deskHour':true,
                  'deakTd':true,
                })}
                style={{height:`${heightCellByDesk}px`}}
                rowSpan={res.rowspan}
              >{res.rowspan ? res.hour : null}</td>
              }
              <td
                data-time-desk={dataTime}
                className={classNames({
                  [calendarDeskStyles.deakTd]:true,
                  [calendarDeskStyles.hourTimeBorder]:mm == 0 && idx != 0 && DeskInfoList.length-1 != idx,
                  'deskMinute':true,
                  'deakTd':true,
                })}
                style={{height:`${heightCellByDesk}px`}}
              ><span className={calendarDeskStyles.deskTdcallCotent}>{res.subNum}</span></td>
            </tr>)
          })}
          </tbody>
        </table> }

        {this.props.isRight && <table
          className={classNames({
            [calendarDeskStyles.deskBox]:true,
            [calendarDeskStyles.deskBoxMarginTop]:this.state.isChrome,
          })}>
          <tbody>
          {DeskInfoList.map((res,idx)=>{
            let dataTime = res.subsetMin
            let mm = moment(dataTime, 'HH:mm:ss').minute()

            return (<tr key={`rigth_${idx}`}>
              <td
                data-time-desk={dataTime}
                className={classNames({
                  [calendarDeskStyles.deakTd]:true,
                  [calendarDeskStyles.hourTimeBorder]:mm == 0 && idx != 0 && DeskInfoList.length-1 != idx,
                  //[calendarDeskStyles.hourTimeBorder45]:mm == 45,
                  'deskHour':true,
                  'deakTd':true,
                })}
                style={{height:`${heightCellByDesk}px`}}
              >
                <span className={calendarDeskStyles.deskTdcallCotent}>{res.subNum}</span>
              </td>
              {res.rowspan &&
                <td
                  data-time-desk={dataTime}
                  className={classNames({
                    [calendarDeskStyles.deakTd]:true,
                    [calendarDeskStyles.deakTdhour]:true,
                    [calendarDeskStyles.hourTimeBorder]:mm == 0 && idx != 0 && DeskInfoList.length-1 != idx,
                    'deskMinute':true,
                    'deakTd':true,
                  })}
                  style={{height:`${heightCellByDesk}px`}}
                  rowSpan={res.rowspan}
                >{res.rowspan ? res.hour : null}</td>
              }
            </tr>)
          })}
          </tbody>
        </table> }
      </div>
    )
  }

  componentDidMount() {
    this.borderUpdate()
  }

  componentWillUpdate(nextProps, nextState, nextContext) {
    if (this.props.heightDesk != nextProps.heightDesk) {}
    this.borderUpdate()
  }

  borderUpdate=()=>{

   /* $('deakTd').each((valEl)=>{
      $(valEl).css({'height':'56.7px'}).css({'height':'56.7px'})
    })*/

    /*let DeskInfoList = getTiemDeskInfo(this.props.Option);
    $('.deskHour').each((idx,valEl)=>{
      let dataTime =  $(valEl).attr('data-time-desk')
      let mm = moment(dataTime, 'HH:mm:ss').minute()
      if(mm == 0 && idx != 0){
        $(valEl).css({
          'border-top-color': '#a7a7a7',
          'border-top-width': '2px',
          'border-top-style': 'solid',
        })
      }
    })

    $('.deskMinute').each((idx,valEl)=>{
      let dataTime =  $(valEl).attr('data-time-desk')
      let mm = moment(dataTime, 'HH:mm:ss').minute()
      if(mm == 0 && idx != 0){
        $(valEl).css({
          'border-top-color': '#a7a7a7',
          'border-top-width': '2px',
          'border-top-style': 'solid',
        })
      }
    })*/

  }
}
