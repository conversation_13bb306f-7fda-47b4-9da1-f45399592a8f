
//引入获取同步状态的接口等相关接口
import {
  findSynStatus,
  synSystemStdDiag,
  synSystemStdWord,
  synSystemEmrTmpt,
  synSystemMrcTmpt
} from "@/services/contentSyn";
import { notification } from 'antd';


const Model = {
  namespace: 'synContent',
  state: {
    synStatusInfo: {}, //同步状态
    systemDiags:[],//平台诊断详情
    stdWords: [],//平台词条详情
  },
  effects: {
    //注意：  所有接口统一添加参数  品牌id，机构id，用户名，用户id
    /**获取同步状态
     *
     * **/
    *findSynStatusService({ payload, callback }, { call, put }) {
      const response = yield call(findSynStatus, payload);
      yield put({
        type: 'findSynStatusInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    /**同步诊断
     *
     * **/
    *synSystemStdDiagService({ payload, callback }, { call, put }) {
      const response = yield call(synSystemStdDiag, payload);
      yield put({
        type: 'synSystemStdDiagInfo',
        payload: response,
      });

      if (response.code != 200){
        notification.error({
          message: response.msg,
        });
      }
      if (callback && typeof callback === 'function') {
        callback(response);
      }
    },
    /**同步词条
     *
     * **/
    *synSystemStdWordsService({ payload, callback }, { call, put }) {
      const response = yield call(synSystemStdWord, payload);
      yield put({
        type: 'synSystemStdWordInfo',
        payload: response,
      });

      if (response.code != 200){
        notification.error({
          message: response.msg,
        });
      }
      if (callback && typeof callback === 'function') {
        callback(response);
      }
    },
    *synSystemEmrTmptService({ payload, callback }, { call, put }) {
      const response = yield call(synSystemEmrTmpt, payload);
      yield put({
        type: 'synSystemEmrTmptInfo',
        payload: response,
      });

      if (response.code != 200){
        notification.error({
          message: response.msg,
        });
      }
      if (callback && typeof callback === 'function') {
        callback(response);
      }
    },
    *synSystemMrcTmptService({ payload, callback }, { call, put }) {
      const response = yield call(synSystemMrcTmpt, payload);
      yield put({
        type: 'synSystemMrcTmptInfo',
        payload: response,
      });

      if (response.code != 200){
        notification.error({
          message: response.msg,
        });
      }
      if (callback && typeof callback === 'function') {
        callback(response);
      }
    },
    /**获取平台诊断详情
     *
     * **/
    // *findSystemDiagsService({ payload, callback }, { call, put }) {
    //   const response = yield call(findSystemDiags, payload);
    //   yield put({
    //     type: 'findSystemDiagsInfo',
    //     payload: response,
    //   });
    //
    //   if (response.code != 200){
    //     notification.error({
    //       message: response.msg,
    //     });
    //   }
    //   if (callback && typeof callback === 'function') {
    //     callback(response);
    //   }
    // },
    /**获取平台词条详情
     *
     * **/
    // *findSysStdWordService({ payload, callback }, { call, put }) {
    //   const response = yield call(findSysStdWord, payload);
    //   yield put({
    //     type: 'findSysStdWordInfo',
    //     payload: response,
    //   });
    //
    //   if (response.code != 200){
    //     notification.error({
    //       message: response.msg,
    //     });
    //   }
    //   if (callback && typeof callback === 'function') {
    //     callback(response);
    //   }
    // },
    /**获取平台同意书详情
     *
     * **/
  //   *findSysStdMrcsService({ payload, callback }, { call, put }) {
  //     const response = yield call(findSysStdMrc, payload);
  //     yield put({
  //       type: 'findSysStdMrcInfo',
  //       payload: response,
  //     });
  //
  //     if (response.code != 200){
  //       notification.error({
  //         message: response.msg,
  //       });
  //     }
  //     if (callback && typeof callback === 'function') {
  //       callback(response);
  //     }
  //   },
  },
  reducers: {
    findSynStatusInfo(state, action) {
      return {
        ...state,
        synStatusInfo: action.payload || [],
      };
    },
    synSystemStdDiagInfo(state, action) {
      return {
        ...state,
        resultMsg: action.payload || [],
      };
    },
    synSystemStdWordInfo(state, action) {
      return {
        ...state,
        resultMsg: action.payload || [],
      };
    },
    synSystemEmrTmptInfo(state, action) {
      return {
        ...state,
        resultMsg: action.payload || [],
      };
    },
    synSystemMrcTmptInfo(state, action) {
      return {
        ...state,
        resultMsg: action.payload || [],
      };
    },
    // findSystemDiagsInfo(state, action) {
    //   return {
    //     ...state,
    //     systemDiags: action.payload || [],
    //   };
    // },
    // findSysStdWordInfo(state, action) {
    //   return {
    //     ...state,
    //     stdWords: action.payload || [],
    //   };
    // },
    // findSysStdMrcInfo(state, action) {
    //   return {
    //     ...state,
    //     stdMrcs: action.payload || [],
    //   };
    // },
  },
};

export default Model;
