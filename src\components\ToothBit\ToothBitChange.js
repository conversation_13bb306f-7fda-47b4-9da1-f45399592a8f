import React , { Component } from 'react'
import { Badge, Icon } from 'antd';
import PropTypes from 'prop-types';

import styles from './ToothBit.less'

/**
 *   topLeft:[
             {index:1, side:['M','O','C']},
           ],
     topRight:[],
     bottomLeft:[],
     bottomRight:[
         {index:3, side:['M','C']},
         {index:5, side:['M']}
   ],
 */

const ToothInfo = {
  topLeft:null,
  topRight:null,
  bottomLeft:null,
  bottomRight:null,
};

class ToothBit extends Component{

  static propTypes = {
    ToothBefore:PropTypes.string,
    ToothAfter:PropTypes.string,
    ToothInfo:PropTypes.object,
    onClickToothBit:PropTypes.func,
  };

  static defaultProps = {
    ToothBefore:null,
    ToothAfter:null,
    ToothInfo:ToothInfo,
    onClickToothBit:()=>{},
  };

  /*disposeInitData=(ToothInfo)=>{


  }*/

  constructor(props) {
    super(props);
    this.state={
      ToothBefore:props.ToothBefore,
      ToothAfter:props.ToothAfter,
      ToothInfo:props.ToothInfo || ToothInfo ,
    }
  }
  componentDidMount() {

  }


  componentWillReceiveProps(nextProps) {
    this.setState({
      ToothBefore:nextProps.ToothBefore,
      ToothAfter:nextProps.ToothAfter,
      ToothInfo: nextProps.ToothInfo || ToothInfo
    })
  }

  onClickToothBit=()=>{
    this.props.onClickToothBit && this.props.onClickToothBit()
  }

  render() {
    const  { data,ToothInfo } = this.state;
    const Style1 = {
      color:'#fff',
    };
    const Style2 = {
      display: 'none'
    };
    return (
      <div onClick={this.onClickToothBit} className={styles.ToothBit}>
        <table border="0">
          <tbody>
          <tr>
            <td rowSpan="2" style={{ borderRightWidth: '0' ,borderBottomWidth: '0' ,paddingRight: '10px'}}>{this.state.ToothBefore}</td>
            <td style={{borderRight:'1px solid black'}}>
              {
                ToothInfo.topLeft && ToothInfo.topLeft.map((val,idx)=>{
                  return (
                    <div className={styles.ToothBitS} key={`TopLeft${idx}`}>
                      <span>{val.teethNumber}</span>
                      <div className={styles.ToothBitS0NE}>
                        {val.side &&  val.side.map((item)=>{
                          let subName = item.subName
                          let classifyName = item.classifyName
                          return `${subName}${classifyName},`
                        })}
                      </div>
                    </div>)
                })
              }
              {
                !ToothInfo.topLeft && <div className={styles.ToothBitEmpty}/>
              }
            </td>
            <td>
              {
                ToothInfo.topRight && ToothInfo.topRight.map((val,idx)=>{
                  return (<div className={styles.ToothBitS} key={`TopRight${idx}`}>
                    <span>{val.teethNumber}</span>
                    <div className={styles.ToothBitS0NE}>
                      {val.side &&  val.side.map((item)=>{
                        let subName = item.subName
                        let classifyName = item.classifyName
                        return `${subName}${classifyName},`
                      })}
                    </div>
                  </div>)
                })
              }
              {
                !ToothInfo.topRight  && <div className={styles.ToothBitEmpty}/>
              }
            </td>
            <td rowSpan="2" style={{ borderLeftWidth: '0' ,borderBottomWidth: '0' ,paddingLeft: '10px'}}>{this.state.ToothAfter}</td>
          </tr>
          <tr>
            <td style={{borderRight:'1px solid black'}}>
              {
                ToothInfo.bottomLeft && ToothInfo.bottomLeft.map((val,idx)=>{
                  return (<div className={styles.ToothBitS} key={`BottomLeft${idx}`}>
                    <span>{val.teethNumber}</span>
                    <div className={styles.ToothBitS0NE}>
                      {val.side &&  val.side.map((item)=>{
                        let subName = item.subName
                        let classifyName = item.classifyName
                        return `${subName}${classifyName},`
                      })}
                    </div>
                  </div>)
                })
              }
              {
                !ToothInfo.bottomLeft  && <div className={styles.ToothBitEmpty}></div>
              }
            </td>
            <td>
              {
                ToothInfo.bottomRight && ToothInfo.bottomRight.map((val,idx)=>{
                  return (<div className={styles.ToothBitS} key={`BottomRight${idx}`}>
                    <span>{val.teethNumber}</span>
                    <div className={styles.ToothBitS0NE}>
                      {val.side && val.side.map((item)=>{
                        let subName = item.subName
                        let classifyName = item.classifyName
                        return `${subName}${classifyName},`
                      })}
                    </div>
                  </div>)
                })
              }
              {
                !ToothInfo.bottomRight  && <div className={styles.ToothBitEmpty}></div>
              }
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    );
  }
}
export default ToothBit
