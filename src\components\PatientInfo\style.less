@import '~antd/es/style/themes/default.less';

.background {
  padding: 16px 20px;
  background-color: #f8f8fa;
  border-radius: 5px;
}
.fontWeight {
  font-weight: bold;
  font-size: 16px;
}
.nameEllipse {
  //border:1px solid #000c17;
  max-width: 15%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.pd_lr {
  padding: 0 4px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}
.mg_top {
  margin-top: 8px;
}
.titleBorderStyle{
  max-width: 100%;
  margin-top: 8px;
  max-height: 31px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.contentTop {
  display: flex;
}
:global {
  .ant-tag-orange {
    color: rgba(245, 34, 45, 1);
    font-size: 12px;
    background-color: rgba(255, 242, 232, 1);
    border: 1px solid rgba(255, 187, 150, 1);
    border-radius: 4px !important;
  }
  .ant-tag-purple {
    color: rgba(114, 46, 209, 1);
    font-size: 12px;
    background-color: rgba(249, 240, 255, 1);
    border: 1px solid rgba(211, 173, 247, 1);
    border-radius: 5px !important;
  }
  .ant-tag-blue {
    color: rgba(24, 144, 255, 1);
    font-size: 12px;
    background-color: rgba(230, 247, 255, 1);
    border: 1px solid rgba(145, 213, 255, 1);
    border-radius: 5px !important;
  }
}
.icon_diamond {
  width: 16px;
  height: 14px;
  margin-top: 5px;
  margin-right: 4px;
}
.icon_boy {
  width: 14px;
  height: 14px;
  margin-top: 5px;
  margin-right: 4px;
}
.icon_girl {
  width: 20px;
  height: 20px;
  margin-top: 1px;
  margin-right: 4px;
}
.line {
  width: 1px;
  height: 9px;
  margin: 0 4px;
  margin-top: 7px;
  background-color: #d8d8d8;
}
