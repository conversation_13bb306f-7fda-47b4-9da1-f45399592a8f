import React, { Component } from 'react';
import {Card,Radio,message} from 'antd';
import $ from 'jquery';
import moment from 'moment';
import 'fullcalendar';
import 'fullcalendar-scheduler';
import 'fullcalendar/dist/locale/zh-cn';
/*import 'fullcalendar/dist/fullcalendar.css';*/
import classNames from 'classnames';
import styles from './TimeAgendaCalendar.less'
import stylesHeader from './TimeCalendarHeader.less'
import commonStyles from '../AgendaCaleander.less'
import PropTypes from 'prop-types';
import Ellipsis from '@/components/Ellipsis';
import chairIcon from '@/assets/registerAndArrival/chair.png'
import doctorIcon  from '@/assets/registerAndArrival/icon_doctor.png'

const workStatusForDayOff =  2 // 休息日
const workStatusForOther  =  3 // 走诊医生
const workStatusForNot    =  4 // 未排班医生
const workStatusForOrganizationStatus  =  5             // 走诊机构

const rowTotal = 7              // 列表项总列数
const lableClickClassName = 'labelTh' // 表头时间行点击className
import {
  getEventElement,
  getChairNum,
  newCustomerEventType,
  oldCustomerEventType,
  otherEventType,
  backgroundType,
  LarageHeight,
  MediumHeight,
  SmallHeight,
  tittleHight,
  slotLabelFormat,
  schedulerLicenseKey,
  eventPatientNameEllipsis // 患者名称添加省略号
} from '@/utils/CalendarUtils'


/**
 * 区分使用 拆分 表格头部组件 和 表格列组件
 */
export default class TimeCalendarHeader extends Component {

  static propTypes = {
    calendarTableList:PropTypes.any,
    goNext:PropTypes.func,          // 下一页
    goLast:PropTypes.func,          // 上一页
    onClickTitleForDoctor:PropTypes.func,
    onClickTitleForData:PropTypes.func,
    isDisenableClickTitle:PropTypes.bool,
    isWaitingInfo:PropTypes.bool,      // 是否展示等待列表时间信息
    onClickWaitingInfo:PropTypes.func, // 点击医生等待表头
    isfullScreen:PropTypes.bool,
  }

  static defaultProps = {
    calendarTableList:null,
    goNext:()=>{},
    goLast:()=>{},
    onClickTitleForDoctor:()=>{},
    onClickTitleForData:()=>{},
    isDisenableClickTitle:true,  // 是否禁用
    isWaitingInfo:false,         // 是否展示等待列表时间信息
    onClickWaitingInfo:()=>{},   // 点击医生等待表头
    isfullScreen:false
  }

  /**
   * 渲染列表方法
   * @returns {XML}
   */
  applyCalendarHeader=(props)=>{
    this.setState({
      calendarTableList:props.calendarTableList,
      resources:this.getResources(props.calendarTableList)
    })
  }

  /**
   * 获取resources列表项目数据
   * 遍历列表结构的同时添加椅位资源列事件
   * @returns {Array}
   */
  getResources=(calendarTableList)=>{
    let resources = [];                         // 椅位资源列
    let groupTotal = calendarTableList.length;  // 医生总数
    let EventListPlue = [];
    calendarTableList.forEach((val,idx)=>{
      if(val.resources){
        val.resources.forEach((res,resIdx)=>{
          const { staffStatus,chairTotal, type, workStatus, workTimeEnd, workTimeStart, date, doctorId, groupId, name, organizationName,
          } = val || {}
          //id: 4, chairNum: 2, title: "椅位2"
          resources.push({
            ...res,
            chairTotal,
            workStatus:res.organizationStatus == 2 ? 5 : workStatus,
            workTimeEnd,
            workTimeStart,
            staffStatus,
            type,
            date,
            doctorId,
            groupId,
            name,
            organizationName:res.organizationStatus == 2 ? res.organizationName : organizationName,
            resLength:val.resources.length
          });
        })
      }
    })
    return resources;
  }

  /**
   * 上一页
   */
  goToLast=()=>{
    this.props.goLast(); // 调用前往上一页方法
  }

  /**
   * 下一页
   */
  goToNext=()=>{
    this.props.goNext()
  }

  /**
   * 点击表格头的医生
   */
  onClickTitleForDoctor=(res)=>{
    const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1
    if (doctorIdentificationState) { //   医生登录
      this.props.onClickTitleForDoctor(res)
      /*if (res.operationType == 1) {
        this.props.onClickTitleForDoctor(res)
      } else {
        message.warning('当前医生无操作权限', 1)
      }*/
    }else { // 客服登录
      this.props.onClickTitleForDoctor(res)
    }
  }

  /**
   * 点击表格头的日期
   */
  onClickTitleForData=(res)=>{
    this.props.onClickTitleForData(res)
  }


  /**
   * ------------------生命周期---------------
   * @param props
   * <i id="goDisableLastIcon" class="goDisableLastIcon"></i>
   * <i id="goNextIcon" class="goNextIcon"></i>
   */

  constructor(props){
    super(props);
    this.state = {
      resources:[],         // 全部椅位列表
      calendarTableList:[]  // 医生和椅位列表
    }
  }

  render(){
    const {current, pageSize, total, pageCount } = this.props.appointmentPageInfo
    console.log('AgendaHeadTypeAgendaHeadType :: ',this.props.AgendaHeadType,this.props.isfullScreen,!this.props.isfullScreen);
    return (
      <div className={classNames({
        [stylesHeader.HeaderTableWeap]:true,
        [stylesHeader.HeaderTableDisClickWeap]:this.props.isDisenableClickTitle
      })}>
        <table>
          <thead styles={{border: 1}}>
          <tr className={classNames({
            "DoctorNameTr":true,
            [styles.DoctorNameTrBy38]:!!this.props.isfullScreen && this.props.AgendaHeadType == 1,
            [styles.DoctorNameTrBy45]:!!this.props.isfullScreen && this.props.AgendaHeadType == 2,
            [styles.DoctorNameTrBy38]:!this.props.isfullScreen,
          })}>
            {/*fc-axis fc-widget-header*/}
            {/*<th className={classNames({
              'fc-axis fc-widget-header':true,
              [stylesHeader.doctorIconTh]:true,
            })} style={{width: 68, textAlign: 'center',paddingLeft:'0px'}}>
              <img src={doctorIcon}  className={stylesHeader.doctorIcon}/>
            </th>*/}
            {
              this.state.calendarTableList && this.state.calendarTableList.map((res,idx)=>{
                let conSpanNum = res.resources.length;
                // let workStatusText = res.workStatus ==  workStatusForDayOff ?  '(休息)' : ''
                // let workStatusText = res.workStatus ==  workStatusForOther?  `(${res.organizationName})` : workStatusText
                // workStatusText = res.workStatus ==  workStatusForNot ?  `(未排班)` : workStatusText

                let showWaitingListInfo = true
                /*if(conSpanNum == 1){
                  showWaitingListInfo = res.resources[0] && res.resources[0].organizationStatus == 2 ? false : true
                }*/
                console.log('1123123123res ::',res);
                const {
                  doctorId,
                  doctorUserId
                } = res || {}
                if (res.groupId != 'suplus') {
                  if (res.name && this.props.AgendaHeadType == 1) {
                    return (
                      <th
                        key={'AgendaTitile' + idx}
                        className={'agendaTitileTh'}
                        colSpan={conSpanNum}
                        doctorId={doctorId}
                        doctorUserId={doctorUserId}
                      >
                      <div className={stylesHeader.AgendaHeadName}>
                        <div>
                          <span
                            // onClick={() => { this.onClickTitleForDoctor(res)}}
                            className={classNames({[stylesHeader.AgnedaTextOnClick]:this.props.isfullScreen})}
                          >{res.name}</span>
                        </div>
                        {this.props.isWaitingInfo &&
                          <div
                            className={stylesHeader.AgendaExtraInfo}
                            onClick={(event) => {
                              event.stopPropagation()

                              //判定等待列表展示左边还是右边
                              let appintmentCalendarWidth = $('#AppintmentCalendar').width()
                              let thOffsetLeft = $(event.target).parents('.agendaTitileTh').offset().left;

                              let hiftWidth = appintmentCalendarWidth / 2

                              if(hiftWidth < thOffsetLeft) {
                                res.waitingListSite = 'left'
                              }else {
                                res.waitingListSite = 'right'
                              }
                              // 点击当前医生等待表头
                              this.props.onClickWaitingInfo(res)
                            }}
                          >
                            {showWaitingListInfo &&
                            <span>
                                <span>空闲<span className={stylesHeader.extraNumRed}>{res.workStatus == 3 ? 0 : res.freeTime}</span>分钟</span>
                                <span>,等待中<span
                                  className={stylesHeader.extraNumRed}>{res.waitingListCount}</span>人</span>
                              </span>
                            }

                          </div>
                        }
                      </div>
                    </th>)
                  } else {
                    let titleDate = moment(res.date, "YYYY-MM-DD").format('YYYY-MM-DD')
                    let titleWeek = moment(res.date, "YYYY-MM-DD").format('dddd')
                    //let dateClickStyle = dateClickStatus ? 'cursor: pointer' : ''
                    return (
                      <th key={'head' + idx} colSpan={conSpanNum} className={classNames(lableClickClassName,'agendaTitileTh')} date={titleDate}>
                        <div className={stylesHeader.lableTitleHead}>
                          <div
                            // onClick={() => { this.onClickTitleForData(res) }}
                            className={classNames({[stylesHeader.AgnedaTextOnClick]:this.props.isfullScreen})}
                          >
                            <div className={stylesHeader.labelTdsTitle}>{titleDate} {titleWeek}</div>
                            {/*<div className={stylesHeader.labelTdsTitle}>{titleWeek}</div>*/}
                          </div>
                          {/*<p className={stylesHeader.labelTdsTitle}>{workStatusText}</p>*/}
                          { this.props.isWaitingInfo &&
                            <div>
                              <div
                                className={stylesHeader.AgendaExtraInfo}
                                onClick={(event) => {
                                  event.stopPropagation()
                                  //判定等待列表展示左边还是右边
                                  let appintmentCalendarWidth = $('#AppintmentCalendar').width()
                                  let thOffsetLeft = $(event.target).parents('.agendaTitileTh').offset().left;

                                  let hiftWidth = appintmentCalendarWidth / 2

                                  if(hiftWidth < thOffsetLeft) {
                                    res.waitingListSite = 'left'
                                  }else {
                                    res.waitingListSite = 'right'
                                  }
                                  // 点击当前医生等待表头
                                  this.props.onClickWaitingInfo(res)
                                }}
                              >
                                {showWaitingListInfo &&
                                  <span>
                                      <span>空闲<span className={stylesHeader.extraNumRed}>{res.workStatus == 3 ? 0 : res.freeTime}</span>分钟</span>
                                      <span>,等待中<span className={stylesHeader.extraNumRed}>{res.waitingListCount}</span>人</span>
                                    </span>
                                }

                              </div>
                            </div>
                          }
                        </div>
                      </th>
                    )
                  }
                  // 不足当前页数条数 补充资源头
                }else {
                  return (
                    <th key={'head' + idx} colSpan={conSpanNum} className={classNames({
                      [lableClickClassName]:true,
                      [stylesHeader.suplusHeader]:true,
                    })}>
                      <div className={stylesHeader.lableTitleHead}></div>
                    </th>
                  )
                }
              })
            }
          </tr>
          <tr>
            {/*<th className="fc-axis fc-widget-header" style={{width: 68, textAlign: 'center'}}>
              <img src={chairIcon}  className={stylesHeader.chairIcon}/>
            </th>*/}
            {
              this.state.resources && this.state.resources.map((res,idx)=>{
                if (res.groupId != 'suplus') {
                  if (res.organizationStatus == 2) {
                    return (
                      <th key={'cell' + idx} className="fc-resource-cell" data-resource-id={res.id}>
                        <div className={styles.chairIconWarp}><i className={styles.chairIcon}></i></div>
                        <span className={styles.chiarText}>外</span>
                      </th>
                    )
                  }
                  return (
                    <th key={'cell' + idx} className="fc-resource-cell" data-resource-id={res.id}>
                      <div
                         // style={{height:"25px"}}
                        className={styles.chiarItemWarp}
                      >
                        {/*<div className={styles.chairIconWarp}><i className={styles.chairIcon}></i></div>*/}
                        {res.chairTotal == 1 ? <span className={styles.chiarText}><span>{res.chairTotal}</span></span> :
                          <span className={styles.chiarText}>
                            <span className={styles.redChiarNum}>{res.chairNum}</span>
                            <span>/</span>
                            <span>{res.chairTotal}</span>
                          </span>}
                        {res.chairStatus == 2 && <div className={styles.chiarState}>(休息)</div> }
                        {this.props.isWaitingInfo && res.chairStatus == 1 && res.chairTotal != 1 &&
                        <div className={styles.chairFireTime}>
                          空闲 <span className={stylesHeader.extraNumRed}>{res.freeTimeOfChair}</span> 分钟
                        </div>
                        }
                      </div>

                    </th>
                  )
                }else {
                  //fc-resource-cell
                  return (
                    <th key={'cell' + idx} className={classNames({
                      'fc-resource-cell':true,
                      [stylesHeader.suplusHeader]:true
                    })}>

                    </th>
                  )
                }
              })
            }
          </tr>
          </thead>
        </table>
        {/* { current > 1 && <div onClick={this.goToLast} className={stylesHeader.goToLast}> <i id="goDisableLastIcon" className="goDisableLastIcon"></i></div>}
        { current < pageCount && <div onClick={this.goToNext} className={stylesHeader.goToNext}> <i id="goNextIcon" className="goNextIcon"></i></div>} */}
      </div>
    )
  }

  componentWillReceiveProps(nextProps){
    this.applyCalendarHeader(nextProps)
  }

  componentDidMount() {
    this.applyCalendarHeader(this.props)
  }
}


