import request from '@/utils/request';
import { stringify } from 'querystring';

// 获取基础治疗分组列表
export async function appointmentList(params) {
  const {
    pageSize,             // 获取条数
    pageNow,               // 获取页数
  } = params || {}
  return request(`/api/appointment/appointmentMornMeet/appointmentList?${stringify({pageSize,pageNow})}`, {
    method: 'POST',
    data: params,
  });
}

// 根据预约ID查询预约建议
export async function getAppointmentOpinion(params) {
  return request(`/api/appointment/appointmentMornMeet/getAppointmentOpinion?${stringify(params)}`, {
    method: 'GET',
    data: params,
  });
}


// 编辑或保存晨会意见
export async function saveOrUpdateOpinion(params) {
  return request(`/api/appointment/appointmentMornMeet/saveOrUpdateOpinion`, {
    method: 'POST',
    data: params,
  });
}
