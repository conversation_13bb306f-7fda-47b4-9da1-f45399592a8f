export default [
  // userInfo
  {
    path: '/userinfo',
    component: '../layouts/InfoLayout',
    routes: [
      { path: '/userinfo', redirect: '/userinfo/information' },
      { path: '/userinfo/information', component: './UserInforMation' }, // 新完善信息页面
    ],
  },
  // user
  {
    path: '/market',
    component: '../layouts/MarketLayout',
    routes: [
      { path: '/market', redirect: '/market/CloudClinic' },
      { path: '/market/CloudClinic', component: './MarketPage/CloudClinic/index' },
    ],
  },

  // 三方进入CRM方案页面
  { path: '/marketPlan', component: './MarketPage/Plan/index', },

  //新400客服
  {
    path: '/customerService',
    component: '../layouts/CustomerServiceNewLayout',
    Routes: ['src/pages/Authorized'],
    routes: [
      {
        path: '/customerService', redirect: '/customerService/appointment'
      },
      {
        path: '/customerService/SavePassword', component: './CustomerServiceNew/SetPass/index.js',
      },
      { //患者预约
        path: '/customerService/appointment',
        component: './CustomerServiceNew/Appointment',
        name: 'customerServiceAppointment',
        icon: 'appointmentActive',
        authority: ['76'],
      },
      { // 患者管理
        path: '/customerService/patient',
        name: 'customerServicePatient', icon: 'patientActive',
        authority: ['77'],
        routes: [
          {
            path: '/customerService/patient',
            redirect: '/customerService/patient/index'
          },
          {
            path: '/customerService/patient/index',
            component: './CustomerServiceNew/Patient/index',
          },
          { //个人信息
            path: '/customerService/patient/details',
            component: './CustomerServiceNew/ArchivesNew/index',
            routes: [
              {
                path: '/customerService/patient/details',
                redirect: '/customerService/patient/details/personal'
              },
              { // 个人信息
                path: '/customerService/patient/details/personal',
                component: './CustomerServiceNew/ArchivesNew/PersonalAndSocial/index'
              },
              { // 健康偏好展示页面
                path: '/customerService/patient/details/health',
                component: './CustomerServiceNew/ArchivesNew/Health/index'
              },
              { // 服务历史
                path: '/customerService/patient/details/service',
                component: './CustomerServiceNew/ArchivesNew/Service/index'
              },
              { // 延保记录
                path: '/customerService/patient/details/warrantyrecord',
                component: './CustomerServiceNew/ArchivesNew/YanBaoPart/index'
              }
            ]
          },
        ]
      },

      { // 设置
        path: '/customerService/set',
        name: 'customerServiceSet', icon: 'setActive',
        authority: ['78'],
        routes: [
          {
            path: '/customerService/set',
            redirect: '/customerService/set/index',

          },
          {
            path: '/customerService/set/index',
            component: './CustomerServiceNew/Set',
          },
          // 医生专业配置页
          {
            path: '/customerService/set/professional',
            component: './CustomerServiceNew/Set/Professional',
          }
        ]
      },
      { // 福利查询
        path: '/customerService/welfare',
        component: './CustomerServiceNew/Welfare',
        name: 'customerServiceWelfare', icon: 'welfareActive',
        authority: ['79'],
      },
      { // 打印页面
        path: '/customerService/printpatment',
        component: './CustomerServiceNew/ArchivesNew/PrintPages/PrintPayment.js',
      },
      { // 投诉管理
        path: '/customerService/complaint',
        name: 'customerServiceComplaint', icon: 'complaintActive',
        authority: ['108'],
        routes: [
          {
            path: '/customerService/complaint',
            redirect: '/customerService/complaint/list',
          },
          {
            path: '/customerService/complaint/list',
            component: './Complaint/List',
          },
          {
            path: '/customerService/complaint/create',
            component: './Complaint/Create'
          },
          {
            path: '/customerService/complaint/edit/:id',
            component: './Complaint/Create'
          },
          {
            path: '/customerService/complaint/details/:id',
            component: './Complaint/Details'
          },
          {
            path: '/customerService/complaint/followUp/:id',
            component: './Complaint/FollowUp'
          },
        ]
      },
      { // 查询收费项
        path: '/customerService/searchChargeItem',
        component: './CustomerServiceNew/SearchChargeItem',
        name: 'searchChargeItem', icon: 'searchChargeItemActive',
        authority: ['120'],
      },
    ],
  },

  // 病历质检  - 详情页
  {
    path: '/CaseQualityDetail',
    component: '../layouts/CaseQualityDetailLayout',
    Routes: ['src/pages/Authorized'],
    routes: [
      {
        path: '/CaseQualityDetail/:emrSubId', component: './CaseQualityDetail/index'
      }]
  },

  // app
  {
    path: '/',
    component: '../layouts/BasicLayout',
    Routes: ['src/pages/Authorized'],
    routes: [
      // dashboard
      { path: '/', redirect: '/home' },
      // 账户
      {
        path: '/account',
        routes: [
          // 账户中心
          {
            path: '/account/center',
            component: './Account/index',
            routes: [
              {
                path: '/account/center',
                redirect: '/account/center/personal',
              },
              { // 个人信息
                path: '/account/center/personal',
                component: './Account/Personal/index',
              },
              { // 个人信息编辑页
                path: '/account/center/personal/redact',
                component: './Account/Personal/redact',
              },
              { // 我的证书
                path: '/account/center/certificate',
                component: './Account/Certificate/index',
              },
              { // 账户安全设置
                path: '/account/center/security',
                component: './Account/Security/index',
              },
              { // 账号授权管理
                path: '/account/center/authorization',
                component: './Account/Authorization/index',
              }
            ]
          },
          // 其他设置
          {
            path: '/account/customer',
            component: './Acustomer/index',
            routes: [
              {
                path: '/account/customer',
                redirect: '/account/customer/subscribe',
              },
              { // 预约规则
                path: '/account/customer/subscribe',
                component: './Acustomer/Subscribe/index',
              },
              {// 操作记录
                path: '/account/customer/operating',
                component: './Appointment/OperatingData'
              },
              { // 400开放预约量
                path: '/account/customer/quantity',
                component: './Acustomer/Quantity',
              },
              { // 默认收费级别
                path: '/account/customer/charge',
                component: './Acustomer/Charge/index',
              },
            ]
          },
        ],
      },
      /*-----------------------业务常用------------------------- */
      // 首页
      {
        name: 'home',
        icon: 'Home',
        path: '/home',
        class: 'menu',
        className: "header",
        grouping: 'common',       // 分组字段
        styleColor: '0',           // 样式大小
        backgroundColor: '0',        // 背景色
        routes: [
          { path: '/home', redirect: '/home/<USER>' },
          {
            path: '/home/<USER>', // 新首页
            component: './Home/newHomeIndex',
          },
          {
            path: '/home/<USER>', // 预约日模式首页（旧首页）
            component: './Home/homeIndex',
          }
        ],
      },
      {
        path: '/allTasks', // 全部任务
        component: './Home/AllTasks/index',
        subIcon: 'menu_task',         // 左侧菜单  icon
        name: 'allTasks',           // 左侧菜单  名字
        subType: '1',                 // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/allTasks/subscribeTouch', // 预触
            component: './Home/AllTasks/index'
          },
          {
            path: '/allTasks/firstTouch', // 首触
            component: './Home/AllTasks/index'
          },
          {
            path: '/allTasks/diagnosticTouch', // 诊触
            component: './Home/AllTasks/index'
          },
          {
            path: '/allTasks/endTouch', // 末触
            component: './Home/AllTasks/index'
          },
          {
            path: '/allTasks/continueTouch', // 延触
            component: './Home/AllTasks/index'
          },
        ]
      },
      {
        name: 'tiemCalendar',
        icon: 'Subscribe',
        authority: ['59'],
        path: '/subscribe',
        grouping: 'statistics',       // 分组字段
        styleColor: '0',           // 样式大小
        backgroundColor: '1',        // 背景色
        subIcon: 'menu_service',        // 左侧菜单  icon
        subName: '服务中心',             // 左侧菜单  名字
        subType: '2',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          { path: '/subscribe', redirect: '/subscribe/fullscreen' }, // 1.3.1 版本直接默认全屏
          {
            path: '/subscribe/fullscreen/:date?', // 全屏预约
            component: './Appointment/Subscribe-Fullscreen/Subscribe-Fullscreen'
          },
        ],
      },
      {  // 医生工作台=>电子病历的
        name: 'Diagnosis',
        icon: 'Diagnosis',
        path: '/ArriveToday',
        authority: ['201'],
        grouping: 'statistics',       // 分组字段
        styleColor: '1',           // 样式大小
        backgroundColor: '2',        // 背景色
        subType: '3',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/ArriveToday',
            redirect: '/ArriveToday/index',
          },
          {
            path: '/ArriveToday/index',
            component: './EmrPart/index',
          },
        ]
      },
      {  // 医生工作台=>病历质检
        name: 'CaseQualityInspection',
        icon: 'CaseQualityInspection',
        path: '/CaseQualityInspection',
        authority: ['210'],
        grouping: 'statistics',       // 分组字段
        styleColor: '1',           // 样式大小
        backgroundColor: '2',        // 背景色
        subType: '3',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/CaseQualityInspection',
            component: './CaseQualityInspection/index',
          },
          {
            path: '/CaseQualityInspection/CaseQualityCheck',
            component: './CaseQualityCheck/index',
          },
          {
            path: '/CaseQualityInspection/CaseQualityRedLine',
            component: './CaseQualityRedLine/index',
          },
          {
            path: '/CaseQualityInspection/CaseQualityDistribution',
            component: './CaseQualityDistribution/index',
          },
          {
            path: '/CaseQualityInspection/CaseQualitySetting',
            component: './CaseQualitySetting/index',
          },
        ]
      },
      // 补开收费项目（新）
      {
        path: '/surcharge',
        name: 'surcharge',
        icon: 'Repairfees',
        authority: ['75'],
        grouping: 'statistics',       // 分组字段
        styleColor: '1',           // 样式大小
        backgroundColor: '3',        // 背景色
        subType: '3',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/surcharge',
            component: './Surcharge/index'
          }
        ]
      },
      // 医疗中心——优秀病例
      {
        path: '/ExcellentCaseStudy',
        name: 'excellentCaseStudy',
        icon: 'CaseStudy',
        authority: ['142'],
        grouping: 'statistics',       // 分组字段
        styleColor: '1',           // 样式大小
        backgroundColor: '3',        // 背景色
        subType: '3',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/excellentCaseStudy',
            redirect: '/excellentCaseStudy/index',
          },
          {
            path: '/excellentCaseStudy/index', // 优秀病历列表
            component: './ExcellentCaseStudy/index'
          },
          {
            path: '/excellentCaseStudy/details/:id/:starUserId', // 优秀病历-病历详情
            component: './ExcellentCaseStudy/Details/index'
          }
        ]
      },
      // 医疗中心——专家指导
      {
        path: '/ExpertConsultation',
        name: 'expertConsultation',
        icon: 'ExpertConsultation',
        // authority: ['142'],
        grouping: 'statistics',       // 分组字段
        styleColor: '1',           // 样式大小
        backgroundColor: '3',        // 背景色
        subType: '3',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/ExpertConsultation',
            redirect: '/ExpertConsultation/index',
          },
          {
            path: '/ExpertConsultation/index', // 专家指导
            component: './ExpertConsultation/index'
          },
          {
            path: '/ExpertConsultation/ExpertDetail', // 专家指导主页
            component: './ExpertConsultation/ExpertDetail'
          },
        ]
      },
      // 医疗中心——专家指导
      {
        path: '/MyConsultation',
        name: 'myConsultation',
        icon: 'MyConsultation',
        // authority: ['142'],
        grouping: 'statistics',       // 分组字段
        styleColor: '1',           // 样式大小
        backgroundColor: '3',        // 背景色
        subType: '3',              // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/MyConsultation',
            redirect: '/MyConsultation/index',
          },
          {
            path: '/MyConsultation/index', // 我的病例
            component: './MyConsultation/index'
          },
        ]
      },
      { // 诊所晨会
        name: 'ClinicMornMeet',
        path: '/ClinicMornMeet',
        icon: 'MeetingDiscuss',
        authority: ['143'],
        grouping: 'statistics',          // 分组字段
        styleColor: '1',                 // 样式大小
        backgroundColor: '2',            // 背景色
        subType: '3',                    // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/ClinicMornMeet',
            redirect: '/ClinicMornMeet/List',
          },
          {
            path: '/ClinicMornMeet/List',
            component: './EmrPart/index',
          },
        ]
      },

      // 延保管理     种植牙保单录入
      {
        path: '/YanBaoPart',
        name: 'YanBaoPart',
        icon: 'Yanbaopart',
        authority: ['110'],
        grouping: 'remind',       // 分组字段
        styleColor: '1',           // 样式大小
        backgroundColor: '4',        // 背景色
        routes: [
          { path: '/YanBaoPart', component: 'YanBaoPart' },
          { path: '/YanBaoPart/YanBaoPartRecord', component: './YanBaoPart/YanBaoPartRecord' },
          { path: '/YanBaoPart/YanBaoPartInput', component: './YanBaoPart/YanBaoPartInput' },
          //{ path: '/YanBaoPart/YanBaoPartCard',component:'./YanBaoPart/YanBaoPartCard'},
        ]
      },
      // 采购系统
      {
        path: '/procurementsystem',
        name: 'garworth',
        icon: 'GarworthIcon',
        grouping: 'remind',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '0',        // 背景色
        subType: '6',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          { path: '/procurementsystem', redirect: '/procurementsystem/index' },
          { path: '/procurementsystem/index', component: './Garworth/ProcurementSystem.js' },
          { path: '/procurementsystem/successful', component: './Garworth/AuthorizationSuccessful.js' }, // 授权成功
        ]
      },
      // 福利核销
      {
        name: 'welfareWriteOff',
        icon: 'WelfareCenter',
        path: '/welfareWriteOff',
        authority: ['134'],
        grouping: 'remind',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '1',        // 背景色
        subType: '5',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          { path: '/welfareWriteOff', redirect: '/welfareWriteOff/index' },
          // 核销首页-搜索框和核销记录tab
          {
            path: '/welfareWriteOff/index',
            component: './WelfareWriteOff/Index/index',
          },
          // 核销页
          {
            path: '/welfareWriteOff/start',
            component: './WelfareWriteOff/Start/index',
          }
        ],
      },
      // 福利中心
      {
        name: 'welfarecenter',
        icon: 'WelfareCenter',
        path: '/welfarecenter',
        authority: ['96'],
        grouping: 'remind',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '1',        // 背景色
        subType: '5',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          { path: '/welfarecenter', redirect: '/welfarecenter/welfarelist' },
          {
            path: '/welfarecenter/welfarelist',
            component: './WelfareCenter/WelfareList',
          },
          {
            path: '/welfarecenter/addwelfare/:id?',
            component: './WelfareCenter/AddWelfare',
          },
          {
            path: '/welfarecenter/welfaredetail/:id',
            component: './WelfareCenter/WelfareDetail',
          },
        ],
      },
      /*------------------------查询与统计------------------------- */
      // 日结
      {
        path: '/dailyknots',
        icon: 'DailyJob',
        name: 'dailyknots',
        authority: ['64'],
        component: './DailyKnots/index',
        grouping: 'statistics',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '2',        // 背景色
        subType: '5',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          { path: '/dailyknots', redirect: '/dailyknots/StatisticalTable/List' },
          {
            path: '/dailyknots/StatisticalTable',
            component: './DailyKnots/StatisticalTable/StatisticalTable',
            routes: [
              { path: '/dailyknots/StatisticalTable', redirect: '/dailyknots/StatisticalTable/List' },
              { path: '/dailyknots/StatisticalTable/List', component: './DailyKnots/StatisticalTable/StatisticalTableList' },
              { path: '/dailyknots/StatisticalTable/Detail', component: './DailyKnots/StatisticalTable/StatisticalTableDetail' },
            ]
          },
          {
            path: '/dailyknots/StatementsDaily',
            component: './DailyKnots/StatementsDaily/StatementsDaily',
          },

        ]
      },


      //  客户档案
      // {
      //   name: 'customerArchives',
      //   icon: 'customerArchives',
      //   path: '/customerArchives',
      //   authority: ['65'],
      //   grouping: 'statistics',       // 分组字段
      //   styleColor: '2',           // 样式大小
      //   backgroundColor: '3',        // 背景色
      //   routes: [
      //     {
      //       path: '/customerArchives',
      //       redirect: '/customerArchives/index',
      //     },
      //     {
      //       path: '/customerArchives/index',
      //       component: './CustomerArchives/index',
      //     },
      //   ]
      // },

      //  客户管理
      /* {
        // name: 'customerManagement',
        icon: 'Customerfile',
        path: '/customerManagement',
        // authority: ['119'],
        // grouping: 'statistics',       // 分组字段
        // styleColor: '2',           // 样式大小
        // backgroundColor: '4',        // 背景色
        routes: [
          {
            path: '/customerManagement',
            redirect: '/customerManagement/index',
          },
          {
            path: '/customerManagement/index',
            component: './CustomerManagement/transferPage',
          },
          // 客户池
          {
            path: '/customerManagement/customerPool',
            component: './CustomerManagement/customerList/index',
          },
          // 客户详情
          {
            path: '/customerManagement/customerInfo',
            component: './CustomerManagement/customerInfo/index',

          },
          // 配置过权限跳转到的 客户管理   this is 客户管理的children 跟分组没有关系
          {
            path: '/customerManagement',
            component: './CustomerManagement/index',
            routes: [
              {
                path: '/customerManagement/overview',
                component: './CustomerManagement/overview/transferPage',
                routes: [
                  {
                    path: '/customerManagement/overview',
                    redirect: '/customerManagement/overview/index',
                  },
                  {
                    path: '/customerManagement/overview/index',
                    component: './CustomerManagement/overview/index',
                  },
                  {
                    path: '/customerManagement/overview/breakAnAppointmentList',
                    component: './CustomerManagement/overview/clinicLegend/breakAnAppointmentList/index',
                  },
                  {
                    path: '/customerManagement/overview/expirationOfInsuranceList',
                    component: './CustomerManagement/overview/clinicLegend/expirationOfInsuranceList/index',
                  },
                ]
              },
              {
                path: '/customerManagement/consultingAchievement',
                routes: [
                  {
                    path: '/customerManagement/consultingAchievement',
                    redirect: '/customerManagement/consultingAchievement/index'
                  },
                  {
                    path: '/customerManagement/consultingAchievement/index',
                    component: './CustomerManagement/consultingAchievement/index',
                  },
                ]
              },
              {
                path: '/customerManagement/customerArchives',
                routes: [
                  {
                    path: '/customerManagement/customerArchives',
                    redirect: '/customerManagement/customerArchives/index'
                  },
                  {
                    path: '/customerManagement/customerArchives/index',
                    component: './CustomerArchives/index',
                  },
                ]
              },
              {
                path: '/customerManagement/customerList',
                routes: [
                  {
                    path: '/customerManagement/customerList',
                    redirect: '/customerManagement/customerList/index'
                  },
                  {
                    path: '/customerManagement/customerList/index',
                    component: './CustomerManagement/customerList/index',
                  },
                ]
              },
              // { // 电子病历医生在客户管理模块新增回访任务列表入口
              //   path: '/customerManagement/customerReturnVisit',
              //   routes: [
              //     {
              //       path: '/customerManagement/customerReturnVisit',
              //       redirect: '/customerManagement/customerReturnVisit/index'
              //     },
                  // {
                  //   path: '/customerManagement/customerReturnVisit/index',
                  //   component: './CustomerManagement/CustomerTaskList/ReturnVisit',
                  // },
              //   ]
              // }
            ]
          },
        ]
      }, */
      // 客户档案
      {
        name: 'customerManagement',
        icon: 'Customerfile',
        path: '/customerProfile',
        authority: ['119'],
        grouping: 'statistics',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '4',        // 背景色
        subIcon: 'menu_customer',        // 左侧菜单  icon
        subName: '患者中心',             // 左侧菜单  名字
        subType: '4',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/customerProfile',
            redirect: '/customerProfile/index',
          },
          // 客户档案列表
          {
            path: '/customerProfile/index',
            component: './CustomerProfile/index',
          },
          // 客户详情
          {
            path: '/customerProfile/customerDetails/:patientId',
            component: './CustomerDetails/index',
          },
        ]
      },
      // 客户方案编辑
      {
        path: '/customerPlan',
        component: './CustomerDetails/Plan/index',
      },
      // 报表
      {
        path: '/ReportForms',
        name: 'ReportForms',
        icon: 'ReportForms',
        authority: ['94'],
        grouping: 'statistics',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '0',        // 背景色
        subType: '8',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          { path: '/ReportForms', redirect: '/ReportForms/index' },
          { path: '/ReportForms/index', component: './ReportForms/index' },
        ]
      },
      // 统计查询
      {
        path: '/statisticsQuery',
        name: 'StatisticsQuery',
        icon: 'StatisticsQuery',
        authority: ['135'],
        grouping: 'statistics',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '1',        // 背景色
        subType: '8',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          { path: '/statisticsQuery', redirect: '/statisticsQuery/index' },
          { path: '/statisticsQuery/index', component: './StatisticsQuery/index' },
        ]
      },
      // 收费项查询
      {
        path: '/searchChargeItem',
        icon: 'SearchChargeItem',
        name: 'searchChargeItem',
        authority: ['88'],
        grouping: 'statistics',       // 分组字段
        styleColor: '1',           // 样式大小
        backgroundColor: '3',        // 背景色
        subType: '5',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/searchChargeItem', redirect: '/searchChargeItem/index'
          },
          {
            path: '/searchChargeItem/index', component: './SearchChargeItem',
          },
        ]
      },
      //  诊所缴费记录
      {
        path: '/settlement',
        icon: 'Settlement',
        name: 'settlement',
        authority: ['63'],
        grouping: 'statistics',       // 分组字段
        styleColor: '1',           // 样式大小
        backgroundColor: '0',        // 背景色
        subType: '5',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          { path: '/settlement', redirect: '/settlement/list' },
          {
            path: '/settlement/list',
            component: './SettlementEntry/List',
          },
          {
            path: '/settlement/payment/:id',
            component: './SettlementEntry/Payment'
          },
        ]
      },
      /* ------------------------设置与管理------------------------- */
      // 医生排班
      {
        path: '/doctormonth',
        name: 'doctormonth',
        icon: 'Scheduling',
        authority: ['68'],
        grouping: 'setup',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '1',        // 背景色
        subType: '9',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/doctormonth', // 医生 资源管理
            component: './doctormonth/LogginsticsPlan'
          },
        ]
      },
      // 配台管理=>护士配台
      {
        path: '/logisticsmonth',
        name: 'logisticsmonth',
        authority: ['70'],
        grouping: 'setup',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '1',        // 背景色
        icon: 'Gestational',
        routes: [
          {
            path: '/logisticsmonth', // 配台管理左侧
            component: './Logisticsmonth/LogginsticsPlan'
          },
        ]
      },
      // 预约规则
      {
        name: 'appointmentrules',
        icon: 'AppointmentRuleIcon',
        path: '/appointmentrules',
        authority: ['91'],
        grouping: 'setup',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '2',        // 背景色
        subType: '9',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          { path: '/appointmentrules', redirect: '/appointmentrules/appointmentruleslist' },
          {
            path: '/appointmentrules/appointmentruleslist',
            component: './AppointmentRules/AppointmentRulesList',
          },
          {
            path: '/appointmentrules/addappointmentrules',
            component: './AppointmentRules/AddAppointmentRules',
          },
        ],
      },

      //  资源管理
      {
        path: '/resource',
        name: 'resource',
        icon: 'ResourceModel',
        authority: ['67'],
        grouping: 'setup',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '0',        // 背景色
        subType: '9',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/resource',
            redirect: '/resource/logistics'
          },
          {
            path: '/resource/logistics',
            component: './ResourceAllocation/logistics',
            routes: [
              {
                path: '/resource/logistics',
                redirect: '/resource/logistics/management'
              },
              //人员管理
              {
                path: '/resource/logistics/management',
                component: './ResourceAllocation/management',
              },
              //医疗资源
              {
                path: '/resource/logistics/resource',
                component: './ResourceAllocation/resource',
                routes: [
                  {
                    path: '/resource/logistics/resource',
                    redirect: '/resource/logistics/resource/list'
                  },
                  {
                    path: '/resource/logistics/resource/list',
                    component: './ResourceAllocation/resourceList',
                  },
                  //医疗小队新增页
                  {
                    path: '/resource/logistics/resource/resourceTeam',
                    component: './ResourceAllocation/resourceTeam',
                  },
                  //医生新增页
                  {
                    path: '/resource/logistics/resource/resourceDoctor',
                    component: './ResourceAllocation/resourceDoctor',
                  },
                  //医生助理新增页
                  {
                    path: '/resource/logistics/resource/resourceAssistant',
                    component: './ResourceAllocation/resourceAssistant',
                  }
                ]
              },
              //护士管理
              {
                path: '/resource/logistics/nursemanage',
                component: './ResourceAllocation/nurseManage',
                routes: [
                  {
                    path: '/resource/logistics/nursemanage',
                    redirect: '/resource/logistics/nursemanage/list'
                  },
                  {
                    path: '/resource/logistics/nursemanage/list',
                    component: './ResourceAllocation/nurseManageList'
                  },
                  //护士管理添加固定配台页
                  {
                    path: '/resource/logistics/nursemanage/addNurseManage',
                    component: './ResourceAllocation/addNurseManage'
                  }
                ]
              },
              //健康顾问管理(咨询师管理)
              {
                path: '/resource/logistics/physicianassistant',
                component: './ResourceAllocation/PhysicianAssistant',
              },
              //科室管理
              {
                path: '/resource/logistics/Department',
                component: './ResourceAllocation/Department',
                routes: [
                  {
                    path: '/resource/logistics/Department',
                    redirect: '/resource/logistics/Department/list'
                  },
                  {
                    path: '/resource/logistics/Department/list',
                    component: './ResourceAllocation/DepartmentList'
                  },
                  //科室管理新增页
                  {
                    path: '/resource/logistics/Department/addDepartment',
                    component: './ResourceAllocation/addDepartment'
                  }
                ]
              },
              //设备管理
              {
                path: '/resource/logistics/equipment',
                component: './ResourceAllocation/equipment',
                routes: [
                  {
                    path: '/resource/logistics/equipment',
                    redirect: '/resource/logistics/equipment/list'
                  },
                  {
                    path: '/resource/logistics/equipment/list',
                    component: './ResourceAllocation/equipmentList'
                  },
                  //设备管理新增设备页
                  {
                    path: '/resource/logistics/equipment/additional',
                    component: './ResourceAllocation/equipmentAdditional'
                  }
                ]
              },
            ]
          }
        ]
      },
      // 设置    机构管理
      {
        name: 'setup',
        icon: 'Setting',
        path: '/setup',
        authority: ['66'],
        grouping: 'setup',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '4',        // 背景色
        subType: '9',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/setup',
            component: './Setup/index',
            routes: [
              {
                path: '/setup',
                redirect: '/setup/base',
              },
              // 医疗机构配置
              {
                path: '/setup/base',
                component: './Setup/Base/index',
              },
              // 专属客服配置
              {
                path: '/setup/security',
                component: './Setup/Security/index',
              },
              // 资源收费级别配置
              {
                path: '/setup/notification',
                component: './Setup/Notification/index',
              },
              // 回访人类型配置
              {
                path: '/setup/returnvisit',
                component: './Setup/Returnvisit/index',
              },
              // 客户管理配置
              {
                path: '/setup/customer',
                component: './Setup/Customer/index',
              },
              // 信息技术服务
              {
                path: '/setup/itservices',
                component: './Setup/ITServices/index',
              },
            ]
          },
        ]
      },

      /*------------------------系统配置------------------------- */
      {  // 内容管理=>电子病历的
        name: 'RecordManagement',
        icon: 'RecordManagement',
        path: '/ContentCenter/DownLoad',
        authority: ['203'],
        grouping: 'system',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '1',        // 背景色
        subType: '9',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/ContentCenter/DownLoad',
            redirect: '/ContentCenter/DownLoad/index',
          },
          {
            path: '/ContentCenter/DownLoad/index',
            component: './EmrPart/index',
          }
        ]
      },
      {  // 医生管理=>电子病历的
        name: 'DoctorManagement',
        icon: 'DoctorManagement',
        path: '/DoctorManage/MedicalRecordAuthority',
        authority: ['204'],
        grouping: 'system',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '0',        // 背景色
        subType: '9',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/DoctorManage/MedicalRecordAuthority',
            redirect: '/DoctorManage/MedicalRecordAuthority/index',
          },
          {
            path: '/DoctorManage/MedicalRecordAuthority/index',
            component: './EmrPart/index',
          }
        ]
      },
      {  // 基础字典设置=>电子病历的
        name: 'Consultation',
        icon: 'Consultation',
        path: '/SystemSetup/ImageManage',
        authority: ['202'],
        grouping: 'system',       // 分组字段
        styleColor: '1',           // 样式大小
        backgroundColor: '3',        // 背景色
        subType: '9',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
        routes: [
          {
            path: '/SystemSetup/ImageManage',
            redirect: '/SystemSetup/ImageManage/index',
          },
          {
            path: '/SystemSetup/ImageManage/index',
            component: './EmrPart/index',
          }
        ]
      },
      // 回访模板配置
      {
        path: '/templateEdit',
        name: 'templateEdit',
        icon: 'InformedConsent',
        authority: ['130'],
        grouping: 'setup',       // 分组字段
        styleColor: '1',           // 样式大小
        backgroundColor: '0',        // 背景色
        component: './ReturnTemplate/TemplateEdit',

        subType: '9',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
      },
      // 我的审批
      {
        path: '/myApprove',
        name: 'myApprove',
        icon: 'PracticePermissions',
        authority: ['131'],
        grouping: 'setup',       // 分组字段
        styleColor: '2',           // 样式大小
        backgroundColor: '3',        // 背景色
        component: './ReturnTemplate/TemplateEdit',
        subType: '9',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
      },
      // 找牙e
      {
        path: '/findDentist',
        name: 'findDentist',
        subType: '5',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心  10商城
      },
      // 学习中心
      {
        path: '/arrailSchool',
        name: 'arrailSchool',
        subIcon: 'menu_study',          // 左侧菜单  icon
        subType: '7',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心   10商城
      },
      // 商城
      {
        path: '/consumableShop',
        name: 'consumableShop',
        subIcon: 'menu_shop',          // 左侧菜单  icon
        subType: '10',                   // 左侧菜单类型   1全部任务  2服务中心  3医疗中心   4患者中心  5运营中心  6支持中心  7学习中心  8数据中心  9管理中心   10商城
      },
      /*             预存款                */
      {
        path: '/userpayment/payment',
        routes: [
          {  // 退款
            path: '/userpayment/payment/refupayment',
            component: './ArchivesNew/AdvPayment/PaymentRef/index'
          },
          {  // 充值
            path: '/userpayment/payment/upspayment',
            component: './ArchivesNew/AdvPayment/PaymentUps'
          },
          {  // 交易记录
            path: '/userpayment/payment/servicepayment',
            component: './ArchivesNew/AdvPayment/PayService'
          },
        ]
      },
      {
        path: '/printpages',
        hideInMenu: true,
        component: './PrintPages/PrintPages',
      },
      {
        path: '/printpatment',
        hideInMenu: true,
        component: './PrintPages/PrintPayment',
      },
      // 系统更新详情页
      {
        path: 'updataInformation/detail',
        hideInMenu: true,
        component: './UpdateInformation/details',
      },

      {
        name: 'exception',
        icon: 'warning',
        hideInMenu: true,
        path: '/exception',
        routes: [
          // exception
          {
            path: '/exception/403',
            name: 'not-permission',
            component: './Exception/403',
          },
          {
            path: '/exception/404',
            name: 'not-find',
            component: './Exception/404',
          },
          {
            path: '/exception/500',
            name: 'server-error',
            component: './Exception/500',
          },
          {
            path: '/exception/trigger',
            name: 'trigger',
            hideInMenu: true,
            component: './Exception/TriggerException',
          },
        ],
      },
      {
        component: '404',
      },
    ],
  },

];






