import React, {Component,} from 'react';
import {Select, Input, Modal, Radio, Button, Menu, message, Tree} from 'antd';
import styles from './style.less';//引入样式
import {connect} from "dva";
import moment from "moment";
import commonStyle from '@/pages/common.less'; //引入公共样式
import noData from '@/assets/<EMAIL>';//引入图片
const {SubMenu} = Menu;
const { Search } = Input;
class EdittableDialg extends Component {
  constructor(props) {
    super(props);
    this.state = {
      newKey: Math.random,//key值
      consultationProjectOption: ['种植', '填充', '修复', '检查'],
      statusOption: ['待开发', '未完成', '已完成'],
      nextStep: ['方案A', '方案B', '方案C'],
      teethPosition: {//恒牙
        above: [//上排牙
          {index: '8', position: '18'},
          {index: '7', position: '17'},
          {index: '6', position: '16'},
          {index: '5', position: '15'},
          {index: '4', position: '14'},
          {index: '3', position: '13'},
          {index: '2', position: '12'},
          {index: '1', position: '11'},
          {index: '1', position: '21'},
          {index: '2', position: '22'},
          {index: '3', position: '23'},
          {index: '4', position: '24'},
          {index: '5', position: '25'},
          {index: '6', position: '26'},
          {index: '7', position: '27'},
          {index: '8', position: '28'},
        ],
        below: [//下排牙
          {index: '8', position: '48'},
          {index: '7', position: '47'},
          {index: '6', position: '46'},
          {index: '5', position: '45'},
          {index: '4', position: '44'},
          {index: '3', position: '43'},
          {index: '2', position: '42'},
          {index: '1', position: '41'},
          {index: '1', position: '31'},
          {index: '2', position: '32'},
          {index: '3', position: '33'},
          {index: '4', position: '34'},
          {index: '5', position: '35'},
          {index: '6', position: '36'},
          {index: '7', position: '37'},
          {index: '8', position: '38'},
        ],
      },
      milkTeethPosition: {//乳牙
        above: [//上排牙
          {index: 'E', position: '1E'},
          {index: 'D', position: '1D'},
          {index: 'C', position: '1C'},
          {index: 'B', position: '1B'},
          {index: 'A', position: '1A'},
          {index: 'A', position: '2A'},
          {index: 'B', position: '2B'},
          {index: 'C', position: '2C'},
          {index: 'D', position: '2D'},
          {index: 'E', position: '2E'},
        ],
        below: [//下排牙
          {index: 'E', position: '4E'},
          {index: 'D', position: '4D'},
          {index: 'C', position: '4C'},
          {index: 'B', position: '4B'},
          {index: 'A', position: '4A'},
          {index: 'A', position: '3A'},
          {index: 'B', position: '3B'},
          {index: 'C', position: '3C'},
          {index: 'D', position: '3D'},
          {index: 'E', position: '3E'},
        ],
      },
      hoverIndex: '',//动态index
      isMilkTooth: 1, //恒牙 1  乳牙 2
      menuItems: [],//获取检查项目树数据
      lastClickPosition: props.selectedPosition[0],//切换图片
      tmpCheckResult: props.checkResult,//获取选择牙位信息
      visibleBoxTooth: this.props.visible,//弹框状态
      selectedPosition:this.props.selectedPosition//选择牙位数据
      // btnClassNameArry: [ ],
    };
    this.handlerMouseOver = this.handlerMouseOver.bind(this);
    this.submitData = this.submitData.bind(this);
    this.handlerMouseLeave = this.handlerMouseLeave.bind(this);
    this.handleMenuClick = this.handleMenuClick.bind(this);
    this.deleteResult = this.deleteResult.bind(this);
  }
  //监听
  componentWillReceiveProps(nextProps) {
    if (this.props.checkResult != nextProps.checkResult) {
      this.setState({
        tmpCheckResult: nextProps.checkResult
      })

    }
  }
  //生命周期初始化
  componentDidMount() {
    this.findToothExamCache();
    this.props.onRef(this)
  }
  //搜索检查项
  onSearchChange=(value )=>{
    this.findToothExamCache(value);
  }

  /**获取检查项目树**/
  findToothExamCache = (searchValue) => {
    const {dispatch} = this.props;
    let params = {
      searchValue:searchValue?searchValue:null
    }
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/findToothExamCacheService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              menuItems: res.rows,
            })
          }
        }
      });
    }
  }

  //全选
  allSelected= () =>{
    this.props.selectedPosition.splice(0,1)

    const above = this.state.teethPosition.above;
    const below = this.state.teethPosition.below;
    const milkAbove = this.state.milkTeethPosition.above;
    const milkBelow = this.state.milkTeethPosition.below;
    if(this.state.isMilkTooth==1) {
      for (let i = 0; i < above.length; i++) {
        const arr = !this.props.selectedPosition.includes(above[i].position) ? [...this.props.selectedPosition, above[i].position] :
          this.props.selectedPosition.filter((item) => {
            return item !== above[i].position;
          });
        this.props.selectedPosition.push(above[i].position)
        this.props.changeSelected(arr);
      }
      for (let i = 0; i < below.length; i++) {
        const arr = !this.props.selectedPosition.includes(below[i].position) ? [...this.props.selectedPosition, below[i].position] :
          this.props.selectedPosition.filter((item) => {
            return item !== below[i].position;
          });
        this.props.selectedPosition.push(below[i].position)
        this.props.changeSelected(arr);
      }

    }else {
      for (let i = 0; i < milkAbove.length; i++) {
        const arr = !this.props.selectedPosition.includes(milkAbove[i].position) ? [...this.props.selectedPosition, milkAbove[i].position] :
          this.props.selectedPosition.filter((item) => {
            return item !== milkAbove[i].position;
          });
        this.props.selectedPosition.push(milkAbove[i].position)
        this.props.changeSelected(arr);
      }
      for (let i = 0; i < milkBelow.length; i++) {
        const arr = !this.props.selectedPosition.includes(milkBelow[i].position) ? [...this.props.selectedPosition, milkBelow[i].position] :
          this.props.selectedPosition.filter((item) => {
            return item !== milkBelow[i].position;
          });
        this.props.selectedPosition.push(milkBelow[i].position)
        this.props.changeSelected(arr);
      }
    }
  }

  // 清空选中牙位
  clearSelected = () => {
    this.props.changeSelected([]);
  }
  //口腔检查保存点击事件
  submitData = () => {
    this.props.ok(this.state.tmpCheckResult);
  }

  // 牙位鼠标移入
  handlerMouseOver = (position) => {
    this.setState(() => ({
      hoverIndex: position,
    }));
  };

  // 牙位鼠标移出
  handlerMouseLeave = () => {
    this.setState(() => ({
      hoverIndex: '',
    }));
  };

  // 牙位点击
  handlePositionClick = (position, isShow) => {
    const arr = !this.props.selectedPosition.includes(position) ? [...this.props.selectedPosition, position] : this.props.selectedPosition.filter((item) => {
      return item == position;
    });
    if(!isShow){
      this.props.changeSelected(arr);
    }


    // 切换图片
    this.setState(() => ({
      lastClickPosition: arr[arr.length - 1],
    }));
  };

  // 恒牙/乳牙
  radioChange = (e) => {
    this.setState({
      isMilkTooth: e.target.value,
    });
    this.props.changeSelected([]);
  };
  //口腔检查删除点击事件
  deleteResult = (index, e) => {
    e.nativeEvent.stopImmediatePropagation();
    e.stopPropagation();
    const tmp = Array.from(this.state.tmpCheckResult);
    tmp.splice(index, 1);
    this.setState(() => ({
      newKey: Math.random,
      tmpCheckResult: tmp,
    }));
    this.props.tmpCheckResult(tmp)
    this.clearSelected();
  }
  //请选择牙位
  openDentalFaceDialog=(e)=>{
    this.props.openDentalFaceDialog(e)
  }
  // 菜单点击点击检查项目
  handleMenuClick = (e) => {
    if (this.props.selectedPosition.length === 0) {
      message.error('请选择牙位！');
      return false;
    }
    let dentalFace = e.item.props.data.dentalFace;
    if(dentalFace==1){
      this.openDentalFaceDialog(e);
    }else{
      this.selectedPosition(e);
    }

  }
  //选择牙位点击事件
  selectedPosition(e){
    // 添加检查结果
    this.props.selectedPosition.map((element) => {
      this.setState((state, props) => (
          {
            newKey: Math.random,//key值
            tmpCheckResult: state.tmpCheckResult ? [...state.tmpCheckResult, {
              id: '',
              toothPosition: element,//牙位
              // status: '待开发',
              examName: e.key,//检查名称
              examCode: e.item.props.data.examCode,//检查code值
              consultationProject: '',
              nextStep: '',//下个方案
              suggest: '',//建议
              checkDate: moment(new Date()).format('YYYY-MM-DD'),//时间
              doctorId: localStorage.getItem("id"),//医生id
              doctorName: localStorage.getItem("userName"),//医生姓名
            }] : [...props.checkResult, {
              id: '',
              toothPosition: element,//牙位
              // status: '待开发',
              examName: e.key,//检查名称
              examCode: e.item.props.data.examCode,//检查code值
              consultationProject: '',
              nextStep: '',//下个方案
              examRemark: '',//备注
              checkDate: moment(new Date()).format('YYYY-MM-DD'),//时间
              doctorId: localStorage.getItem("id"),//医生id
              doctorName: localStorage.getItem("userName"),//医生姓名
            }],
          }),
        () => {
          this.clearSelected();// 清空选中牙位
          this.state.tmpCheckResult=this.state.tmpCheckResult.reverse();
          this.setState({
            lastClickPosition:this.state.tmpCheckResult[0].toothPosition,
            tmpCheckResult: this.state.tmpCheckResult.reverse()
          })
        }
      );
    });
  }

  // table 下拉框
  handleProposal(value, index) {
    const tmp = this.state.tmpCheckResult;
    tmp[index].nextStep = value;
    this.setState(() => ({
      tmpCheckResult: tmp,
    }));
  }
  //项目点击事件
  handleProject(value, index) {
    const tmp = this.state.tmpCheckResult;
    tmp[index].status = value;
    this.setState(() => ({
      tmpCheckResult: tmp,
    }));
  }

  handleState(value, index) {
    const tmp = this.state.tmpCheckResult;
    tmp[index].consultationProject = value;
    this.setState(() => ({
      tmpCheckResult: tmp,
    }));
  }
  //获取备注事件
  inputChange(value, index) {
    this.state.tmpCheckResult[index].examRemark = value.target.value;
    // this.setState(() => ({
    //   tmpCheckResult: this.state.tmpCheckResult,
    // }));
  }

  render() {
    const {newKey} = this.state;
    return (
      <div>
        <Modal
          title="口腔检查"
          okText="确定"
          cancelText="取消"
          visible={this.props.visible}
          onOk={this.submitData}
          onCancel={this.props.cancel}
          maskClosable={false}
          width={1330}
          height={800}
          destroyOnClose
          className={styles.popUp_check_box}
        >
          <div className={styles.leftBox}>
            <div className={styles.siteBox}>
              <span>牙位</span>

              <Button type="primary" className={styles.reset} onClick={this.clearSelected.bind(this)} ghost
                      size="small">重选</Button>
              <Button type="primary" className={styles.reset} onClick={this.allSelected.bind(this)} ghost
                      size="small">全口</Button>
            </div>
            <Radio.Group onChange={this.radioChange} className={styles.choice} value={this.state.isMilkTooth}>
              <Radio value={1}>恒牙</Radio>
              <Radio value={2}>乳牙</Radio>
            </Radio.Group>
            <div className={this.state.isMilkTooth === 1 ? (styles.parment) : (styles.hide)}>
              <div className={styles.pterTeeth}>
                {
                  this.state.teethPosition.above.map((item) => {
                    return (
                      <span
                        key={item.position}
                        className={[this.state.tmpCheckResult.filter((res) => {return res.toothPosition === item.position}).length > 0 ? (styles.red) :
                          this.props.selectedPosition.includes(item.position) || this.state.hoverIndex === item.position ? (styles.gray) : (styles.grays)].join(' ')}
                        onMouseOver={() => this.handlerMouseOver(item.position)}
                        onMouseLeave={this.handlerMouseLeave}
                        onClick={() => this.handlePositionClick(item.position)}
                      >
                        {item.index}
                      </span>
                    );
                  })
                }
              </div>
              <div className={styles.pterTeeth}>
                {
                  this.state.teethPosition.below.map((item) => {
                    return (
                      <span
                        key={item.position}
                        className={[this.state.tmpCheckResult.filter((res) => {
                          return res.toothPosition === item.position;
                        }).length > 0 ? (styles.red) :
                          this.props.selectedPosition.includes(item.position) || this.state.hoverIndex === item.position ? (styles.gray) : (styles.grays)].join(' ')}
                        onMouseOver={() => this.handlerMouseOver(item.position)}
                        onMouseLeave={this.handlerMouseLeave}
                        onClick={() => this.handlePositionClick(item.position)}
                      >
                        {item.index}
                      </span>
                    );
                  })
                }
              </div>
              <p className={styles.lineborder}/>
              {
                this.state.tmpCheckResult
                && (this.state.lastClickPosition || this.props.selectedPosition.length > 0)
                && [...this.props.teeth.above, ...this.props.teeth.below]
                  .filter((t) => {
                    return t.position === this.props.selectedPosition[this.props.selectedPosition.length - 1] || t.position === this.state.lastClickPosition;
                  })
                  .map((element) => {
                    return (
                      <div className={(styles.cusp)} key={element.position}>
                        {
                          [element.toothImgs[0], ...(this.state.tmpCheckResult.filter((tcr) => {
                            return tcr.toothPosition === element.position;
                          }).map((cr) => {
                            let code =cr.toothCode?cr.toothCode.map((i) => {
                              return `/assets/${cr.examCode+i}/${cr.toothPosition}.png`;
                            }):  `/assets/${cr.examCode}/${cr.toothPosition}.png`
                            return code;
                          }))].map((it) =>
                            Array.isArray(it)?it.map((k) => {return <img src={k} alt="tooth" key={k} style={{position: 'absolute'}} onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = '/assets/blank.png';
                            }}/>}): <img src={it} alt="tooth" key={it} style={{position: 'absolute'}} onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = '/assets/blank.png';
                            }}/>
                          )
                        }
                      </div>
                    );
                  })
              }

            </div>
            <div className={this.state.isMilkTooth === 2 ? (styles.decid) : (styles.hide)}>
              <p className={styles.decidteeth}>
                {
                  this.state.milkTeethPosition.above.map((item) => {
                    return (
                      <span
                        key={item.position}
                        className={[this.state.tmpCheckResult.filter((res) => {
                          return res.toothPosition === item.position;
                        }).length > 0 ? (styles.red) :  this.props.selectedPosition.includes(item.position) || this.state.hoverIndex === item.position ? (styles.gray) : (styles.grays)].join(' ')}
                        onMouseOver={() => this.handlerMouseOver(item.position)}
                        onMouseLeave={this.handlerMouseLeave}
                        onClick={() => this.handlePositionClick(item.position)}
                      >
                        {item.index}
                      </span>
                    );
                  })
                }
              </p>
              <p className={styles.decidteeth}>
                {
                  this.state.milkTeethPosition.below.map((item) => {
                    return (
                      <span
                        key={item.position}
                        className={[this.state.tmpCheckResult.filter((res) => {
                          return res.toothPosition === item.position;
                        }).length > 0 ? (styles.red) :
                          this.props.selectedPosition.includes(item.position) || this.state.hoverIndex === item.position ? (styles.gray) : (styles.grays)].join(' ')}
                        onMouseOver={() => this.handlerMouseOver(item.position)}
                        onMouseLeave={this.handlerMouseLeave}
                        onClick={() => this.handlePositionClick(item.position)}
                      >
                        {item.index}
                      </span>
                    );
                  })
                }
              </p>
              <p className={styles.decidline}/>
              {
                (this.state.lastClickPosition || this.props.selectedPosition.length > 0)
                && [...this.props.teeth.above, ...this.props.teeth.below]
                  .filter((t) => {
                    return t.milkToothPosition === this.props.selectedPosition[this.props.selectedPosition.length - 1] || t.milkToothPosition === this.state.lastClickPosition;
                  })
                  .map((element) => {
                    return (
                      <div className={(styles.cusp)} key={element.toothPosition}>
                        {
                          [element.toothImgs[0], ...(this.state.tmpCheckResult.filter((tcr) => {
                            return tcr.toothPosition === element.milkToothPosition;
                          }).map((cr) => {
                            let code =cr.toothCode?cr.toothCode.map((i) => {
                              return `/assets/${cr.examCode+i}/${cr.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(cr.toothPosition[1])}.png`;
                            }): `/assets/${cr.examCode}/${cr.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(cr.toothPosition[1])}.png`;
                            return code;
                          }))].map((it) =>
                            Array.isArray(it)?it.map((k) => {return <img src={k} alt="tooth" key={k} style={{position: 'absolute'}} onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = '/assets/blank.png';
                            }}/>}): (<img src={it} alt="tooth" key={it} style={{position: 'absolute'}} onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = '/assets/blank.png';
                            }}/>)
                          )
                        }
                      </div>
                    );
                  })
              }
            </div>
          </div>
          <div className={styles.rightBox} >
            <div className={styles.siteBox}>
              <span>口腔检查</span>
            </div>
            <div className={styles.tableData} key={newKey}>
              <div className={styles.tablenav}>
                <span>牙位</span>
                <span>检查项目</span>
                <span style={{width: 258}}>备注</span>
                <span>操作</span>
              </div>
              {
                this.state.tmpCheckResult.map((element, index) => {
                  return (
                    <div className={styles.datasoure} key={element.examCode+"-"+index} onClick={() => this.handlePositionClick(element.toothPosition,1)}>
                      <span>{element.toothPosition}</span>
                      <span>
                        {/*M-近中，D远中，O-颌面，B-颊侧，L-舌侧*/}
                        <span>{element.examName}
                          { element.toothDesc?<span> {element.toothDesc.BSelected==true?",颊侧":""}
                            {element.toothDesc.MSelected==true?",近中":""}
                            {element.toothDesc.DSelected==true?",远中":""}
                            {element.toothDesc.OSelected == true ?",颌面":""}
                            {element.toothDesc.LSelected==true?",舌侧":""}</span>:null}
                          {/*{element.toothCode?element.toothCode.map((i) => {return i=='B'?"颊侧,":i=='M'?"近中,":i=='D'?"远中,":i=='O'?"颌面,":i=='L'?"舌侧,":""}):null}*/}
                        </span>
                      </span>
                      <span style={{width: 258}}>
                        <Input placeholder="治疗建议" type="text"
                               maxLength={90}
                               defaultValue={element.examRemark}
                               style={{width: '100%'}}
                               onChange={e => element.examRemark = e.target.value}/>
                      </span>
                      <span onClick={e => this.deleteResult(index, e)}>删除</span>
                    </div>
                  );
                })
              }
            </div>
          </div>
          <div className={styles.centerBox}>
            <div className={styles.siteBoxs}>
              <div>检查项目</div>
              <Search style={{width: '60%',marginTop:3}} placeholder="搜索检查项" onSearch={this.onSearchChange} />
            </div>
            <Menu style={{marginTop: 40}} mode="inline" selectable={false} onClick={this.handleMenuClick}>
              {this.state.menuItems.length>0?
                this.state.menuItems.map((item) => {
                  const level2Items = item.children.map((level2Item) => {
                    const level3Items = level2Item.children.map((level3Item) => {
                      const level4Items = level3Item.children ? level3Item.children.map((level4Item) => {
                        return (<Menu.Item
                          icon={<img src={level4Item.examImg} alt={level4Item.examName} width="30px" height="30px"/>}
                          key={level4Item.examName} data={level4Item}>{level4Item.examName}</Menu.Item>);
                      }) : <></>;
                      return level3Item.children && level3Item.children.length > 0
                        ? (<SubMenu title={level3Item.examName}
                                    icon={<img src={level3Item.examImg} alt={level3Item.examName} width="30px"
                                               height="30px"/>} key={level3Item.examName}>{level4Items}</SubMenu>)
                        : (<Menu.Item
                          icon={<img src={level3Item.examImg} alt={level3Item.examName} width="30px" height="30px"/>}
                          key={level3Item.examName} data={level3Item}>{level3Item.examName}</Menu.Item>);
                    });
                    return level2Item.children.length > 0
                      ? (<SubMenu title={level2Item.examName}
                                  icon={<img src={level2Item.examImg} alt={level2Item.examName} width="30px"
                                             height="30px"/>} key={level2Item.examName}>{level3Items}-</SubMenu>)
                      : (<Menu.Item
                        icon={<img src={level2Item.examImg} alt={level2Item.examName} width="30px" height="30px"/>}
                        key={level2Item.examName} data={level2Item}>{level2Item.examName}</Menu.Item>);
                  });
                  return item.children.length > 0
                    ? (<SubMenu title={item.examName}
                                icon={<img src={item.examImg} alt={item.examName} width="30px" height="30px"/>}
                                key={item.examName}>{level2Items}</SubMenu>)
                    : (<Menu.Item icon={<img src={item.examImg} alt={item.examName} width="30px" height="30px"/>}
                                  key={item.examName} data={item}>{item.examName}</Menu.Item>);
                }):(
              // 暂无数据
                <div className={commonStyle.nodataContent} style={{marginTop:'45%'}}>
                  <img src={noData} className={commonStyle.imgStyle} alt=""/>
                  <div className={commonStyle.fontStyle}>暂无数据</div>
                </div>
                )
              }
            </Menu>
          </div>

        </Modal>
      </div>
    );
  }
}

export default connect(({Public, loading,}) => ({
  Public,
}))(EdittableDialg);
