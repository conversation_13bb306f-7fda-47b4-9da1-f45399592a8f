@import '~@/utils/utils.less';

.PopoverByEventElent {
  z-index: 199;
  :global {
    .ant-popover-inner {
      background: rgba(0, 0, 0, 0.4);
    }

    .ant-popover-inner .ant-popover-inner-content {
      background: rgba(0, 0, 0, 0.4);
      cursor: pointer;
    }

    .ant-popover-arrow {
      width: 1px;
      height: 1px;
      border-width: 4px;
    }

    .ant-popover-arrow {
      top: 6px;
      border-top-color: rgba(0, 0, 0, 0.6) !important;
      border-right-color: transparent;
      border-bottom-color: transparent;
      border-left-color: rgba(0, 0, 0, 0.6) !important;;
      box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
    }
  }
}

.PopoverByEventElentText {
  color: #FFFFFF;
  width: 149px;
}

.PopoverByEventElentTextBtn {
  color: #6697CD;
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.eventWarp {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.patientEventicon {
  // float: right;
  margin-right: 1px;
  width: 16px;
  height: 16px;
  display: inline-block;
}
eventWarp
.emergencyEventicon {
  float: right;
  margin-right: 1px;
  width: 16px;
  height: 16px;
}

.Eventicon {
  float: right;
  margin-right: 1px;
}

.Diamonds {
  margin-top: -3px;
}

.referralIcon {
  width:17px;
  height:18px;
}

.eventBytemporaryStorage {
  font-size: 14px;
  color: #444444;
  padding-left: 8%;
  width:99%;
  box-sizing: border-box;

  .ShutDownBtn {
    background: red;
    border-radius: 50%;
    opacity: 1;
    width: 20px;
    height: 20px;
    background: url("../../../assets/AppointmentRules/TemporaryStorageEvent_ShutDownBtn.png");
    background-color: #FFFFFF;
    background-size: 20px 20px;
    border-radius: 50%;
    opacity: 1;
    position: absolute;
    top: -8px;
    right: -8px;
    cursor: pointer;
    z-index: 10;
  }

  p {
    margin-top: 5px;
    margin-bottom: 5px;
    line-height: 19px;
  }

  .eventBytemporaryStoragePatientName {
    margin-right: 5px;
    max-width: 50%;
    font-weight: 700;
    font-size: 15px;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
  }

  .TimeBox {
    font-size: 12px;
    font-weight: 500;
    color: rgba(0,0,0,0.65);
    color: #444444;
    position: relative;
    top: 5px;
  }
}



.eventElent {
  font-size: 14px;
  color: #444444;
  font-family: 'MicrosoftYaHei';
  padding-left: 8px;
  position: absolute;
  width:99%;
  top: 40%;
  transform: translate(0, -50%);
  box-sizing: border-box;
  z-index: 10;
  p {
    margin-top: 5px;
    margin-bottom: 5px;
    line-height: 19px;
  }
}

.eventElentOther {
  font-size: 14px;
  font-weight: 700;
  color: #fff;
  font-family: 'MicrosoftYaHei';
  //padding-left: 8%;
  text-align: center;
  position: absolute;
  width:99%;
  top: 50%;
  overflow: hidden;
  transform: translate(0, -50%);

  p {
    margin-top: 5px;
    margin-bottom: 5px;
    line-height: 19px;
  }
}

.evnetOffTextColor {
  color: #444;
}

.age {
  margin-left: 8px;
}


.dingwei5 {
  width: 21px;
  height: 21px;
  border: 0;
  background: url("../../../assets/imgqq.png") no-repeat;
  background-size: 90px 18px;
  background-position: -18px 0;
  margin-top: 1px;
}

.jichu {
  width: 27px;
  height: 27px;
  background: url("../../../assets/calendar/event/jichu.png") no-repeat;
  background-size: 27px 27px;
  display: inline-block;
  position: absolute;
  top:0px;
}

.zhongda {
  width: 27px;
  height: 27px;
  background: url("../../../assets/calendar/event/zhongda.png") no-repeat;
  background-size: 27px 27px;
  display: inline-block;
  position: absolute;
  top:0px;
}

.appmentEnter {
  width: 27px;
  height: 27px;
  display: inline-block;
  background: url('../../../assets/calendar/event/appmentEnter.png') no-repeat;
  background-size: 27px 27px;
  display: inline-block;
  position: absolute;
  bottom:0px;
  right: 0px;
}

.SmallerAppointment{
  width: 24px;
  height: 24px;
  display: inline-block;
  background: url('../../../assets/registerAndArrival/smaller.png') no-repeat;
  background-size: 24px 24px;
  display: inline-block;
  position: absolute;
  bottom:0px;
  right: 0px;
}

/*治疗项目标签*/

.cureProjectIcon {
  width:98%;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}

.cureProjectIcon i {
  vertical-align: text-bottom;
  margin-left: 2px;
  margin-right: 2px;
}

.daiguan {
  width: 16px;
  height: 20px;
  background: url('../../../assets/calendar/event/daiguan.png') no-repeat;
  background-size: 16px 20px;
  display: inline-block;
}


.meibai {
  width: 17px;
  height: 22px;
  background: url('../../../assets/calendar/event/meibai.png') no-repeat;
  background-size: 17px 22px;
  display: inline-block;
}

.yachixiufu {
  width: 16px;
  height: 20px;
  background: url('../../../assets/calendar/event/yachixiufu.png') no-repeat;
  background-size: 16px 20px;
  display: inline-block;
}

.yagen {
  width: 16px;
  height: 20px;
  background: url('../../../assets/calendar/event/yagen.png') no-repeat;
  background-size: 16px 20px;
  display: inline-block;
}

.zhengji {
  width: 24px;
  height: 14px;
  background: url('../../../assets/calendar/event/zhengji.png') no-repeat;
  background-size: 24px 14px;
  display: inline-block;
}

.zhiding {
  width: 14px;
  height: 14px;
  background: url('../../../assets/tuli/A88.png') no-repeat;
  background-size: 14px 14px;
  display: inline-block;
}

.zhongzhi {
  width: 16px;
  height: 20px;
  background: url('../../../assets/calendar/event/zhongzhi.png') no-repeat;
  background-size: 16px 20px;
  display: inline-block;
}

.zhaobaijin {
  width: 33px;
  height: 14px;
  display: inline-block;
  background: url('../../../assets/calendar/event/zhaobajin.png') no-repeat;
  background-size: 33px 14px;
}

.vipicon {
  width: 15px;
  height: 15px;
  display: inline-block;
  background: url('../../../assets/calendar/event/vipicon.png') no-repeat;
  background-size: 15px 13px;
  float: left;
  position: relative;
  top: 4px;
  margin-right: 1px;
}

.MSH {
  width: 30px;
  height: 13px;
  display: inline-block;
  background: url('../../../assets/calendar/event/MSH.png') no-repeat;
  background-size: 30px 13px;
}

.LAB {
  width: 26px;
  height: 13px;
  display: inline-block;
  background: url('../../../assets/calendar/event/LAB.png') no-repeat;
  background-size: 26px 11px;
}

.jizhen {
  width: 24px;
  height: 14px;
  display: inline-block;
  background: url('../../../assets/calendar/event/jizhen.png') no-repeat;
  background-size: 24px 14px;
}

.closeWarp {
  width: 22px;
  height: 22px;
  float: right;
  margin-right: 0%;
  margin-top: 1%;
  position: relative;
  z-index: 1;
}

.closeIcon {
  width: 15px;
  height: 15px;
  display: inline-block;
  background: url('../../../assets/calendar/event/close.png') no-repeat;
  background-size: 15px 15px;
}




.patientName{
  // margin-right: 5px;
  // float: left;
  font-weight: 700;
  font-size: 15px;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  z-index: 89;
}



.patientNameWidth1 {
  width: 66%;
}
.patientNameWidth2 {
  width: 50%;
}

.patientNameWidth3 {
  width: 29%;
}
.patientNameWidth4 {
  width: 25%;
}

.remark {
  width:98%;
  /*overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;*/

}


.remarkForHidden {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}


.complaintList {
  width:98%;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}

.PatienticonRigth {
  // float: right;
  margin-left: 3px;
}

.patienBox {
  .clearfix()
}

.patienLineHeigth {
  margin-bottom: 3px;
  display: flex;
}



.patienLinePadding {
  margin-left: 7px;
}


.huizhenText {
  float: right;
  margin-right: 8px;
  font-size:16px;
  font-family:'MicrosoftYaHei-Bold';
  font-weight:bold;
  color:rgba(200,42,229,1);
}

.stayToDirectorWarp {
  display: inline-block;
  width: 48px;
  height: 20px;
  border-radius: 48px;
  background: #FC9E2E;
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  text-align: center;
  padding-top: 1px;
}

.alreadyToDirectorWarp {
  display: inline-block;
  width: 48px;
  height: 20px;
  border-radius: 48px;
  background: #31CB21;
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  text-align: center;
  padding-top: 1px;
}

.TimeSpan {
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
  margin-left: 3px;
}

.boxWaitWarp {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border: 1px solid #FF9A5F;
  background:rgba(255,244,237,0.9);
  border-radius: 4px;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.stateByWait {
  height: 20px;
  line-height: 20px;
  background: #FF9A5F;
  border-bottom-left-radius: 4px;
  color: #FFFFFF;
  text-align: center;
  padding-left: 5px;
  padding-right: 3px;
  font-size: 12px;
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.stateByWaitIcon {
  width: 14px;
  height: 14px;
  background: url('../../../assets/calendar/event/waitIcon.png') no-repeat;
  background-size: 14px 14px;
  display: inline-block;
  margin-right: 2px;
}
.TimeByText {
  align-items: center;
  display: inline-block;
  height: 20px;
  line-height: 20px;
  color: #FF9A5F;
  z-index: 20;
  font-size: 14px;
  font-weight: 500;
}

.timeoutBorder {
  border: 1px solid #FF2E2E;
}
.timeoutBackground {
  background: #FF2E2E;
}
.timeoutBackgroundTransparency {
  background: rgba(255, 46, 46, 0.2);
}
.timeoutColor {
  color: #FF2E2E;
}


.warp_element_notName {
  opacity: 0.1;
}
.warp_element_notName_none {
  display: none;
}


.isHiddenElement {
  display: none;
}


