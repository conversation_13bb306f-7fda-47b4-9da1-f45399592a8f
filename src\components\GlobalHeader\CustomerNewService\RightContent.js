import React, { PureComponent } from 'react';
import { FormattedMessage, formatMessage } from 'umi/locale';
import { Tag, Menu, Icon, Dropdown, Avatar, Card } from 'antd';
import moment from 'moment';
import PictUrl from '@/assets/user/user.png';
import Link from 'umi/link';
import styles from './index.less';

export default class GlobalHeaderRight extends PureComponent {
  render() {
    const {
      currentUser,
      fetchingNotices,
      onNoticeVisibleChange,
      onMenuClick,
      onNoticeClear,
      theme,
      visibleType,
    } = this.props;
    const menu = (
      <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick}>
        <Menu.Item key="SavePassword">
          <Icon type="lock" />
          <span>修改密码</span>
        </Menu.Item>
        <Menu.Item key="logout">
          <Icon type="logout" />
          <FormattedMessage id="menu.account.logout" defaultMessage="退出登录" />
        </Menu.Item>
      </Menu>
    );
    let className = styles.right;
    if (theme === 'dark') {
      className = `${styles.right}  ${styles.dark}`;
    }
    const userName = localStorage.getItem('userName'); //用户名
    const useAvatar = localStorage.getItem("avatar"); //用户头像
    return (

      <div className={className}>
        {useAvatar ? (
          <Dropdown overlay={menu}>
            <span className={`${styles.action} ${styles.account}`}>
              {userName}&nbsp;&nbsp;
                <Icon type="caret-down" />
            </span>
          </Dropdown>
        ) : (
            <Dropdown overlay={menu}>
              <span className={`${styles.action} ${styles.account}`}>
                {userName}&nbsp;&nbsp;
                <Icon type="caret-down" />
              </span>
            </Dropdown>
          )}
      </div>
    );
  }
}
