import request from '@/utils/request';

//查询影像分类列表
export async function findCheckClasses(params) {
  return request('/api/emr/system/findCheckClasses', {
    method: 'POST',
    data: params,
  });
}
//获取影像类型详情
export async function ClassesInfoByCode(params) {
  return request('/api/emr/system/getCheckClassesInfoByCode', {
    method: 'POST',
    data: params,
  });
}
//新增/编辑影像类型
export async function saveCheckClass(params) {
  return request('/api/emr/system/saveCheckClass', {
    method: 'POST',
    data: params,
  });
}
//删除影像类型
export async function deleteCheckClass(params) {
  return request('/api/emr/system/deleteCheckClass', {
    method: 'POST',
    data: params,
  });
}
