import {Col, Row, Input, Button, Form, Modal, Popconfirm, Radio, message,Select,Spin } from 'antd';
import {EditorState,convertToRaw } from 'draft-js';
import draftToHtml from 'draftjs-to-html';
import Editors from './components/editor';//引入编辑器组件
import { ExclamationCircleOutlined} from '@ant-design/icons';
import React, { Component } from 'react';
import { GridContent } from '@ant-design/pro-layout';
import styles from './style.less';//引入样式
//引入图标
import Unfold from "@/assets/<EMAIL>";
import Fold from "@/assets/<EMAIL>";
import Folder from "@/assets/<EMAIL>";
import FileSelected from "@/assets/<EMAIL>";
import FileUnselected from "@/assets/<EMAIL>";
import FolderSelected from "@/assets/<EMAIL>";
import NewFile from "@/assets/<EMAIL>";
import Delete from "@/assets/<EMAIL>";
import Edit from "@/assets/<EMAIL>";
import noData from "@/assets/<EMAIL>";

import {connect} from "dva";
import commonStyle from "@/pages/common.less";//公共样式
import {StringUtils} from "@/utils/StringUtils";//公共验证
const { Option } = Select;
const { Search } = Input;
const { confirm } = Modal;
const {TextArea} = Input;
class InforConsent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      clientHeight: document.documentElement.clientHeight, // 屏幕高度
      LanguageVal:'1',
      editorState:EditorState.createEmpty(),//创建一个空的富文本
      show2:null,//一级文件夹点击状态
      show3:null,//二级文件夹点击状态
      editorStatus:false,//编辑显示状态
      addFileBorderStatus:false,//点击...弹窗
      title:"",
      hover1:false,//一级文件夹hover状态
      setVisible:false,//删除气泡框
      addFileBorderStatus3:false,//新建模板菜单状态
      hover2:false,//二级文件夹hover状态
      newModel:false,//新建模板弹窗状态
      editorName:false,//修改模板名称弹窗
      addFileBorderStatus4:false,//新建文件菜单状态
      editorBookStatus:false,//是否编辑知情同意书
      tabEnglish:false,
      InforList:[],//左侧菜单列表
      ModelListName:[],//根据知情同意书模板分类获取模板名
      paramMode:{},//新增模板参数
      MrcMessage:{},//知情同意书详情
      language:1,//编辑时选择语言的状态
      FolderClasscode:"",//点击文件夹传的classcode
      searchList:[],//搜索后的菜单
      searchStatus:false,//搜索input框是否有数据状态
      ListStatus:false,//分组列表loading
      MsgStatus:false,//知情同意书详情loading
      MrcMessageLang:{},//知情同意书详情内容
      addLoading:false,//新建文件夹创建按钮loading
      addModelLoading:false,//新建模板创建按钮loading
      editorModelLoading:false,//编辑模板创建按钮loading
      finishStatus:false//完成按钮状态
    };
    this.resize = this.resize.bind(this);//监听屏幕高度
  }
  //初始化
  componentDidMount() {
    this.InforList()//模板分组列表
    window.addEventListener("resize", this.resize); //增加
    // 点击其他地方隐藏输入框
    document.addEventListener('click', (e) => {
      if(this.state.addFileBorderStatus3){
        this.setState({
          addFileBorderStatus3:false
        })
      }
      if(this.state.addFileBorderStatus4){
        this.setState({
          addFileBorderStatus4:false
        })
      }
    })
  }
  //在组件卸载及销毁之前直接调用
  componentWillUnmount() {
    window.removeEventListener("resize", this.resize); //取消监听高度
  }
  //模板分组列表
  InforList=()=>{
    const {dispatch} = this.props
    this.setState({
      ListStatus:true
    })
    let params={
      // tenantId:localStorage.getItem('tenantId')
    }
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/findCheckService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              InforList:res.rows,
              ListStatus:false,
            })
            if(res.rows[0].classCode&&this.state.show2==null){
              this.getModelName(res.rows[0].classCode)
              this.setState({
                show2:res.rows[0].id,
                FolderClasscode:res.rows[0].classCode
              })
            }else{
              let art=res.rows;
              let art1={};
              art.forEach((text,index)=>{
                if(text.classCode==this.state.FolderClasscode){
                  art1=text
                }
              })
              this.getModelName(art1.classCode)
            }
          }else{
            this.setState({
              ListStatus:false
            })
          }
        }
      });
    }
  }
  //根据知情同意书模板分类获取模板名
  getModelName=(classCode,key)=>{
    const {dispatch} = this.props
    let params={
      classCode:classCode,
      tenantId:localStorage.getItem('tenantId'),
    }
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/findMrcTmptNamesService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            // console.log("返回的数据==",JSON.stringify(res.rows))
            this.setState({
              ModelListName:res.rows
            })
            if(res.rows.length>0&&res.rows[0].id&&!key&&key!==2){
              this.getMrcMessage(res.rows[0].id)
              this.setState({
                show3:res.rows[0].id
              })
            }
          }
        }
      });
    }
  }
  //获取知情同意书详情
  getMrcMessage=(id)=>{
    const {dispatch} = this.props
    this.setState({
      MsgStatus:true,
      MrcMessageLang:{}
    })
    let params={
      id:id,
      tenantId:localStorage.getItem('tenantId'),
    }
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/getMrcTempInfoService',
        payload: params,
        callback: (res) => {
          // console.log("res.contentres.content=",res.content)
          if (res.code == 200) {
            this.setState({
              MrcMessage:res.content,
              MsgStatus:false,
              showConfirmid:""
            })
            this.state.MrcMessage.id=id;
          }else{
            this.setState({
              MsgStatus:false
            })
          }
        }
      });
    }
  }
  //监听屏幕高度
  resize() {
    this.setState({ clientHeight: document.documentElement.clientHeight });
  }
  // 搜索菜单
  onSearch=(value)=>{
    if(StringUtils.isNotBlank(value)){
      this.setState({
        searchStatus:true
      })
      this.searchval(value)
    }else{
      this.setState({
        searchStatus:false,
        show2:null,
      })
      this.InforList()
    }
  }
  //有内容时的搜索
  searchval=(value,key)=>{
    const {dispatch} = this.props
    this.setState({
      ListStatus:true,
      searchval:value,
    })
    let params={
      tmptName:value,
      tenantId:localStorage.getItem('tenantId'),
    }
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/findMrcTempClassSearchService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              searchList:res.rows,
              ModelListName:[],
              ListStatus:false
            })
            // console.log("搜索后===",JSON.stringify(res.rows))
            // console.log("搜索后ModelListName===",this.state.ModelListName)
            if(res.rows && res.rows.length>0 && !key && key!==2){
              let id=res.rows[0].children[0].id;
              // console.log("id==",id)
              this.setState({
                show2:res.rows[0].id,
                show3:id,
              })
              this.getMrcMessage(id)
            }
          }else{
            this.setState({
              ListStatus:false
            })
          }
        }
      });
    }
  }
  // 选择语言
  LanguageChange=(e)=>{
    this.setState({
      language:e.target.value
    })
  }
  // 选择状态
  ChangeStatus=(e)=>{
    this.state.MrcMessage.status=e.target.value;
  }
  //编辑状态
  onEditorStateChange=(editorState)=>{
    this.setState({
      editorState,
    })
  }
  editorChange(){
    const {editorState} =this.state;
    let templateContent=draftToHtml(convertToRaw(editorState.getCurrentContent()));
    // this.addTemplate(templateContent);
  }
  // 第一级鼠标hover菜单事件
  hoverTwo= (hoverTwo,id) => {
    this.setState({
      ['hoverTwo'+id]: hoverTwo
    })
  }
  // 第一级鼠标点击菜单事件
  opentwo=(item1,index1,id)=>{
    this.setState({
      ModelListName:[],
      FolderClasscode:item1.classCode?item1.classCode:item1.code
    })
    if(item1.classCode){
      this.getModelName(item1.classCode);
    }
    if(this.state.show2==id){
      this.setState({
        show2:null,
        show3:null,
      })
    }else{
      this.setState({
        show2:id
      })
    }
  }
  // 第二级鼠标hover菜单事件
  hoverThree= (hoverThree,id) => {
    this.setState({
      ['hoverThree'+id]: hoverThree
    })
  }
  // 第二级点击事件
  openthree=(item2)=>{
    if(this.state.editorBookStatus&&this.state.show3!==item2.id){
      this.showConfirm(item2.id)
    }else{
      this.setState({
        MrcMessage:{}
      })
      if(this.state.show3==item2.id){
        this.setState({
          show3:item2.id
        })
      }else{
        this.setState({
          show3:item2.id
        })
      }
      this.getMrcMessage(item2.id);
    }
  }
  //提示框
  showConfirm = (id) => {
    confirm({
      title: '当前同意书模板未保存，是否保存?',
      icon: <ExclamationCircleOutlined />,
      onOk:() =>  {
        this.editorInforConsent();
        this.setState({
          editorBookStatus:false,
          showConfirmid:id,
        })
        if(this.state.show3==id){
          this.setState({
            show3:null
          })
        }else{
          this.setState({
            show3:id
          })
        }
      },
      onCancel:() =>{
        this.setState({
          editorBookStatus:false,
        })
        if(this.state.show3==id){
          this.setState({
            show3:null
          })
        }else{
          this.setState({
            show3:id
          })
        }
        this.getMrcMessage(id)
      },
    });
  };
  //新建模板
  addModel=(e,id)=>{
    e.nativeEvent.stopImmediatePropagation()
    if(this.state.addFileBorderStatus3){
      this.setState({
        addFileBorderStatus3:false,
        addFileBorderStatus4:false,
      })
    }else{
      this.setState({
        hover2:id,
        addFileBorderStatus3:true,
        addFileBorderStatus4:false,
      })
    }
  }
  //文件操作
  fileModel=(e,id)=>{
    e.nativeEvent.stopImmediatePropagation()
    if(this.state.addFileBorderStatus4){
      this.setState({
        addFileBorderStatus3:false,
        addFileBorderStatus4:false,
      })
    }else{
      this.setState({
        hover3:id,
        addFileBorderStatus3:false,
        addFileBorderStatus4:true,
      })
    }
  }
  //新建文件夹确定
  newFileOk=()=>{
    this.setState({
      newFile:false,
    })
  }
  //新建文件夹取消
  newFileCancel=()=>{
    this.setState({
      newFile:false,
    })
  }
  //删除文件
  showPopconfirm=(e)=>{
    e.nativeEvent.stopImmediatePropagation()
    this.setState({
      setVisible:true,
    })
  }
  //新建模板
  newModel=(item1)=>{
    // console.log("搜索后===",JSON.stringify(item1))
    this.setState({
      paramMode:item1,
      newModel:true,
      ['hoverTwo'+item1.id]: false
    })
  }
  //新建模板确定按钮
  newModelOk=()=>{
    const {dispatch} = this.props
    const {paramMode} = this.state;
    let params={
      classCode:paramMode.classCode?paramMode.classCode:paramMode.code,//分类编码
      tenantId:localStorage.getItem('tenantId'),//平台标识
      tmptName:paramMode.tmptName,//模板名称
      createName:localStorage.getItem('userName'),//创建者
      createId:localStorage.getItem('userId')//创建者id不能为空
    }
    if(!params.tmptName){
      message.warning({
        content: '模板名称不能为空',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if(params.tmptName.length>100){
      message.warning({
        content: '模板名称超长',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
    }
    this.setState({
      addModelLoading:true
    })
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/saveMrcTmptService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.state.paramMode.tmptName="";
            this.setState({
              newModel:false,
              addModelLoading:false
            })
            this.getModelName(params.classCode)
          }else{
            this.state.paramMode.tmptName="";
            message.error({
              content: res.msg,
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              addModelLoading:false
            })
          }
        }
      });
    }
  }
  //新建模板取消按钮
  newModelCancel=()=>{
    this.setState({
      newModel:false,
      addModelLoading:false
    })
  }
  //修改模板名称
  editorFile=(fileName,item1)=>{
    if(fileName=='file'){
      this.setState({
        modelName:item1.file,
        editorName:true,
        ['hoverTwo'+item1.id]: false,
        ['hoverThree'+item1.id]: false,
      })
    }
    if(fileName=='model'){
      this.setState({
        modelName:item1.tmptName?item1.tmptName:item1.name,
        editorName:true,
        ['hoverTwo'+item1.id]: false,
        ['hoverThree'+item1.id]: false,
      })
      this.getMrcMessage(item1.id);
    }

  }
  //编辑名称确定
  editorNameOk=()=>{
    this.editorInforConsent();
  }
  //编辑名称取消
  editorNameCancel=()=>{
    this.setState({
      editorName:false,
      editorModelLoading:false
    })
  }
  //删除知情同意书确定
  DeletehandleOk=(item)=>{
    this.setState({
      setVisible:false,
      ['hoverTwo'+item.fileid]: false,
      ['hoverThree'+item.contentid]: false,
    })
    const {dispatch} = this.props
    let params={
      id:item.id,
      tenantId:localStorage.getItem('tenantId')
    }
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/deleteMrcTmptService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            if(this.state.searchval){
              this.searchval(this.state.searchval)
            }else{
              this.InforList()
            }
          }else{
            message.error({
              content: '操作失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
          }
        }
      });
    }
  }
  //删除知情同意书取消
  DeletehandleCancel=(item)=>{
    this.setState({
      setVisible:false,
      ['hoverTwo'+item.fileid]: false,
      ['hoverThree'+item.contentid]: false,
    })
  }
  //点击编辑知情同意书
  editorBook=()=>{
    this.setState({
      editorBookStatus:true
    })
  }
  //编辑知情同意书（完成按钮）
  editorInforConsent=()=>{
    const {dispatch} = this.props
    const {MrcMessage,MrcMessageLang} = this.state;
    let tmptCont=encodeURIComponent(MrcMessage.tmptCont);
    let tmptContEn=encodeURIComponent(MrcMessage.tmptContEn);
    let params={
      id:MrcMessage.id,
      classCode:MrcMessage.classCode,//分类编码不能为空
      tmptName:MrcMessage.tmptName,//模板名称不能为空
      tmptCont:(MrcMessageLang.tmptCont&&MrcMessageLang.tmptCont!=="null")?MrcMessageLang.tmptCont:(tmptCont&&tmptCont!=="null")?tmptCont:" ",//知情同意书 模板内容
      tmptContEn:(MrcMessageLang.tmptContEn&&MrcMessageLang.tmptContEn!=="null")?MrcMessageLang.tmptContEn:(tmptContEn&&tmptContEn!=="null")?tmptContEn:" ",//知情同意书 模板内容-英文
      status:MrcMessage.status,
      updateName:localStorage.getItem('userName'),//更新者
      updateId:localStorage.getItem('userId'),//更新者id
      tenantId:localStorage.getItem('tenantId')//平台标识
    }
    if(!params.tmptName){
      message.warning({
        content: '模板名称不能为空',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if(params.tmptName.length>100){
      message.warning({
        content: '模板名称超长',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    const cont = decodeURIComponent(params.tmptCont)
    if(this.state.language==1&&(!cont||cont=="<br>")){
      message.warning({
        content: '请输入知情同意书中文内容',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if(this.state.language==2&&(!MrcMessageLang.tmptContEn||MrcMessageLang.tmptCont=="<br>")){
      message.warning({
        content: '请输入知情同意书英文内容',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    this.setState({
      editorModelLoading:true,
      finishStatus:true,
    })
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/editMrcTmptService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              // show2:MrcMessage.id,
              editorBookStatus:false,
              editorModelLoading:false,
              finishStatus:false,
              editorName:false,
            })
            if(this.state.searchval) {
              this.searchval(this.state.searchval,2)
            }
            this.getMrcMessage(this.state.showConfirmid?this.state.showConfirmid:MrcMessage.id)
            this.getModelName(this.state.FolderClasscode,2)
          }else{
            message.error({
              content: '操作失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              editorModelLoading:false,
              finishStatus:false,
              editorName:false,
            })
          }
        }
      });
    }
  }
  //取消按钮（不编辑）
  cancelagreeBook=()=>{
    const {MrcMessage} =this.state;
    this.setState({
      editorBookStatus:false,
      finishStatus:false
    })
    this.getMrcMessage(MrcMessage.id)
  }
  // 中英切换（点击中文）
  tabChinese=()=>{
    this.setState({
      language:1
    })
  }
  // 中英切换（点击英文）
  tabEnglish=()=>{
    this.setState({
      language:2,
    })
  }
  //获取知情同意书内容
  handleEditorChange(e){
    let arr= encodeURIComponent(e);
    if(this.state.language==1){
      this.state.MrcMessageLang.tmptCont=arr
    }else{
      this.state.MrcMessageLang.tmptContEn=arr
    }
  }
  //编辑知情同意书选择分类
  classifySelect = e => {
    this.state.MrcMessage.classCode = e;
  };
  render() {
    const {LanguageVal,newFile,clientHeight,InforList,ModelListName,
      paramMode,MrcMessage,searchList,searchStatus,
      ListStatus,MsgStatus,MrcMessageLang,addLoading,addModelLoading,editorModelLoading}=this.state;
    return (
      <GridContent>
        <Row>
          <Col span={5}>
            <div className={styles.leftMenu} style={{height:clientHeight}}>
              <div className={styles.MenuTitle}>知情同意书</div>
              <div style={{margin:12}}>
                <Search
                  placeholder="搜索"
                  onSearch={this.onSearch}
                  style={{
                    width: '100%',
                  }}
                />
              </div>
              <Spin spinning={ListStatus}>
              {searchList&&searchStatus?
              <>
                <div className={styles.modelContent}>
                  {searchList.length>0?
                  <>
                    {searchList.map((item1,index1)=>(
                      <div style={{marginLeft:15}} key={index1}>
                        <div
                          className={this.state['hoverTwo'+item1.id]? (`${styles.pointer} ${styles.chooseBgcolor}`) :  (`${styles.pointer}`)}
                          style={{cursor:'pointer',marginTop:12}}
                          onMouseEnter={()=>this.hoverTwo(true,item1.id)}
                          onMouseLeave={()=>this.hoverTwo(false,item1.id)}
                        >
                          <div
                            onClick={()=>this.opentwo(item1,index1,item1.id)}
                            style={{display:'flex',width:'80%'}}>
                            {this.state.show2==item1.id?
                              <img src={Unfold} className={styles.arrows} alt=""/>:
                              <img src={Fold} className={styles.arrows} alt=""/>
                            }
                            {this.state['hoverTwo'+item1.id] ?
                              <img
                                onMouseEnter={()=>this.hoverTwo(true,item1.id)}
                                onMouseLeave={()=>this.hoverTwo(false,item1.id)}
                                src={FolderSelected}
                                className={styles.fileIcon} alt=""/> :
                              <img
                                onMouseEnter={()=>this.hoverTwo(true,item1.id)}
                                onMouseLeave={()=>this.hoverTwo(false,item1.id)}
                                src={Folder}
                                className={styles.fileIcon}
                                alt=""/>
                            }
                            <div
                              className={this.state['hoverTwo'+item1.id]?(`${styles.filetitle} ${styles.chooseFontcolor}`):(`${styles.filetitle}`)}
                              style={{marginLeft:8}}>{item1.name}</div>
                          </div>
                          {this.state['hoverTwo'+item1.id] ?
                            <div className={styles.addBtns} onClick={e=>this.addModel(e,item1.id)}>...</div>:
                            ""
                          }
                          {this.state.addFileBorderStatus3 &&this.state.hover2==item1.id ?
                            <div className={styles.addFile} style={{top:25,height:32}}>
                              <div
                                onClick={()=>this.newModel(item1)}
                                className={styles.addFileLine}>
                                <img src={NewFile} className={styles.iconStyle} alt=""/>
                                <span className={styles.addFileName}>新建模板</span>
                              </div>
                              {/*<div*/}
                              {/*  onClick={()=>this.editorFile('file',item1)}*/}
                              {/*  className={styles.addFileLine}>*/}
                              {/*  <img src={Edit} className={styles.iconStyle} alt=""/>*/}
                              {/*  <span className={styles.addFileName}>编辑名称</span>*/}
                              {/*</div>*/}
                            </div>:
                            null}
                        </div>
                        {item1.children?
                          <>
                            {item1.children.map((item2,index2)=>(
                              <div style={{marginLeft:15}} key={index2} className={this.state.show2==item1.id?(styles.show) : (styles.hidden)}>
                                <div
                                  className={this.state['hoverThree'+item2.id]? (`${styles.fileName} ${styles.chooseBgcolor}`) :  (`${styles.fileName}`)}
                                  style={{justifyContent:'space-between',marginTop:12,position:'relative'}}
                                  onMouseEnter={()=>this.hoverThree(true,item2.id)}
                                  onMouseLeave={()=>this.hoverThree(false,item2.id)}
                                >
                                  <div
                                    onClick={()=>this.openthree(item2)}
                                    style={{display:'flex',cursor:'pointer',width:'80%'}}
                                  >
                                    {this.state.show3==item2.id || this.state['hoverThree'+item2.id]?
                                      <img src={FileSelected} className={styles.Unselected} alt=""/> :
                                      <img src={FileUnselected} className={styles.Unselected} alt=""/>
                                    }
                                    <div
                                      className={this.state.show3==item2.id || this.state['hoverThree'+item2.id]?(styles.SelectedStyle) : (styles.UnselectedStyle)} >{item2.name}</div>
                                  </div>
                                  {this.state['hoverThree'+item2.id] ?
                                    <div className={styles.addBtns} onClick={e=>this.fileModel(e,item2.id)}>...</div>:
                                    ""
                                  }
                                  {this.state.addFileBorderStatus4 &&this.state.hover3==item2.id ?
                                    <div className={styles.addFile} style={{top:25,height:62}}>
                                      <div
                                        onClick={()=>this.editorFile('model',item2)}
                                        className={styles.addFileLine}>
                                        <img src={Edit} className={styles.iconStyle} alt=""/>
                                        <span className={styles.addFileName}>编辑名称</span>
                                      </div>
                                      <Popconfirm
                                        title="确定删除？"
                                        visible={this.state.setVisible}
                                        onConfirm={()=>this.DeletehandleOk(item2)}
                                        // okButtonProps={{
                                        //   loading: confirmLoading,
                                        // }}
                                        onCancel={()=>this.DeletehandleCancel(item2)}
                                      >
                                        <div
                                          onClick={this.showPopconfirm}
                                          className={styles.addFileLine}>
                                          <img src={Delete} className={styles.iconStyle} alt=""/>
                                          <span className={styles.addFileName}>删除</span>
                                        </div>
                                      </Popconfirm>
                                    </div>:
                                    null}
                                </div>
                              </div>
                            ))}
                          </>:null}

                      </div>
                    ))}
                  </>:
                    <div className={commonStyle.nodataContent} style={{marginTop:'45%'}}>
                      <img src={noData} className={commonStyle.imgStyle} alt=""/>
                      <div className={commonStyle.fontStyle}>暂无数据</div>
                    </div>
                  }

                </div>
              </>:
              <>
                <div className={styles.modelContent}>
                  {InforList.length>0?
                  <>
                    {InforList.map((item1,index1)=>(
                      <div style={{marginLeft:15}} key={index1}>
                        <div
                          className={this.state['hoverTwo'+item1.id]? (`${styles.pointer} ${styles.chooseBgcolor}`) :  (`${styles.pointer}`)}
                          style={{cursor:'pointer',marginTop:12}}
                          onMouseEnter={()=>this.hoverTwo(true,item1.id)}
                          onMouseLeave={()=>this.hoverTwo(false,item1.id)}
                        >
                          <div
                            onClick={()=>this.opentwo(item1,index1,item1.id)}
                            style={{display:'flex',width:'80%'}}>
                            {this.state.show2==item1.id?
                              <img src={Unfold} className={styles.arrows} alt=""/>:
                              <img src={Fold} className={styles.arrows} alt=""/>
                            }
                            {this.state['hoverTwo'+item1.id] ?
                              <img
                                onMouseEnter={()=>this.hoverTwo(true,item1.id)}
                                onMouseLeave={()=>this.hoverTwo(false,item1.id)}
                                src={FolderSelected}
                                className={styles.fileIcon} alt=""/> :
                              <img
                                onMouseEnter={()=>this.hoverTwo(true,item1.id)}
                                onMouseLeave={()=>this.hoverTwo(false,item1.id)}
                                src={Folder}
                                className={styles.fileIcon}
                                alt=""/>
                            }
                            <div
                              className={this.state['hoverTwo'+item1.id]?(`${styles.filetitle} ${styles.chooseFontcolor}`):(`${styles.filetitle}`)}
                              style={{marginLeft:8}}>{item1.className}</div>
                          </div>
                          {this.state['hoverTwo'+item1.id] ?
                            <div className={styles.addBtns} onClick={e=>this.addModel(e,item1.id)}>...</div>:
                            ""
                          }
                          {this.state.addFileBorderStatus3 &&this.state.hover2==item1.id ?
                            <div className={styles.addFile} style={{top:25,height:32}}>
                              <div
                                onClick={()=>this.newModel(item1)}
                                className={styles.addFileLine}>
                                <img src={NewFile} className={styles.iconStyle} alt=""/>
                                <span className={styles.addFileName}>新建模板</span>
                              </div>
                              {/*<div*/}
                              {/*  onClick={()=>this.editorFile('file',item1)}*/}
                              {/*  className={styles.addFileLine}>*/}
                              {/*  <img src={Edit} className={styles.iconStyle} alt=""/>*/}
                              {/*  <span className={styles.addFileName}>编辑名称</span>*/}
                              {/*</div>*/}
                            </div>:
                            null}
                        </div>
                        {ModelListName?
                          <>
                            {ModelListName.map((item2,index2)=>(
                              <div style={{marginLeft:15}} key={index2} className={this.state.show2==item1.id?(styles.show) : (styles.hidden)}>
                                <div
                                  className={this.state['hoverThree'+item2.id]? (`${styles.fileName} ${styles.chooseBgcolor}`) :  (`${styles.fileName}`)}
                                  style={{justifyContent:'space-between',marginTop:12,position:'relative'}}
                                  onMouseEnter={()=>this.hoverThree(true,item2.id)}
                                  onMouseLeave={()=>this.hoverThree(false,item2.id)}
                                >
                                  <div
                                    onClick={()=>this.openthree(item2)}
                                    style={{display:'flex',cursor:'pointer',width:'80%'}}
                                  >
                                    {this.state.show3==item2.id || this.state['hoverThree'+item2.id]?
                                      <img src={FileSelected} className={styles.Unselected} alt=""/> :
                                      <img src={FileUnselected} className={styles.Unselected} alt=""/>
                                    }
                                    <div
                                      className={this.state.show3==item2.id || this.state['hoverThree'+item2.id]?(styles.SelectedStyle) : (styles.UnselectedStyle)} >{item2.tmptName}</div>
                                  </div>
                                  {this.state['hoverThree'+item2.id] ?
                                    <div className={styles.addBtns} onClick={e=>this.fileModel(e,item2.id)}>...</div>:
                                    ""
                                  }
                                  {this.state.addFileBorderStatus4 &&this.state.hover3==item2.id ?
                                    <div className={styles.addFile} style={{top:25,height:62}}>
                                      <div
                                        onClick={()=>this.editorFile('model',item2)}
                                        className={styles.addFileLine}>
                                        <img src={Edit} className={styles.iconStyle} alt=""/>
                                        <span className={styles.addFileName}>编辑名称</span>
                                      </div>
                                      <Popconfirm
                                        title="确定删除？"
                                        visible={this.state.setVisible}
                                        onConfirm={()=>this.DeletehandleOk(item2)}
                                        // okButtonProps={{
                                        //   loading: confirmLoading,
                                        // }}
                                        onCancel={()=>this.DeletehandleCancel(item2)}
                                      >
                                        <div
                                          onClick={this.showPopconfirm}
                                          className={styles.addFileLine}>
                                          <img src={Delete} className={styles.iconStyle} alt=""/>
                                          <span className={styles.addFileName}>删除</span>
                                        </div>
                                      </Popconfirm>
                                    </div>:
                                    null}
                                </div>
                              </div>
                            ))}
                          </>:null}

                      </div>
                    ))}
                  </>:
                    <div className={commonStyle.nodataContent} style={{marginTop:'45%'}}>
                      <img src={noData} className={commonStyle.imgStyle} alt=""/>
                      <div className={commonStyle.fontStyle}>暂无数据</div>
                    </div>
                  }
                </div>
              </>
              }
              </Spin>

            </div>
          </Col>
          <Col span={19} style={{position:'relative'}}>
            <Spin spinning={MsgStatus}>
            <div style={{position:'relative',height:clientHeight}}>
              {(MrcMessage.tmptCode && (ModelListName && ModelListName.length>0)) || (MrcMessage.tmptCode &&(searchList &&searchList.length>0))?
              <>
                {this.state.editorBookStatus?
                  <div>
                    <div>
                      <div className={styles.Rightcontent}>
                        <div className={styles.smallTitle}>编辑知情同意书</div>
                        <Form
                          name="nest-messages"
                          initialValues={{
                            // language: this.state.language,
                            // status:MrcMessage.status,
                            // tmptName:MrcMessage.tmptName,
                            // classCode:MrcMessage.classCode,
                          }}
                          className={styles.Bookcontent}
                        >
                          <Row gutter={16}>
                            <Col className="gutter-row" span={12}>
                              <Form.Item
                                name='tmptName'
                                label="名称"
                                rules={[
                                  {
                                    required: true,
                                    message:'模板名称不能为空'
                                  },
                                  {
                                    max: 100,
                                    message:'最多可输入100个文字'
                                  },
                                ]}
                              >
                                <Input
                                  key={MrcMessage.tmptName}
                                  onChange={e=>{MrcMessage.tmptName = e.target.value}}
                                  defaultValue={MrcMessage.tmptName}
                                  placeholder="请输入模板名称"/>
                              </Form.Item>
                            </Col>
                            <Col className="gutter-row" span={12}>
                              <Form.Item
                                name='classCode'
                                label="分类"
                                rules={[
                                  {
                                    required: true,
                                  },
                                ]}
                              >
                                <Select
                                  defaultValue={MrcMessage.classCode}
                                  placeholder="请输入分类"
                                  key={MrcMessage.classCode}
                                  onSelect={this.classifySelect}
                                >
                                  {InforList.map((item) =>
                                    <Option key={item.classCode} >{item.className}</Option>
                                  )}
                                </Select>
                              </Form.Item>
                            </Col>
                            <Col className="gutter-row" span={12}>
                              <Form.Item
                                name='language'
                                label="语言"
                                rules={[
                                  {
                                    required: true,
                                  },
                                ]}
                              >
                                <Radio.Group
                                  key={this.state.language}
                                  defaultValue = {this.state.language}
                                  onChange={this.LanguageChange}>
                                  <Radio value={1}>中文</Radio>
                                  <Radio value={2}>英文</Radio>
                                </Radio.Group>
                              </Form.Item>
                            </Col>
                            <Col className="gutter-row" span={12}>
                              <Form.Item
                                name='status'
                                label="状态"
                                rules={[
                                  {
                                    required: true,
                                  },
                                ]}
                              >
                                <Radio.Group
                                  key={MrcMessage.status}
                                  defaultValue = {MrcMessage.status}
                                  onChange={this.ChangeStatus}>
                                  <Radio value={0}>启用</Radio>
                                  <Radio value={1}>停用</Radio>
                                </Radio.Group>
                              </Form.Item>
                            </Col>
                            <Col className="gutter-row" span={24}>
                              <Form.Item
                                name='tmptCont'
                                label="内容"
                                rules={[
                                  {
                                    required: true,
                                    message: '请输入知情同意书内容'
                                  },
                                ]}
                              >
                                <div className={styles.editorBorder}>
                                  <Editors
                                    handleChange={this.handleEditorChange.bind(this)}
                                    defaultValue={this.state.language==1?(MrcMessage.tmptCont?MrcMessage.tmptCont:MrcMessageLang.tmptCont):(MrcMessage.tmptContEn?MrcMessage.tmptContEn:MrcMessageLang.tmptContEn)}
                                  />

                                </div>
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form>
                      </div>
                    </div>
                    <div className={styles.bottomBtns}>
                      <div className={styles.btn_position}>
                        <Button className={styles.mL_16} onClick={this.cancelagreeBook}>取消</Button>
                        <Button disabled={this.state.finishStatus}  className={styles.mL_16} onClick={this.editorInforConsent} type="primary">完成</Button>
                      </div>
                    </div>
                  </div>:
                  <div>
                    <div className={styles.Rightcontent}>
                      <div className={styles.titles}>
                        <span className={styles.title}>{MrcMessage.tmptName?MrcMessage.tmptName:""}</span>
                        {MrcMessage.status==0?
                          <span className={styles.status}>使用中</span>
                          :
                          <span className={styles.status} style={{color:'#F53F3F'}}>已停用</span>
                        }
                      </div>
                      <div className={styles.Intertranslation}>
                        {this.state.language==1?
                          <div
                            onClick={this.tabChinese}
                            className={styles.Choose}>中</div>:
                          <div
                            onClick={this.tabChinese}
                            className={styles.notChoose}>中</div>
                        }
                        {this.state.language==2?
                          <div
                            onClick={this.tabEnglish}
                            className={styles.Choose}>英</div>:
                          <div
                            onClick={this.tabEnglish}
                            className={styles.notChoose}>英</div>
                        }
                        <span
                          onClick={this.editorBook}
                          className={styles.editor}
                          style={{cursor:'pointer'}}>编辑</span>
                      </div>
                    </div>
                    <div
                      className={styles.inner}
                      style={{height:this.state.clientHeight-70,overflowY:'scroll'}}
                      dangerouslySetInnerHTML = {{ __html:this.state.language==1?MrcMessage.tmptCont:MrcMessage.tmptContEn}}
                    >
                    </div>
                  </div>
                }
              </>:
                <div className={commonStyle.nodataContent} style={{marginTop:'20%'}}>
                  <img src={noData} className={commonStyle.imgStyle} alt=""/>
                  <div className={commonStyle.fontStyle}>暂无数据</div>
                </div>
              }

            </div>
            </Spin>
            </Col>
        </Row>
        {/*新建文件夹*/}
        <Modal
          title="新建文件夹"
          visible={newFile}
          destroyOnClose={true}
          onOk={this.newFileOk}
          onCancel={this.newFileCancel}
          okText="创建"
          cancelText="取消"
          width={660}
          maskClosable={false}
          confirmLoading={addLoading}
        >
          <TextArea
            showCount
            maxLength={100}
            placeholder="请输入文件夹名称"/>
        </Modal>
        {/*新建模板文件*/}
        <Modal
          title="新建模板"
          visible={this.state.newModel}
          destroyOnClose={true}
          onOk={this.newModelOk}
          onCancel={this.newModelCancel}
          okText="创建"
          cancelText="取消"
          width={660}
          maskClosable={false}
          confirmLoading={addModelLoading}
        >
          <TextArea
            showCount
            maxLength={100}
            onChange={e=>{paramMode.tmptName=e.target.value}}
            autoSize={{minRows: 1, maxRows: 10}}
            placeholder="请输入模板名称"/>
        </Modal>
        {/*编辑名称*/}
        <Modal
          title="编辑名称"
          visible={this.state.editorName}
          destroyOnClose={true}
          onOk={this.editorNameOk}
          onCancel={this.editorNameCancel}
          okText="确定"
          cancelText="取消"
          width={660}
          maskClosable={false}
          confirmLoading={editorModelLoading}
        >
          <TextArea
            showCount
            maxLength={100}
            onChange={e=>{MrcMessage.tmptName = e.target.value}}
            defaultValue={this.state.modelName}
            autoSize={{minRows: 1, maxRows: 10}}
            placeholder="请输入模板名称"/>
        </Modal>
      </GridContent>
    );
  }
}

export default connect(({ }) => ({}))(InforConsent);
