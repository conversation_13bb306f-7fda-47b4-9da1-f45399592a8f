@import '~antd/lib/style/themes/default.less';
.TodaySeecards{
  .spinLoading{
    position: absolute;
    top: 45%;
    left: 50%;
    z-index: 999;
  }
}
.cardContent{
  border: 1px solid rgba(0, 0, 0, 0.06);
}
.cardTitle{
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: #F7F7F7;
  font-size: 16px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  padding:13px 16px;
  display: flex;
}
.cardHover {
  &:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    //transform: translate3d(0, -3px, 0);
    cursor: pointer;
    border: 1px solid #4292FF;
  }
}
.border_bot {
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.bottom {
  display: flex;
  justify-content: space-between;
  padding-top: 8px;
  padding-left: 8px;
  padding-Right: 8px;
}

.word6 {
  display: block;
  width: 36px;
  height: 20px;
  margin: 1px 0 0 2px;
  color: rgba(0, 180, 42, 1);
  font-size: 12px;
  font-family: PingFang SC;
  line-height: 20px;
  white-space: nowrap;
  text-align: center;
  overflow-wrap: break-word;
}
.topFlex {
  display: flex;
  justify-content: space-between;
}
.fontWeight {
  font-weight: bold;
  font-size: 16px;
}

.pd_lr {
  padding: 0 4px;
  color: #9a999c;
  font-size: 14px;
}
.icon_diamond {
  width: 16px;
  height: 14px;
  margin-top: 5px;
  margin-right: 4px;
}
.icon_boy {
  width: 14px;
  height: 14px;
  margin-top: 5px;
  margin-right: 4px;
}
.icon_girl {
  width: 20px;
  height: 20px;
  margin-top: 1px;
  margin-right: 4px;
}
.name {
  //width: 105px;
  display: inline-block;
  max-width: 45%;
}
.marginSpan {
  //border:1px solid #000c17;
  margin-top: 2px;
  //float: right;
  //padding-bottom: 2px;
}

.marginTag {
  margin-right: 0;
}
.ellipse {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.tagSuccess {
  padding: 0 2px;
  color: #00b42a !important;
  background: rgba(0, 180, 42, 0.1) !important;
  border-color: rgba(0, 180, 42, 0.3) !important;
  border-radius: 4px !important;
}

.tagRed {
  padding: 0 2px;
  color: rgba(245, 63, 63) !important;
  background: rgba(245, 63, 63, 0.1) !important;
  border-color: rgba(245, 63, 63, 0.3) !important;
  border-radius: 4px !important;
}

.tagWarn {
    padding: 0 2px;
    color: rgba(245, 63, 63) !important;
    background: rgba(255, 125, 0, 0.1) !important;
    border-color: rgba(255, 125, 0, 0.3) !important;
    border-radius: 4px !important;
}
:global {
  .ant-card-body {
    padding: 8px !important;
  }
}
.line {
  width: 1px;
  height: 9px;
  margin: 0 4px;
  margin-top: 7px;
  background-color: #d8d8d8;
}
.reminder{
  min-width: 20px;
  height: 16px;
  text-align: center;
  background: rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  border: 1px solid #FFFFFF;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 14px;
  margin-top: 5px;
  margin-left: 5px;
  padding:0 5px;
}
.fontLeftSize{
  font-size: 14px;
  width:60%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
