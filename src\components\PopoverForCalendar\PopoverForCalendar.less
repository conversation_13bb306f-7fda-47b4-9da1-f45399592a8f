.PopoverCalendarItem {
  text-align: center;
  width: 28px;
  height: 28px;
  margin-left: 5px;
  line-height: 28px;
  border-radius: 28px;
  color: #444444;
  cursor: pointer;

}

.PopoverCalendarItemSelected {
  text-align: center;
  width: 28px;
  height: 28px;
  margin-left: 5px;
  line-height: 28px;
  border-radius: 28px;
  color: #fff;
  background: #4ca2ff;
  cursor: pointer;
}

.PopoverCalendarItemCurrent {
  //border-radius: 28px;
  border:1px solid red;
}

.PopoverOtherItem {
  text-align: center;
  width: 28px;
  height: 28px;
  margin-left: 5px;
  line-height: 28px;
  border-radius: 28px;
  color: #c5c8ce;
  cursor: not-allowed;
}


.PopoverFullItem {
  text-align: center;
  width: 28px;
  height: 28px;
  margin-left: 5px;
  line-height: 28px;
  border-radius: 28px;
  //color: #c5c8ce;
  color: #444444;
  cursor: pointer;
}


.PropverOtherItemIcon {
  font-size: 12px;
  position: absolute;
  right: -3px;
  top: 0px;
  cursor: not-allowed;
}

.PropverUnscheduledItemIcon {
  font-size: 12px;
  -webkit-transform: scale(0.7);
  margin: 0;
  padding: 0;
  position: absolute;
  right: 1px;
  color: #ee6666;
  bottom: -6px;
  cursor: not-allowed;
}

.PropverOtherRedItemIcon {
  font-size: 12px;
  position: absolute;
  right: -3px;
  top: 0px;
  color: #ee6666;
}

.selectCalendarPopover {
  :global {
    .ant-fullcalendar-header .ant-select:first-child{
      margin-right: 10px;
    }
    .ant-select-sm .ant-select-selection--single{
      width: 100px;
    }
  }
}
.PopoverCalendar{
  :global{
    .ant-popover-content .ant-popover-inner-content {
      padding: 0px;
    }
    .ant-fullcalendar-header .ant-radio-group{
      display: none;
    }
  }
}


.reset {
  position: absolute;
  top: 22px;
  left: 15px;
  cursor: pointer;
  //display: block;
  z-index: 10;
}

.containerWarp {
  padding-left: 6px;
  padding-right: 6px;
  padding-bottom: 10px;
  padding-top: 10px;
}

.controlBoxWarp {
  height: 44px;
  width: 100%;
  border-bottom: 1px solid #E5E7EE;
  display: flex;
  justify-content: space-between
}

.controlBoxLastWarp {
  height: 44px;
  width: 100%;
  border-top: 1px solid #E5E7EE;
  border-bottom: 1px solid #E5E7EE;
  display: flex;
  justify-content: space-around;
}

.DoubleLeftOutlined {
  width: 30px;
  height: 46px;
  font-size: 12px;
  color: #000000;
  line-height: 46px;
  cursor: pointer;
  user-select: none;
}

.DoubleLeftOutlined:hover {
  color: #4f4f4f;
}

.LeftOutlined {
  width: 30px;
  height: 46px;
  font-size: 12px;
  color: #000000;
  line-height: 46px;
  cursor: pointer;
  user-select: none;
}

.LeftOutlined:hover {
  color: #2f2f2f;
}

.CalendarControl {
  font-size: 14px;
  height: 46px;
  line-height: 42px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  user-select: none;
}

.CalendarControlYears {
  margin-right: 5px;
}

.CalendarBox {
  padding-top: 1px;
  padding-bottom: 1px;
  padding-left: 13px;
  padding-right: 13px;
}





