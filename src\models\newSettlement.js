import {
  selectByTaskId,               // 市场化查询结账单信息
  getAllStatus,                 // 查询预约状态、结算状态、接诊状态、收费模式
  setSettlementStatus,          // 修改医生工作台处的结算状态，1 未结算，2 结算中，3 已结算
  selectCompanyList,            // 查询品牌下的所有保险公司
  saveOrUpdateInsurance,        // 添加或修改保险
  selectDiscountRule,           // 根据保险公司id查询保险公司的折扣比例
  useWelfareForAllMs,           // 使用保险保存 查询结账单信息，三方诊所
  openCancelSettlement,         // (开放流程)暂不收费
  processEndToUpdateVisitStatus, // (开放流程)流程结束
  cancelVisiting,               // （开放流程）取消接诊
  selectDetailForPatient,       // 查询结账单详情
  checkPrice,                   // 结算前校验价格
  settlementById,               // 对结账单进行结算
  getAccountCanPayMoney,        // 查询账户信息和可支付金额(需要查询可支付金额时调)
  getAccountInfo,               // 查询账户信息
  checkIsInitial,            // 查询是否为初始密码
  QRCode,                       // 令牌口，(生成、撤销二维码)
  searchPatient,                // 查询患者
  checkTelCode,                 // 结算时使用预付款付款校验验证码
  sendTelCode,                  // 预付款结算时获取验证码
  againSettlement,              // 重新结算
  temporaryStorageSettlement,   // 结算最小化暂存数据
  transitBeforePrintingSettlement,   // 打印单据数据中转保存
  saveSettlementRemark,             // 结算备注保存
  insert,                            // 插入退款
  selectDetailForArrear,             // 查看欠款单的详情
  settlementForArrear,               // 补缴欠款
  asynchronousProcessSettlement,     // 查询缓存的结账单数据
  findOrderRecordBySettlementId,     // （开放流程）根据结算单查找开单操作记录
  settlementSpecOperation,      // 操作收费项-批量保存、单条保存、单条删除
  submitSettlement,             // 提交结算
  revocationSettlement,         // 撤销结算
  findSchemeVersion,                 // 查询定价方案以及左侧收费项目
  findTherapyOrderByCureIdOrLikeSpecName,     // 根据治疗ID以及搜索条件查询收费项
  editOften,                                   // 编辑常用
  findTodayVisitInfoByDoctorId,                // （补开收费项）查询医生今日就诊信息列表
  getChargeItemList,                           // （补开收费项）查询患者收费历史和本次收费
  storagePatientProblemList,                           // 退款窗口最小化暂存功能
  queryPatientProblemListBySettlementId,                           // 退款根据结算单id查询问题单数据接口
  storageArrears,                           // 欠款补缴窗口最小化暂存功能
  addDoctorOften,                           // 收藏收费项
  delDoctorOften,                           // 取消收藏收费项
  selectCustomerForArrears,                 // 欠款详情
  checkDepositPhone,                           // 账户信息变更手机号，校验原手机号
  sendDepositChangePhoneCode,                           // 账户信息变更手机号，发送验证码
  checkDepositChangePhoneCode,                           // 账户信息变更手机号，修改手机号
  getAllOrganization,                           // 查询所有的机构
  getPatientPreDepositInfo,                           // 查询余额明细
  getPatientPreDepositList,                           // 查询交易记录
  getInfoById,                           // 查询交易记录详情
  activeOldWelfare,                           // 激活福利
  getActivityList,                           // 按条件分页查询福利中心列表
  saveOrUpdateActivity,                           // 新增或修改福利活动
  getActivityInfo,                           // 根据活动id查询活动信息编辑回显信息
  checkActivityName,                           // 校验福利名称是否重复
  startOrStopOrDelActivity,                           // 停止、启用、删除福利活动
  getByActivityId,                           // 获取福利详情（菜单--->查询福利）
  updateTaskAndEsAmount,                     // 关闭弹窗，修改结算两种任务中的账单金额
  getSmsTemplateInfo,                        // 根据模板code、用户ID、租户和机构查询模板信息，管理权限
} from '@/services/newSettlement';

import {
  appointmentView,                           // （补开收费项）获取患者标签显示
} from '@/services/InsurancePayment'

export default {
  namespace: 'newSettlement',
  state: {
    newSettlementVisible:false,   // 治疗收费结算弹窗状态
    newSettlementChargeVisible:false,   // 收银台弹窗状态
    newSettlementDetailsVisible:false,   // 结算详情弹窗状态
    newSettlementRefundVisible:false,    // 退款弹窗状态
    newSettlementPayDebtVisible:false,    // 欠款补缴弹窗状态
    settlement: {},             // 全部数据，chargeModel  收费模式，1 规范化，2 简洁化，3 无医生
    totalSum: {},               // 预付款账户的信息
    allWelfareTripartite: [],  // 全部的福利，第三方诊所
    patientInfo: {},            // 患者信息
    paymentModes: [],           // 支付方式
    treatmentClass: [],        // 诊疗类
    arrearsList: [],           // 欠款明细
    paymentModesDetails: [],     // 详情页、退款页、欠款缴纳页，支付金额信息
    // 自定义的数据
    isRepayment: false,        // 本次是否还款（选中后不能更改）
    repaymentMoney: null,      // 还欠款的金额，默认为本机构全部欠款
    sumArrears: 0,                    // 患者总欠款
    orgArrears:0,                     // 患者当前机构欠款
    settlementArrearsDaoList:[],       // 欠款补缴收费项数据List
    useOnlyWelfareList: [],    // 已使用的客户福利，为了删除处理时判断是否已使用
    newSettlementId: null,     // 重结后生成的新的结算id
    useInsuranceStatus:false,   // 使用保险之后修改数量或单价
    // 预付款支付
    advancePayIsLoad: false,             // 弹窗是否已加载
    accountPatientInfo: {},               // 账户的患者的信息
    accountStatus: 'default',            // default 空白，null 账户未做过充值，1 账户已被锁定，2 账户正常，3 显示二维码，4 付款成功，5 付款失败
    advancePayList: [],                   // 预存款已支付金额的信息
    advancePayPassword: {},               // 预存款支付已输入过密码的账户信息（患者id）
    // 保险弹窗
    insuranceList: [],                // 查询到的所有的保险
    newInsuranceInfo: {},             // 选择的保险的详情
    newInsuranceId: null,             // 保险的id
    newInsuranceName: null,           // 保险名字的简称，若无简称则为全称
    newInsuranceNameFull: null,       // 保险名字的全称+简称
    newInsuranceCard: null,           // 保险单号
    selectedRowKeys: [],              // 勾选上的收费项
    initNewInsuranceInfo: {},         // 带init的数据，点取消时回显用，下同
    initNewInsuranceId: null,
    initNewInsuranceName: null,
    initNewInsuranceNameFull: null,
    initNewInsuranceCard: null,
    initSelectedRowKeys: [],
    commercialInsuranceDto:{},             // 保险相关信息

    settlementRemark:'',       // 收费结算备注
    isSettlementRemark: 0,     // 收费项备注是否显示打印单（0不显示，1显示，默认不显示0）

    maximizeIndex: null, // 最小化下标
    settlementId: null, // 结算Id
    patientId: null, // 患者Id
    patientName: null, // 患者名称

    paymentShowStatus:true,   // 接诊、收费、补开收费项收款信息是否展示“-”

    // 补开收费项
    patientInfoDto: {},               // 当前选中的就诊信息
    settlementOrderVOList: [],        // 既往结算记录
    patientSettlementEntity: null,   // 本次收费项


    // 欠款缴纳
    paymentArreatList: {}, // 欠款补缴勾选中的数据（{结算id：欠款金额}）
    paymentArrearsIdList: [],       // 欠款反显选中ID集合
    settlementIdList: [], // 欠款选中ID集合
  },
  effects: {
    // 市场化查询结账单信息
    *selectByTaskId({ payload: params }, { call }) {
      const res = yield call(selectByTaskId, params);
      return res;
    },
    // 查询预约状态、结算状态、接诊状态、收费模式
    *getAllStatus({ payload: params }, { call }) {
      const res = yield call(getAllStatus, params);
      return res;
    },
    // 修改医生工作台处的结算状态，1 未结算，2 结算中，3 已结算
    *setSettlementStatus({ payload: params }, { call }) {
      const res = yield call(setSettlementStatus, params);
      return res;
    },
    // 查询品牌下的所有保险公司
    *selectCompanyList({ payload: params }, { call }) {
      const res = yield call(selectCompanyList, params);
      return res;
    },
    // 添加或修改保险
    *saveOrUpdateInsurance({ payload: params }, { call }) {
      const res = yield call(saveOrUpdateInsurance, params);
      return res;
    },
    // 根据保险公司id查询保险公司的折扣比例
    *selectDiscountRule({ payload: params }, { call }) {
      const res = yield call(selectDiscountRule, params);
      return res;
    },
    // 使用保险保存、选择使用福利
    *useWelfareForAllMs({ payload: params }, { call }) {
      const res = yield call(useWelfareForAllMs, params);
      return res;
    },
    // (开放流程) 暂不收费
    *openCancelSettlement({ payload: params }, { call }) {
      const res = yield call(openCancelSettlement, params);
      return res;
    },
    // (开放流程) 流程结束
    *processEndToUpdateVisitStatus({ payload: params }, { call }) {
      const res = yield call(processEndToUpdateVisitStatus, params);
      return res;
    },
    // （开放流程）取消接诊
    *cancelVisiting({ payload: params }, { call }) {
      const res = yield call(cancelVisiting, params);
      return res;
    },
    // 查询结账单详情
    *selectDetailForPatient({ payload: params }, { call }) {
      const res = yield call(selectDetailForPatient, params);
      return res;
    },
    // 结算前校验价格
    *checkPrice({ payload: params }, { call }) {
      const res = yield call(checkPrice, params);
      return res;
    },
    // 对结账单进行结算
    *settlementById({ payload: params }, { call }) {
      const res = yield call(settlementById, params);
      return res;
    },
    // 查询账户信息和可支付金额(需要查询可支付金额时调)
    *getAccountCanPayMoney({ payload: params }, { call }) {
      const res = yield call(getAccountCanPayMoney, params);
      return res;
    },
    // 查询账户信息
    *getAccountInfo({ payload: params }, { call }) {
      const res = yield call(getAccountInfo, params);
      return res;
    },
    // 查询是否为初始密码
    *checkIsInitial({ payload: params }, { call }) {
      const res = yield call(checkIsInitial, params);
      return res;
    },
    // 令牌口，(生成、撤销二维码)
    *QRCode({ payload: params }, { call }) {
      const res = yield call(QRCode, params);
      return res;
    },
    // 查询患者
    *searchPatient({ payload: params }, { call }) {
      const res = yield call(searchPatient, params);
      return res;
    },
    // 结算时使用预付款付款校验验证码
    *checkTelCode({ payload: params }, { call }) {
      const res = yield call(checkTelCode, params);
      return res;
    },
    // 预付款结算时获取验证码
    *sendTelCode({ payload: params }, { call }) {
      const res = yield call(sendTelCode, params);
      return res;
    },
    // 重新结算
    *againSettlement({ payload: params }, { call }) {
      const res = yield call(againSettlement, params);
      return res;
    },
    // 结算最小化暂存数据
    *temporaryStorageSettlement({ payload: params }, { call }) {
      const res = yield call(temporaryStorageSettlement, params);
      return res;
    },
    // 打印单据数据中转保存
    *transitBeforePrintingSettlement({ payload: params }, { call }) {
      const res = yield call(transitBeforePrintingSettlement, params);
      return res;
    },
    // 结算备注保存
    *saveSettlementRemark({ payload: params }, { call }) {
      const res = yield call(saveSettlementRemark, params);
      return res;
    },
    // 插入退款
    *insert({ payload: params }, { call }) {
      const res = yield call(insert, params);
      return res;
    },
    // 查看欠款单的详情
    *selectDetailForArrear({ payload: params }, { call }) {
      const res = yield call(selectDetailForArrear, params);
      return res;
    },
    // 欠款详情
    *selectCustomerForArrears({ payload: params }, { call }) {
      const res = yield call(selectCustomerForArrears, params);
      return res;
    },
    // 补缴欠款
    *settlementForArrear({ payload: params }, { call }) {
      const res = yield call(settlementForArrear, params);
      return res;
    },
    // 查询缓存的结账单数据
    *asynchronousProcessSettlement({ payload: params }, { call }) {
      const res = yield call(asynchronousProcessSettlement, params);
      return res;
    },
    // 查询定价方案以及左侧收费项目
    *findSchemeVersion({ payload: params }, { call }) {
      const res = yield call(findSchemeVersion, params);
      return res;
    },
    // 根据治疗ID以及搜索条件查询收费项
    *findTherapyOrderByCureIdOrLikeSpecName({ payload: params }, { call }) {
      const res = yield call(findTherapyOrderByCureIdOrLikeSpecName, params);
      return res;
    },
    // 编辑常用
    *editOften({ payload: params }, { call }) {
      const res = yield call(editOften, params);
      return res;
    },
    // （开放流程）根据结算单查找开单操作记录
    *findOrderRecordBySettlementId({ payload: params }, { call }) {
      const res = yield call(findOrderRecordBySettlementId, params);
      return res;
    },
    // 操作收费项-批量保存、单条保存、单条删除
    *settlementSpecOperation({ payload: params }, { call }) {
      const res = yield call(settlementSpecOperation, params);
      return res;
    },
    // 提交结算
    *submitSettlement({ payload: params }, { call }) {
      const res = yield call(submitSettlement, params);
      return res;
    },
    // 撤销结算
    *revocationSettlement({ payload: params }, { call }) {
      const res = yield call(revocationSettlement, params);
      return res;
    },
    // （补开收费项）查询医生今日就诊信息列表
    *findTodayVisitInfoByDoctorId({ payload: params }, { call }) {
      const res = yield call(findTodayVisitInfoByDoctorId, params);
      return res;
    },
    // （补开收费项）获取患者标签显示
    *appointmentView({ payload: params }, { call }) {
      const res = yield call(appointmentView, params);
      return res;
    },
    // （补开收费项）查询患者收费历史和本次收费
    *getChargeItemList({ payload: params }, { call }) {
      const res = yield call(getChargeItemList, params);
      return res;
    },
    // 退款窗口最小化暂存功能
    *storagePatientProblemList({ payload: params }, { call }) {
      const res = yield call(storagePatientProblemList, params);
      return res;
    },
    // 退款根据结算单id查询问题单数据接口
    *queryPatientProblemListBySettlementId({ payload: params }, { call }) {
      const res = yield call(queryPatientProblemListBySettlementId, params);
      return res;
    },
    // 欠款补缴窗口最小化暂存功能
    *storageArrears({ payload: params }, { call }) {
      const res = yield call(storageArrears, params);
      return res;
    },
    // 收藏收费项
    *addDoctorOften({ payload: params }, { call }) {
      const res = yield call(addDoctorOften, params);
      return res;
    },
    // 取消收藏收费项
    *delDoctorOften({ payload: params }, { call }) {
      const res = yield call(delDoctorOften, params);
      return res;
    },

    // 账户信息变更手机号，校验原手机号
    *checkDepositPhone({ payload: params }, { call }) {
      const res = yield call(checkDepositPhone, params);
      return res;
    },
    // 账户信息变更手机号，发送验证码
    *sendDepositChangePhoneCode({ payload: params }, { call }) {
      const res = yield call(sendDepositChangePhoneCode, params);
      return res;
    },
    // 账户信息变更手机号，修改手机号
    *checkDepositChangePhoneCode({ payload: params }, { call }) {
      const res = yield call(checkDepositChangePhoneCode, params);
      return res;
    },
    // 查询所有的机构
    *getAllOrganization({ payload: params }, { call }) {
      const res = yield call(getAllOrganization, params);
      return res;
    },
    // 查询余额明细
    *getPatientPreDepositInfo({ payload: params }, { call }) {
      const res = yield call(getPatientPreDepositInfo, params);
      return res;
    },
    // 查询交易记录
    *getPatientPreDepositList({ payload: params }, { call }) {
      const res = yield call(getPatientPreDepositList, params);
      return res;
    },
    // 查询交易记录详情
    *getInfoById({ payload: params }, { call }) {
      const res = yield call(getInfoById, params);
      return res;
    },
    // 激活福利
    *activeOldWelfare({ payload: params }, { call }) {
      const res = yield call(activeOldWelfare, params);
      return res;
    },
    // 按条件分页查询福利中心列表
    *getActivityList({ payload: params }, { call }) {
      const res = yield call(getActivityList, params);
      return res;
    },
    // 新增或修改福利活动
    *saveOrUpdateActivity({ payload: params }, { call }) {
      const res = yield call(saveOrUpdateActivity, params);
      return res;
    },
    // 根据活动id查询活动信息编辑回显信息
    *getActivityInfo({ payload: params }, { call }) {
      const res = yield call(getActivityInfo, params);
      return res;
    },
    // 校验福利名称是否重复
    *checkActivityName({ payload: params }, { call }) {
      const res = yield call(checkActivityName, params);
      return res;
    },
    // 停止、启用、删除福利活动
    *startOrStopOrDelActivity({ payload: params }, { call }) {
      const res = yield call(startOrStopOrDelActivity, params);
      return res;
    },
    // 获取福利详情（菜单--->查询福利）
    *getByActivityId({ payload: params }, { call }) {
      const res = yield call(getByActivityId, params);
      return res;
    },
    // 关闭弹窗，修改结算两种任务中的账单金额
    *updateTaskAndEsAmount({ payload: params }, { call }) {
      const res = yield call(updateTaskAndEsAmount, params);
      return res;
    },
    // 根据模板code、用户ID、租户和机构查询模板信息，管理权限
    *getSmsTemplateInfo({ payload: params }, { call }) {
      const res = yield call(getSmsTemplateInfo, params);
      return res;
    },
  },
  reducers: {
    // 保存数据
    save(state, { payload }) {
      return {
        ...state,
        ...payload
      }
    },
    // 重置预付款支付金额
    clearAdvancePay(state, { payload }) {
      return {
        ...state,
        advancePayIsLoad: false,            // 弹窗是否已加载
        accountStatus: 'default',                 // null 账户未做过充值，1 账户已被锁定，2 账户正常，3 显示二维码，4 付款成功，5 付款失败
        advancePayList: [],                   // 预存款已支付金额的信息
      }
    },
    // 清空数据
    clear(state, { payload }) {
      return {
        newSettlementVisible:false,   // 治疗收费结算弹窗状态
        newSettlementChargeVisible:false,   // 收银台弹窗状态
        newSettlementDetailsVisible:false,   // 结算详情弹窗状态
        newSettlementRefundVisible:false,    // 退款弹窗状态
        newSettlementPayDebtVisible:false,    // 欠款补缴弹窗状态
        settlement: {},             // 全部数据，chargeModel  收费模式，1 规范化，2 简洁化，3 无医生
        totalSum: {},               // 预付款账户的信息
        allWelfareTripartite: [],  // 全部的福利，第三方诊所
        patientInfo: {},            // 患者信息
        paymentModes: [],           // 支付方式
        treatmentClass: [],        // 诊疗类
        arrearsList: [],           // 欠款明细
        paymentModesDetails: [],     // 详情页、退款页、欠款缴纳页，支付金额信息
        // 自定义的数据
        isRepayment: false,        // 本次是否还款（选中后不能更改）
        repaymentMoney: null,      // 还欠款的金额，默认为本机构全部欠款
        sumArrears: 0,                    // 患者总欠款
        orgArrears:0,                     // 患者当前机构欠款
        settlementArrearsDaoList:[],       // 欠款补缴收费项数据List
        useOnlyWelfareList: [],    // 已使用的客户福利，为了删除处理时判断是否已使用
        newSettlementId: null,     // 重结后生成的新的结算id
        useInsuranceStatus:false,   // 使用保险之后修改数量或单价
        // 预付款支付
        advancePayIsLoad: false,             // 弹窗是否已加载
        accountPatientInfo: {},               // 账户的患者的信息
        accountStatus: 'default',            // default 空白，null 账户未做过充值，1 账户已被锁定，2 账户正常，3 显示二维码，4 付款成功，5 付款失败
        advancePayList: [],                   // 预存款已支付金额的信息
        advancePayPassword: {},               // 预存款支付已输入过密码的账户信息（患者id）
        // 保险弹窗
        insuranceList: [],                // 查询到的所有的保险
        newInsuranceInfo: {},             // 选择的保险的详情
        newInsuranceId: null,             // 保险的id
        newInsuranceName: null,           // 保险名字的简称，若无简称则为全称
        newInsuranceNameFull: null,       // 保险名字的全称+简称
        newInsuranceCard: null,           // 保险单号
        selectedRowKeys: [],              // 勾选上的收费项
        initNewInsuranceInfo: {},         // 带init的数据，点取消时回显用，下同
        initNewInsuranceId: null,
        initNewInsuranceName: null,
        initNewInsuranceNameFull: null,
        initNewInsuranceCard: null,
        initSelectedRowKeys: [],
        commercialInsuranceDto:{},             // 保险相关信息
        
        settlementRemark:'',       // 收费结算备注
        isSettlementRemark:0,     // 收费项备注是否显示打印单（0不显示，1显示，默认不显示0）

        maximizeIndex: null, // 最小化下标
        settlementId: null, // 结算Id
        patientId: null, // 患者Id
        patientName: null, // 患者名称

        paymentShowStatus:true,   // 接诊、收费、补开收费项收款信息是否展示“-”

        // 补开收费项
        patientInfoDto: {},               // 当前选中的就诊信息
        settlementOrderVOList: [],        // 既往结算记录
        patientSettlementEntity: null,   // 本次收费项
        paymentArreatList: {},          // 欠款补缴勾选中的数据（{结算id：欠款金额}）
        paymentArrearsIdList: [],       // 欠款反显选中ID集合
        settlementIdList: [], // 欠款选中ID集合
      }
    },
  },
  //监听路由变化重置model
  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, search }) => {
        if (pathname.indexOf('/allTasks') === -1) {
          dispatch({
            type: 'clear',
          })
        }
      });
    }
  },
};
