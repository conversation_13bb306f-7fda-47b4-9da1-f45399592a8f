import React, { PureComponent } from 'react';
import { Icon, Menu, Dropdown, message } from 'antd';
import Link from 'umi/link';
import Debounce from 'lodash-decorators/debounce';
import styles from '../index.less';
import RightContent from './RightContent';
import { getOrganizationInfo } from "@/utils/utils";
import menuico from '@/assets/menuico.png';
import changemenu from '@/assets/changemenu.png';
import { getUrlParam, serilizeURL } from '@/utils/utils'
import { connect } from 'dva'
import router from 'umi/router'
import {
  AppintmentCSUrlSubscribe,
  AppintmentCSUrlRegisterCalendar
} from '@/utils/CalendarUtils.js'
import { stringify } from 'qs';



@connect(({ Getscreen, loading }) => ({
  Getscreen,
  loading
}))
export default class GlobalHeader extends PureComponent {
  state = {
    obj: {}
  }
  componentWillUnmount() {
    this.triggerResizeEvent.cancel();
  }
  /* eslint-disable*/
  @Debounce(600)
  triggerResizeEvent() {
    // eslint-disable-line
    const event = document.createEvent('HTMLEvents');
    event.initEvent('resize', true, false);
    window.dispatchEvent(event);
  }
  toggle = () => {
    /*const { collapsed, onCollapse } = this.props;
    onCollapse(!collapsed);
    this.triggerResizeEvent();*/
    router.replace({
      pathname: `/customerservice`,
      //query:urlParamsActiveKey
    })
  };
  // 获取机构列表
  DropdownClick = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'user/getOrganizationList',
      payload: {
        "userId": localStorage.getItem('id')
      }
    })
  }
  // 切换机构
  BrandOrganization = (ele) => {
    //localStorage.removeItem("firstLogin");
    //const { dispatch } = this.props;
    /*this.setState({
      obj: i
    }, () => {
      localStorage.setItem('organizationInfoId', i.id);
      dispatch({
        type: 'user/getSwitchLogin',
        payload: {
          "userId": localStorage.getItem('id')
        }
      })
      dispatch({
        type: 'user/getMessageCount',
      });
    })*/
    const { systems, startuStatus, id } = ele || {}

    if (systems == null) {
      message.warning('诊所未配置机构制度，请联系管理员！')
      /*this.setState({
        onClickResultListID:id,
      })*/
    } else {
      if (startuStatus != 1) {
        message.warning('诊所已经停诊不可预约！')
        /*this.setState({
          onClickResultListID: id,
        })*/
      } else {
        localStorage.setItem('organizationInfoJsonCS', JSON.stringify(ele));
        localStorage.setItem('organizationInfoJson', JSON.stringify(ele));
        localStorage.setItem('organizationInfoId', id);


        let href = window.location.href;
        const urlIndex = href.indexOf('appointment')
        const urlIndexParams = href.indexOf('?')
        const urlSubStr = href.substring(urlIndex, urlIndexParams);
        const urlParamsPatientId = urlSubStr.split('/')[1];
        const urlParamsOrganizationId = urlSubStr.split('/')[2];
        const urlParamsActiveKey = serilizeURL(href)


        if (urlParamsOrganizationId == ele.id) {
          window.location.reload(true)
        } else {
          //OrganizationInfo.systems == 1,     '                    // 0挂号制 1预约制
          //window.location.replace()
          let AppintmentUrl = ele.systems == 1 ? AppintmentCSUrlSubscribe : AppintmentCSUrlRegisterCalendar;
          window.location.replace(`${AppintmentUrl}${urlParamsPatientId}/${ele.id}?${stringify(urlParamsActiveKey)}`)
          /*router.replace({
            pathname: `${AppintmentUrl}${urlParamsPatientId}/${ele.id}`,
            query:urlParamsActiveKey
          });*/
        }
        //window.location.reload()
      }
    }
  };

  // 返回预约
  GoToAppointment = () => {
    router.push(`/customerservice/subscribe/appointment/${localStorage.getItem('userIdss')}/${localStorage.getItem('organizationInfoId')}?issue=4`)
  };
  render() {
    const { obj } = this.state;
    const { collapsed, isMobile, logo, Getscreen } = this.props;
    const { openTelAppointmentOrgList } = Getscreen || {}
    // console.log('openTelAppointmentOrgList 123:: ',openTelAppointmentOrgList);

    const arr = openTelAppointmentOrgList ? openTelAppointmentOrgList : []
    const menu = (
      <Menu>
        {arr && arr.map(i => (
          <Menu.Item style={{ margin: "10px 0px" }} key={i.id} onClick={() => this.BrandOrganization(i)}>{i.organizationName}</Menu.Item>
        ))}
      </Menu>
    );


    let href = window.location.href
    let showUrlArr = ["/customerservice/subscribe/appointment", '/customerservice/registerCalendar/appointment']
    let showUrlArrs = ["/customerservice/subscribe/appointment", 'customerservice/user/details']
    let regState = false;
    let regStates = false;
    showUrlArr.forEach((customerURl) => {
      if (href.indexOf(customerURl) != -1) {
        regState = true;
      }
    });
    showUrlArrs.forEach((customerURl) => {
      if (href.indexOf(customerURl) != -1) {
        regStates = true;
      } else {
        regStates = false
      }
    });


    const organizationName = getOrganizationInfo().organizationName;
    const organizationSize = localStorage.getItem("organizationSize");
    return (
      <div className={styles.header}>
        {
          regStates
            ? <i className={styles.trigger} onClick={this.GoToAppointment}>
              <Icon type="rollback" />
            </i>
            : <i className={styles.trigger} onClick={this.toggle}>
              <Icon type="home" />
            </i>
        }



        <>
          {
            regStates
              ?
              <span style={{ cursor: 'pointer', fontSize: 16, color: '#17233d' }} onClick={this.GoToAppointment}>&nbsp;&nbsp;返回预约</span>
              :
              <>
                {regState &&
                  <Dropdown overlay={menu} overlayStyle={{ maxHeight: '400px', overflowY: 'scroll' }} trigger={['click']}>
                    <span className={styles.switchclinic}>
                      {obj && obj.id ? obj.organizationName : organizationName}
                      {organizationSize == 1 ? null : (
                        <i style={{ marginLeft: 10, fontSize: 20 }}>
                          <img src={changemenu} />
                        </i>)}
                    </span>
                  </Dropdown>
                }
              </>


          }
        </>

        <RightContent {...this.props} />
      </div>
    );
  }
  componentDidMount() {

  }
}
