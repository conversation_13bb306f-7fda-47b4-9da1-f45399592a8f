import React from 'react';
import { Icon, Layout, LocaleProvider, message, Row, Spin } from 'antd';
import DocumentTitle from 'react-document-title';
import isEqual from 'lodash/isEqual';
import memoizeOne from 'memoize-one';
import { connect } from 'dva';
import { ContainerQuery } from 'react-container-query';
import classNames from 'classnames';
import pathToRegexp from 'path-to-regexp';
import { enquireScreen, unenquireScreen } from 'enquire-js';
import { formatMessage } from 'umi/locale';
import SiderMenu from '@/components/SiderMenu';
import Authorized, { reloadAuthorized } from '@/utils/Authorized';
import Footer from './Footer';
import Header from './Header';
import Context from './MenuContext';
import Exception403 from '../pages/Exception/403';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import styles from './index.less';
import router from 'umi/router';
import CryptoJS from 'crypto-js';
import UpdateInformation from '@/pages/UpdateInformation/Information.js';
import ModalByGoJwsmed from '@/pages/Account/Authorization/components/ModalByGoJwsmed/index.js'
import ModalByConsumablesMall from '@/pages/Account/Authorization/components/ModalByConsumablesMall/index.js'
import { CheckPostern, getPageQuery } from '@/utils/utils'; // 系统更新弹窗
import mp3 from '@/assets/home/<USER>'
const { Content } = Layout;

// 转换为浏览器url协议安全的Base64序列串
function urlSateBase64Encode(base64Str) {
  if (!base64Str) return null;
  let safeStr = base64Str.replace(/\+/g, '-').replace(/\//g, '_').replace(/\=/g, "");
  return safeStr
}

// Conversion router to menu.
function formatter(data, parentAuthority, parentName) {
  return data
    .map(item => {
      if (!item.name || !item.path) {
        return null;
      }
      let locale = 'menu';
      if (parentName) {
        locale = `${parentName}.${item.name}`;
      } else {
        locale = `menu.${item.name}`;
      }
      const result = {
        ...item,
        name: formatMessage({ id: locale, defaultMessage: item.name }),
        locale,
        authority: item.authority || parentAuthority,
      };
      if (item.routes) {
        const children = formatter(item.routes, item.authority, locale);
        // Reduce memory usage
        result.children = children;
      }
      delete result.routes;
      return result;
    })
    .filter(item => item);
}
const taskTypeObj = [
  '无',
  "接诊",
  "诊前检查",
  "回复",
  "咨询",
  "会诊",
  "预约",
  "结算",
  "发药",
  "投诉",
  "转诊",
  "回访",
  "提醒",
  "特殊福利审批",
  "退款审批",
  "补偿审批",
  "分诊叫号",
  "任务",
  "任务",
  "任务",
  "任务",
  "任务",
  "预约意向",

];

/** websocket类型
 * taskObj.message.taskType
 * 医生预约tasktype：6
 * 医生工作台（接诊）tasktype：1||2||5
 * 发药tasktype：8
 * 结算（未处理）tasktype：7&&status：1
 * 结算（已处理）tasktype：7&&status：2
 * 任务（回复）tasktype：3
 * 任务（投诉）tasktype：9
 * 任务（预约确认）tasktype：10
 * 任务（回访）tasktype：11
 * 任务（提醒）tasktype：12
 * 任务（咨询）tasktype：4
 * 任务（特殊福利审批）tasktype：13
 * 任务（退款审批）tasktype：14
 * 任务（补偿审批）tasktype：15
 * 任务（预约规则）tasktype：17
 * 任务（时间占用）tasktype：18
 * 电子建档成功tasktype：21
 * 预约意向tasktype：22
 * 投诉跟进tasktype：24
 * 信息收费支付结果tasktype：25
 */

/**
 * websocket后端推送的数据
 {
    isAll: 0,
    message: {
        isTask: 0,                          // 1 任务，出现通知条，0 非任务类，不出通知条的
        messageContent: "内容",
        organizationId: "机构ID",
        taskType: "类型",
        // 下面的字段taskType不同返回的具体字段也不同
        outTradeNo: "订单号",
        smsOrdersId: "订单ID",
        transactionStatus: "交易状态"           // 信息收费支付交易状态 1支付失败 2支付成功 3 取消支付（超时自动关单）
        transactionStatus: "交易状态"           // 预存款支付状态
        transactionId: "预存款支付id",
        patientId: "患者id",
        patientName: "患者姓名",
        settlementId: "结算id",
        registerId: "预约id",
        taskId: "任务类型不同，含义也不同，比如结算任务代表结算id，预约任务代表预约id",
        maxType: "",
        state: "",
        createdGmtAt: "",
        ...
    },
    tenantId: "租户ID",
    userName: ["4797" ],                    // 通知用户ID
 }
 *
 */


const memoizeOneFormatter = memoizeOne(formatter, isEqual);

const query = {
  'screen-xs': {
    maxWidth: 575,
  },
  'screen-sm': {
    minWidth: 576,
    maxWidth: 767,
  },
  'screen-md': {
    minWidth: 768,
    maxWidth: 991,
  },
  'screen-lg': {
    minWidth: 992,
    maxWidth: 1199,
  },
  'screen-xl': {
    minWidth: 1200,
    maxWidth: 1599,
  },
  'screen-xxl': {
    minWidth: 1600,
  },
};

class BasicLayout extends React.PureComponent {
  constructor(props) {
    super(props);
    this.getPageTitle = memoizeOne(this.getPageTitle);
    this.getBreadcrumbNameMap = memoizeOne(this.getBreadcrumbNameMap, isEqual);
    this.breadcrumbNameMap = this.getBreadcrumbNameMap();
    this.matchParamsPath = memoizeOne(this.matchParamsPath, isEqual);
  }

  state = {
    rendering: true,
    isMobile: true,
    message: false,
    menuData: this.getMenuData(),
    taskObj: {},   // websocket 返回数据信息
    holdStatus: false,
    websocket: null,
    Role: '',
    Isupdate: localStorage.getItem('isRead') == 0 ? true : false,
    CurrentGroups: 'statistics' // 初始化默认展开的是查询与统计
  };

  componentDidMount() {

    reloadAuthorized();

    // 如果当前用户为400客服则直接跳转至400客服页面
    const fourCustomer = localStorage.getItem('fourCustomer');
    if (fourCustomer == 1) {
      const scope = JSON.parse(localStorage.getItem('scope'));
      let path = Math.min.apply(Math, scope);
      switch (path) {
        case 76:
          router.replace("/customerService/appointment");
          break;
        case 77:
          router.replace("/customerService/patient");
          break;
        case 78:
          router.replace("/customerService/set");
          break;
        case 79:
          router.replace("/customerService/welfare");
          break;
        case 108:
          router.replace("/customerService/complaint/list");
          break;
        case 120:
          router.replace("/customerService/searchChargeItem");
          break;
        default:
          break;
      }
    }

    const userRole = localStorage.getItem("role") ? JSON.parse(localStorage.getItem("role")) : null;
    const userRoleCode = [];
    let Role = ""
    if (userRole) {
      userRole && userRole.map((item, index) => userRoleCode.push(item.roleId))
      Role = userRoleCode.join(" ")
      this.setState({
        Role
      })
    }
    const { dispatch } = this.props;
    dispatch({
      type: 'setting/getSetting',
    });
    // dispatch({
    //   type: 'user/getReplaceShowUrl',
    //   payload: { userId: localStorage.getItem('id') }
    // })
    this.renderRef = requestAnimationFrame(() => {
      this.setState({
        rendering: false,
      });
    });
    this.enquireHandler = enquireScreen(mobile => {
      const { isMobile } = this.state;
      if (isMobile !== mobile) {
        this.setState({
          isMobile: true,
        });
      }
    });
    this.connectWebSocket();

  }

  // 获取cookie
  getCookie = (_name) => {
    let { cookie } = document;
    let arr = cookie.split(";");
    for (let i = 0; i < arr.length; i++) {
      let newArr = arr[i].split(">");
      if (newArr[0] == _name) {
        return newArr[2];
      }
    }
  }

  componentDidUpdate(preProps) {
    // After changing to phone mode,
    // if collapsed is true, you need to click twice to display
    this.breadcrumbNameMap = this.getBreadcrumbNameMap();
    const { isMobile } = this.state;
    const { collapsed } = this.props;
    if (isMobile && !preProps.isMobile && !collapsed) {
      this.handleMenuCollapse(false);
    }
  }

  // 定时器 1 分钟检测一下 重新打开
  timerWebsocket = (count) => {
    // this.intervalA = setInterval(() => {
    // 先关闭再重新连
    this.closeWebSocket()
    this.connectWebSocket();
    // }, 1000)
  }

  // 建立websocket链接
  connectWebSocket = () => {
    const userID = localStorage.getItem('id');
    const tenantId = localStorage.getItem('tenantId');
    const organizationId = localStorage.getItem("organizationInfoId")
    const username = localStorage.getItem('token_username');
    const userTenantId = localStorage.getItem("userTenantId") ? localStorage.getItem("userTenantId") : localStorage.getItem("tenantId") ? localStorage.getItem("tenantId") : getPageQuery().tenantId; // 登录用户的租户Id（登录第三方400时用的）
    const CheckPosternParameter = CheckPostern() ? '' : '/f';
    let usernameOptions = username + '/' + userTenantId + CheckPosternParameter;   //用户输入的账户名称

    const a = '|'

    const agense = usernameOptions + a + 'PC' + a + localStorage.getItem('access_token')
    let wordArray = CryptoJS.enc.Utf8.parse(agense);
    let agensebase64 = CryptoJS.enc.Base64.stringify(wordArray);
    let urlSateAgensebase64 = urlSateBase64Encode(agensebase64)


    const websocket = new WebSocket(`wss://${location.host}/taskHandler/ID=${userID}${tenantId}${organizationId}${parseInt(Math.random() * (99999 - 10000))}`, [urlSateAgensebase64])
    // const websocket = new WebSocket(`wss://dental-test.friday.tech/taskHandler/ID=${userID}${tenantId}${organizationId}${parseInt(Math.random() * (99999 - 10000))}`,[urlSateAgensebase64])
    // const websocket = new WebSocket(`ws://***********:9032/taskHandler/ID=${userID}${tenantId}${organizationId}${parseInt(Math.random() * (99999 - 10000))}`,[urlSateAgensebase64])
    // const websocket = new WebSocket(`ws://**********:8000/taskHandler/ID=${userID}${tenantId}${organizationId}${parseInt(Math.random() * (99999 - 10000))}`)
    // websocket

    this.setState({
      websocket
    }, () => {
      // 打开webSokcet连接时，回调该函数
      this.state.websocket.onopen = (res) => {
        console.log('链接成功')
        console.log(res)
        // this.timerWebsocket();
        if (localStorage.getItem("socket") != 1) {
          const { dispatch } = this.props;
          localStorage.setItem("socket", 1);
          // dispatch({
          //   type: 'user/getQueryTask',
          // })
        } else {
          localStorage.setItem("socket", 1);
        }
      }
      this.state.websocket.onerror = (event) => {
        // 消息推送报错捕获～！
        // console.info(`${event}:尝试捕获报错～！`)
      }
      // 关闭webSocket连接时，回调该函数
      this.state.websocket.onclose = (e) => {
        // console.info(`状态码是${e.code},已尝试重新链接消息～！`)
        // 如果非正常断连，就重新建立起链接～！
        if ((!e.wasClean || e.code == 1006 || localStorage.getItem('socket')) && location.hostname != 'localhost') {
          // 主动关闭长链接，再次重新链接
          this.timerWebsocket();
          return;
        }
      }

      // 接收信息
      this.state.websocket.onmessage = (msg) => {
        console.log('asdasd', msg)
        if (msg.data != "成功建立socket连接") {
          const { dispatch, location } = this.props;
          const taskObj = JSON.parse(msg.data);

          const maxTypeArr = [2, 3, 6,];
          const taskTypeArr = [2, 3, 6,];
          // 如果用户id 与机构id 相同弹出message信息条
          if (Array.isArray(taskObj.userName) && taskObj.userName.length > 0) {
            taskObj.userName.map((item) => {
              if (item == localStorage.getItem('id')) {
                if (taskObj.message.organizationId == localStorage.getItem("organizationInfoId")) {
                  if (taskObj.message.isTask == 1) { // isTask 是否是任务 1是  0否
                    taskObj.message.state = taskObj.message.taskType == 21 ? 1 : taskObj.message.state;
                    if (taskObj.message.taskType == 1) { // 接诊
                      const music = new Audio(mp3);
                      music.play().then(()=>{}).catch(()=>{});
                    };
                    // 如果当前是首页
                    if (location.pathname == "/home/<USER>") {
                      // 判断首页推送任务是否为  接诊  结算
                      if (taskObj.message.maxType == 2 || taskObj.message.maxType == 3) {
                        dispatch({  // 刷新首页数量
                          type: 'homeIndex/setTaskListState',
                          payload: { getTaskListCount: true }
                        })
                      }
                      // 在首页情况下 如果当前信息的状态值与首页显示一致 则刷新列表
                      if (location.query.homeLeftLiType == taskObj.message.maxType) {
                        dispatch({
                          type: 'homeIndex/setTaskListState',
                          payload: { getTaskListState: true }
                        })
                      }
                      // 判断是否为 回访 咨询 投诉
                      if ((taskObj.message.taskType == 11 || taskObj.message.taskType == 4 || taskObj.message.taskType == 24) && taskObj.message.taskType == location.query.homeLeftLiType) {
                        dispatch({
                          type: 'global/setPayStatus',
                          payload: {
                            isDefineoffers: true
                          }
                        })
                      }
                    }
                    // 如果是新首页，刷新
                    if (location.pathname == "/home/<USER>") {
                      dispatch({
                        type: 'newHomeIndex/setTaskListState',
                        payload: { getMyTaskLists: true }
                      })
                    }
                    // 全部任务
                    if (location.pathname == '/allTasks') {
                      dispatch({  // 刷新首页数量
                        type: 'allTasks/setTaskListState',
                        payload: { getAllTaskLists: true }
                      })
                    }
                    // 预触任务
                    if (location.pathname == '/allTasks/subscribeTouch' && taskObj.message.maxType == 6) {
                      dispatch({
                        type: 'allTasks/setTaskListState',
                        payload: { getAllTaskLists: true }
                      })
                    }
                    // 首触任务
                    if (location.pathname == '/allTasks/firstTouch' && taskObj.message.maxType == 9) {
                      dispatch({
                        type: 'allTasks/setTaskListState',
                        payload: { getAllTaskLists: true }
                      })
                    }
                    // 诊触任务
                    if (location.pathname == '/allTasks/diagnosticTouch' && (taskObj.message.taskType == 4 || taskObj.message.maxType == 3)) {
                      dispatch({
                        type: 'allTasks/setTaskListState',
                        payload: { getAllTaskLists: true }
                      })
                    }
                    // 末触任务
                    if (location.pathname == '/allTasks/endTouch' && taskObj.message.maxType == 2) {
                      dispatch({
                        type: 'allTasks/setTaskListState',
                        payload: { getAllTaskLists: true }
                      })
                    }
                    // 延触任务
                    if (location.pathname == '/allTasks/continueTouch' && (taskObj.message.taskType == 11 || taskObj.message.taskType == 24)) {
                      dispatch({
                        type: 'allTasks/setTaskListState',
                        payload: { getAllTaskLists: true }
                      })
                    }


                    this.setState({
                      message: true,
                      taskObj: taskObj.message
                    }, () => {
                      this.timer(5)
                    })
                    dispatch({
                      type: 'global/setPayStatus',
                      payload: {
                        payment: {
                          taskType: taskObj.message.taskType,  // 任务类型 id
                          patientId: taskObj.message.patientId, // 患者id
                        },
                      }
                    })
                  } else {
                    if (taskObj.message.taskType == 1) { // 接诊
                      const music = new Audio(mp3);
                      music.play().then(()=>{}).catch(()=>{});
                    };
                    // 如果当前在首页 从新获取数据
                    if (location.pathname == "/home/<USER>") {
                      // 判断首页推送任务是否为  接诊  结算
                      if (taskObj.message.taskType == 2 || taskObj.message.taskType == 3) {
                        dispatch({  // 刷新首页数量
                          type: 'homeIndex/setTaskListState',
                          payload: { getTaskListCount: true }
                        })
                      }
                      // 在首页情况下 如果当前信息的状态值与首页显示一致 则刷新列表
                      if (location.query.homeLeftLiType == taskObj.message.taskType) {
                        dispatch({
                          type: 'homeIndex/setTaskListState',
                          payload: { getTaskListState: true }
                        })
                        dispatch({
                          type: 'global/setPayStatus',
                          payload: { isDefineoffers: true }
                        })
                      }
                    }

                    // 如果是新首页，刷新
                    if (location.pathname == "/home/<USER>") {
                      dispatch({
                        type: 'newHomeIndex/setTaskListState',
                        payload: { getMyTaskLists: true }
                      })
                    }

                    // 全部任务
                    if (location.pathname == '/allTasks') {
                      dispatch({  // 刷新首页数量
                        type: 'allTasks/setTaskListState',
                        payload: { getAllTaskLists: true }
                      })
                    }
                    // 预触任务
                    if (location.pathname == '/allTasks/subscribeTouch' && taskObj.message.maxType == 6) {
                      dispatch({
                        type: 'allTasks/setTaskListState',
                        payload: { getAllTaskLists: true }
                      })
                    }
                    // 首触任务
                    if (location.pathname == '/allTasks/firstTouch' && taskObj.message.maxType == 9) {
                      dispatch({
                        type: 'allTasks/setTaskListState',
                        payload: { getAllTaskLists: true }
                      })
                    }
                    // 诊触任务
                    if (location.pathname == '/allTasks/diagnosticTouch' && (taskObj.message.taskType == 4 || taskObj.message.maxType == 3)) {
                      dispatch({
                        type: 'allTasks/setTaskListState',
                        payload: { getAllTaskLists: true }
                      })
                    }
                    // 末触任务
                    if (location.pathname == '/allTasks/endTouch' && taskObj.message.maxType == 2) {
                      dispatch({
                        type: 'allTasks/setTaskListState',
                        payload: { getAllTaskLists: true }
                      })
                    }
                    // 延触任务
                    if (location.pathname == '/allTasks/continueTouch' && (taskObj.message.taskType == 11 || taskObj.message.taskType == 24)) {
                      dispatch({
                        type: 'allTasks/setTaskListState',
                        payload: { getAllTaskLists: true }
                      })
                    }

                    dispatch({
                      type: 'global/setPayStatus',
                      payload: {
                        payment: {
                          settlementId: taskObj.message.settlementId,  // 结算id
                          transactionId: taskObj.message.transactionId,  // 支付id
                          transactionStatus: taskObj.message.transactionStatus,  // 预付款状态
                          patientId: taskObj.message.patientId, // 患者id

                        },
                        intentCountDto: taskObj.message.intentCountDto,
                        isSignature: (taskObj.message.transactionStatus == 4),  // 是否是签名
                        // 信息收费支付结果
                        noTaskContent: {
                          taskType: taskObj.message.taskType,
                          outTradeNo: taskObj.message.outTradeNo,                  // 订单号
                          smsOrdersId: taskObj.message.smsOrdersId,                // 订单ID
                          transactionStatus: taskObj.message.transactionStatus,    // 交易状态 1支付失败 2支付成功 3 取消支付（超时自动关单）
                        }
                      }
                    })
                    this.setState({
                      taskObj: taskObj.message
                    })
                  }
                }

              }
            })
          } else {
            message.error('websocket返回数据格式错误！')
          }

        } else {
          console.log('asdasd', msg.data)
        }
      }
    })

  }

  // 关闭连接
  closeWebSocket = () => {
    if (this.state.websocket != null) {
      localStorage.removeItem("socket");
      this.state.websocket.close();
    }
  }

  DeleteTask = () => {
    clearInterval(this.interval);
    this.setState({
      message: false,
      taskObj: {}
    })
  }

  // 跳转
  /**
   * id   任务ID
   * taskType  当前任务类型
   * status 当前任务状态
   * patientId  患者ID
   * createdGmtAt  创建时间
   * registrationId  回访ID执行人
   * */
  JumpClick = (id, taskType, status, patientId, createdGmtAt, registrationId, settlementId, patientName) => {
    const { dispatch } = this.props;
    const organizationId = localStorage.getItem("organizationInfoId"); // 获取机构ID
    if (taskType == 6) {
      // 明日预约列表
      router.push({
        pathname: '/home/<USER>',
        query: { homeLeftLiType: 6 }
      })
    } else if (taskType == 1 || taskType == 2 || taskType == 5) {
      // 医生工作台（接诊）
      dispatch({
        type: "NewDoctorWorkbench/save",
        payload: {
          // doctorWorkbenchVisible: true,
          patientId,
          appointmentId: id,
          settlementId,
          patientName,
        }
      })
      router.push({
        pathname: '/allTasks',
      })
    } else if (taskType == 7 && status == 1) {
      // 结算 (未处理)
      dispatch({
        type: "newSettlement/save",
        payload: {
          // newSettlementVisible: true,
          settlementId: id,
          patientId,
          patientName,
        }
      })
      router.push({
        pathname: '/allTasks'
      })
    } else if (taskType == 7 && status == 2) {
      // 结算 (已处理)
      dispatch({
        type: "newSettlement/save",
        payload: {
          // newSettlementDetailsVisible: true,
          settlementId: id,
          patientId,
          patientName,
        }
      })
      router.push({
        pathname: '/allTasks'
      })
    } else if (taskType == 22) {
      // 预约-预约意向
      router.push({
        pathname: `/subscribe/fullscreen`,
        query: {
          date: 'day', intentId: id
        }
      })
    } else if (taskType == 24) {
      // 投诉跟进
      router.push(
        {
          // TODO 跳转患者详情
          pathname: `/customerProfile/customerDetails/${patientId}`,
          query: {
            tabKey: '001'
          }
        }
      )
      // router.push(`/complaint/followUp/${id}`)
    } else if (taskType == 4) { // 咨询任务
      router.push({
        // TODO 跳转患者详情
        pathname: `/customerProfile/customerDetails/${patientId}`, // 跳转CRM详情页
        query: {
          tabKey: '001',
          taskIDID: id,
          isOpenProgramme: 'true', // 打开做方案新页签
        }
      })
    } else if (taskType == 11) {
      // 回访任务
      router.push({
        // TODO 跳转患者详情
        pathname: `/customerProfile/customerDetails/${patientId}`,
        query: {
          taskId: id,
          tabKey: '009'
        }
      })
    }
    this.DeleteTask();
    dispatch({
      type: 'global/setStatus',
      payload: {
        getStatus: true,
        taskType,
        status,
      }
    })
  }


  timer = (count) => {
    this.interval = setInterval(() => {
      count -= 1;
      this.setState({ count });
      if (count === 0) {
        clearInterval(this.interval);
        this.setState({
          message: false,
          taskObj: {}
        })
      }
    }, 1000)
  }

  componentWillUnmount() {
    clearInterval(this.interval); // 清除定时器
    this.closeWebSocket();
    cancelAnimationFrame(this.renderRef);
    unenquireScreen(this.enquireHandler);
  }

  getContext() {
    const { location } = this.props;
    return {
      location,
      breadcrumbNameMap: this.breadcrumbNameMap,
    };
  }

  getMenuData() {
    const {
      route: { routes, authority },
    } = this.props;
    return memoizeOneFormatter(routes, authority);
  }

  /**
   * 获取面包屑映射
   * @param {Object} menuData 1菜单配置
   */
  getBreadcrumbNameMap() {
    const routerMap = {};
    const mergeMenuAndRouter = data => {
      data.forEach(menuItem => {
        if (menuItem.children) {
          mergeMenuAndRouter(menuItem.children);
        }
        // Reduce memory usage
        routerMap[menuItem.path] = menuItem;
      });
    };
    mergeMenuAndRouter(this.getMenuData());
    return routerMap;
  }

  matchParamsPath = pathname => {
    const pathKey = Object.keys(this.breadcrumbNameMap).find(key =>
      pathToRegexp(key).test(pathname)
    );
    return this.breadcrumbNameMap[pathKey];
  };

  getPageTitle = pathname => {
    const currRouterData = this.matchParamsPath(pathname);

    if (!currRouterData) {
      return 'FRIDAY口腔业务综合管理平台';
    }
    const pageName = formatMessage({
      id: currRouterData.locale || currRouterData.name,
      defaultMessage: currRouterData.name,
    });
    return `${pageName} - FRIDAY口腔业务综合管理平台`;
  };

  getContentStyle = () => {
    const { fixedHeader, menuCollapsed } = this.props;

    var menuPlanData = [
      "/customerPlan",
    ]
    // 左侧菜单消失判断
    if (menuPlanData.includes(this.props.location.pathname)) {
      return {
        paddingTop: fixedHeader ? 50 : 0,
        paddingLeft: 0,
      };
    }

    var arr = [
      "^/home/<USER>",
      "^/allTasks",
      "^/greatMedicalRecord",
      "^/customerfollow",
      "^/customerProfile",
      "^/customerDetails",
      "^/DirectionalInvitations",
      "^/appointmentrules/appointmentruleslist$",
      "^/appointmentrules/addappointmentrules$",
      "^/dailyknots$", "^/home/<USER>",
      "^/taskhistory/list$",
      "^/notification$",
      "^/subscribe/fullscreen",
      "^/settlement/list",
      "^/settlement/payment",
      "^/userarchives/archiveslist$",
      "^/customvisit/visitlist$",
      "^/welfarecenter/welfarelist$",
      "^/welfarecenter/welfaredetail/",
      "^/welfarecenter/addwelfare",
      "^/points",
      "^/userpayment/payment/refupayment",
      "^/exclusiveCustomerService",
      "^/updataInformation/detail",
      "^/complaint/",
      "^/garworth/",
      "^/customerManagement/",
      "^/customerManagement/customerInfo",
      "^/customerManagement/customerPool",
      "^/customerArchives",
      "^/customerManagement",
      "^/ArriveToday",
      "^/ContentCenter/DownLoad",
      "^/DoctorManage/MedicalRecordAuthority",
      "^/SystemSetup/ImageManage",
      "^/printpages",
      "^/excellentCaseStudy/details",
      "^/statisticsQuery"
    ]

    // 新的，间距需要设为 16px 的页面
    const arr2 = [
      "^/surcharge",
      "^/setup",
    ]
    const reg2 = new RegExp(arr2.join("|"), "g");
    if (reg2.test(this.props.location.pathname)) {
      return {
        margin: '16px 16px 0',
        paddingTop: fixedHeader ? 50 : 0,
        paddingLeft: menuCollapsed ? 180 : 56,
      }
    }

    // 老的，间距24的页面
    var reg = new RegExp(arr.join("|"), "g");
    if (!(reg.test(this.props.location.pathname))) {
      return {
        margin: '20px 20px 0',
        paddingTop: fixedHeader ? 50 : 0,
        paddingLeft: menuCollapsed ? 180 : 56,
      };
    }

    // 不要间距的页面
    return {
      paddingTop: fixedHeader ? 50 : 0,
      paddingLeft: menuCollapsed ? 180 : 56,
    };


  };

  getFooterStyle = () => {
    let arr = [
      "^/garworth/",
      "^/home/<USER>",
      "^/customerManagement/",
      "^/customerManagement/customerInfo",
      '^/customerManagement/customerPool',
      '^/customerArchives',
      '^/customerManagement',
    ]
    let reg = new RegExp(arr.join("|"), "g");
    if (reg.test(this.props.location.pathname)) {
      return {
        height: 0,
        padding: 0,
        background: '#DCDCDC',
        overflow: 'hidden'
      };
    }
  }

  handleMenuCollapse = collapsed => {
    const { dispatch } = this.props;
    dispatch({
      type: 'global/changeLayoutCollapsed',
      payload: collapsed,
    });
  };
  // 去除项目主题相关的设置
  // renderSettingDrawer() {
  //   // Do not render SettingDrawer in production
  //   // unless it is deployed in preview.pro.ant.design as demo
  //   const { rendering } = this.state;
  //   if ((rendering || process.env.NODE_ENV === 'production') && APP_TYPE !== 'site') {
  //     return null;
  //   }
  //   return <SettingDrawer />;
  // }

  // 关闭更新弹窗方法
  UpdateModalClose = () => {
    this.setState({
      Isupdate: false
    })
  }

  // 修改当前打开的菜单
  ChangeCurrentGroups = (CurrentGroups = 'statistics') => {
    this.setState({
      CurrentGroups
    })
  }

  render() {
    const { taskObj, holdStatus, Isupdate } = this.state;
    const {
      navTheme,
      layout: PropsLayout,
      children,
      location: { pathname },
      loading
    } = this.props;
    const { isMobile, menuData, CurrentGroups } = this.state;
    const isTop = PropsLayout === 'topmenu';
    const routerConfig = this.matchParamsPath(pathname);

    const AtonceBtnLoading = !!loading.effects['newHomeIndex/getCustomerConfigByStaffJustAuth'];

    const layout = (
      <Layout>
        {isTop && !isMobile ? null : (
          <SiderMenu
            // logo={logo}
            Authorized={Authorized}
            theme={navTheme}
            onCollapse={this.handleMenuCollapse}
            ChangeCurrentGroups={this.ChangeCurrentGroups}
            menuData={menuData}
            isMobile={isMobile}
            CurrentGroups={CurrentGroups}
            {...this.props}
          />
        )}
        <Layout
          style={{
            minHeight: '100vh',
            minWidth: '1024px'
          }}
        >
          <Header
            menuData={menuData}
            handleMenuCollapse={this.handleMenuCollapse}
            // logo={logo}
            isMobile={isMobile}
            {...this.props}
          />
          {/* 任务通知 */}
          {this.state.taskObj && this.state.taskObj.isTask == 1 &&

            this.state.message ? (<Row type='flex' justify="space-between" className={styles.parents}>
              {this.state.taskObj.state == 1 ? (
                <div style={{ width: '90%' }}> {this.state.taskObj.taskType == 21 ? `${this.state.taskObj.patientName}电子档案建档成功` : this.state.taskObj.messageContent}</div>
              ) : (
                <div style={{ width: '90%' }}>
                  {this.state.taskObj.taskType == 24 ?
                    <div>
                      <strong>您有1条新<span style={{ color: '#FF6701' }}>投诉</span></strong>
                    </div>
                    :
                    <div>
                      请处理<i style={{ color: "#4b9efa", margin: "0px 5px 0px 5px" }}>
                        {this.state.taskObj.patientName}</i>的{taskTypeObj[this.state.taskObj.taskType]}{this.state.taskObj.taskType != 22 && '任务'}，
                      选择暂缓存处理操作后，您可以从{this.state.taskObj.taskType == 22 ? '预约-预约意向' : '当前任务列表'}中找到该任务
                    </div>
                  }

                </div>
              )}

              <div style={{ display: 'flex', alignItems: 'center' }}>
                {this.state.taskObj.state != 1 ? (
                  <Spin spinning={AtonceBtnLoading}>
                    <a onClick={() => this.JumpClick(taskObj.taskId, taskObj.taskType, 1, taskObj.patientId, taskObj.createdGmtAt, taskObj.registrationId, taskObj.settlementId, taskObj.patientName)}>马上处理</a>
                  </Spin>
                ) : null}
                <Icon className={styles.icon} type="close" onClick={this.DeleteTask} />
              </div>
            </Row>) : null

          }

          <Content style={this.getContentStyle()}>
            <Authorized
              authority={routerConfig && routerConfig.authority}
              noMatch={<Exception403 />}
            >
              {children}
            </Authorized>
          </Content>
          <UpdateInformation
            UpdateModalClose={this.UpdateModalClose}
            Isupdate={Isupdate}
          />
          {/*<Footer getFooterStyle={this.getFooterStyle()} />*/}
        </Layout>
        <ModalByGoJwsmed/>
        <ModalByConsumablesMall/>
      </Layout>
    );
    return (
      <LocaleProvider locale={zhCN}>
        <React.Fragment>
          <DocumentTitle title={this.getPageTitle(pathname)}>
            <ContainerQuery query={query}>
              {params => (
                <Context.Provider value={this.getContext()}>
                  <div className={classNames(params)}>{layout}</div>
                </Context.Provider>
              )}
            </ContainerQuery>
          </DocumentTitle>
          {/* 去除主题设置 */}
          {/* {this.renderSettingDrawer()} */}
        </React.Fragment>


      </LocaleProvider>
    );
  }
}

export default connect(({ global, setting, login, loading }) => ({
  collapsed: global.collapsed,
  menuCollapsed: global.menuCollapsed,
  layout: setting.layout,
  login,
  ...setting,
  loading
}))(BasicLayout);


