.all_content{
  //padding:10px;
  .header_infor{
    .infor_icon{
      width:16px;
    }
    span{
      margin-right:10px;
    }
  }
}
.label{
  margin-right:10px;
  font-weight: bold;
  font-size: 15px;
}
.table_inner{
  width:100%;
  margin:8px 0;
  img{
    width:100%;
    height:100%;
  }
}
.tabs{
  margin-top: 10px;
  :global{
    .ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab-active, .ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab-active{
      border-bottom-color: #f0f0f0;
    }
    .ant-tabs-top > .ant-tabs-nav::before, .ant-tabs-bottom > .ant-tabs-nav::before, .ant-tabs-top > div > .ant-tabs-nav::before, .ant-tabs-bottom > div > .ant-tabs-nav::before{
      border-bottom: 0 solid #fff;
    }
  }
}
.ant-checkbox-wrapper + .ant-checkbox-wrapper{
  margin-left: 15px;
}
.table_box{
  .lineBlueLeft,
  .lineBlueContent,
  .lineBlueH{
    background-color: #76BADC;
  }
  .lineBlueLeft{
    width: 50px;
    text-align: center;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    th{
      padding:0px 8px;
      //border: #FE0000 solid 1px;
      font-size: 8px;
      height: 48px
    }
  }
  .lineBlueContent{
    width: 20px;
    display: flex;
  }
}
.divBorder18{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 24px 0 24px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #ffffff transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 1px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word18{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.divBorder17{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 24px 0 24px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #ffffff transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 51px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word17{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.divBorder16{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 24px 0 24px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #ffffff transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 101px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word16{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.divBorder18:hover{
  border-color: #0078D7 transparent transparent transparent;
  .word18{
    color: #ffffff
  }
}
.divBorder17:hover{
  border-color: #0078D7 transparent transparent transparent;
  .word17{
    color: #ffffff
  }
}
.divBorder16:hover{
  border-color: #0078D7 transparent transparent transparent;
  .word16{
    color: #ffffff
  }
}


.divBorderBox18 {
  position: absolute;
  z-index: 1;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 23px 25px 0 25px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #D7D7D7 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 0; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
}
.divBorderBox17 {
  position: absolute;
  z-index: 1;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 23px 25px 0 25px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #D7D7D7 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 50px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
}
.divBorderBox16{
  position: absolute;
  z-index: 1;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 23px 25px 0 25px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #D7D7D7 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 100px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
}
.divBorder28{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 24px 0 24px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #ffffff transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 1px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word28{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.divBorder27{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 24px 0 24px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #ffffff transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 51px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word27{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.divBorder26{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 24px 0 24px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #ffffff transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 103px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word26{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.divBorder28:hover{
  border-color: #0078D7 transparent transparent transparent;
  .word18{
    color: #ffffff
  }
}
.divBorder27:hover{
  border-color: #0078D7 transparent transparent transparent;
  .word27{
    color: #ffffff
  }
}
.divBorder26:hover{
  border-color: #0078D7 transparent transparent transparent;
  .word26{
    color: #ffffff
  }
}

.divBorderBox28 {
  position: absolute;
  z-index: 1;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 23px 25px 0 25px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 0; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
}
.divBorderBox27 {
  position: absolute;
  z-index: 1;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 23px 25px 0 25px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 50px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
}
.divBorderBox26{
  position: absolute;
  z-index: 1;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 23px 25px 0 25px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 102px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
}

.abscissa{
  background-color: #76BADC;
  border: 1px solid #76BADC;
  position: absolute;
  top: 51%;
  display: flex;
  font-size: 11px;
  color: #333333;
  font-weight: 600;
  left: 59px;
  .ableTd{
    text-align: center;
    //width: 55px;
    width: 54.2px;
  }
}





.upperLeftTable{
  position: absolute;
  top: 159px;
  background-color: #ffffff;
  left: 59px;
  font-size: 12px;
  .trH20{
    height: 24px;
    .td{
      cursor: pointer;
      border: #939496 solid 1px;
      text-align:center;
    }
  }
  td{
    border: #939496 solid 1px;
    text-align:center;
  }
  td:hover{
    background-color: #0078D7;
    color: #000c17;
  }
  .tdW18{
    width: 18px;
    height: 43px;
  }
  .trH40{
    height: 47px;
    .td{
      cursor: pointer;
      border: #939496 solid 1px;
      text-align:center;
    }
  }
}
.upperRightTable{
  position: absolute;
  top: 159px;
  background-color: #ffffff;
  left: 510px;
  font-size: 12px;
  .trH20{
    height: 24px;
    .td{
      cursor: pointer;
      border: #939496 solid 1px;
      text-align:center;
    }
  }
  td{
    border: #939496 solid 1px;
    text-align:center;
  }
  td:hover{
    background-color: #0078D7;
    color: #000c17;
  }
  .tdW18{
    width: 18px;
    height: 43px;
  }
  .trH40{
    height: 47px;
    .td{
      cursor: pointer;
      border: #939496 solid 1px;
      text-align:center;
    }
  }
}
.triangle18{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 27px 0 27px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #ffffff transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 1px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word18{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.triangle18:hover{
  border-color: #0078D7 transparent transparent transparent;
  .word18{
    color: #ffffff
  }
}
.triangleBg18{
  position: absolute;
  z-index: 1;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 24px 28px 0 28px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 0; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
}
.triangle17{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 27px 0 27px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #ffffff transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 55px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word17{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.triangle16{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 27px 0 27px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #ffffff transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 108px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word16{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.triangle17:hover{
  border-color: #0078D7 transparent transparent transparent;
  .word17{
    color: #ffffff
  }
}
.triangle16:hover{
  border-color: #0078D7 transparent transparent transparent;
  .word16{
    color: #ffffff
  }
}


.triangleBg17 {
  position: absolute;
  z-index: 1;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 24px 28px 0 28px;  /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 54px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
}
.triangleBg16{
  position: absolute;
  z-index: 1;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 24px 28px 0 28px;  /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 107px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
}
.triangle26{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 27px 0 27px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #ffffff transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 110px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word26{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.triangleBg26{
  position: absolute;
  z-index: 1;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 24px 28px 0 28px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 109px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
}
.triangle26:hover{
  border-color: #0078D7 transparent transparent transparent;
  .word26{
    color: #ffffff
  }
}
.triangle27{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 27px 0 27px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #ffffff transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 56px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word27{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.triangleBg27{
  position: absolute;
  z-index: 1;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 24px 28px 0 28px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 55px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
}
.triangle27:hover{
  border-color: #0078D7 transparent transparent transparent;
  .word27{
    color: #ffffff
  }
}
.triangle28{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 27px 0 27px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #ffffff transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 1px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word28{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.triangleBg28{
  position: absolute;
  z-index: 1;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 24px 28px 0 28px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 0; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
}
.triangle28:hover{
  border-color: #0078D7 transparent transparent transparent;
  .word28{
    color: #ffffff
  }
}

.divBorder2{
  background-color: #ffffff;
  height: 46px;
  width: 50%;
  z-index: 3;
  float: left;
  padding-top: 28%;
  text-align: center;
}
.divBorder2:hover{
  background-color: #0078D7;
  color: #ffffff;
}
.divBorder3{
  display: table-column;
  vertical-align: middle;
  background-color: #ffffff;
  height: 46px;
  width: 50%;
  border-left: solid 1px #939496;
  float: left;
  padding-top: 28%;
  text-align: center;
}
.divBorder3:hover{
  background-color: #0078D7;
  color: #ffffff;
}
.triangleActive{
  border-color: #0078D7 transparent transparent transparent;
  .word18{
    color: #ffffff
  }
}
.triangleActive2{
  background-color: #0078D7;
  color: #ffffff;
}
.lowerRightTable{
  position: absolute;
  top: 52.8%;
  background-color: #ffffff;
  left: 510px;
  font-size: 12px;
  .trH20{
    height: 24px;
    .td{
      cursor: pointer;
      border: #939496 solid 1px;
      text-align:center;
    }
  }
  td{
    border: #939496 solid 1px;
    text-align:center;
  }
  td:hover{
    background-color: #0078D7;
    color: #000c17;
  }
  .tdW18{
    width: 18px;
    height: 43px;
  }
  .trH40{
    height: 47px;
    .td{
      cursor: pointer;
      border: #939496 solid 1px;
      text-align:center;
    }
  }
}

.lowerLeftTable{
  position: absolute;
  top: 52.8%;
  background-color: #ffffff;
  left: 59px;
  font-size: 12px;
  .trH20{
    height: 24px;
    .td{
      cursor: pointer;
      border: #939496 solid 1px;
      text-align:center;
    }
  }
  td{
    border: #939496 solid 1px;
    text-align:center;
  }
  td:hover{
    background-color: #0078D7;
    color: #000c17;
  }
  .tdW18{
    width: 18px;
    height: 43px;
  }
  .trH40{
    height: 47px;
    .td{
      cursor: pointer;
      border: #939496 solid 1px;
      text-align:center;
    }
  }
}
.tdActive{
  background-color: #0078D7;
  color: #000c17;
}
.lineBlueH{
  background-color: #6DC0E7;
  width: 20px;
  height: 76%;
  position: absolute;
  left: 492px;
  top: 14.1%;
}
.divBorder2Misses{
  background-color: #939496;
  height: 46px;
  width: 50%;
  z-index: 3;
  float: left;
  padding-top: 28%;
  text-align: center;
}
.divBorder3Misses{
  display: table-column;
  vertical-align: middle;
  background-color: #939496;
  height: 46px;
  width: 50%;
  border-left: solid 1px #939496;
  float: left;
  padding-top: 28%;
  text-align: center;
}
.missingTooth{
  background-color:#939496;
}
.missingTooth2{
  border-color:#939496;
}
.triangleMisses18{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 27px 0 27px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 1px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word18{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.triangleMisses17{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 27px 0 27px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 55px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word17{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.triangleMisses16{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 27px 0 27px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  left: 112px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word16{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.triangleMisses26{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 27px 0 27px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 110px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word26{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}
.triangleMisses27{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 27px 0 27px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 56px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word27{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}

.triangleMisses28{
  position: absolute;
  z-index: 2;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 14px;
  border-width: 22px 27px 0 27px; /*如果想改变倒三角形的大小,就改变这里的像素值*/
  border-style: solid;
  border-color: #939496 transparent transparent transparent; /*如果倒三角形的父元素背景不是白色,就把后面三个#fff改为对应的背景色*/
  top: 0;
  right: 1px; /*如果倒三角形前面的字多于两个,就设置这里的像素值大些*/
  .word28{
    text-align: center;
    margin: auto;
    position: absolute;
    display: inline-block;
    top: -15px;
    left: -3px;
    color: #000;
    font-size: 12px;
  }
}

.toothMap{
  width:950px;
  background-color: #96A7B7;
  :global{
    .ant-checkbox + span{
      padding-right: 3px;
    }
  }
}
.leftTitle{
  padding:0 7px;
  font-weight: 600;
  font-size: 13px;
}
.smallBox{
  margin-top: 10px;
}
.smallBox2{
  margin-top: 8px;
}
.lineGreyLeft,.lineGreyLeft2,.lineGreyLeft3{
  width: 27px;
  position: absolute;
  border-bottom: 1px solid #E5E9ED;
  transform: rotate(29deg);
}
.lineGreyLeft{
  top: 6px;
  left: -1px;
}
.lineGreyLeft2{
  top: 6px;
  left: 48px;
}
.lineGreyLeft3{
  top: 6px;
  left: 97px;
}
.lineGreyRight,.lineGreyRight2,.lineGreyRight3{
  width: 25px;
  position: absolute;
  border-bottom: 1px solid #E5E9ED;
  transform: rotate(152deg);
}
.lineGreyRight{
  top: 5px;
  left: 24px;
}
.lineGreyRight2{
  top: 5px;
  left: 73px;
}
.lineGreyRight3{
  top: 5px;
  left: 123px;
}
.lineGrey,.lineGrey2,.lineGrey3{
  width: 15px;
  line-height: 2px;
  position: absolute;
  border-bottom: 1px solid #E5E9ED;
  transform: rotate(90deg);
}
.lineGrey{
  top: 17px;
  left: 16px;
}
.lineGrey2{
  top: 17px;
  left: 66px;
}
.lineGrey3{
  top: 17px;
  left: 115px;
}


.tooth_position{
  font-size: 12px;
  td{
    width:20px;
    text-align: center;
  }
  .first{
    height:16px;
    border-right:2px solid #DEDEDE;
    border-bottom: 1px solid #DEDEDE;
  }
  .second{
    height:16px;
    border-bottom: 1px solid #DEDEDE;
  }
  .third{
    height:16px;
    border-top: 1px solid #DEDEDE;
    border-right:2px solid #DEDEDE;
  }
  .fourth{
    height:16px;
    border-top: 1px solid #DEDEDE;
  }
}
.tableSearch{
  background-color: #108DE9!important;
  color: #fff!important;
}

.tableSearchBtns{
  background-color: #ffffff!important;
  color: #333!important;
}
.tableAll{
  .table{
    width: 100%;
    height: 168px;
    td {
      height: 100px;
    }
  }
  .bottom_td {
    width: 22px;
    border: 1.6px solid #DEDEDE;
    border-right: 0;
    border-bottom: 0;
  }
  .td {
    width: 22px;
    border: 1.6px solid #DEDEDE;
    border-top: 0;
    border-left: 0;
  }
  .btn_tooth_Eng{
    text-align: right;
  }
  .btn_tooth_Eng_left{
    text-align: left;
  }
  :global{
    .ant-btn{
      height: 17px;
      line-height: 0.5715;
      font-size: 12px;
      margin: 0 6px;
    }
    .ant-btn-circle, .ant-btn-circle-outline {
      min-width: 17px;
    }
  }
}

.table_row{
  width: 120px;
  margin-left: 10px;
  display: inline-block;
  //position: absolute;
  //top: 0px;
  //left: 0px;
  td,th{
    width: 56px!important;
  }
}
.line_th_row,.line_th_col{
  width: 33px;
  text-align: center;
  border-bottom: 1.6px solid #DEDEDE;
}
.line_th_row{
  border-right: 1.6px solid #DEDEDE;
}
.line_row,.line_col{
  width: 33px;
  text-align: center;
}
.line_th_row,.line_th_col,
.line_row,.line_col{
  font-size: 12px;
  height: 16px;
  color: #333333;
  font-weight: 500;
}
.line_row{
  border-right: 1.6px solid #DEDEDE;
}

.row_deep_input{
  width: 60%;
  margin-top: 10px;
  margin-left: 50px;
  //position: absolute;
  //right: 0;
  //top: 0;
}
.select_tooth{
  :global{
    .ant-btn:focus {
      border-color: #D9D9D9;
    }
  }
}

//.pdl div{
//  margin-top: 13px;
//}
//.lpd div:first-child{
//  margin-top: -22px;
//}
//.lpd div:first-child {
//  margin-top: -36px;
//}

