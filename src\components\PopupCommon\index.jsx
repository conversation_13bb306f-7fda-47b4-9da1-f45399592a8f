import {Button, Row, Col, Image, Table, Input, Popconfirm, Form, Tree, Switch, Select} from 'antd';

import DentalFace from './components/dentalFaceDialog';//牙面选择
import EditTooth from './components/editTooth';//编辑牙位
import EditableTable from './components/editableTable';// 口腔检查
import EditDialg from './components/editDialg';//口腔检查
import React, {Component, useContext, useState, useEffect, useRef} from 'react';
import styles from './style.less';//样式
//图片
import Phone from "@/assets/phone.png";
import sex from "@/assets/nan.png";
import Nv from "@/assets/girl.png";
import moment from "moment";

// 牙齿组件
class Common extends Component {
  // 牙齿组件数据
  constructor(props) {
    super(props);
    this.state = {
      rightPatientInfos:{...props.rightPatientInfos},//获取右侧患者信息
      patientInfoDtos:{...props.patientInfoDtos},//获取患者数据
      patientData: {...props.patientData},//获取患者数据
      data:props.data,//获取传参数据
      pagination6: {...props.state},//获取传参数据
      newKey:new Date() + Math.random(),//key值
      visible: false,//弹框是否显示状态
      dentalFaceVisible: false,//弹框是否显示状态
      dentalFaceTitle: '牙面',//牙面
      teeth: {//牙面图
        above: [{//上排牙位图
          toothImgs: ['/assets/teeth/18.png'],
          index: '8',
          position: '18',
          milkToothPosition: '',
          isMilkTooth: false,
        },
          {
            toothImgs: ['/assets/teeth/17.png'],
            index: '7',
            position: '17',
            milkToothPosition: '',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/16.png'],
            index: '6',
            position: '16',
            milkToothPosition: '',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/15.png'],
            index: '5',
            position: '15',
            milkToothPosition: '1E',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/14.png'],
            index: '4',
            position: '14',
            milkToothPosition: '1D',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/13.png'],
            index: '3',
            position: '13',
            milkToothPosition: '1C',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/12.png'],
            index: '2',
            position: '12',
            milkToothPosition: '1B',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/11.png'],
            index: '1',
            position: '11',
            milkToothPosition: '1A',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/21.png'],
            index: '1',
            position: '21',
            milkToothPosition: '2A',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/22.png'],
            index: '2',
            position: '22',
            milkToothPosition: '2B',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/23.png'],
            index: '3',
            position: '23',
            milkToothPosition: '2C',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/24.png'],
            index: '4',
            position: '24',
            milkToothPosition: '2D',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/25.png'],
            index: '5',
            position: '25',
            milkToothPosition: '2E',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/26.png'],
            index: '6',
            position: '26',
            milkToothPosition: '',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/27.png'],
            index: '7',
            position: '27',
            milkToothPosition: '',
            isMilkTooth: false,
          },
          {
            toothImgs: ['/assets/teeth/28.png'],
            index: '8',
            position: '28',
            milkToothPosition: '',
            isMilkTooth: false,
          }],
        below: [{//下排牙位图
          toothImgs: ['/assets/teeth/48.png'],
          index: '8',
          position: '48',
          milkToothPosition: '',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/47.png'],
          index: '7',
          position: '47',
          milkToothPosition: '',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/46.png'],
          index: '6',
          position: '46',
          milkToothPosition: '',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/45.png'],
          index: '5',
          position: '45',
          milkToothPosition: '4E',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/44.png'],
          index: '4',
          position: '44',
          milkToothPosition: '4D',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/43.png'],
          index: '3',
          position: '43',
          milkToothPosition: '4C',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/42.png'],
          index: '2',
          position: '42',
          milkToothPosition: '4B',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/41.png'],
          index: '1',
          position: '41',
          milkToothPosition: '4A',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/31.png'],
          index: '1',
          position: '31',
          milkToothPosition: '3A',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/32.png'],
          index: '2',
          position: '32',
          milkToothPosition: '3B',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/33.png'],
          index: '3',
          position: '33',
          milkToothPosition: '3C',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/34.png'],
          index: '4',
          position: '34',
          milkToothPosition: '3D',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/35.png'],
          index: '5',
          position: '35',
          milkToothPosition: '3E',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/36.png'],
          index: '6',
          position: '36',
          milkToothPosition: '',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/37.png'],
          index: '7',
          position: '37',
          milkToothPosition: '',
          isMilkTooth: false,
        }, {
          toothImgs: ['/assets/teeth/38.png'],
          index: '8',
          position: '38',
          milkToothPosition: '',
          isMilkTooth: false,
        }
        ],
      },
      selectedPosition: [],//选择牙位数组
      checkResult: [],//口腔检查数组
      dateFilter: '',//操作中牙选择显示
      toothDesc:{},//选择牙面
      dentalTooth:{},
      tooth:"",
    };
    this.openDentalFaceDialog = this.openDentalFaceDialog.bind(this);
    this.dentalFaceDialogOk = this.dentalFaceDialogOk.bind(this);
    this.dentalFaceDialogCancel = this.dentalFaceDialogCancel.bind(this);
    this.openEditDialog = this.openEditDialog.bind(this);
    this.dialgOk = this.dialgOk.bind(this);
    this.dialgCancel = this.dialgCancel.bind(this);
    this.changeSelected = this.changeSelected.bind(this);
    this.refreshTeethData = this.refreshTeethData.bind(this);
  }
  //生命周期初始化
  componentDidMount() {

    this.props.onRef(this);

    if (this.state.data) {
      this.state.checkResult = this.state.data;
    }
    this.refreshTeethData();
  }
  //牙面选择
  openDentalFaceDialog(e) {
    this.setState(() => ({
      dentalFaceVisible: true,
      dentalFaceTitle: e.key,
      dentalTooth:e
    }))
  }
  //获取子组件
  onRef=(ref)=>{
    this.child = ref
  }
  //保存牙面选择数据
  dentalFaceDialogOk = (e,item) => {
    this.setState({newKey: new Date() + Math.random()})
    const {selectedPosition} = this.state;
    this.state.toothDesc = e;
    this.setState({
      dentalFaceVisible: false,
    });
    selectedPosition&&selectedPosition.map((element) => {
      this.child.state.tmpCheckResult.push({
        id: '',
        toothPosition: element,//牙位
        examName: this.state.dentalTooth.key,//检查名
        // examImg: this.state.dentalTooth.item.props.data.examImg,
        examCode: this.state.dentalTooth.item.props.data.examCode,//检查类型
        toothCode: item,//牙位状态
        toothDesc: e,//牙位描述
        consultationProject: '',//会诊数据项目
        nextStep: '',
        suggest: '',//建议
        checkDate: moment(new Date()).format('YYYY-MM-DD'),//时间
        doctorId: localStorage.getItem("id"),//用户id
        doctorName: localStorage.getItem("userName"),//用户名
      })
      this.setState({
        selectedPosition:[...this.state.selectedPosition]
      })
    })
    this.child.clearSelected();
  }
  //取消牙面选择弹框
  dentalFaceDialogCancel = () => {
    this.setState({
      dentalFaceVisible: false,
    });
  }
  //保存修改口腔检查数据
  dialgOk = (data) => {
    this.updateCheckResult(data);
    this.setState({
      visible: false,
    });
    // this.props.ok(data);
  }
  //取消口腔检查弹框
  dialgCancel = () => {
    this.setState({
      visible: false,
    });
  }
  // 发送请求更新数据
  updateCheckResult = (data) => {
    // 发送请求更新数据
    this.setState(() => ({
      checkResult: data,
    }), () => {
      this.refreshTeethData();
      //this.props.ok(data);
    });
  }
  //获取数据类型
  handleChange(e) {
    this.setState({
      dateFilter: e,
    });
  }
  //选择牙位
  changeSelected(selected) {
    this.setState(() => ({
      selectedPosition: selected,
    }));
  }
  //编辑牙位
  openEditDialog(position) {
    this.setState(() => ({
      selectedPosition: [position],
      visible: true,
    }));
  }

  //打开编辑更新弹框事件
  openEditUpdate() {
    this.setState(() => ({
      visible: true,
    }));
  }
  //刷新口腔检查牙位数据
  refreshTeethData() {
    const milkToothIdx = ['', 'A', 'B', 'C', 'D', 'E'];
    const tmp = this.state.teeth;
    tmp.above.forEach((item, index) => {
      tmp.above[index].toothImgs = [item.toothImgs[0]];
    });
    tmp.below.forEach((item, index) => {
      tmp.below[index].toothImgs = [item.toothImgs[0]];
    });
    this.state.checkResult.map((item) => {
      const index = item.toothPosition;
      if (index) {
        if (index.substring(0, 1) === '1' || index.substring(0, 1) === '2') {
          const toothDataIndex = tmp.above.findIndex(tooth => tooth.position === item.toothPosition || tooth.milkToothPosition === item.toothPosition);
          if (milkToothIdx.indexOf(item.toothPosition[1]) === -1) {
            if(item.toothDesc){
              let code = item.toothDesc.BSelected==true? `/assets/${item.examCode+"B"}/${item.toothPosition}.png`:"";
              let code2 = item.toothDesc.MSelected==true? `/assets/${item.examCode+"M"}/${item.toothPosition}.png`:"";
              let code3 = item.toothDesc.DSelected==true? `/assets/${item.examCode+"D"}/${item.toothPosition}.png` :"";
              let code4 = item.toothDesc.OSelected==true? `/assets/${item.examCode+"O"}/${item.toothPosition}.png`:"";
              let code5 = item.toothDesc.LSelected==true?`/assets/${item.examCode+"L"}/${item.toothPosition}.png` : "";
              tmp.above[toothDataIndex].toothImgs.push(code,code2,code3,code4,code5);
            }else {
              let code6 =  `/assets/${item.examCode}/${item.toothPosition}.png`;

              tmp.above[toothDataIndex].toothImgs.push(code6);
            }
          } else {
            if(item.toothDesc){
              let codes = item.toothDesc.BSelected==true? `/assets/${item.examCode+"B"}/${item.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(item.toothPosition[1])}.png`:"";
              let codes2 = item.toothDesc.MSelected==true? `/assets/${item.examCode+"M"}/${item.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(item.toothPosition[1])}.png`:"";
              let codes3 = item.toothDesc.DSelected==true? `/assets/${item.examCode+"D"}/${item.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(item.toothPosition[1])}.png` :"";
              let codes4 = item.toothDesc.OSelected==true? `/assets/${item.examCode+"O"}/${item.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(item.toothPosition[1])}.png`:"";
              let codes5 = item.toothDesc.LSelected==true?`/assets/${item.examCode+"L"}/${item.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(item.toothPosition[1])}.png` : "";
              tmp.above[toothDataIndex].toothImgs.push(codes,codes2,codes3,codes4,codes5);
            }else{
              let code6 = `/assets/${item.examCode}/${item.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(item.toothPosition[1])}.png`;

              tmp.above[toothDataIndex].toothImgs.push(code6);
            }
          }
        } else {
          const toothDataIndex = tmp.below.findIndex(tooth => tooth.position === item.toothPosition || tooth.milkToothPosition === item.toothPosition);
          if (milkToothIdx.indexOf(item.toothPosition[1]) === -1) {
            if(item.toothDesc){
              let code = item.toothDesc.BSelected==true? `/assets/${item.examCode+"B"}/${item.toothPosition}.png`:"";
              let code2 = item.toothDesc.MSelected==true? `/assets/${item.examCode+"M"}/${item.toothPosition}.png`:"";
              let code3 = item.toothDesc.DSelected==true? `/assets/${item.examCode+"D"}/${item.toothPosition}.png` :"";
              let code4 = item.toothDesc.OSelected==true? `/assets/${item.examCode+"O"}/${item.toothPosition}.png`:"";
              let code5 = item.toothDesc.LSelected==true?`/assets/${item.examCode+"L"}/${item.toothPosition}.png` : "";

              tmp.below[toothDataIndex].toothImgs.push(code,code2,code3,code4,code5);
            }else{
              let code6 =  `/assets/${item.examCode}/${item.toothPosition}.png`;
              tmp.below[toothDataIndex].toothImgs.push(code6);
            }
          } else {
            if(item.toothDesc){
              let codes = item.toothDesc.BSelected==true? `/assets/${item.examCode+"B"}/${item.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(item.toothPosition[1])}.png`:"";
              let codes2 = item.toothDesc.MSelected==true? `/assets/${item.examCode+"M"}/${item.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(item.toothPosition[1])}.png`:"";
              let codes3 = item.toothDesc.DSelected==true? `/assets/${item.examCode+"D"}/${item.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(item.toothPosition[1])}.png` :"";
              let codes4 = item.toothDesc.OSelected==true? `/assets/${item.examCode+"O"}/${item.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(item.toothPosition[1])}.png`:"";
              let codes5 = item.toothDesc.LSelected==true?`/assets/${item.examCode+"L"}/${item.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(item.toothPosition[1])}.png` : "";
              tmp.below[toothDataIndex].toothImgs.push(codes,codes2,codes3,codes4,codes5);
            }else{
              let code6 =  `/assets/${item.examCode}/${item.toothPosition[0] + ['', 'A', 'B', 'C', 'D', 'E'].indexOf(item.toothPosition[1])}.png`;
              tmp.below[toothDataIndex].toothImgs.push(code6);
            }
          }
        }
      }
      // })
    })
    this.setState(() => ({
      teeth: tmp,
    }), () => {
    });
  }
// 添加检查结果
  tmpCheckResult(data){
    this.setState(() => ({
      checkResult:data
    }), () => {
      this.refreshTeethData();
    });
  }
  render() {
    return (
      <Row className={styles.popup_check_tooth}>
        <Col span={24}>
          <div className={styles.all_content}>
            <div className={styles.header_infor}>
              <span
                style={{fontWeight: 'bold'}}>{this.state.rightPatientInfos.name?this.state.rightPatientInfos.name:""}</span>
              <span>{this.state.rightPatientInfos.sex == 1 ?
                <img className={styles.infor_icon} src={sex}/> : this.state.rightPatientInfos.sex == 2 ?
                  <img className={styles.infor_icon} src={Nv}/> : null}
                {this.state.patientInfoDtos.birthday}
                {this.state.patientInfoDtos.age==null||this.state.patientInfoDtos.age==""?"":
                  /^[\u4e00-\u9fa5]+$/.test(this.state.patientInfoDtos.age)||this.state.patientInfoDtos.age.indexOf("岁") !== -1?
                    <>({this.state.patientInfoDtos.age})</>:
                    <>({this.state.patientInfoDtos.age}岁)</>
                }
              </span>
              <span style={{fontWeight: 'bold'}}>{this.props.emrId}</span><span><img
              className={styles.infor_icon} src={Phone}/>{this.state.patientInfoDtos.oftenTel}{this.state.patientInfoDtos.oftenTelRelation?'('+this.state.patientInfoDtos.oftenTelRelation+')':""}</span>
            </div>

          </div>
          <div className={styles.teeth_img}>
            <div>
              <EditTooth key="0" click={this.openEditDialog} teeth={this.state.teeth}
                         checkResult={this.state.checkResult}/>
              <EditableTable
                checkResult={this.state.dateFilter ? this.state.checkResult.filter(item => item.checkDate === this.state.dateFilter) : this.state.checkResult}
                update={this.updateCheckResult} openEditDialog={this.openEditDialog}/>
            </div>
            <EditDialg
              changeSelected={this.changeSelected}
              cancel={this.dialgCancel}
              ok={this.dialgOk}
              teeth={this.state.teeth}
              checkResult={this.state.checkResult}
              selectedPosition={this.state.selectedPosition}
              tmpCheckResult={this.tmpCheckResult.bind(this)}
              visible={this.state.visible}
              openDentalFaceDialog={this.openDentalFaceDialog}
              onRef={this.onRef}

            />
            <DentalFace
              cancel={this.dentalFaceDialogCancel}
              ok={this.dentalFaceDialogOk.bind(this)}
              dentalFaceVisible={this.state.dentalFaceVisible}
              key={this.state.newKey}
              title={this.state.dentalFaceTitle}
            />
          </div>

        </Col>

      </Row>
    );
  }
}

export default Common;
