import {
  searchPatient,      //选择患者，搜索
  getPatientInfo,     //查询患者信息
  getCityMenu,        //获取城市三级联动菜单
  getOrganization,    //根据省市区查询机构列表
  getDoctor,          //查询医生
  getMajor,           //查询专业
  createNewPatient,   //添加新患者
  getSourceOptions,   //查询初诊来源选项
  getExistingAppointment,   //患者的现有预约
  getCalendar,              //预约查询日历情况
  searchWelfareByNumber,    //查询福利详情 -> 通过卡号查
  getAllCardType,           //勾选无福利号
  searchWelfareByCardType,   //福利，选择客户类型的事件
  getComplaint,              //查询患者主诉
  createNewAppt,   //新建预约
  changeAppt,      //更改预约
  checkTime,       //新增，修改预约时，校验时间
  apptConfirm,     //预约确认

  //预约操作区
  getMechanism,                  //根据省、市、区，查询开放400的机构列表
  setDoctorSpecialty,            //修改机构配置
  getAllSpecialty,               //获取400客服创建的所有的医生专业
  checkDoctorMajorName,          //医生专业名称重复检查
  saveDoctorMajorName,           //保存医生专业
  deleteDoctorMajor,             //删除医生专业
  getResourceList,               //根据专业ID获取资源的配置情况
  saveDoctorMajorRelation,       //保存医生资源配置
  getDataListtest,               //客户档案列表
  getDrawerInfo,                 //客户档案获取筛选信息
  getAppointmentDefault1New,     // ①预约列表展示    20190723 注释 老地址，  新api 为 getAppointmentDefault1New
  remarkReplace,                 //详情备注 修改
  dropAppointmentCS,             //取消预约
  modifyAppointmentIcons,        //手工标签
  getAllNode,                    //投诉获取管理节点
  getAllNodeStaff,               //获取所有投诉管理节点下对应的人员
  configurationComplain,         //投诉管理节点下人员修改设置
} from '@/services/customerServiceNew';

import {
  getResourceListAll,        // 获取全部预约医生
  getMonthAppintmentDefaul,  // 预约月模式
  getAppointmentDefault1,    // ①预约列表展示    20190723 注释 老地址，  新api 为 getAppointmentDefault1New
  dropAppointment,           // 取消预约
  appointmentOfPatient,      // 根据选择资源及日期查询可约列表
  sameMonthMedicalResources, // 获取当月上班资源信息 用于获取会诊医生
  appointmentAsTimeCalendar, // 日历预约量状态查询
  appointmentAsNumberCalendar,
  searchSomeone,             // 预约列表搜索功能接口
  exportExcel,               // 导出预约
} from '@/services/subscribeFullscreen';
import { getscreen, getnurse } from '@/services/api';
import { defaultDate, setEventDtoListItem, set400EventDtoListItem } from '@/utils/CalendarUtils';

/**
 * 预约组件 预约制度Model
 * 包含 全屏Model 预约制度组件
 */
const typeClass = {
  '1':{ textColor:'#444444', color:'#f9c8c9',},   // 老患者预约
  '2':{ textColor:'#444444', color:'#fef3f3',},   // 新患者预约
  '3':{ textColor:'#444444', color:'#fef3f3',},   // 会诊
  '4':{ textColor:'#444444', color:'#eef1f6',},   // 个人占用时间
  '5':{ textColor:'#444444', color:'#eef1f6',},   // 个人占用时间
};

export default {
  namespace: 'customerServiceNew',
  state: {
    appointmentInfoEventDtoList: [],            // ①预约列表展示 ,预约事件集合
    calendarTableDtoList: [],                    // ①预约列表展示  , table列表椅位集合
    appointmentPageInfo: {},                     //  预约列表分页信息
    appointmentListDtoByChange: {},             // [改约] 改约列表展示
    appointmentListDtoByChangePageInfo: {},    // [改约] 改约列表分页信息
    changeListDtoByChange: {},                  // [改约] 现有预约列表展示
    consultationListDtoByChange: {},            // [改约] 会诊预约列表展示
    allEventDtoList: [],                         // [改约] 所有的预约事件
    MedicalResources: [],                        // 获取会诊医生
    appointmentAsTimeCalendar: [],              // 日历预约量状态查询
    doctorTimeCalendarPageInfo: {},             // [医生使用] 预约组件分页信息
    TimeCalendarQuery: {},                       // 获取地址栏参数
    onTimeBlankClickByFull: null,               // 当前在全屏选中的选中区域
    resourceListAll: [],                         // [月模式] 全部医生
    AgendaHeadType: '2',                         //
    patientBasicInfo: {},                        // 当前患者的信息
    // 400预约日历设置参数
    CSCselectDate: null,                     //日期
    CSCorganizationName: null,              //机构名称
    CSCorganizationInfoId: null,            //机构id
    CSCselectedDoctorIdList: null,          //选择的医生的id
    CSCappointmentWork: null,               // 机构上班时间
    filtrate: null,                          // 客户档案筛选条件
  },
  effects: {
    //修改手工标签
    *modifyAppointmentIcons({ payload }, { call }) {
      const res = yield call(modifyAppointmentIcons, payload);
      return res;
    },
    //取消预约
    *dropAppointments({ payload },{ call }){
      const res = yield call(dropAppointmentCS, payload);
      return res;
    },
    //预约详情，卡片，修改备注
    * remarkReplace({ payload }, { call }) {
      const res = yield call(remarkReplace, payload);
      return res;
    },
    //选择患者，搜索
    *searchPatient({ payload }, { call }) {
      const res = yield call(searchPatient, payload);
      return res;
    },
    //查询患者信息
    *getPatientInfo({ payload }, { call }) {
      const res = yield call(getPatientInfo, payload);
      return res;
    },
    // 获取城市三级联动菜单
    *getCityMenu({ payload }, { call }) {
      const res = yield call(getCityMenu, payload);
      return res;
    },
    //根据省市区查询机构列表
    *getOrganization({ payload }, { call }) {
      const res = yield call(getOrganization, payload);
      return res;
    },
    //查询医生
    *getDoctor({ payload }, { call }) {
      const res = yield call(getDoctor, payload);
      return res;
    },
    //查询专业
    *getMajor({ payload }, { call }) {
      const res = yield call(getMajor, payload);
      return res;
    },
    //添加新患者
    *createNewPatient({ payload }, { call }) {
      const res = yield call(createNewPatient, payload);
      return res;
    },
    //查询初诊来源选项
    *getSourceOptions({ payload }, { call }) {
      const res = yield call(getSourceOptions, payload);
      return res;
    },
    //患者的现有预约
    *getExistingAppointment({ payload }, { call }) {
      const res = yield call(getExistingAppointment, payload);
      return res;
    },
    //预约查询日历情况
    *getCalendar({ payload }, { call }) {
      const res = yield call(getCalendar, payload);
      return res;
    },
    //查询福利详情 -> 通过卡号查
    *searchWelfareByNumber({ payload }, { call }) {
      const res = yield call(searchWelfareByNumber, payload);
      return res;
    },
    //勾选无福利号
    *getAllCardType({ payload }, { call }) {
      const res = yield call(getAllCardType, payload);
      return res;
    },
    //选择客户类型的事件
    *searchWelfareByCardType({ payload }, { call }) {
      const res = yield call(searchWelfareByCardType, payload);
      return res;
    },
    //查询患者主诉
    *getComplaint({ payload }, { call }) {
      const res = yield call(getComplaint, payload);
      return res;
    },
    //新建预约
    *createNewAppt({ payload }, { call }) {
      const res = yield call(createNewAppt, payload);
      return res;
    },
    //更改预约
    *changeAppt({ payload }, { call }) {
      const res = yield call(changeAppt, payload);
      return res;
    },
    //新增，修改预约时，校验时间
    *checkTime({ payload }, { call }) {
      const res = yield call(checkTime, payload);
      return res;
    },
    //预约确认
    *apptConfirm({ payload }, { call }) {
      const res = yield call(apptConfirm, payload);
      return res;
    },
    //[月模式]查询所有医生
    *getResourceListAll({ payload }, { call, put }){
      const response = yield call(getResourceListAll, payload);
      if(response && response.code == 200 && Array.isArray(response.content)){
        let content = response.content;
        yield put({
          type: 'save',
          payload: {
            resourceListAll: content,
          },
        });
      }else {
        yield put({
          type: 'save',
          payload: {
            resourceListAll: [],
          },
        });
      }
      return response;
    },
    //获取①预约列表展示
    *getAppointmentDefault({ payload }, { call, put }){
      const response =  yield call(getAppointmentDefault1New, payload);
      if(response && response.code == 200 && response.content){
        let getAppointmentDefaultObj = {};
        let content = response.content;
        const {
          appointmentInfoEventDtoList,
          calendarTableDtoList,
          current,  // 当前页
          pageSize, // 每页几条
          total,    // 总条数
        } = content;

        let eventlist = Array.isArray(appointmentInfoEventDtoList) ? appointmentInfoEventDtoList : [];
        eventlist.map((val)=>{
          return {
            ...set400EventDtoListItem(val),
          }
        });

        getAppointmentDefaultObj.appointmentInfoEventDtoList = eventlist;
        getAppointmentDefaultObj.calendarTableDtoList = calendarTableDtoList ? calendarTableDtoList : [];

        //分页处理
        let pageCount = 0; //总页数
        if(total && pageSize) {
          pageCount = Math.ceil(total / pageSize);
        }
        getAppointmentDefaultObj.appointmentPageInfo = {
          current,
          pageSize,
          total,
          pageCount,
        };

        yield put({
          type: 'saveAppointmentInfoEventDtoList',
          payload: {
            ...getAppointmentDefaultObj,
            AgendaHeadType: '1',
          },
        });
      }else {
        yield put({
          type: 'saveAppointmentInfoEventDtoList',
          payload: {
            appointmentInfoEventDtoList: [],
            calendarTableDtoList: [],
            appointmentPageInfo: {},
            AgendaHeadType: '1',
          },
        });
      }
      return response;
    },
    //预约切换至 一人多天 全屏
    *appointmentOfPatientForFull({ payload }, { call, put }){
      const response = yield call(appointmentOfPatient, payload);
      if(response && response.code == 200 && response.content){
        let getAppointmentDefaultObj = {};
        let content = response.content;
        const {
          appointmentInfoEventDtoList,
          calendarTableDtoList,
          current,  // 当前页
          pageSize, // 每页几条
          total,    // 总条数
        } = content;

        let eventlist = Array.isArray(appointmentInfoEventDtoList) ? appointmentInfoEventDtoList : [];
        eventlist.map((val)=>{
          return {
            ...set400EventDtoListItem(val),
          }
        });

        getAppointmentDefaultObj.appointmentInfoEventDtoList = eventlist;
        getAppointmentDefaultObj.calendarTableDtoList = calendarTableDtoList ? calendarTableDtoList : [];

        //分页处理
        let pageCount = 0; //总页数
        if(total && pageSize) {
          pageCount = Math.ceil(total / pageSize);
        }
        getAppointmentDefaultObj.appointmentPageInfo = {
          current,
          pageSize,
          total,
          pageCount,
        };

        yield put({
          type: 'saveAppointmentInfoEventDtoList',
          payload: {
            ...getAppointmentDefaultObj,
            AgendaHeadType: '2',
          },
        });
      }else {
        yield put({
          type: 'saveAppointmentInfoEventDtoList',
          payload: {
            appointmentInfoEventDtoList: [],
            calendarTableDtoList: [],
            appointmentPageInfo: {},
            AgendaHeadType: '2',
          },
        });
      }
      return response;
    },
    //②取消预约
    *dropAppointment({ payload }, { call }){
      const response = yield call(dropAppointment, payload);
      if(response.code == 200){
        return response.content;
      }
    },
    //根据选择资源及日期查询可约列表
    *appointmentOfPatient({ payload }, { call, put }){
      const response = yield call(appointmentOfPatient, payload);
      let content = response && response.content;
      if(response && response.code == 200 && content){
        let appointmentListDto = content;

        let {
          status,
          current,
          pageSize,
          total,
        } = appointmentListDto || {};
        current = current || 1;
        pageSize = pageSize || 4;
        status = status || 0;
        total = total || 0;
        let pageCount = 0;
        if(total && pageSize) {
          pageCount = Math.ceil(total / pageSize);
        }
        let doctorTimeCalendarPageInfo = {
          current,
          pageSize,
          status,
          total,
          pageCount,
        };
        yield put({
          type: 'saveAppointmentOfPatient1',
          payload: {
            appointmentListDtoByChange: {
              ...appointmentListDto,
              appointmentPageInfo: doctorTimeCalendarPageInfo,
            } || {},   // 改约列表
            appointmentOfPatient: appointmentOfPatient || {},
          }
        })
      }else {
        yield put({
          type: 'saveAppointmentOfPatient1',
          payload: {
            appointmentListDtoByChange: {},
          }
        })
      }
      return response;
    },
    //获取会诊医生 信息接口
    *sameMonthMedicalResources({ payload }, { call, put }){
      const response = yield call(sameMonthMedicalResources, payload);
      let list = Array.isArray(response && response.content) ? response.content : [];

      let MedicalResources = [
        { resourcesName: '无', resourcesId: null },
        ...list,
      ];
      if(response && response.code == 200) {
        yield put({
          type: 'save',
          payload: {
            'MedicalResources': MedicalResources,
          },
        })
      }
    },
    //日历预约量状态查询
    *appointmentAsTimeCalendar({ payload }, { call, put }){
      const response = yield call(appointmentAsTimeCalendar, payload);
      if(response && response.code == 200) {
        yield put({
          type: 'save',
          payload: {
            'appointmentAsTimeCalendar': Array.isArray(response.content) ? response.content : [],
          },
        })
      }
    },
    //挂号日历预约量状态查询
    *appointmentAsNumberCalendar({ payload }, { call, put }){
      const response = yield call(appointmentAsNumberCalendar, payload);
      if(response && response.code == 200) {
        yield put({
          type: 'save',
          payload: {
            'appointmentAsTimeCalendar': Array.isArray(response.content) ? response.content : [],
          }
        })
      }
    },
    //预约列表关键字检索
    *searchSomeone({ payload }, { call }){
      const response = yield call(searchSomeone, payload);
      if(response && response.code == 200 && response.content){
        return response.content;
      }else {
        return {};
      }
    },
    //导出预约
    *exportExcel({ payload }, { call }){
      const response = yield call(exportExcel, payload);
      return response;
    },

    /*设置模块*/
    //设置  根据省、市、区，查询开放400的机构列表
    *getMechanism({payload}, { call }) {
      const response = yield call(getMechanism, payload);
      return response;
    },
    //修改机构配置
    *setDoctorSpecialty({ payload }, { call }) {
      const response = yield call(setDoctorSpecialty, payload);
      return response;
    },
    //获取400客服创建的所有的医生专业
    *getAllSpecialty({ payload }, { call }) {
      const response = yield call(getAllSpecialty, payload);
      return response;
    },
    //医生专业名称重复检查
    *checkDoctorMajorName({ payload }, { call }) {
      const response = yield call(checkDoctorMajorName, payload);
      return response;
    },
    //保存医生专业
    *saveDoctorMajorName({ payload }, { call }) {
      const response = yield call(saveDoctorMajorName, payload);
      return response;
    },
    //删除医生专业
    *deleteDoctorMajor({ payload }, { call }) {
      const response = yield call(deleteDoctorMajor, payload);
      return response;
    },
    //根据专业ID获取资源的配置情况
    *getResourceList({ payload }, { call }) {
      const response = yield call(getResourceList, payload);
      return response;
    },
    //保存医生资源配置
    *saveDoctorMajorRelation({ payload }, { call }) {
      const response = yield call(saveDoctorMajorRelation, payload);
      return response;
    },
    //投诉获取管理节点
    *getAllNode({ payload }, { call }) {
      const response = yield call(getAllNode, payload);
      return response;
    },
    //获取所有投诉管理节点下对应的人员
    *getAllNodeStaff({ payload }, { call }) {
      const response = yield call(getAllNodeStaff, payload);
      return response;
    },
    //投诉管理节点下人员修改设置
    *configurationComplain({ payload }, { call }) {
      const response = yield call(configurationComplain, payload);
      return response;
    },
    /*患者管理模块*/
    //获取列表数据
    *getDataListtest({ payload }, { call, put }) {
      const response = yield call(getDataListtest, payload);
      yield put({
        type: 'saves',
        payload: payload,
      });
      return response;
    },
    //客户档案获取筛选信息
    *getDrawerInfo({ payload }, { call }) {
      const response = yield call(getDrawerInfo, payload);
      return response;
    },
  },

  reducers:{
    //保存全屏选中的时间区域参数
    saveOnTimeBlankClickByFull(state, { payload }){
      return {
        ...state,
        onTimeBlankClickByFull: payload,
      }
    },
    //预约保存，列表头 和 事件集合
    saveAppointmentInfoEventDtoList(state, { payload }){
      let appointmentInfoEventDtoList = payload.appointmentInfoEventDtoList.map((res, idx) => {
        return set400EventDtoListItem(res);
      });
      return {
        ...state,
        ...payload,
        appointmentInfoEventDtoList,
      }
    },
    //有患者预约半屏列表
    saveAppointmentOfPatient1(state, { payload }){
      const {
        appointmentListDtoByChange,    // 改约列表
      } = payload;
      //改约列表，普通预约 和 事件
      let appointmentInfoEventDtoListByAppointment = Array.isArray(appointmentListDtoByChange.appointmentInfoEventDtoList) ? appointmentListDtoByChange.appointmentInfoEventDtoList : [];
      let calendarTableDtoListByAppointment =  Array.isArray(appointmentListDtoByChange.calendarTableDtoList) ? appointmentListDtoByChange.calendarTableDtoList : [];
      //处理，改约分页
      let {
        current,
        pageSize,
        total,
        status,
      } = appointmentListDtoByChange || {};
      current = current || 0;
      pageSize = pageSize || 4;
      status = status || 0;
      total = total || 0;
      //总页数
      let pageCount = 0;

      if(total && pageSize) {
        pageCount = Math.ceil(total / pageSize);
      }
      let appointmentListDtoByChangePageInfo = {
        current,
        pageSize,
        status,
        total,
        pageCount,
      };
      //处理改约列表，事件内容
      appointmentInfoEventDtoListByAppointment = appointmentInfoEventDtoListByAppointment.map((res, idx) => {
        let getEvent = set400EventDtoListItem(res);
        return {
          ...getEvent,
        }
      });
      calendarTableDtoListByAppointment.map((res, idx) => {
        res.type = 2;
      });
      //改约封装判空数据
      let appointmentListDto = {
        ...appointmentListDtoByChange,
        appointmentListDtoByChangePageInfo: appointmentListDtoByChangePageInfo,
        appointmentInfoEventDtoList: appointmentInfoEventDtoListByAppointment,
        calendarTableDtoList: calendarTableDtoListByAppointment,
      };
      return {
        ...state,
        appointmentListDtoByChange: appointmentListDto,    //半屏有患者显示
        appointmentPageInfo: appointmentListDtoByChangePageInfo,
      }
    },
    save(state, { payload }){
      return {
        ...state,
        ...payload,
      };
    },
    saves(state, { payload }){
      return {
        ...state,
        filtrate: payload,
      };
    },
  },
  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, query, search }) => {
        dispatch({
          type: 'save',
          payload: {
            TimeCalendarQuery: query,
          }});
      })
    }
  }
}


