import React, { PureComponent } from 'react';
import { Icon, Menu, Dropdown, Row, Spin, message } from 'antd';
import Link from 'umi/link';
import Debounce from 'lodash-decorators/debounce';
import styles from './index.less';
import RightContent from './RightContent';
import { getOrganizationInfo } from "@/utils/utils";
import menuico from '@/assets/menuico.png';
import changemenu from '@/assets/changemenu.png';
import { reloadAuthorized } from '@/utils/Authorized';

export default class GlobalHeader extends PureComponent {
  state = {
    obj: {},
    checkCity: 0,
    CityOrNode: 1,  // 1代表城市   2代表节点
  }
  componentWillUnmount() {
    this.triggerResizeEvent.cancel();
  }
  /* eslint-disable*/
  @Debounce(600)
  triggerResizeEvent() {
    // eslint-disable-line
    const event = document.createEvent('HTMLEvents');
    event.initEvent('resize', true, false);
    window.dispatchEvent(event);
  }
  toggle = () => {
    const { collapsed, onCollapse } = this.props;
    onCollapse(!collapsed);
    this.triggerResizeEvent();
    reloadAuthorized();
  };
  // 获取机构列表
  DropdownClick = () => {
    const { dispatch } = this.props;
    const ID = localStorage.getItem('id')?localStorage.getItem('id') : false;
    // 添加对于本地存储是否存在的判断
    if(ID){
      dispatch({
        type: 'user/getOrganizationList',
        payload: {
          "userId": localStorage.getItem('id')
        }
      })
    }else {
      message.error(`没有患者信息===${localStorage.getItem('id')}`);
    }
  }
  // 切换机构
  BrandOrganization = (e, i) => {
    // e.stopPropagation();
    // e.domEvent.stopPropagation();
    localStorage.removeItem("firstLogin");
    localStorage.removeItem("submitList"); // 切换诊所重新获取配置的常用菜单
    const { dispatch } = this.props;
    this.setState({
      obj: i
    }, () => {
      localStorage.setItem('organizationInfoId', i.id);
      dispatch({
        type: 'user/getSwitchLogin',
        payload: {
          "userId": localStorage.getItem('id')
        }
      })
      localStorage.removeItem('minimizeStorageKey');   // 切换机构清除本地存储的预约最小化数据
      localStorage.removeItem('minimizeVisitStorageKey');   // 切换机构清除本地存储的回访任务最小化数据
      this.setState({
        checkCity: 0,
        CityOrNode: 1,  // 1代表城市   2代表节点
      })
    })

  }

  // 切换城市
  changeCity = (e, key) => {
    console.log(e)
    e.stopPropagation();
    this.setState({
      checkCity: key
    })
  }
  // 切换机构展示方式
  onBrandCity = (e) => {
    e.stopPropagation();
    const { CityOrNode } = this.state;
    this.setState({
      CityOrNode: CityOrNode == 1 ? 2 : 1,
      checkCity: 0,
    })
  }
  onVisibleChange = (values) => {
    if (!values) {
      this.setState({
        CityOrNode: 1,
        checkCity: 0,
      })
    }
  }

  render() {
    const { obj, checkCity, CityOrNode } = this.state;
    const { collapsed, isMobile, logo, loading } = this.props;
    const arr = this.props.organizationList && this.props.organizationList.content ? this.props.organizationList.content : []
    const menu = (
      // <Menu>
      //   {arr && arr.map(i => (
      //     <Menu.Item style={{ margin: "10px 0px" }} key={i.id} onClick={(e) => this.BrandOrganization(e, i)}>{i.organizationName}</Menu.Item>
      //   ))}
      // </Menu>
      // organizationDtoList
      <div>
        <Spin spinning={loading && loading.effects['user/getOrganizationList']}>
          <div style={{ display: 'flex' }}>
            <div className={styles.cityList}>
              <h4 onClick={(e) => this.onBrandCity(e)}>按{CityOrNode == 1 ? '城市' : '分院'}筛选</h4>
              <div style={{ maxHeight: 600, overflowY: 'auto' }}>
                {CityOrNode == 1 ?
                  arr && arr.cityInfoDtoList && arr.cityInfoDtoList[0] ? arr && arr.cityInfoDtoList && arr.cityInfoDtoList.map((item, key) => (
                    <div key={key} style={checkCity == key ? { background: '#FFFFFF', color: "#176CE0" } : null} onClick={(e) => this.changeCity(e, key)}>{item.name} </div>
                  )) : <div>您未配置机构诊所，暂无数据</div>
                  :
                  arr && arr.nodeInfoDtoList && arr.nodeInfoDtoList[0] ? arr && arr.nodeInfoDtoList && arr.nodeInfoDtoList.map((item, key) => (
                    <div key={key} style={checkCity == key ? { background: '#FFFFFF', color: "#176CE0" } : null} onClick={(e) => this.changeCity(e, key)}>{item.name} </div>
                  )) : <div>您未设置管理节点，没有分院</div>
                }
              </div>
            </div>
            <div className={styles.organNameList}>
              <h4>选择诊所</h4>
              <div style={{ maxHeight: 600, overflowY: 'auto' }}>
                {
                  CityOrNode == 1 ?
                    arr && arr.cityInfoDtoList && arr.cityInfoDtoList[checkCity] &&
                    arr.cityInfoDtoList[checkCity].organizationDtoList && arr.cityInfoDtoList[checkCity].organizationDtoList.map((item, key) => (
                      <div key={key} style={item.selectionOrganization == 1 ? { background: '#4BA2FF', color: "#176CE0" } : null} onClick={(e) => this.BrandOrganization(e, item)}>{item.organizationName}</div>
                    )) :
                    arr && arr.nodeInfoDtoList && arr.nodeInfoDtoList[checkCity] &&
                    arr.nodeInfoDtoList[checkCity].organizationDtoList && arr.nodeInfoDtoList[checkCity].organizationDtoList.map((item, key) => (
                      <div key={key} style={item.selectionOrganization == 1 ? { background: '#4BA2FF', color: "#176CE0" } : null} onClick={(e) => this.BrandOrganization(e, item)}>{item.organizationName}</div>
                    ))
                }
              </div>
            </div>
          </div>
        </Spin>
      </div>
    );
    const organizationName = getOrganizationInfo().organizationName;
    const organizationSize = localStorage.getItem("organizationSize");
    return (
      <div className={styles.header}>
        <i className={styles.trigger} onClick={this.toggle}>
          <img src={menuico} />
        </i>
        <span onClick={organizationSize == 1 ? null : this.DropdownClick} id="Scroll" className={styles.scrollDrop}>
          {/* overlayStyle={{ maxHeight: '400px', overflowY: 'scroll' }} */}
          <Dropdown
            overlay={menu}
            getPopupContainer={() => document.getElementById('Scroll')}
            overlayStyle={{
              maxHeight: '674px', width: 390, overflow: 'hidden',
              minHeight: 216, boxShadow: '0px 0px 5px 2px rgba(15,27,41,0.3)',
            }}
            disabled={organizationSize == 1 ? true : false}
            trigger={['click']}
            onVisibleChange={this.onVisibleChange}
          >
            <span className={styles.switchclinic} style={{fontWeight: 600}}>
              {obj && obj.id ? obj.organizationName : organizationName}
              {organizationSize == 1 ? null : (
                <i style={{ marginLeft: 5, fontSize: 20 }}>
                  <img src={changemenu} style={{width: 16,height: 16}} />
                </i>)}
            </span>
          </Dropdown>
        </span>
        <RightContent {...this.props} />

      </div>
    );
  }
}
