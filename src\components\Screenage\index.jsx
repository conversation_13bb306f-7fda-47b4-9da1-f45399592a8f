import {
  Row,
  Col,
  Checkbox,
  Form,
  Input,
  Button,
  Select,
  DatePicker,
  Upload,
  message,
  Image,
  Spin,
  Modal,
  Popconfirm
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import React, { useState, useRef, Component } from 'react';
import { GridContent } from '@ant-design/pro-layout';
import styles from './style.less';//样式

import commonStyle from '@/pages/common.less'; //引入公共样式
import CdnImgs from "@/components/CdnImgs"//图片转换
//图片
import img from '@/assets/img1.png';
import noData from '@/assets/<EMAIL>';
import Delete from "@/assets/<EMAIL>";
import Preview from "@/assets/<EMAIL>";
import moment from 'moment'
import {connect} from "dva";
//引入图片
const { Option } = Select;
const { TextArea } = Input;
// 关联影像
class Screenage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      params:{
        tenantId: localStorage.getItem('tenantId'),//平台标识
        emrId: this.props.emrId,//大病历号
        pageSize: 10,//分页大小
        pageNum: 1//分页页码
      },
      Typeparams:{
        tenantId:localStorage.getItem('tenantId'),//租户标识
        className:"",//类型（类型名称）
        pageNum:1,//分页页码
        pageSize:""//分页大小
      },
      ImageTypeList:[],//影像类型列表
      uploadParams:{
        uploadType:[],//上传选择的类型
        fileDesc:"",
        uploadDate:"",//时间选择
      },
      fileList:[],//上传文件
      ImgListData:[],//图片列表数据
      ImageLibrary: [],//影像库的选择
      emrLoading: false, //分页加载数据
      Imgvisible:false,//弹框状态
      Imgvisible1:false//弹框状态
    };
  }
  //生命周期初始化
  componentDidMount() {
    this.props.onRef(this)
    if(this.props.chooseImgs){
      this.setState({
        ImageLibrary: this.props.chooseImgs,
      });
      // console.log("artartart",JSON.stringify(art))
    }
    this.getImgList()
    this.getImageTypeList()
  }
  //影像资料库
  getImgList=()=>{
    const { dispatch } = this.props;
    const {params}=this.state;
    this.setState({
      emrLoading: true,
    });
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/findCheckImgsService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            let arr;
            if (params.pageNum == 1) {
              arr = res.rows;
            } else {
              arr = [...this.state.ImgListData, ...res.rows];
            }
            this.setState({
              ImgListData:arr,
              total: res.total,
            })
          }
          this.setState({
            emrLoading: false,
          });
        },
      });
    }
  }
  // 加载更多
  emrNext = () => {
    this.setState({
      emrLoading: true,
    });
    const { params, total } = this.state;
    if (params.pageNum <= total / params.pageSize) {
      params.pageNum++;
      this.getImgList();
    }
  };
  //影像类型列表
  getImageTypeList=()=>{
    const {Typeparams}=this.state;
    const {dispatch} = this.props
    if (dispatch) {
      dispatch({
        type: 'ImageManageModel/ImageManageService',
        payload: Typeparams,
        callback: (res) => {
          if (res.code == 200) {
            // console.log("影像类型列表数据===",JSON.stringify(res))
            this.setState({
              totals:res.total,
              ImageTypeList:res.rows,
            })
          }
        }
      });
    }
  }
  onChange=(value)=> {
  }
  //获取检查影像库数据
  checkImg = (item) => {
    const {ImgListData,ImageLibrary} = this.state;
    let images = ImageLibrary;
    if(item.target.checked){
      for(let time of ImgListData){
        if(time.classes) {
          for (let cla of time.classes) {
            if (cla.checkList) {
              for (let check of cla.checkList) {
                if (check.id === item.target.value) {
                  images.push({
                    checkId: check.id, //检查id 影像库影像必传
                    className: check.className,
                    classCode:check.classCode,
                    url: check.fileUrl,
                    shootingTime:check.shootingTime,
                    createdGmtAt:check.createdGmtAt,
                    fileDesc:check.fileDesc
                  })
                }
              }
            }
          }
        }
      }
    }else{
      images = images.filter(image=>image.checkId != item.target.value)
    }
    this.setState({
      ImageLibrary:images
    });
  }
  //点击事件
  getChecked = (id) => {
    const {ImageLibrary} = this.state;
    for(let item of ImageLibrary){
      if(item.checkId===id){
        return true;
      }
    }
    return false;
  }
  //类型点击事件
  handleChange=(value)=>{
    const {ImageTypeList}=this.state;
    let arrs=[];
    ImageTypeList.forEach((key,index)=>{
      if(key.classCode==value){
        arrs.push({
          key,
        })
      }
    })
    this.state.uploadParams.uploadType=arrs
  }
  //拍摄时间
  choosedate=(date,dateString)=>{
    this.state.uploadParams.uploadDate=dateString;
  }
  // 鼠标移入移出影像图片
  onMouseIn = (mouseIn,uid) => {
    this.setState({
      ['mouseIn'+uid]: mouseIn
    })
  }
  //上传限制
  handleBeforeUploadPDF = file => {
    const isSize = (file.size / 1024 / 1024) < 15;
    if (!isSize) {
      Modal.error({
        title: '超过15M限制，不允许上传~',
      });
      return false;
    }
    const isType = file.type.indexOf("image")!=-1;
    if (!isType) {
      Modal.error({
        title: '只能上传图片文件~',
      });
      return false;
    }
    return isType;
  };
  //上传文件移除
  fileRemove = (file)=>{
    let fileList = this.state.fileList;
    for (let i in fileList) {
      if (file.uid === fileList[i].uid) {
        fileList.splice(i, 1);
        this.setState({
          fileList: fileList,
        });
        return;
      }
    }
  }
  //上传
  handleChangeImg = info => {
    let fileList = info.fileList;
    if (info.file.status === 'done') {
      if (info.file.response.code === 200) {
        // console.log("info.fileinfo.file===",info.file)
        info.file.thumbUrl = info.file.response.content.fileUrlView;
        message.success({
          content: '上传成功',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
      } else {
        fileList.forEach((text,index)=>{
          if(info.file.uid==text.uid){
            fileList.splice(index, 1);
            this.setState({
              fileList: fileList,
            });
          }
        })
        message.error({
          content: '上传失败，请上传png、jpg图片文件',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
      }
    }
    if(!info.file.status){
      fileList.forEach((text,index)=>{
        if(info.file.uid==text.uid){
          fileList.splice(index, 1);
          this.setState({
            fileList: fileList,
          });
        }
      })
    }
    this.setState({ fileList });
  };
  //鼠标进入进出
  chooseonMouseIn=(mouseIn,id) => {
    this.setState({
      ['chooseonMouseIn'+id]: mouseIn
    })
  }
  //查看大图
  LookImg=(url)=>{
    const { dispatch } = this.props;
    let params = {
      filePath: url,
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/dbPathTransformService',
        payload: params,
        callback: (res) => {
          this.setState({
            Imgvisible:true,
            bigImgurl: res,
          });
        },
      });
    }
  }
  //查看大图
  LookImg1=(url)=>{
    this.setState({
      Imgvisible1:true,
      bigImgurl1: url,
    });
  }
  //禁止选用的日期
  disabledDate=(current)=>{
    return current && current >moment().subtract("days");//当天之后的不可选，不包括当天
  }
  render() {
    const {ImageTypeList,uploadParams,fileList,ImgListData,emrLoading,total,bigImgurl,bigImgurl1,params}=this.state;
    //上传参数
    const props = {
      action: `/api/medical/ossFile/uploadFile?tenantId=`+localStorage.getItem('tenantId')+`&fileType=1`,//接口路径
      multiple: false,//支持多个文件
      headers: {
        access_token: localStorage.getItem('access_token'),
        client: 'PC',
        username:localStorage.getItem("username")
      },
    }
    return (
      <GridContent>
        <Row gutter={16}>
          <Col className="gutter-row" span={12}>
            <div>
              <div className={commonStyle.font_16} style={{marginLeft:7}}>影像库</div>
              <div className={styles.ListHeight}>
                <div className={styles.imageBorder}>
     {/*             <Checkbox.Group style={{ width: '100%' }} onChange={this.checkImg} value={this.state.checkIds} key={"images"}>*/}
                    {ImgListData.length > 0 ?
                      <>
                        {ImgListData.map((item,index)=>(
                          // <>
                            <div key={index}>
                              <div className={styles.date}>{item.createdGmtAt}</div>
                              {item.classes.map((item1,index1)=>(
                                <div className={styles.ctImageList} key={index1}>
                                  <div className={styles.title}>{item1.className}</div>
                                  <Row style={{padding:10}}>
                                    {item1.checkList.map((item2,index2)=>(
                                      <Col span={12} style={{marginBottom:16}} key={index2}>
                                        <div style={{display:'flex'}}>
                                          <Checkbox value={item2.id} checked={this.getChecked(item2.id)} onChange={this.checkImg} />
                                          <div
                                            className={styles.imgCard}>
                                            <div
                                              className={styles.imgborder}
                                              onMouseOver={()=>this.chooseonMouseIn(true,item2.id)}
                                              onMouseOut={()=>this.chooseonMouseIn(false,item2.id)}
                                            >
                                              <CdnImgs
                                                fileUrl={item2.fileUrl}
                                              />
                                              <div
                                                hidden={!this.state['chooseonMouseIn'+item2.id]}
                                                className={styles.ctimgdelete}
                                              >
                                                <div style={{marginTop:'27%',cursor:'pointer'}} onClick={this.LookImg.bind(this,item2.fileUrl)}>
                                                  <img src={Preview} className={styles.icon_delete} alt=""/>
                                                  <span className={styles.deleteFont}>预览</span>
                                                </div>
                                              </div>

                                            </div>
                                            <div className={styles.contentStyle}>
                                              <div className={styles.maxLine}>影像分析:{item2.fileDesc}</div>
                                              <div style={{marginTop:4}}>上传时间:{item2.createdGmtAt}</div>
                                            </div>
                                          </div>
                                        </div>
                                      </Col>
                                    ))}
                                  </Row>
                                </div>
                              ))}
                            </div>
                          // </>
                        ))}
                        <div>
                          <Spin spinning={emrLoading} tip="加载中...">
                            {params.pageNum >= total / params.pageSize ?
                              <div
                                className={styles.loading_more}
                                hidden={emrLoading}
                              >
                                <div className={styles.width100}>
                                  <div style={{width:'100%',textAlign:'center'}} className={styles.typeTitle}>
                                    没有更多啦！
                                  </div>
                                </div>
                              </div>
                              : <div
                                className={styles.loading_more}
                                style={{ textAlign: 'center', cursor: 'pointer' }}
                                hidden={emrLoading}
                                onClick={this.emrNext.bind(this)}
                              >
                                加载更多
                              </div>}
                          </Spin>
                        </div>
                      </>:(
                      <Spin spinning={emrLoading} tip="加载中...">
                        <div className={commonStyle.nodataContent} style={{marginTop:'45%'}}>
                          <img src={noData} className={commonStyle.imgStyle} alt=""/>
                          <div className={commonStyle.fontStyle}>影像库里没有关联过任何病例的影像</div>
                        </div>
                      </Spin>
                    )}


                </div>

              </div>

            </div>
          </Col>
          <Col className="gutter-row" span={12}>
            <div>
              <div className={commonStyle.font_16} style={{marginLeft:7}}>本地上传</div>
              <div className={styles.ListRightHeight}>
                <Form
                  name="basic"
                  colon={false}
                  labelCol={{
                    span: 5,
                  }}
                  wrapperCol={{
                    span: 19,
                  }}
                  initialValues={{
                    remember: true,
                  }}
                  // autoComplete="off"
                >
                  <Form.Item
                    label="类型"
                    name="type"
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                  >
                    <Select placeholder="请选择" style={{ width: '100%' }} onChange={this.handleChange} >
                      {ImageTypeList.map((item,index)=>(
                        <Option key={index} value={item.classCode}>{item.className}</Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    label="拍摄时间"
                    name="password"
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                  >
                    <DatePicker
                      disabledDate={this.disabledDate}
                      style={{ width: '100%' }}
                      onChange={this.choosedate} />
                  </Form.Item>

                  <Form.Item
                    label="上传"
                    name="password"
                  >
                    <Upload
                      {...props}
                      fileList={this.state.fileList}
                      beforeUpload={this.handleBeforeUploadPDF}
                      onRemove={this.fileRemove}
                      multiple={true}
                      maxCount={10}
                      onChange={this.handleChangeImg}
                      showUploadList={false}
                    >
                      <Button icon={<UploadOutlined/>}>上传文件</Button>
                    </Upload>
                    <div className={styles.showImgborder}>
                      {(fileList || []).map((item, index) => (
                        <div key={index}>
                          <Spin  spinning={item.status === 'uploading'}>
                            <div
                              onMouseOver={()=>this.onMouseIn(true,item.uid)}
                              onMouseOut={()=>this.onMouseIn(false,item.uid)}
                              className={styles.imgborder}>
                              <img src={item.thumbUrl} className={styles.showImg} alt=""/>
                              <div
                                hidden={!this.state['mouseIn'+item.uid]}
                                className={styles.ctimgdelete}
                              >
                                <div style={{marginTop:'25%',display:'flex'}}>
                                  <div style={{cursor:'pointer'}} onClick={this.LookImg1.bind(this,item.thumbUrl)}>
                                    <img src={Preview} className={styles.icon_delete} style={{marginLeft:20}} alt=""/>
                                    <span className={styles.deleteFont}>预览</span>
                                  </div>
                                  <Popconfirm
                                    title="确定删除?"
                                    onConfirm={() => this.fileRemove(item)}
                                    okText="是"
                                    cancelText="否"
                                  >
                                    <div style={{cursor:'pointer'}}>
                                      <img src={Delete} className={styles.icon_delete} style={{marginLeft:10}} alt=""/>
                                      <span
                                        className={styles.deleteFont}
                                      >删除</span>
                                    </div>
                                  </Popconfirm>
                                </div>
                              </div>
                            </div>
                          </Spin>
                        </div>
                      ))}
                        </div>
                  </Form.Item>

                  <Form.Item
                    label="影像分析"
                    name="fileDesc"
                    style={{ marginTop:'-8px'}}
                    className={styles.textInputStyle}
                  >
                    <TextArea
                      showCount
                      maxLength={100}
                      autoSize={{minRows: 6, maxRows: 10}}
                      onChange={e=>{uploadParams.fileDesc=e.target.value}}
                      placeholder="请输入"/>
                  </Form.Item>
                  {/*<Form.Item*/}
                  {/*  wrapperCol={{*/}
                  {/*    offset: 8,*/}
                  {/*    span: 16,*/}
                  {/*  }}*/}
                  {/*>*/}
                  {/*  <Button type="primary" htmlType="submit">*/}
                  {/*    Submit*/}
                  {/*  </Button>*/}
                  {/*</Form.Item>*/}
                </Form>
                <Image
                  width={200}
                  style={{
                    display: 'none',
                  }}
                  preview={{
                    visible:this.state.Imgvisible,
                    src:bigImgurl,
                    onVisibleChange: (value) => {
                      this.setState({
                        Imgvisible:false
                      })
                    },
                  }}
                />
                <Image
                  width={200}
                  style={{
                    display: 'none',
                  }}
                  preview={{
                    visible:this.state.Imgvisible1,
                    src:bigImgurl1,
                    onVisibleChange: (value) => {
                      this.setState({
                        Imgvisible1:false
                      })
                    },
                  }}
                />
              </div>
            </div>
          </Col>
        </Row>
      </GridContent>
    );
  }
}

export default connect(({ }) => ({}))(Screenage);
