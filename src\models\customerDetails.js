import {
  focusOnCustomer,
  updateCustomerFollowPerson,
  customerInfoBasicInfo,
  getCustomerInfo,
  getCustomerInsurance,
  getAllAreas,
  getCustomerWayTree,
  getDataListtest,
  getConsultantAll,
  getDirectorData,
  getResourceListAll,
  getReturnVisitTaskList, // 获取回访任务筛选数据
  getVisitTaskList, // 获取回访任务列表
  getReturnVisitNewInfo, // 获取回访任务详情
  preservationVisit, // 保存回访任务
  queryCustomerSettlementRecord,
  saveCustomerInsurancePC,
  saveReturnVisitNew, // 保存设回访任务getConsultantAll
  getReturnVisitTemplate, // 获取回访模板
  getReturnVisitPersonList, // 获取建议回访人
  updateCustomer,
} from '@/services/CustomerDetails';


export default {
  namespace: 'customerDetails',

  state: {
    isInfoChange: false,
    setTaskModalType: false, // 首页引入的设任务弹窗状态
    openMaximizeData: {
      // maximizeIndex: null,  // 打开最小化时获取的下标
      // patientId,  // 患者Id
      // visitTaskId, // 回访任务Id
      // visitStatus, // 回访状态   1已回访  2未回访
      // visitSwitchValue, // 回访弹窗切换值  1做回访  2设回访
      // visitTaskList, // 做回访列表数据
      // setVisitList, // 设回访列表数据
      // formId
    }, // 最小化卡片数据
    minimizeVisitList: [], // 最小化卡片列表
    userInfoHeadList: [], // 客户详情头部信息
    complaintLabelArr: [], // 客户详情头部潜在治疗标签
    isOnPassage: 2, // 是否在途
    UPDATECustomerInfoModalByCustomerInformationToUserInfoHeader: false,  // 是否需要更新客户详情
  },

  effects: {
    // 客户基本信息（头部）
    *customerInfoBasicInfo({ payload }, { call }) {
      const res = yield call(customerInfoBasicInfo, payload);
      return res;
    },

    // 修改健康顾问、责任医生、专属客服
    *updateCustomerFollowPerson({ payload }, { call }) {
      const res = yield call(updateCustomerFollowPerson, payload);
      return res;
    },

    // 关注客户
    *focusOnCustomer({ payload }, { call }) {
      const res = yield call(focusOnCustomer, payload);
      return res;
    },

    // 客户基本信息（编辑）
    *getCustomerInfo({ payload }, { call }) {
      const res = yield call(getCustomerInfo, payload);
      return res;
    },

    // 健康顾问-客服
    *getConsultantAll({ payload }, { call }) {
      const res = yield call(getConsultantAll, payload);
      return res;
    },

    // 获取全部医生
    *getResourceListAll({ payload }, { call }) {
      const res = yield call(getResourceListAll, payload);
      return res;
    },

    // 查询客户账户信息以及缴费记录
    *queryCustomerSettlementRecord({ payload }, { call }) {
      const res = yield call(queryCustomerSettlementRecord, payload);
      return res;
    },

    // 查询客户保险信息-客户详情
    *getCustomerInsurance({ payload }, { call }) {
      const res = yield call(getCustomerInsurance, payload);
      return res;
    },

    // 编辑保存保险信息
    *saveCustomerInsurancePC({ payload }, { call }) {
      const res = yield call(saveCustomerInsurancePC, payload);
      return res;
    },

    // 获取城市列表
    *getAllAreas({ payload }, { call }) {
      const res = yield call(getAllAreas, payload);
      return res;
    },

    // 来源渠道
    *getCustomerWayTree({ payload }, { call }) {
      const res = yield call(getCustomerWayTree, payload);
      return res;
    },

    // 患者列表信息
    *getDataListtest({ payload }, { call }) {
      const res = yield call(getDataListtest, payload);
      return res;
    },

    *updateCustomer({ payload }, { call }) {
      const res = yield call(updateCustomer, payload);
      return res;
    },

    // 获取回访任务筛选数据
    *getReturnVisitTaskList({ payload }, { call }) {
      const res = yield call(getReturnVisitTaskList, payload);
      return res;
    },

    // 获取回访任务列表
    *getVisitTaskList({ payload }, { call }) {
      const res = yield call(getVisitTaskList, payload);
      return res;
    },

    // 获取回访任务详情
    *getReturnVisitNewInfo({ payload }, { call }) {
      const res = yield call(getReturnVisitNewInfo, payload);
      return res;
    },

    // 保存回访任务
    *preservationVisit({ payload }, { call }) {
      const res = yield call(preservationVisit, payload);
      return res;
    },

    // 保存设回访任务
    *saveReturnVisitNew({ payload }, { call }) {
      const res = yield call(saveReturnVisitNew, payload);
      return res;
    },

    // 获取回访模板
    *getReturnVisitTemplate({ payload }, { call }) {
      const res = yield call(getReturnVisitTemplate, payload);
      return res;
    },

    // 获取建议回访人
    *getReturnVisitPersonList({ payload }, { call }) {
      const res = yield call(getReturnVisitPersonList, payload);
      return res;
    },
  },

  reducers: {
    // 更新状态值数据
    setTaskListState(state, { payload }) {
      return {
        ...state,
        ...payload
      }
    },

    // 清空数据
    clean(state, { payload }){
      return {
        ...state,
      }
    },

    // 更新最小化数据
    updateMinimizeData(state, { payload }) {
      return {
        ...state,
        ...payload
      }
    },


    /*
      创建当客户详情被编辑更新的事件 [UPDATA_CustomerInfo_ModalByCustomerInformation_To_UserInfoHeader]
      ① [ModalByCustomerInformation] 患者中心->患者详情->客户病历->病历首页->点击编辑客户信息
      ② 当编辑成功后,需要刷新当前[病历首页]中的患者详情信息,
      ③ [UserInfoHeader/index] 患者中心->患者详情->患者详情头部 也需要更新患者详情中的头部患者信息展示区域的更新
      为了简化代码,减少代码层级中的回调函数使用自定义事件机制进行处理
    */
    updateTACustomer(state, { payload }) {
      return {
        ...state,
        UPDATECustomerInfoModalByCustomerInformationToUserInfoHeader:payload
      }
    }
  },
  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, search }) => {
        if (!((/\/consult/.test(pathname)) || (/\/subscribe/.test(pathname)) || (/\/doctorworkbenchsimplify/.test(pathname)) || (/\/newHomeIndex/.test(pathname)) || (/\/settlement/.test(pathname)) || (/\/customerfollow/.test(pathname)))) {
          dispatch({
            type: "clean",
            payload: {}
          })
        }
      })
    }
  }
};
