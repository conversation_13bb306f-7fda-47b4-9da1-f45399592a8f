import React from 'react';
import { Layout, message, Row, Icon, Modal } from 'antd';
import DocumentTitle from 'react-document-title';
import isEqual from 'lodash/isEqual';
import memoizeOne from 'memoize-one';
import { connect } from 'dva';
import { ContainerQuery } from 'react-container-query';
import classNames from 'classnames';
import pathToRegexp from 'path-to-regexp';
import { enquireScreen, unenquireScreen } from 'enquire-js';
import { formatMessage } from 'umi/locale';
import SiderMenu from '@/components/CustomerServiceSiderMenu';
import Authorized from '@/utils/Authorized';
// import SettingDrawer from '@/components/SettingDrawer';
// import logo from '../assets/logo.svg';
import Header from './CustomerServiceNewHeader';
import Context from './MenuContext';
import Exception403 from '../pages/Exception/403';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import styles from './CustomerServiceNewLayout.less';
import { reloadAuthorized } from '@/utils/Authorized';
import Link from 'umi/link';
import { LocaleProvider } from 'antd';
import { Socket } from 'net';
const { Content, Sider } = Layout;

// Conversion router to menu.
function formatter(data, parentAuthority, parentName) {
  return data
    .map(item => {
      if (!item.name || !item.path) {
        return null;
      }

      let locale = 'menu';
      if (parentName) {
        locale = `${parentName}.${item.name}`;
      } else {
        locale = `menu.${item.name}`;
      }

      const result = {
        ...item,
        name: formatMessage({ id: locale, defaultMessage: item.name }),
        locale,
        authority: item.authority || parentAuthority,
      };
      if (item.routes) {
        const children = formatter(item.routes, item.authority, locale);
        // Reduce memory usage
        result.children = children;
      }
      delete result.routes;
      return result;
    })
    .filter(item => item);
}
const memoizeOneFormatter = memoizeOne(formatter, isEqual);

const query = {
  'screen-xs': {
    maxWidth: 575,
  },
  'screen-sm': {
    minWidth: 576,
    maxWidth: 767,
  },
  'screen-md': {
    minWidth: 768,
    maxWidth: 991,
  },
  'screen-lg': {
    minWidth: 992,
    maxWidth: 1199,
  },
  'screen-xl': {
    minWidth: 1200,
    maxWidth: 1599,
  },
  'screen-xxl': {
    minWidth: 1600,
  },
};

class BasicLayout extends React.PureComponent {
  constructor(props) {
    super(props);
    this.getPageTitle = memoizeOne(this.getPageTitle);
    this.getBreadcrumbNameMap = memoizeOne(this.getBreadcrumbNameMap, isEqual);
    this.breadcrumbNameMap = this.getBreadcrumbNameMap();
    this.matchParamsPath = memoizeOne(this.matchParamsPath, isEqual);
  }

  state = {
    rendering: true,
    isMobile: true,
    message: false,
    menuData: this.getMenuData(),
    holdStatus: false,
    Role: '',
  };

  componentDidMount() {
    reloadAuthorized();
    this.renderRef = requestAnimationFrame(() => {
      this.setState({
        rendering: false,
      });
    });
    this.enquireHandler = enquireScreen(mobile => {
      const { isMobile } = this.state;
      if (isMobile !== mobile) {
        this.setState({
          isMobile: true,
        });
      }
    });
  }


  // 获取cookie
  getCookie = (_name) => {
    let { cookie } = document;
    let arr = cookie.split(";");
    for (let i = 0; i < arr.length; i++) {
      let newArr = arr[i].split(">");
      if (newArr[0] == _name) {
        return newArr[2];
      }
    }
  }

  componentDidUpdate(preProps) {
    // After changing to phone mode,
    // if collapsed is true, you need to click twice to display
    this.breadcrumbNameMap = this.getBreadcrumbNameMap();
    const { isMobile } = this.state;
    const { collapsed } = this.props;
  }
  componentWillUnmount() {
    cancelAnimationFrame(this.renderRef);
    unenquireScreen(this.enquireHandler);
  }

  getContext() {
    const { location } = this.props;
    return {
      location,
      breadcrumbNameMap: this.breadcrumbNameMap,
    };
  }

  getMenuData() {
    const {
      route: { routes, authority },
    } = this.props;
    return memoizeOneFormatter(routes, authority);
  }

  /**
   * 获取面包屑映射
   * @param {Object} menuData 1菜单配置
   */
  getBreadcrumbNameMap() {
    const routerMap = {};
    const mergeMenuAndRouter = data => {
      data.forEach(menuItem => {
        if (menuItem.children) {
          mergeMenuAndRouter(menuItem.children);
        }
        // Reduce memory usage
        routerMap[menuItem.path] = menuItem;
      });
    };
    mergeMenuAndRouter(this.getMenuData());
    return routerMap;
  }

  matchParamsPath = pathname => {
    const pathKey = Object.keys(this.breadcrumbNameMap).find(key =>
      pathToRegexp(key).test(pathname)
    );
    return this.breadcrumbNameMap[pathKey];
  };

  getPageTitle = pathname => {
    const currRouterData = this.matchParamsPath(pathname);

    if (!currRouterData) {
      return 'FRIDAY口腔业务综合管理平台';
    }
    const pageName = formatMessage({
      id: currRouterData.locale || currRouterData.name,
      defaultMessage: currRouterData.name,
    });
    return `${pageName} - FRIDAY腔业务综合管理平台`;
  };

  getLayoutStyle = () => {
    const { isMobile } = this.state;
    const { fixSiderbar, collapsed, layout } = this.props;
    if (fixSiderbar && layout !== 'topmenu' && !isMobile) {
      return {
        paddingLeft: collapsed ? '80px' : '256px',
      };
    }
    return null;
  };
  render() {
    const { taskObj, holdStatus } = this.state;
    const {
      navTheme,
      layout: PropsLayout,
      children,
      location: { pathname },
    } = this.props;
    const { isMobile, menuData } = this.state;
    const isTop = PropsLayout === 'topmenu';
    const routerConfig = this.matchParamsPath(pathname);
    const {location}=this.props
    const layout = (
      <Layout>

        <Layout
          style={{
            ...this.getLayoutStyle(),
            minHeight: '100vh',
            minWidth: '1024px'
          }}
        >
          <Sider collapsed={false}>
            <SiderMenu
              // logo={logo}
              Authorized={Authorized}
              theme={navTheme}
              menuData={menuData}
              {...this.props}
            />
          </Sider>
          <Header
            menuData={menuData}
            // logo={logo}
            isMobile={isMobile}
            {...this.props}
          />
          <Content className={location.pathname=="/customerService/patient/index"?styles.aaa:styles.content}>
            <Authorized
              authority={routerConfig && routerConfig.authority}
              noMatch={<Exception403 />}
            >
              {children}
            </Authorized>
          </Content>
        </Layout>
      </Layout>
    );
    return (
      <LocaleProvider locale={zhCN}>
        <React.Fragment>
          <DocumentTitle title={this.getPageTitle(pathname)}>
            <ContainerQuery query={query}>
              {params => (
                <Context.Provider value={this.getContext()}>
                  <div className={classNames(params)}>{layout}</div>
                </Context.Provider>
              )}
            </ContainerQuery>
          </DocumentTitle>
          {/* 去除主题设置 */}
          {/* {this.renderSettingDrawer()} */}
        </React.Fragment>


      </LocaleProvider>
    );
  }
}

export default connect(({ global, setting }) => ({
  collapsed: global.collapsed,
  layout: setting.layout,
  ...setting,
}))(BasicLayout);
