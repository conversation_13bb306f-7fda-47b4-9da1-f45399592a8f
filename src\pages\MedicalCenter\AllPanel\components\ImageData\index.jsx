// import { PlusOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import { UploadOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  DatePicker,
  Form,
  Image,
  Input,
  message,
  Modal,
  Row,
  Select,
  Spin,
  Upload,
} from 'antd';
import moment from 'moment';
import React, { Component } from 'react';
// import { useIntl, FormattedMessage } from 'umi';
import Delete from '@/assets/<EMAIL>';
import Preview from '@/assets/<EMAIL>';
import LoadingOutlined from '@ant-design/icons/lib/icons/LoadingOutlined';
import { GridContent } from '@ant-design/pro-layout';
import { connect } from 'dva';
import styles from './style.less';//引入样式
import TimeRecord from './TimeRecord'; //按时间顺序
import TypeRecord from './TypeRecord';//按类型显示
import {StringUtils} from "@/utils/StringUtils";//公共验证
//Base64图片

const { TextArea } = Input;
/**form表单控制布局**/
const modalLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};

class ImageData extends Component {
  /*
  ImageData: undefined
  callback: ƒ (value)
  childKey: 1
  dispatch: ƒ (action)
  emrLoading: false
  imageListParams: undefined
  imgByClass: []
  imgByClassCount: 10
  imgByDate: []
  imgByDateCount: 10
  loading: undefined
  patientData: {
    fileNumber: "JY0A000051"
    name: "测试预约流程"
    patientId: 1386
  }
  tabKey: "2"
  * */


  infoFormRef = React.createRef();

  constructor(props) {
    super(props);
    this.state = {
      fileList: [], //上传文件
      imageClass: [], //影像类型列表
      uploadModalVisible: false, //上传影像弹窗
      currentType: '', // 当前展示的类型  分类&时间
      patientData: props.patientData,//患者详情
      formData: {
        tenantId: localStorage.getItem('tenantId'), //平台标识
        emrId: props.patientData?props.patientData.fileNumber:null, //大病历号
        patientId: props.patientData?props.patientData.patientId:null, //患者标识
        patientName: props.patientData?props.patientData.name:null, //患者姓名
        userId: localStorage.getItem('userId'), // 医生标识
        userName: localStorage.getItem('userName'), //医生姓名
        classCode: '', //分类标识
        fileName: '', //文件名称
        fileType: 'img', //文件类型
        fileUrl: '', //文件存储地址  阿里云OSS地址
        fileDesc: '', //文件描述
        filesSize: '', //文件大小  kb
        organizationId: localStorage.getItem('organizationId'), //机构ID
        organizationName: localStorage.getItem('organizationName'), //机构名称
        shootingTime: '', //拍摄时间
      },
      clientHeight: document.documentElement.clientHeight, // 屏幕高度
      previewVisible: false, // 预览图片弹窗
      previewImage: '', //预览图片url
    };
    this.resize = this.resize.bind(this);//监听屏幕高度
  }
  //初始化
  componentDidMount() {
    this.showAll(1); // 按时间排序1 & 按类型排序2
    window.addEventListener('resize', this.resize); //监听屏幕高度
  }
  //在组件卸载及销毁之前直接调用
  componentWillUnmount() {
    window.removeEventListener('resize', this.resize); //监听屏幕高度
  }

  //监听数据变化
  componentWillReceiveProps(nextProps) {
    if (nextProps.patientData) {
      if (!this.state.patientData || nextProps.patientData.patientId != this.state.patientData.patientId) {
        // 页面中的操作都初始化一下
        this.setState({
          fileList: [], //上传文件
          imageClass: [], //影像类型列表
          uploadModalVisible: false, //上传影像弹窗
          currentType: '', // 当前展示的类型  分类&时间
          patientData: nextProps.patientData,//患者信息
          formData: {
            tenantId: localStorage.getItem('tenantId'), //平台标识
            emrId: nextProps.patientData?nextProps.patientData.fileNumber:null, //大病历号
            patientId: nextProps.patientData?nextProps.patientData.patientId:null, //患者标识
            patientName: nextProps.patientData?nextProps.patientData.name:null, //患者姓名
            userId: localStorage.getItem('userId'), // 医生标识
            userName: localStorage.getItem('userName'), //医生姓名
            classCode: '', //分类标识
            fileName: '', //文件名称
            fileType: 'img', //文件类型
            fileUrl: '', //文件存储地址  阿里云OSS地址
            fileDesc: '', //文件描述
            filesSize: '', //文件大小  kb
            organizationId: localStorage.getItem('organizationId'), //机构ID
            organizationName: localStorage.getItem('organizationName'), //机构名称
            shootingTime: '', //拍摄时间
          },
          clientHeight: document.documentElement.clientHeight, // 屏幕高度
          previewVisible: false, // 预览图片弹窗
          previewImage: '', //预览图片url
        });
      }
    }
  }

//监听屏幕高度
  resize() {
    this.setState({ clientHeight: document.documentElement.clientHeight }); //监听
  }

  // 按时间排序1 & 按类型排序2
  showAll = (key = 1) => {
    this.props.callback(key);
    if (key == 1) {
      this.setState({
        imageKey: Math.random(),
        currentType: 'dateType',
      });
      // 请求按时间排序
    } else if (key == 2) {
      // 请求按类型排序
      this.setState({
        imageKey: Math.random(),
        currentType: 'classType',
      });
    }
  };
  // 上传影像
  uploadImage = () => {
    this.getImageClassData();
    this.setState({
      uploadModalVisible: true,
    });
  };
  // 上传确认
  saveHideModal = () => {
    if (!this.state.formData.classCode) {
      message.warning('请选择类型');
      return false;
    }
    if (!this.state.formData.shootingTime) {
      message.warning('请选择拍摄时间');
      return false;
    }

    if (this.state.fileList.length === 0) {
      message.warning('请上传图片');
      return false;
    } else {
      for (let file of this.state.fileList) {
        if (file.status != 'done') {
          message.warning('有文件未上传完成，无法提交');
          return false;
        }
      }
    }
    this.saveCheckImgs();
  };
  // 保存影像接口
  saveCheckImgs = () => {
    this.setState({
      saveLoading: true
    });

    const { dispatch } = this.props;
    const { currentType, fileList } = this.state;
    let params = this.state.formData;

    let fileUrls = [];

    for(let file of fileList){
      fileUrls.push(file.response.content.fileUrl);
    }
    params.fileUrl = fileUrls.join(";");
    if (dispatch) {
      dispatch({
        type: 'homePage/saveCheckImgs',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '保存成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.hideModal();

            // 调用成功后调用列表接口
            // 要分类型 上传的是按时间还是按分类
            if (currentType == 'dateType') {
              this.timeRecord.getCheckImgByDate(1);
            } else {
              this.typeRecord.getCheckImgByClass(1);
            }
          } else {
            message.error({
              content: '保存失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
          }

          this.setState({
            saveLoading: false
          });
        },
      });
    }
  };
  //获取检查类型
  getImageClassData = () => {
    const { dispatch } = this.props;
    let params = {
      tenantId: localStorage.getItem('tenantId'),
    };
    if (dispatch) {
      dispatch({
        type: 'ImageManageModel/ImageManageService',
        payload: params,
        callback: (res) => {
          if (res.code === 200) {
            this.setState({
              imageClass: res.rows,
            });
          }
        },
      });
    }
  };

  // 关闭影像上传弹窗
  hideModal = () => {
    this.setState({
      uploadModalVisible: false,
      fileList: [],
      formData: {
        tenantId: localStorage.getItem('tenantId'), //平台标识
        emrId: this.state.patientData.fileNumber, //大病历号
        patientId: this.state.patientData.patientId, //患者标识
        patientName: this.state.patientData.name, //患者姓名
        userId: localStorage.getItem('userId'), // 医生标识
        userName: localStorage.getItem('userName'), //医生姓名
        classCode: '', //分类标识
        fileName: '', //文件名称
        fileType: 'img', //文件类型
        fileUrl: '', //文件存储地址  阿里云OSS地址
        fileDesc: '', //文件描述
        filesSize: '', //文件大小  kb
        organizationId: localStorage.getItem('organizationId'), //机构ID
        organizationName: localStorage.getItem('organizationName'), //机构名称
        shootingTime: '', //拍摄时间
      },
    });
    this.onReset();
  };
  // 表单重置
  onReset = () => {
    this.infoFormRef.current.resetFields();
  };

  // 鼠标移入移出影像图片
  onMouseIn = (mouseIn,uid) => {
    this.setState({
      ['mouseIn'+uid]: mouseIn,
    });
  };
  // 上传文件
  //上传保存
  handleChange = (info) => {
    let fileList = info.fileList;
    if (info.file.status === 'done') {
      if (info.file.response.code === 200) {
        info.file.thumbUrl = info.file.response.content.fileUrlView;
        info.file.url = info.file.response.content.fileUrl;
        message.success({
          content: '上传成功',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        this.setState({ fileList });
      } else {
        for(let index in fileList){
          if(info.file.uid===fileList[index].uid){
            fileList.splice(index, 1);
            break;
          }
        }
        message.error({
          content: '上传失败',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
      }
    }else if(info.file.status === 'error'){
      for(let index in fileList){
        if(info.file.uid===fileList[index].uid){
          fileList.splice(index, 1);
          break;
        }
      }
      message.error({
        content: '上传失败',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
    }
    this.setState({ fileList });
  };
  //图片删除事件
  fileRemove = (file) => {
    let fileList = this.state.fileList;
    for (let i in fileList) {
      if (file.uid === fileList[i].uid) {
        fileList.splice(i, 1);

        this.setState({
          fileList: fileList,
        });
        return;
      }
    }
  };

  //查看大图
  LookImg = (url) => {
    const { dispatch } = this.props;
    let params = {
      filePath: url,
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/dbPathTransformService',
        payload: params,
        callback: (res) => {
          this.setState({
            previewVisible: true,
            previewImage: res,
          });
        },
      });
    }
  };
  //pdf上传事件
  handleBeforeUploadPDF = (file) => {
    const isSize = file.size / 1024 / 1024 < 15;
    if (!isSize) {
      Modal.error({
        title: '超过15M限制，不允许上传~',
      });
      return false;
    }
    const isType = file.type.indexOf('image') != -1;
    if (!isType) {
      Modal.error({
        title: '只能上传图片~',
      });
      this.setState({ fileList: [] });

      return false;
    }
    return isType;
  };

  // 上传的拍摄时间不能大于今天
  disabledDate = (current) => {
    // Can not select days after today
    return current && current > moment().endOf('day');
  };
  render() {
    const {imageKey,
      patientData,
      formData,
      uploadModalVisible,
      fileList,
      imageClass,
      previewVisible,
      previewImage,saveLoading
    } = this.state;
    const { childKey } = this.props;
    //上传属性及参数数据
    const props = {
      action:
        `/api/medical/ossFile/uploadFile?tenantId=` +
        localStorage.getItem('tenantId') +
        `&fileType=1`, //接口路径
      multiple: true, //支持多个文件
      maxCount:10,
      showUploadList:false,
      headers: {
        access_token: localStorage.getItem('access_token'),
        client: 'PC',
        username: localStorage.getItem('username'),
      },
    };
    const antIcon = (
      <LoadingOutlined
        style={{
          fontSize: 24,
        }}
        spin
      />
    );
    return (
      <GridContent >
        <Row
          style={{
            paddingLeft: '16px',
            paddingBottom: '12px',
            borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <Col span={8}>
            <span
              className={childKey == 1 ? styles.tagChecked : styles.tagNoCheck}
              style={{ width: '90px' }}
              onClick={() => this.showAll(1)}
            >
              按时间顺序
            </span>
            <span
              className={childKey == 2 ? styles.tagChecked : styles.tagNoCheck}
              style={{ width: '90px' }}
              onClick={() => this.showAll(2)}
            >
              按类型显示
            </span>
          </Col>

          <Col span={3} style={{ float: 'right' }}>
            <div onClick={this.uploadImage} hidden={!this.state.patientData||StringUtils.isBlank(this.state.patientData.fileNumber)}>
              <span style={{ marginLeft: 10 }} className={styles.txt11}>
                <UploadOutlined />
                <span style={{ marginLeft: '8px' }}>上传影像</span>
              </span>
            </div>
          </Col>
          <Modal
          className={styles.textInputStyle}
          title="上传影像"
          width={790}
          maskClosable={false}
          visible={uploadModalVisible}
          // onOk={this.saveHideModal}
          onCancel={() => this.hideModal()}
          okText="确认上传"
          cancelText="取消"
          footer={
            <>
              <Button onClick={() => this.hideModal()}>取消</Button>
              <Button type="primary" loading={saveLoading} onClick={this.saveHideModal}>确认</Button>
            </>}
        >
          <Form
            {...modalLayout}
            className={styles.width100}
            onFinish={this.formFinish}
            ref={this.infoFormRef}
          >
            <Row className={styles.displayFlex}>
              <Col span={24}>
                <Form.Item name='classCode' labelAlign="right" label="类型" required>
                  <Select
                    placeholder="请选择检查类型"
                    onSelect={(e) => {
                      formData.classCode = e;
                    }}
                  >
                    {(imageClass || []).map((item) => (
                      <Select.Option value={item.classCode} key={item.id}>
                        {item.className}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <Form.Item name='shootingTime' labelAlign="right" label="拍摄时间" required>
                  <DatePicker
                    placeholder="请选择拍摄时间"
                    style={{ width: '100%' }}
                    disabledDate={this.disabledDate}
                    onChange={(date, dateString) => (formData.shootingTime = dateString)}
                  ></DatePicker>
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <Form.Item name='upload' labelAlign="right" label="上传">
                  <Upload
                    {...props}
                    fileList={this.state.fileList}
                    beforeUpload={this.handleBeforeUploadPDF}
                    onChange={this.handleChange}
                  >
                    <Button icon={<UploadOutlined />}>上传图片</Button>
                  </Upload>

                  <div className={styles.showImgborder}>
                    {(fileList || []).map((item, index) => (
                      <Spin spinning={item.status === 'uploading'}>
                        <div
                          key={index}
                          onMouseOver={() => this.onMouseIn(true,item.uid)}
                          onMouseOut={() => this.onMouseIn(false,item.uid)}
                          className={styles.imgborder}
                        >
                          <img src={item.thumbUrl} className={styles.showImg} alt="" />
                          <div hidden={!this.state['mouseIn'+item.uid]} className={styles.ctimgdelete}>
                            <div style={{ marginTop: '25%', display: 'flex' }}>
                              <div
                                style={{ cursor: 'pointer' }}
                                onClick={this.LookImg.bind(this, item.url)}
                              >
                                <img
                                  src={Preview}
                                  className={styles.icon_delete}
                                  style={{ marginLeft: 20 }}
                                  alt=""
                                />
                                <span className={styles.deleteFont}>预览</span>
                              </div>
                              <div style={{ cursor: 'pointer' }}>
                                <img
                                  src={Delete}
                                  className={styles.icon_delete}
                                  style={{ marginLeft: 10 }}
                                  alt=""
                                />
                                <span
                                  className={styles.deleteFont}
                                  onClick={() => this.fileRemove(item)}
                                >
                                    删除
                                  </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Spin>
                    ))}

                  </div>
                </Form.Item>
              </Col>
            </Row>
            <Row style={{ marginTop: '-8px' }}>
              <Col span={24}>
                <Form.Item name='fileDesc' labelAlign="right" label="影像分析">
                  <TextArea
                    showCount
                    maxLength={100}
                    autoSize={{ minRows: 4, maxRows: 10 }}
                    onChange={(e) => (formData.fileDesc = e.target.value)}
                    placeholder="请输入"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
        </Row>
        <Image
          width={200}
          style={{
            display: 'none',
          }}
          preview={{
            visible: previewVisible,
            src: previewImage,
            onVisibleChange: (value) => {
              this.setState({
                previewVisible: false,
              });
            },
          }}
        />
        <div style={{ overflowY: 'auto', height: this.state.clientHeight - 215 }} >
          {childKey == 1 ? (
            // 按时间顺序
            <Row className={`${styles.block9} `}>
              <TimeRecord patientData={patientData} onRef={(ref) => (this.timeRecord = ref)} />
            </Row>
          ) : (
            <div className={styles.block9}>
              <TypeRecord patientData={patientData} onRef={(ref) => (this.typeRecord = ref)} />
            </div>
          )}
        </div>
      </GridContent>
    );
  }
}
export default connect(({ ImageData, loading }) => ({
  ImageData,
  loading: loading.models.ImageData,
}))(ImageData);
