import {EntryList,saveWords,deleteWord} from '@/services/entryManage';
import {notification} from "antd";
const entryCenterModel = {
  namespace: 'entryModel',
  state: {
    EntryData:{},//词条列表
    saveWordsData:{},//新增编辑词条返回结果
    deleteWordData:{},//删除编辑词条返回结果
    loading:false,
    loadTip:"加载中",
  },
  //异步
  effects: {
    // 获取词条列表
    *EntryListService({payload, callback} , { call, put }) {
      const response = yield call(EntryList ,payload);
      yield put({
        type: 'EntryListInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    // 新增编辑词条
    *saveWordsService({payload, callback} , { call, put }) {
      const response = yield call(saveWords ,payload);
      yield put({
        type: 'saveWordsInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    // 删除词条
    *deleteWordService({payload, callback} , { call, put }) {
      const response = yield call(deleteWord ,payload);
      yield put({
        type: 'deleteWordInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
  },

  //同步
  reducers: {
    EntryListInfo(state, action) {
      return {
        ...state,
        EntryData: action.payload || {},
      };
    },
    saveWordsInfo(state, action) {
      return {
        ...state,
        saveWordsData: action.payload || {},
      };
    },
    deleteWordInfo(state, action) {
      return {
        ...state,
        deleteWordData: action.payload || {},
      };
    },
  },
};
export default entryCenterModel;
