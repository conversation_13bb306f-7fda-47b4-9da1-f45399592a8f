import React, { Component } from 'react';


/**特殊字符**/
class SpecialCharacter extends Component {
  constructor(props) {
    super(props);
    this.state = {
      characterData: this.props.data,//字符数据
      index: this.props.index,//所选中的input下标
    }
  }
  //字符点击事件
  handleClick = (e, val, label, index) => {
    this.props.handleVal(val, label, index)
  }
  render() {
    //特殊字符
    const numberArr = [
      {
        descriptsSPCode: "Ⅰ",
        descripts: "Ⅰ",
        id: "1",
        descriptsEN: "one",
        seq: "1"
      },
      {
        descriptsSPCode: "Ⅱ",
        descripts: "Ⅱ",
        id: "2",
        descriptsEN: "two",
        seq: "2"
      },
      {
        descriptsSPCode: "Ⅲ",
        descripts: "Ⅲ",
        id: "3",
        descriptsEN: "three",
        seq: "3"
      },
      {
        descriptsSPCode: "Ⅳ",
        descripts: "Ⅳ",
        id: "4",
        descriptsEN: "four",
        seq: "4"
      },
      {
        descriptsSPCode: "Ⅴ",
        descripts: "Ⅴ",
        id: "5",
        descriptsEN: "five",
        seq: "5"
      },
      {
        descriptsSPCode: "Ⅵ",
        descripts: "Ⅵ",
        id: "6",
        descriptsEN: "six",
        seq: "6"
      },
      {
        descriptsSPCode: "Ⅶ",
        descripts: "Ⅶ",
        id: "7",
        descriptsEN: "seven",
        seq: "7"
      },
      {
        descriptsSPCode: "Ⅷ",
        descripts: "Ⅷ",
        id: "8",
        descriptsEN: "eight",
        seq: "8"
      }, {
        descriptsSPCode: "Ⅸ",
        descripts: "Ⅸ",
        id: "9",
        descriptsEN: "nine",
        seq: "9"
      }, {
        descriptsSPCode: "Ⅹ",
        descripts: "Ⅹ",
        id: "10",
        descriptsEN: "ten",
        seq: "10"
      },
      {
        descriptsSPCode: "+",
        descripts: "+",
        id: "11",
        descriptsEN: "eleven",
        seq: "11"
      },
      {
        descriptsSPCode: "++",
        descripts: "++",
        id: "12",
        descriptsEN: "twelve",
        seq: "12"
      },
      {
        descriptsSPCode: "+++",
        descripts: "+++",
        id: "13",
        descriptsEN: "thirteen",
        seq: "13"
      },
      {
        descriptsSPCode: "-",
        descripts: "-",
        id: "14",
        descriptsEN: "fourteen",
        seq: "14"
      },
      {
        descriptsSPCode: "。",
        descripts: "。",
        id: "15",
        descriptsEN: "fifteen",
        seq: "15"
      },
      {
        descriptsSPCode: "±",
        descripts: "±",
        id: "16",
        descriptsEN: "sixteen",
        seq: "16"
      },
      {
        descriptsSPCode: ">35N",
        descripts: ">35N",
        id: "17",
        descriptsEN: "seventeen",
        seq: "17"
      },
      {
        descriptsSPCode: "=35N",
        descripts: "=35N",
        id: "18",
        descriptsEN: "eighteen",
        seq: "18"
      },
      {
        descriptsSPCode: "<35N",
        descripts: "<35N",
        id: "19",
        descriptsEN: "nineteen",
        seq: "19"
      },
      {
        descriptsSPCode: ">70",
        descripts: ">70",
        id: "20",
        descriptsEN: "twenty",
        seq: "20"
      },
      {
        descriptsSPCode: "=70",
        descripts: "=70",
        id: "21",
        descriptsEN: "twentyOne",
        seq: "21"
      },
      {
        descriptsSPCode: "<70",
        descripts: "<70",
        id: "22",
        descriptsEN: "twentyTwo",
        seq: "22"
      },
      {
        descriptsSPCode: ">35N",
        descripts: ">35N",
        id: "23",
        descriptsEN: "twentyThree",
        seq: "23"
      },
      {
        descriptsSPCode: "0.25g",
        descripts: "0.25g",
        id: "24",
        descriptsEN: "twentyFour",
        seq: "24"
      },

      {
        descriptsSPCode: "0.5g",
        descripts: "0.5g",
        id: "25",
        descriptsEN: "twentyFive",
        seq: "25"
      },
      {
        descriptsSPCode: "13X25mm",
        descripts: "13X25mm",
        id: "26",
        descriptsEN: "twentySix",
        seq: "26"
      },
      {
        descriptsSPCode: "25X25mm",
        descripts: "25X25mm",
        id: "27",
        descriptsEN: "twentySeven",
        seq: "27"
      },
      {
        descriptsSPCode: "100mg",
        descripts: "100mg",
        id: "28",
        descriptsEN: "twentyEight",
        seq: "28"
      },
      {
        descriptsSPCode: "H2O2",
        descripts: "H2O2",
        id: "29",
        descriptsEN: "twentyNine",
        seq: "29"
      },
      {
        descriptsSPCode: "Ca(OH)2",
        descripts: "Ca(OH)2",
        id: "30",
        descriptsEN: "thirty",
        seq: "30"
      },
    ]
    return (
      <div>
        {numberArr && numberArr.map((item, i) => {
          return (
            <button
              className="number-item"
              style={{
                WebkitUserSelect: "none",
                userSelect: "none",
                height: "33px",
                lineHeight: '18px',
                display: "inline-block",
                textAlign: "center",
                padding: "0 4px",
                border: "2px solid #000",
                borderRadius: "4px",
                background: "#F5F5F5",
                color: "#000",
                cursor: "pointer",
                margin: "6px 4px",
                fontSize: "18px",
              }}
              key={i}
              onClick={(e) => this.handleClick(e, item, this.state.characterData, this.state.index)}
            >
              {item.descripts}
            </button>
          )
        })}
      </div>
    );
  }
}

export default SpecialCharacter
