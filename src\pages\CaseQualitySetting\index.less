.settingContainer {
  min-height: 100vh;
  background: #EEF3F9;
  margin: -20px -20px 0;
  min-width: 940px;
  padding: 16px;
  .goback {
    display: flex;
    align-items: center;
    height: 22px;
    font-size: 14px;
    color: #000000;
    margin-bottom: 8px;
    cursor: pointer;
    width: fit-content;
    img {
      width: 6px;
      margin-right: 6px;
    }
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
      color: #071e3f;
      line-height: 25px;
    }
  }

  .settingCard {
    margin-bottom: 8px;
    border-radius: 6px;
    background: #fff;
    .cardHeader {
      border-bottom: 1px solid #f0f0f0;
      min-height: 48px;
      margin: 0;
      padding: 16px 24px 8px;
      display: flex;
      align-items: flex-end;
      .cardTitle {
        font-weight: 500;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 25px;
        margin: 0;
      }
      .cardDes {
        padding-left: 16px;
      }
    }

    .cardBody {
      padding: 16px 24px;
    }
  }

  .settingItem {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    .settingLabel {
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      line-height: 26px;
      margin-right: 8px;
    }

    .settingDesc {
      font-size: 14px;
      color: #8c8c8c;
      line-height: 1.5;
    }
  }

  .settingContent {
    display: flex;
    align-items: center;
  }
  .redlineContent {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
  }
  .fieldLabel {
    font-size: 14px;
    color: #262626;
    margin-right: 12px;
  }

  .tableWrapper {
    margin-top: 8px;

    // 通用表格样式
    :global(.ant-table-small),
    :global(.ant-table-middle) {
      border: none;
    }

    :global(.ant-table-thead > tr > th) {
      background: #fafafa;
      font-weight: 600;
      color: #000;
      font-size: 14px;
      border-bottom: 1px solid #f0f0f0;
    }

    :global(.ant-table-tbody > tr > td) {
      border-bottom: 1px solid #f0f0f0;
      padding: 12px 16px;
      font-size: 14px;
      color: #262626;
      line-height: 1.5;
    }

    :global(.ant-table-tbody > tr:hover > td) {
      background-color: #f5f5f5;
    }

    // 质检评分配置表格特有样式
    .scoreConfigTable {
      // 子级行样式
      :global(.ant-table-expanded-row) {
        :global(.ant-table-tbody > tr > td) {
          background: #fafafa;
          padding-left: 40px; // 增加缩进
        }
      }

      :global(.ant-table-row-expand-icon) {
        display: none;
      }

      :global(.ant-table-row-collapsed) {
        display: none;
      }
    }

    // 红线规则表格继承通用样式，无需额外特殊样式
  }

  .switchWrapper {
    display: flex;
    align-items: center;

    .switchText {
      margin-left: 8px;
      font-size: 14px;
      color: #000;
      font-weight: 400;
    }
  }
}
