.settingContainer {
  // padding: 24px;
  // background: #f5f5f5;
  min-height: 100vh;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
      color: #071e3f;
      line-height: 25px;
    }
  }

  .settingCard {
    margin-bottom: 8px;
    border-radius: 6px;
    background: #fff;
    .cardHeader {
      border-bottom: 1px solid #f0f0f0;
      min-height: 48px;
      margin: 0;
      padding: 16px 24px 8px;
      display: flex;
      align-items: flex-end;
      .cardTitle {
        font-weight: 500;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 25px;
        margin: 0;
      }
      .cardDes {
        padding-left: 16px;
      }
    }

    .cardBody {
      padding: 16px 24px;
    }
  }

  .settingItem {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    .settingLabel {
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      line-height: 26px;
      margin-right: 8px;
    }

    .settingDesc {
      font-size: 14px;
      color: #8c8c8c;
      line-height: 1.5;
    }
  }

  .settingContent {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .fieldLabel {
      font-size: 14px;
      color: #262626;
      margin-right: 12px;
    }
  }

  .tableWrapper {
    margin-top: 8px;

    .scoreConfigTable {
      :global(.ant-table-small) {
        border: none;
      }
      :global(.ant-table-thead > tr > th) {
        background: #fafafa;
        font-weight: 500;
        color: #000;
        font-size: 14px;
        border-bottom: 1px solid #f0f0f0;
      }

      :global(.ant-table-tbody > tr > td) {
        border-bottom: 1px solid #f0f0f0;
        padding: 12px 16px;
      }

      :global(.ant-table-row-expand-icon) {
        color: #1890ff;
      }

      :global(.ant-table-expanded-row > td) {
        background: #fafafa;
      }

      // 父级行样式
      :global(.ant-table-tbody > tr:not(.ant-table-expanded-row)) {
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }

    :global(.ant-table-thead > tr > th) {
      background: #fafafa;
      font-weight: 500;
      color: #000;
      font-size: 14px;
      border-bottom: 1px solid #f0f0f0;
    }

    :global(.ant-table-tbody > tr > td) {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px;
    }

    :global(.ant-table-tbody > tr:hover > td) {
      background-color: #f5f5f5;
    }
    // 子级行样式
    :global(.ant-table-expanded-row) {
      :global(.ant-table-tbody > tr > td) {
        background: #fafafa;
        padding-left: 40px; // 增加缩进
      }
    }
    :global(.ant-table-row-expand-icon) {
      display: none;
    }
    :global(.ant-table-row-collapsed) {
      display: none;
    }
  }

  .switchWrapper {
    display: flex;
    align-items: center;

    .switchText {
      margin-left: 8px;
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .redlineRules {
    margin-top: 24px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    background: #fff;

    .rulesHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      border-radius: 6px 6px 0 0;

      .rulesTitle {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
      }
    }

    .ruleItem {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .ruleText {
        flex: 1;
        font-size: 14px;
        color: #262626;
        line-height: 1.5;
        margin-right: 16px;
      }

      .ruleStatus {
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .switchText {
          margin-left: 8px;
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }
}
