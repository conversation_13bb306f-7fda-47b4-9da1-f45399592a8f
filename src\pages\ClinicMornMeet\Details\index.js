/**
 * 诊所晨会讨论详情-页面
 */
import {Col, Row, Input, Form, Modal, Popconfirm, message, Spin, Button} from 'antd';
import React, { Component } from 'react';
import styles from './index.less'; // 引入样式
import { connect } from 'dva';
import PatientInfo from "@/components/PatientInfo/indexByClinicMornMeet";//右上方患者详情
import AllPatientRecordByClinicMornMeet from "@/pages/MedicalCenter/AllPanel/components/medicalRecord/AllPatientRecordByClinicMornMeet.jsx";
import ImageData from "@/pages/MedicalCenter/AllPanel/components/ImageData/indexByClinicMornMeet.jsx";
import { history } from 'umi';
import {
  ermPatientInfo,        // 患者信息
  findAllMedicalRecords, // 全部病历
  findCheckImgsByDate,   // 影像资料--按时间
} from '@/services/homePage';
import {
  getAppointmentOpinion, // 根据预约ID查询预约建议
  saveOrUpdateOpinion,   // 编辑或保存晨会意见
} from '@/services/ClinicMornMeet';
import {} from 'dva'
import _ from 'lodash'

const { TextArea } = Input;
class ClinicMorningMeeting extends Component {
  constructor(props) {
    super(props);
    this.state = {
      positions:1,                     // 判断是从哪个页面引入的右上方患者信息组件，修饰样式
      loadingByInitialization: false,  // 加载状态
      rightPatientInfos:null,          // 患者信息基本数据
      patientInfoDto:null,             // 患者信息基本数据
      opinion:null,                    // 晨会意见表单字段
      loadingBySaveOrUpdateOpinion:null, // 提交加载loading
      DataByGetAppointmentOpinion:null,  // 当前请求的晨会意见
    }
    this.refByTextArea = React.createRef();
    this.saveOrUpdateOpinion = _.debounce(this.saveOrUpdateOpinion,500);
  }
  componentDidMount() {
    // 初始化
    this.initialization()
  }

  // 初始化
  initialization = async()=>{
    // 从地址栏获取 预约id 和 患者idçç
    let {
      dispatch,
      match: {
        path,
        params: {
          appointmentId,
          patientId,    // 获取患者id
        }}
    } = this.props || {}

    // 根据患者id获取详情信息
    // console.log('params123123  :: ',appointmentId,patientId);
    await this.setState({ loadingByInitialization:true }) // 添加loading
    let dataByErmPatientInfo = await ermPatientInfo({
      patientId,
      tenantId:localStorage.getItem('tenantId'),
      organizationId: localStorage.getItem("organizationId")
    })

    let dataByGetAppointmentOpinion =  await getAppointmentOpinion({
      appointmentId:appointmentId,
      tenantId:localStorage.getItem('tenantId'),
      organizationId: localStorage.getItem("organizationId")
    })
    await this.setState({ loadingByInitialization:false }) // 关闭loading

    const {
      code:codeByErmPatientInfo,
      content:contentByErmPatientInfo,
      data:dataByDataErmPatientInfo,
      msg:msgByErmPatientInfo,
    } = dataByErmPatientInfo || {}

    if (codeByErmPatientInfo == 200 && contentByErmPatientInfo) {
      const {
        patientCircleShowDto,
        patientInfoDto,
        patientSignBaseDto,
      } = contentByErmPatientInfo || {}

      const { baseInfo } = patientSignBaseDto || {}

      // 患者信息
      this.setState({
        patientInfoDto:patientInfoDto,
        rightPatientInfos:baseInfo,
      })
    }

    if(dataByGetAppointmentOpinion && dataByGetAppointmentOpinion.code == 200){
      this.setState({
        opinion:dataByGetAppointmentOpinion.content
      })
    }
  }

  // 编辑或保存晨会意见
  saveOrUpdateOpinion= async ()=>{
    // 晨会意见
    const { opinion } = this.state || {}
    // 从地址栏获取 预约id 和 患者id
    let {
      dispatch,
      match: {
        path,
        params: {
          appointmentId,
          patientId,    // 获取患者id
        }}
    } = this.props || {}
    await this.setState({ loadingBySaveOrUpdateOpinion:true })
    let dataBySaveOrUpdateOpinion = await saveOrUpdateOpinion({
      appointmentId:appointmentId,
      opinion:opinion
    })
    await this.setState({ loadingBySaveOrUpdateOpinion:false })

    const { code,content,msg } = dataBySaveOrUpdateOpinion || {}
    if (code == 200 && content) {
      // 提交成功后回退到晨会列表
      history && history.go(-1)
    }else {
      message.warn(msg ? msg : '保存晨会意见失败!')
    }
    // history && history.go(-1)
    // console.log('dataBySaveOrUpdateOpinion123 :: ',dataBySaveOrUpdateOpinion);
  }


  render() {

    const {
      listHidden,
      waitTodayData,
      rightPatientInfos,
      patientInfo,
      loadingByInitialization,
      patientData,
      patientInfoDto
    } = this.state;

    const { ClinicMornMeetModel } = this.props
    const { paramsByClinicMornMeetDetails } = ClinicMornMeetModel || {}

    return (
      <div className={styles.ClinicMornMeetWarp}>
          <Spin spinning={!!loadingByInitialization}>
            <div className={styles.children}>
              <PatientInfo
                position={this.state.positions}
                patientId={null}
                rightPatientInfos={rightPatientInfos}
                patientInfos={patientInfoDto}
                onRef={(ref) => (this.child = ref)}
              />
            </div>
            <div className={styles.DetailsBox}>
              <div className={styles.DetailsBox_left}>
                {!!patientInfoDto &&
                  <AllPatientRecordByClinicMornMeet
                    // key={emrKey}
                    onRef={(e) => this.allEmr = e}
                    patientData={patientInfoDto}
                    isPrint={false}
                    // todyWaite={todyWaite}
                    // getTodayWaitInfo={null}
                    // clickPatientInfo={null}
                  />
                }
              </div>
              <div className={styles.DetailsBox_Right}>
                  {!!patientInfoDto &&
                    <ImageData
                      // emrLoading={emrLoading}
                      // key={imageKey}
                      // childKey={childImageKey}
                      patientData={patientInfoDto}
                      // tabKey={tabKey}
                      // callback={this.getChildImageKey}
                      // imgByDate={imgByDate}
                      // imgByDateCount={imgByDateCount}
                      // imgByClass={imgByClass}
                      // imgByClassCount={imgByClassCount}
                      // imageListParams={imageListParams}
                    />
                  }
              </div>
            </div>
          </Spin>
          <div className={styles.ClinicMornMeetFrom}>
            <Form>
              <div className={styles.ClinicMornMeetBox}>
                <div className={styles.ClinicMornMeetBoxTitle}>
                  <div className={styles.title}>
                    晨会意见
                  </div>
                  <div className={styles.ClinicMornMeetBoxTextArea}>
                    <TextArea
                      ref={this.refByTextArea}
                      style={{minHeight:'100px'}}
                      maxLength={300}
                      value={this.state.opinion}
                      placeholder={'请输入晨会意见'}
                      row={8}
                      onChange={(e)=>{
                        this.setState({ opinion:e.target.value })
                      }}
                    ></TextArea>
                  </div>
                </div>
                <div className={styles.ClinicMornMeetBoxContent}>
                  <Button
                    className={styles.Btn_ClinicMornMeetBoxContent}
                    type="primary"
                    onClick={this.saveOrUpdateOpinion}
                    loading={!!this.state.loadingBySaveOrUpdateOpinion}
                  >
                    提交
                  </Button>
                </div>
              </div>
              <div></div>
            </Form>
          </div>
      </div>
    )
  }
}
export default connect(({ClinicMornMeetModel}) => ({
  ClinicMornMeetModel,
}))(ClinicMorningMeeting);
