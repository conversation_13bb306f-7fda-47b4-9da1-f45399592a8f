import {<PERSON>, Col, Tabs, Button, Modal, message, Spin, Space, Table} from 'antd';
import React, { useState, useRef, Component } from 'react';
import { useIntl, FormattedMessage, history } from 'umi';
import { GridContent } from '@ant-design/pro-layout';
import styles from './style.less';//引入样式
import PatientInfo from '@/components/PatientInfo';  //右上角客户信息
import VisitToday from './components/visitToday'; //今日就诊左侧列表
import MedicalRecord from './components/medicalRecord'; //病历页面
import ImageData from './components/ImageData'; //影像资料
import { connect } from 'dva';
import moment from 'moment';
import { StringUtils } from "@/utils/StringUtils";//公共验证

const { TabPane } = Tabs;

class AllPanel extends Component {
  constructor(props) {
    super(props);
    this.Child = React.createRef();
    this.ChildList = React.createRef();
    this.state = {
      loading: false, //加载中
      toLoading: false, //加载中
      emrLoading: false, //分页加载更多
      emrNextStatus: true, //分页状态
      listHidden: false, //左侧边栏是否隐藏
      tabKey: '1', // 切换tab
      newKey: new Date() + Math.random(),//key值
      emrKey: Math.random(),//病历tab key值
      imageKey: Math.random(),//影像资料tab key值
      patientKey:Math.random(),//右上患者信息 key值
      currentTabKey: '1', //  tab的key
      isModalVisible: false,// 关联就诊病例弹窗状态
      clientHeight: document.documentElement.clientHeight, // 屏幕高度
      clickPatientInfo: {}, //今日就诊列表选中的患者信息
      childMedicalKey: 1, // 选中的tag 全部病历1、病历首页2
      childImageKey: 1, // | 按时间1、按类型2
      child: '', // 子组件的this

      medicalIndexParams: {
        // 病历首页参数
        patientId: props.location.state ? props.location.state.patientId : null, //患者标识
        tenantId: localStorage.getItem("tenantId"), //平台标识
      },
      patientInfo: null, //病历首页患者信息
      healthCondition: {
        previousHistory: '动脉硬化、起搏器、输血', //既往病史
        familyHistory: '否认', //家族史
        allergicHistory: '阿司匹林过敏', //过敏史
        inTaking: '头孢', //在服药物
        pregnant: '否认', //是否怀孕
        pregnantWeeks: '12', //怀孕周数
        readyForPregnancy: '否认', //是否备孕
        sucklingPeriod: '否认', //是否哺乳期
        contactLenses: '否认', //是否带隐形眼镜
        takingHormones: '否认', //是否服用避孕药或激素代替
        jointReplacement: '否认', //是否做过全关节置换手术
        timeOfOperation: '2020-02-13', //置换手术时间
        complication: '无', //关节置换手术导致的其他并发症
      }, //健康状况
      medicalRecords: [{ id: '1111' }], //病历记录
      // medicalCount: '', //病历记录总条数

      imgByClass: [], //按分类的数据
      imgByClassCount: 10, //按分类的条数
      imgByDate: [], //按时间的数据
      imgByDateCount: 10, //按时间的条数
      tenantId: '', //平台标识
      patientId: '', //患者id
      patientParams: {
        organizationId: localStorage.getItem("organizationId"), //机构ID
        patientId: props.location.state ? props.location.state.patientId : null, //患者标识
        tenantId: localStorage.getItem("tenantId"), //平台标识
      },
      todyWaite: {
        pageNum: 1, //当前页
        pageSize: 9999, //限制页
        tenantId: localStorage.getItem("tenantId"), //平台标识
        userId: localStorage.getItem("userId"), //当前登录医生标识
        visitStatus: 4, //就诊状态1未到诊、2到诊、3已接诊、4全部有效预约
        organizationId: localStorage.getItem("organizationId"), //JSON.parse(localStorage.getItem('organizationlnfo')).id,//机构id
        appointmentDates: [moment().format('YYYY-MM-DD')], //预约日期 date,当list.size=1时查询天数据，当list.size=2时按顺序安排start和end,前后包含
        sortRule: {sort: 2, rule: 2}//排序 正序

      },
      waitTodayData: [], //今日就诊列表
      waitCount: 0, //今日就诊数
      rightPatientInfos: {}, //右上方患者信息
      patientInfoDto: {}, //病历首页信息数据
      chooseMedicalRecord: {},//选择的就诊记录
      getemrsubid: "",//获取的小病历号
      recordloading:false,//就诊记录loading
      selectedRowKeys:[],//选择关联就诊记录key值
      chooseval:{},//选择的就诊记录
      totals:0,//就诊记录条数
      positions:1,//判断哪个页面引入的右上患者详情组件，判断样式
    };
    this.resize = this.resize.bind(this);//监听屏幕高度
  }
  // 初始化
  componentDidMount() {
    // console.log("哈哈哈哈==",JSON.stringify(this.state.selectedRowKeys))
    const { history } = this.props;
    if (typeof this.props.history.location.state == 'undefined') {
      this.getChildMedicalKey('1')// 获取子页面病历页的类型tag
    } else {
      var path = this.props.location.state;//上个页面获取的数据
      const { patientId, tenantId, patientData,
        //  status
      } = path;
      this.state.patientData = patientData;//上个页面获取的数据-患者信息
      this.setState({
          patientInfo: {
            patientId: patientId,
            ...patientData
          },//患者信息
          tenantId: tenantId,//平台标识
          patientData: patientData,//上个页面获取的数据-患者信息
          todyWaite: {
            ...this.state.todyWaite,
            // visitStatus: status,  // 跳转过来时的就诊状态
          },
          emrKey: Math.random()//病历tab key值
        }, () => {
          this.getTodayWaitInfo() //左侧就诊列表
          this.getChildMedicalKey('1',patientData)// 获取子页面病历页的类型tag
        }
      )

    }
    window.addEventListener('resize', this.resize); //监听屏幕高度
  }
  //在组件卸载及销毁之前直接调用
  componentWillUnmount() {
    window.removeEventListener('resize', this.resize); //监听屏幕高度
  }
  //监听事件
  resize() {
    this.setState({ clientHeight: document.documentElement.clientHeight }); //监听屏幕高度
  }
  // 获取子页面病历页的类型tag
  getChildMedicalKey = (value, data) => {
    this.setState({
      childMedicalKey: value,
    });
    this.getTopPatientInfo(data); //右上方患者信息

  };
  // 获取子页面影像资料的类型tag
  getChildImageKey = (value) => {
    this.setState({
      childImageKey: value,
    });
    // console.log(value, 'nmmmm子页面的keychildImageKey');
  };

  // tabs的回调函数
  callback = (key) => {

    let keyName = key === '1' ? 'emrKey' : 'imageKey';

    this.setState({
      [keyName]: Math.random(),//组件key
      tabKey: key,
    });
    if (key == '1') {  // 病历
      if (this.state.childMedicalKey == 1) {
        //this.getPatientOverview(); //患者信息 --右上方
      }
    }
  };

  /**今日就诊列表**/
  getTodayWaitInfo = () => {
    const { dispatch } = this.props;
    this.setState({ toLoading: true })
    let params = this.state.todyWaite;
    if (dispatch) {
      dispatch({
        type: 'homePage/todaySeePending',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            if (typeof this.props.history.location.state == 'undefined'
              && !this.state.patientInfo && res.content.resultList.length > 0) {

              this.getTopPatientInfo(res.content.resultList[0]);
              this.setState({
                patientInfo: res.content.resultList[0],
                patientData: res.content.resultList[0],
                toLoading: false,
                waitTodayData: res.content.resultList,
                waitCount: res.content.total,
              });
            } else {
              this.setState({
                toLoading: false,
                waitTodayData: res.content.resultList,
                waitCount: res.content.total,
              });
            }
          }
        },
      });
    }
  };
  // 查询右上方患者信息接口
  getTopPatientInfo = (data) => {
    const { dispatch } = this.props;
    const { patientParams } = this.state;
    patientParams.organizationId = localStorage.getItem("organizationId"); //机构ID
    patientParams.tenantId = localStorage.getItem("tenantId"); //平台标识
    patientParams.patientId = data ? data.patientId : patientParams.patientId ? patientParams.patientId : "";

    if (!patientParams.patientId) {
      return false;
    }
    let params = {
      ...this.state.patientParams,
    };

    if (dispatch) {
      dispatch({
        type: 'homePage/rightPatientInfo',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            // console.log("右上方患者信息===",res.content)
            this.setState({
              patientKey: Math.random(),
              rightPatientInfos: res.content.patientSignBaseDto.baseInfo,
              patientInfoDto: res.content.patientInfoDto,
            },()=>this.getAssociateList());

          }
        },
      });
    }
  };

  // 获取今日列表点击card的信息
  getCardItem = (info) => {
    // console.log("info", info);
    // 点击左侧卡片时 tab切换到病历的全部病历 页面中的操作都初始化
    this.setState({
        clickPatientInfo: info,
        tabKey: '1',
        patientInfo: info,
        patientData: info
      },
      () => {
        let data = {
          patientId: info.patientId,//患者标识
          organizationId: localStorage.getItem("organizationId"), //机构ID
          tenantId: localStorage.getItem("tenantId"), //平台标识
          ...info
        }
        //this.state.patientData.id = info.id;
        this.getChildMedicalKey('1', data); //子页面病历的key
      },
    );
  };
  //左侧就诊列表隐藏显示状态
  setLeftHidden = () => {
    const { listHidden } = this.state;
    this.setState({
      listHidden: !listHidden,
    });
  };

  // 关联就诊病例弹窗
  writeMedical = () => {
    this.setState({
      isModalVisible: true,
    });
  };
  //选择就诊记录确定
  handleOk = () => {
    if (this.state.chooseval.id) {
      // console.log('预约333===', this.child.state.chooseval);
      this.state.chooseMedicalRecord = this.state.chooseval;
      this.getemrSubId()
    } else {
      message.warning({
        content: '请选择就诊记录',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    // this.setState({
    //   isModalVisible: false,
    // });
  };
  //获取小病历号
  getemrSubId = () => {
    const { dispatch } = this.props;
    const { chooseMedicalRecord, patientData } = this.state;
    let params = {
      tenantId: chooseMedicalRecord.tenantId,//平台标识
      appointmentId: chooseMedicalRecord.id, //预约id
      patientId: chooseMedicalRecord.patientId, //患者标识
      patientName: chooseMedicalRecord.name, //患者姓名
      emrId: chooseMedicalRecord.fileNumber,//大病历号
      oftenTel: chooseMedicalRecord.oftenTel,//电话
      birthday: chooseMedicalRecord.birthday,//生日
      sex: chooseMedicalRecord.sex, //性别
      userName: localStorage.getItem('userName'),//当前用户名称
      userId: localStorage.getItem('userId') //当前用户id
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/generateEmrSubIdService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              getemrsubid: res.content
            })
            if (this.state.getemrsubid) {
              history.push({
                pathname: '/emr/MedicalCenter/CaseReport',
                state: {
                  // visitStatus: this.state.todyWaite.visitStatus,
                  patientData: patientData,
                  inTime: chooseMedicalRecord.inTime,
                  currentCure: chooseMedicalRecord.currentCure,
                  id: chooseMedicalRecord.id,
                  emrSubId: this.state.getemrsubid,
                  emrId: chooseMedicalRecord.fileNumber,
                  emrStatus: chooseMedicalRecord.emrStatus,
                  appointmentDate: chooseMedicalRecord.appointmentDate,
                  name: chooseMedicalRecord.name,
                  isFirstVisit: chooseMedicalRecord.isFirstVisit,
                  patientId: chooseMedicalRecord.patientId,
                },
              });
            }
          }
        },
      });
    }
  }
  //就诊记录弹窗关闭
  handleCancel = () => {
    this.setState({
      isModalVisible: false,
      selectedRowKeys:[],
      chooseval:{}
    });
  };
  //就诊记录列表
  getAssociateList=()=>{
    const {dispatch} = this.props;
    let recordparams={
      fileNumber:this.state.patientInfoDto.fileNumber,
      tenantId: localStorage.getItem('tenantId'),//平台标识
      organizationId: localStorage.getItem('organizationId'),//机构ID
      patientId:this.state.patientInfoDto.patientId,//患者标识
      userId:localStorage.getItem('userId'),//医生标识
      emrStatus:[1],//病历状态 1无病历，2未完成，3已完成，4已归档
      visitStatus:5, //就诊状态1未到诊、2到诊、3已接诊、4全部有效预约、5预约状态大于等于4的 预约记录
      sortRule: {sort: 2, rule: 1}
    }
    if(StringUtils.isBlank(recordparams.fileNumber)){

      return false;
    }

    this.setState({
      recordloading:true
    })
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/findByUserIdAndSearchService',
        payload: recordparams,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              totals:res.content.total,
              resultList:res.content.resultList,
              recordloading:false
            })

          } else {
            console.log("查询失败")
            this.setState({
              recordloading:false
            })
          }
        }
      });
    }
  }
  //点击记录列表事件
  chooseLine = (e) => {
    this.setState({
      selectedRowKeys:[e.id],
      chooseval:e
    })
  };

  // 请求病历首页--患者信息
  // getPatientOverview = () => {
  //   const { dispatch } = this.props;
  //   let params = this.state.medicalIndexParams;
  //   params.organizationId = localStorage.getItem("organizationId"); //机构标识
  //   // console.log(params, dispatch, '患者信息入参参数999---');
  //   if (dispatch) {
  //     dispatch({
  //       type: 'homePage/patientOverview',
  //       payload: params,
  //       callback: (res) => {
  //         // console.log(res, 'res99999999999------');
  //         // return
  //         // if (res.code == 200) {
  //         this.setState({
  //           patientInfo: res.content.patientSignBaseDto.baseInfo,
  //           emrKey: Math.random()
  //         });
  //         // }
  //       },
  //     });
  //   }
  // };




  // onRef = (ref) => {
  //   this.child = ref//接收子组件this
  //   console.log(ref,'子组件的ref')
  //   this.child.test();
  // }
  render() {
    const { listHidden, tabKey, imageKey, emrKey, isModalVisible, patientInfo, clickPatientInfo, childImageKey,patientKey,
      childMedicalKey, imgByDate, imgByClass, imgByClassCount, imgByDateCount, imageListParams,
      emrLoading, waitTodayData, rightPatientInfos, patientData, patientInfoDto, toLoading,resultList,recordloading,totals
      // todyWaite,
    } = this.state;
    //就诊记录
    const columns = [
      {
        title: '就诊时间',
        width:'35%',
        dataIndex: 'name',
        render: (text, record) => {
          return <span>{record.appointmentDate} {record.appointmentStart}-{record.appointmentEnd}</span>;
        },
      },
      {
        title: '主诉',
        width:'50%',
        dataIndex: 'currentCure',
      },
      {
        title: '距今时间',
        width:'15%',
        render: (text, record) => {
          return <span>{moment(new Date()).diff(moment(record.appointmentDate),'days')}天</span>;
        },
      },
    ];
    return (
      <GridContent className={styles.allcontentStyle} style={{ height: this.state.clientHeight }}>
        {/*<div style={{ position: 'absolute', top: 0, left: 0 }}>*/}
        {/*  <Menu />*/}
        {/*</div>*/}
        {/* <div> */}
        <Row >
          <Col span={listHidden ? 1 : 6} style={{ maxWidth: listHidden ? '1.16667%' : '25%' }}>
            <Space size="middle" className={styles.loading}>
              <Spin spinning={toLoading} />
            </Space>
            {/* 今日就诊列表 */}
            <VisitToday
              // todyWaite={todyWaite}
              getTodayWaitInfo={this.getTodayWaitInfo}
              waitTodayData={waitTodayData}
              setHidden={this.setLeftHidden}
              allPanelClick={this.getCardItem}
              patientData={patientData}
              // patientInfo={patientInfo}
              onRef={(ref) => (this.child = ref)}
            />
          </Col>
          <Col
            span={listHidden ? 23 : 18}
            style={{ maxWidth: listHidden ? '98.833%' : '75%', flex: 1 }}
          >
            <div style={{ padding: 16 }}>
              <div className={styles.topborder} key={patientKey}>
                <PatientInfo
                  position={this.state.positions}
                  patientId={this.state.clickPatientInfo.patientId}
                  rightPatientInfos={rightPatientInfos}
                  patientInfos={patientInfoDto}
                  onRef={(ref) => (this.child = ref)}

                />
                <div className={styles.AllbtnStyle}>
                  <Button type="primary" className={styles.btnStyle} disabled={!patientInfo} onClick={this.writeMedical.bind(this,)}>
                    写病历
                  </Button>
                  <Modal
                    maskClosable={false}
                    width={800}
                    title="关联就诊记录"
                    visible={isModalVisible}
                    onCancel={this.handleCancel}
                    footer={[
                      <Button onClick={this.handleCancel}>取消</Button>,
                      <Button disabled={totals==0} key="submit"  type="primary" onClick={this.handleOk}>确认</Button>,
                    ]}
                  >
                    <Table
                      className={styles.AssociatedStyle}
                      loading={recordloading}
                      rowKey={resultList => resultList.id}
                      rowSelection={{
                        type: 'radio',
                        selectedRowKeys:this.state.selectedRowKeys,
                        onSelect: this.chooseLine,
                      }}
                      pagination={false}
                      columns={columns}
                      dataSource={resultList}
                      onRow={(record) => ({
                        onClick: () => {
                          this.chooseLine(record)
                        },
                      })}
                    />
                  </Modal>
                  <Button disabled className={styles.btnStyle}>
                    开费用
                  </Button>
                  <Button disabled className={styles.btnStyle}>
                    做方案
                  </Button>
                </div>
              </div>
              <Tabs activeKey={tabKey} onChange={(key) => this.callback(key)}>
                <TabPane tab="病历" key="1">
                  <MedicalRecord
                    // todyWaite={todyWaite}

                    onRef={ref => this.child = ref}
                    emrLoading={emrLoading}
                    key={emrKey}
                    // getPatientOverview={this.getPatientOverview}
                    getTodayWaitInfo={this.getTodayWaitInfo}
                    getTopPatientInfo={this.getTopPatientInfo}
                    childKey={childMedicalKey}
                    tabKey={tabKey}
                    clickPatientInfo={clickPatientInfo}
                    patientInfo={patientInfo}
                    rightPatientInfos={rightPatientInfos}
                    patientInfoDto={patientInfoDto}
                    callback={this.getChildMedicalKey}
                  />
                </TabPane>
                <TabPane tab="影像资料" key="2">
                  <ImageData
                    emrLoading={emrLoading}
                    key={imageKey}
                    childKey={childImageKey}
                    patientData={patientData}
                    tabKey={tabKey}
                    callback={this.getChildImageKey}
                    imgByDate={imgByDate}
                    imgByDateCount={imgByDateCount}
                    imgByClass={imgByClass}
                    imgByClassCount={imgByClassCount}
                    imageListParams={imageListParams}
                  />
                </TabPane>
                <TabPane tab="收费记录" disabled key="3">
                  Content of Tab Pane 3
                </TabPane>
                <TabPane tab="治疗方案" disabled key="4"></TabPane>
                <TabPane tab="历史动态" disabled key="5"></TabPane>
                <TabPane tab="客户信息" disabled key="6"></TabPane>
              </Tabs>
            </div>
          </Col>
        </Row>
        {/* </div> */}
      </GridContent>
    );
  }
}

export default connect(
  ({ patientInfo, }) => ({
    patientInfo, //病历首页- 患者信息
  }),
)(AllPanel);
