import {findCheckClasses,ClassesInfoByCode,saveCheckClass,deleteCheckClass} from '@/services/ImageManage';
import {notification} from "antd";
const ImageCenterModel = {
  namespace: 'ImageManageModel',
  state: {
    ImageManageData:{},//影像类型列表
    ImageMsgData:{},//影像类型详情
    saveCheckClassData:{},//新增/编辑影像类型返回
    deleteCheckData:{},// 删除影像类型列表
    loading:false,//加载
    loadTip:"加载中",
  },
  //异步
  effects: {
    // 删除影像类型列表
    *deleteCheckService({payload, callback} , { call, put }) {
      const response = yield call(deleteCheckClass ,payload);
      yield put({
        type: 'deleteCheckInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    // 影像类型列表
    *ImageManageService({payload, callback} , { call, put }) {
      const response = yield call(findCheckClasses ,payload);
      yield put({
        type: 'ImageManageInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    // 影像类型详情
    *ClassesInfoService({payload, callback} , { call, put }) {
      const response = yield call(ClassesInfoByCode ,payload);
      yield put({
        type: 'ClassesInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //新增/编辑影像类型
    *saveCheckClassService({payload, callback} , { call, put }) {
      const response = yield call(saveCheckClass ,payload);
      yield put({
        type: 'saveCheckClassInfo',
        payload: response,
      });
      if (response.code === 200 || response.code === 500) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
  },

  //同步
  reducers: {
    deleteCheckInfo(state, action) {
      return {
        ...state,
        deleteCheckData: action.payload || {},
      };
    },
    ImageManageInfo(state, action) {
      return {
        ...state,
        ImageManageData: action.payload || {},
      };
    },
    ClassesInfo(state, action) {
      return {
        ...state,
        ImageMsgData: action.payload || {},
      };
    },
    saveCheckClassInfo(state, action) {
      return {
        ...state,
        saveCheckClassData: action.payload || {},
      };
    },
  },
};
export default ImageCenterModel;
