import { Socket } from "dgram";

/**
 * @return {string}
 */
function EnvAddressFun() {
  let EnvPath = '';
  let StaticPath = '';
  let PlugPath = '';
  let AllPath;
  // 市场化登录
  let PlatformEnvPath = '';
  let MarketEnvPathB = '';
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line default-case
    switch (process.env.ENV_PATH) {
      // 市场化版本各环境配置
      case 'api36':
          (PlatformEnvPath = 'https://dental-dev.friday.tech/Platform');
          (MarketEnvPathB = 'https://dental-dev.friday.tech/apiB');
          (EnvPath = 'https://dental-dev.friday.tech/api');
          (StaticPath = 'https://dental-dev.friday.tech');
        break;
      case 'api37':
          (PlatformEnvPath = 'https://dental-test.friday.tech/Platform');
          (MarketEnvPathB = 'https://dental-test.friday.tech/apiB');
          (EnvPath = 'https://dental-test.friday.tech/api');
          (StaticPath = 'https://dental-test.friday.tech');
        break;
      case 'api38':
          (PlatformEnvPath = 'https://dental-virtual.friday.tech/Platform');
          (MarketEnvPathB = 'https://dental-virtual.friday.tech/apiB');
          (EnvPath = 'https://dental-virtual.friday.tech/api');
          (StaticPath = 'https://dental-virtual.friday.tech');
        break;
      case 'api39':
        (PlatformEnvPath = 'https:/dental.friday.tech/Platform');
          (MarketEnvPathB = 'https://dental.friday.tech/apiB');
          (EnvPath = 'https://dental.friday.tech/api');
          (StaticPath = 'https://dental.friday.tech');
        break;
    }
    AllPath = {

      // 调用B端接口
      '/apiB/uaa': {
        target: `${MarketEnvPathB}/uaa`,
        changeOrigin: true,
        pathRewrite: { '^/apiB/uaa': '' },
      },

      // 图片
      "/img": {
        "target": `${StaticPath}`,
        "changeOrigin": true,
      },
      // 获取token
      "/api/businessusers": {
        "target": `${EnvPath}/businessusers`,
        // "target": "http://10.1.33.95:9002",
        // "target": 'http://10.1.27.149:5002',        // 九峰本地
        "changeOrigin": true,
        "pathRewrite": { "^/api/businessusers": "" }
      },
      // 资源管理
      "/api/resource": {
        "target": `${EnvPath}/resource`,
        // "target": "http://10.1.27.43:5003/",   // 时均瑶本地
        // "target": "http://10.1.27.146:5003/",   // 魏鹏本地
        // "target": "http://10.1.33.126:9003",   //王少彬本地
        // "target": "http://10.1.27.208:5003",// 孙伟 客户管理本地
        // "target": "http://10.1.33.154:9032/resource",   //王少彬
        // "target": "http://10.1.27.59:5003",// 韦铭博 客户管理本地
        "changeOrigin": true,
        "pathRewrite": { "^/api/resource": "" }
      },

      // 数据看板(曹萌萌)
      "/api/medical": {
        "target": `${EnvPath}/medical`,
        // "target": "http://10.1.27.210:5006/",     // 曹萌萌本地
        // "target": "http://10.250.0.110:5006/",     // 曹萌萌本地（家）
        // "target": "http://10.1.33.245:9006/ ",
        // "target": "http://10.1.33.136:9003/",     // 王得众本地
        "changeOrigin": true,
        "pathRewrite": { "^/api/medical": "" }
      },
      // "/api/upload": {
      //   "target": "http://10.1.32.49:5006",
      //   // "target": "http://10.1.33.245:9032/",
      //   "changeOrigin": true,
      //   "pathRewrite": { "^/api/upload": "" }
      // },
      // 数据看板(孙伟) 保险值付/ + 结账单
      "/api/settlement": {
        "target": `${EnvPath}/settlement`,
        // "target": "http://10.250.0.186:9008/", // 时均瑶本地
        // "target": "http://10.250.0.122:9008/",   //王少斌本地
        // "target": "http://10.1.33.162:9008/",   //雅文本地
        // "target": "http://10.1.33.118:9008/",     // 李博本地
        // "target": "http://192.168.3.77:9008/",     // 赵志君本地
        // "target": "http://192.168.3.78:9008/",     // 曹萌萌本地
        // "target": "http://10.1.33.162:9008/",
        "changeOrigin": true,
        "pathRewrite": { "^/api/settlement": "" }
      },
      // 优秀病例、专家指导
      "/api/dmp-server": {
        "target": `${EnvPath}/dmp-server`,
        // "target": `http://192.168.1.17:6008`,      // 时俊瑶
        "changeOrigin": true,
        "pathRewrite": { "^/api/dmp-server": "" }
      },
      // 优秀病例\专家指导绑定服务
      "/api/dmp-user": {
        "target": `${EnvPath}/dmp-user`,
        // "target": "http://192.168.1.17:6001/",
        "changeOrigin": true,
        "pathRewrite": { "^/api/dmp-user": "" }
      },
      // 数据看板(陈力)
      "/api/appointment": {
        "target": `${EnvPath}/appointment`,
        // "target": "http://10.1.27.149:5004/",   //潘九峰本地、
        // "target": "http://10.1.33.95:9004/",   //时均瑶本地
        // "target": "http://10.1.27.136:5004",   //曹萌萌本地
        // "target": "http://10.1.33.126:9004",   //王少彬本地
        //  "target": "http://10.1.33.177:9004",// 陈力本地
        // "target": "http://10.1.33.177:9004", // 本地
        // "target": "http://10.1.33.136:9004", // 德众本地
        // "target": "http://10.1.33.136:9007",     // 王得众 本地
        "changeOrigin": true,
        "pathRewrite": { "^/api/appointment": "" }
      },
      // 通知列表(王邵斌)
      "/api/customer": {
        "target": `${EnvPath}/customer`,   // 开发10.1.33.118:5006
        // "target": "http://10.1.33.118:9009",     // 本地
        // "target": "http://10.1.33.136:9007",     // 王得众 本地
        "changeOrigin": true,
        "pathRewrite": { "^/api/customer": "" }
      },
      // 患者管理
      "/api/patient": {
        "target": `${EnvPath}/patient`,
        // "target": "http://10.1.27.146:5005",
        "changeOrigin": true,
        "pathRewrite": { "^/api/patient": "" }
      },
      // (孙伟 福利本地)
      "/api/locality": {
        "target": `${EnvPath}/locality`,// 开发
        // "target": "http://10.1.33.112:9008",// 孙伟 福利本地
        // "target": "http://10.1.33.95:9003", // 时均瑶 历史记录
        // "target": "http://10.1.33.154:9003", // 时均瑶 历史记录
        "changeOrigin": true,
        "pathRewrite": { "^/api/locality": "" }
      },
      // (孙伟 客户管理)
      "/api/customerManage": {
        "target": `${EnvPath}/customerManage`,// 开发
        // "target": "http://10.1.27.57:8080",// 孙伟 客户管理本地
        // "target": "http://10.1.27.149:8080",// 潘九峰 客户管理本地
        // "target": "http://10.1.27.44:8080",// 王少彬 客户管理本地
        // "target": "http://10.1.27.200:8080",// 张志军 客户管理本地
        // "target": "http://10.1.27.60:8080",// 韦铭博 客户管理本地
        // "target": "http://10.1.27.210:8080",// 曹萌萌 客户管理本地

        "changeOrigin": true,
        "pathRewrite": { "^/api/customerManage": "" }
      },
      // (常英杰 设置本地)
      "/api/customerServiceSet": {
        "target": `${EnvPath}/locality`,// 开发
        // "target": "http://10.1.33.100:5003",// 常英杰 福利本地
        "changeOrigin": true,
        "pathRewrite": { "^/api/customerServiceSet": "" }
      },
      // 雅文报表
      "/api/reports": {
        "target": `${EnvPath}/reports`,
        "changeOrigin": true,
        "pathRewrite": { "^/api/reports": "" }
      },
      // 保险（常）
      "/api/welfare-center": {
        "target": `${EnvPath}/welfare-center`,
        // "target": "http://10.1.27.146:3112", //魏鹏
        "changeOrigin": true,
        "pathRewrite": { "^/api/welfare-center": "" }
      },
      // 保险日志（常）
      "/api/object-logger": {
        "target": `${EnvPath}/object-logger`,
        "changeOrigin": true,
        "pathRewrite": { "^/api/object-logger": "" }
      },
      // 400 设置（魏）
      "/api/apply": {
        "target": `${EnvPath}/apply`,
        // "target": "http://10.1.27.182:9001", // 时俊瑶
        "changeOrigin": true,
        "pathRewrite": { "^/api/apply": "" }
      },
      "/PlugPath": {
        "target": "http://static.5i5ya.com", // 时均瑶 历史记录
        "changeOrigin": true,
        "pathRewrite": { "^/PlugPath": "" }
      },
      "/MS": {
        "target": `${StaticPath}`,
        "changeOrigin": true,
        "pathRewrite": { "^/MS": "" }
      },
      // 配置平台端调用
      "/Platform/auditing": {
        "target": `${PlatformEnvPath}/auditing`,
        "changeOrigin": true,
        "pathRewrite": { "^/Platform/auditing": "" }
      },
      '/api/fri-uc': {
        target: `${EnvPath}/fri-uc`,
        // target: `${MpEnvPath}/fri-uc`,
        pathRewrite: { "^/api/fri-uc": "" },
        "changeOrigin": true,
      },
      // 配置病历质检
      '/api/emr': {
        target: `${EnvPath}/emr`,
        // target: `${MpEnvPath}/fri-uc`,
        pathRewrite: { "^/api/emr": "" },
        "changeOrigin": true,
      },
    }
  } else {
    // 插件地址
    PlugPath = 'https://static.5i5ya.com';
    AllPath = {
      "/PlugPath": {
        "target": `${PlugPath}`,
        "changeOrigin": true,
        "pathRewrite": { "^/PlugPath": "" }
      }
    }
  }
  return AllPath;
}
const EnvAddress = EnvAddressFun();
export default EnvAddress;
