import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { Input, Icon, AutoComplete,Form } from 'antd';
import classNames from 'classnames';
import Debounce from 'lodash-decorators/debounce';
import Bind from 'lodash-decorators/bind';
import styles from './index.less';
import { postSeanchData, getSeanchData } from '@/services/api';
import $ from 'jquery'

const Item = Form.Item;
const Option = AutoComplete.Option;
const Search = Input.Search;

function searchResult(value) {
  return value.map((item, idx) => {
    return item.item;
  });
}
function renderOption(item, idx) {
  return (
    <Option key={item} value={item} text={item}>
      {item}
    </Option>
  );
}
function renderOption2(item, idx) {
  return (
    <Option key={item.id} value={item.id+''} text={item.diagnosisName}>
      {item.diagnosisName}
    </Option>
  );
}

const validate = {
  validateStatus: 'error',
  errorMsg: 'The prime between 8 and 12 is 11!',
}

@Form.create()
export default class HeaderSearch extends PureComponent {
  static propTypes = {
    className: PropTypes.string,
    placeholder: PropTypes.string,
    onSearch: PropTypes.func,
    onPressEnter: PropTypes.func,
    defaultActiveFirstOption: PropTypes.bool,
    dataSource: PropTypes.array,
    defaultOpen: PropTypes.bool,
    keywordsUrl: PropTypes.string,
    query: PropTypes.object,
    keyName: PropTypes.string,
    method: PropTypes.string,
    searchValidateStatus:PropTypes.object,
    addonBefore:PropTypes.any,
    allowClear:PropTypes.bool
  };

  static defaultProps = {
    defaultActiveFirstOption: false,
    onPressEnter: () => { },
    onSearch: () => { },
    className: '',
    placeholder: '',
    dataSource: [],
    defaultOpen: false,
    keywordsUrl: '',
    method: '',
    query: {},
    keyName: 'GlobalSearch',
    searchValidateStatus:{
      validateStatus: 'success',
      errorMsg: null,
    },
    addonBefore:null,
    allowClear:false
  };

  constructor(props) {
    super(props);
    this.state = {
      value: '',
      dataSource: [],
    };
  }

  componentWillUnmount() {
    clearTimeout(this.timeout); // 组件销毁时释放
  }

  onSearch = (value) => {
    console.info('onSearch--- ',value);
    // 去掉首尾空格
    const ValueEnds=value && value.replace(/(^\s*)|(\s*$)/g, "");
    const { onSearch } = this.props;
    this.timeout = setTimeout(() => {
      if (this.selectValue) {
        onSearch(this.selectValue, this.selectName); // 异步输出input中的值
      } else {
        onSearch(ValueEnds, this.selectName); // 异步输出input中的值
      }
      this.selectValue = null;
    }, 200);
  };

  onChange = value => {
    console.log('123123 onChange::: ',value);
    const { onChange, query ,styleType} = this.props;
    const { keywordsUrl,method } = this.props;

    // 去掉首尾空格
    const ValueEnds=value && value.replace(/(^\s*)|(\s*$)/g, "");
    let valuedata = {
      queryText: ValueEnds,
      ...query,
    };

    if (!ValueEnds) {
      this.clearValue()
    }else {

      if (keywordsUrl) {
        if (method == "GET") {

          if (styleType  === 1 ) {
            getSeanchData(`${keywordsUrl}/${ValueEnds}`).then((re) => {
              let data = [];
              if (re && re.content) {
                data = re.content.filter(item => item);
              }
              this.setState({
                dataSource: data
              })
            })
          }else {
            getSeanchData(`${keywordsUrl}?search=${ValueEnds}&name=${ValueEnds}`).then((re) => {
              let data = [];
              if (re && re.content) {
                data = re.content.filter(item => item);
              }
              this.setState({
                dataSource: data
              })
            })
          }

        } else {
          postSeanchData(keywordsUrl, valuedata).then((re) => {
            let data = [];
            if (re && re.content) {
              data = re.content.filter(item => item);
            }
            this.setState({
              dataSource: data
            })
          })
        }
      }
    }

    this.setState({ value:ValueEnds });
    if (onChange) {
      onChange(ValueEnds, this.selectName);
    }
  };

  /**
   * 关联联想数据
   */
  handleSearch = value => {
    //this.onSearch(value)
  };

  /**
   * 清空联想词和Value方法
   */
  clearValue=()=>{
    /*this.setState({
      value: '',
      dataSource: [],
    })*/

    let form = this.props.form
    if(!!form.getFieldValue('searchInput')) {
      form.setFieldsValue({
        [`searchInput`]: '',
      })
      this.setState({
        value: '',
        dataSource: [],
      }, () => {
        /*if (!notRefresh) {
          this.onSearch('')
        }*/
      })
    }

  }

  onSearchFocus=()=>{}

  onSearchBlur=()=>{
    this.clearValue()
  }
  //设置搜索框默认值
  setvalue = (value) => {
    let form = this.props.form
    form.setFieldsValue({
      searchInput: value,
    })
    this.setState({
      value,
    })
  }
  render() {
    const { className, placeholder, keyName, source,addonBefore,allowClear, ...restProps } = this.props;
    const { value, dataSource } = this.state;
    const { getFieldDecorator } = this.props.form;
    delete restProps.defaultOpen;
    const {validateStatus,errorMsg,hasFeedback} = this.props.searchValidateStatus

    return (
      <span className={classNames(className, styles.headerSearch)}>
        <Form>
          <Item
            validateStatus={validateStatus}
            help={errorMsg}
            hasFeedback={hasFeedback || false}
          >
            {getFieldDecorator('searchInput',{
              initialValue:''
            })(
              <AutoComplete
                key={keyName}
                {...restProps}
                //value={value}
                backfill={true}
                onChange={this.onChange}
                onSearch={this.handleSearch}
                onSelect={(value, option) => {
                  this.selectValue = value;
                  this.selectName = option.props.text;
                }}
                dataSource={dataSource.map(source === 'mengmeng' ? renderOption2 : renderOption)}
                allowClear={allowClear}
              >
                <Search
                  id='GlobalSearch'
                  className={'searchInput'}
                  ref={node => {
                    this.input = node;
                  }}
                  /*onBlur={(value)=>{
                    this.onSearchBlur()
                  }}*/
                  onFocus={(value)=>{
                    this.onSearchFocus()
                  }}
                  addonBefore={addonBefore}
                  aria-label={placeholder}
                  placeholder={placeholder}
                  onSearch={this.onSearch}
                />
              </AutoComplete>
            )}
          </Item>
        </Form>
      </span>
    );
  }

  componentDidMount(){
    this.props.onRef && this.props.onRef(this)
    $('#GlobalSearch').attr({'autocomplete':'off'});
  }
}
