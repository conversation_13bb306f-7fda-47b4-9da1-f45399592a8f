import React, {Component} from 'react';
import * as echarts from "echarts";
import Immutable from 'immutable';
import $ from "jquery";

// eslint-disable-next-line @typescript-eslint/no-unused-vars

class Index extends Component {
  constructor(props) {
    super(props);
    this.state={
      data:[],         // 图例数据
      pieText:'',      // 图列title
      type:null,       // 判断数据是否改变更新渲染图例
    }
    this.myChart = null;
    this.myRefPie = React.createRef()
  }

  static getDerivedStateFromProps( props,state ) {
    if(!Immutable.is(Immutable.fromJS(props.data), Immutable.fromJS(state.data)) || !Immutable.is(Immutable.fromJS(props.pieText), Immutable.fromJS(state.pieText))){
      return {
        data:props.data,
        pieText:props.pieText,
        type:'1'
      }
    }
    return null
  }

  componentDidMount() {
    const { data,pieText } = this.state
    const chartDom = this.myRefPie.current ;
    const { EventClickPie }  = this.props
    this.myChart=echarts.init(chartDom)
    const option = {
      tooltip: {
        trigger: 'item',
        show: false,
      },
      title:{
        show: true,
        text:pieText,
        top: 'center',
        left: 'center',
        textStyle:{
          width: 40,
          overflow: 'breakAll',
          fontSize:12,
        },
        triggerEvent:true,
      },
      legend: {
        show: false,
      },
      series:[
        {
          type:'pie',
          radius:['90%', '80%'],
          avoidLabelOverlap: false,
          silent:true,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            scale:false,
          },
          legendHoverLink:false,
          labelLine: {
            show: false
          },
          data:data && data[0] && (data[0][0]!==0 || data[0][1]!==0)?data[0]:[0],
          itemStyle: {
            normal: {
              color(params) {
                const colorList = data && data[0] && (data[0][0]!==0 || data[0][1]!==0)?['#6AE89D','#EDF2F9',]:['#EDF2F9'];
                return colorList[params.dataIndex]
              },
            }
          },
        },
        {
          type:'pie',
          radius:['70%', '60%'],
          avoidLabelOverlap: false,
          silent:true,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            scale:false,
          },
          legendHoverLink:false,
          labelLine: {
            show: false
          },
          data:data && data[1] && (data[1][0]!==0 || data[1][1]!==0)?data[1]:[0],
          itemStyle: {
            normal: {
              color(params) {
                const colorList =data && data[1] && (data[1][0]!==0 || data[1][1]!==0)? ['#6FADFF','#EDF2F9']:['#EDF2F9'];
                return colorList[params.dataIndex]
              },
            },
          },
        },
        {
          type:'pie',
          radius:['50%', '40%'],
          avoidLabelOverlap: false,
          silent:true,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            scale:false,
          },
          legendHoverLink:false,
          labelLine: {
            show: false
          },
          data:data && data[2] && (data[2][0]!==0 || data[2][1]!==0)?data[2]:[0],
          itemStyle: {
            normal: {
              color(params) {
                const colorList =data && data[2] && (data[2][0]!==0 || data[2][1]!==0)? ['#8985FF','#EDF2F9']:['#EDF2F9'];
                return colorList[params.dataIndex]
              },
            },
          },
        }
      ]
    };
    this.myChart.clear();
    option && this.myChart.setOption(option);
    this.myChart.on('click', (params)=> {
      EventClickPie && EventClickPie()
    });
  }

  componentDidUpdate() {
    const { data,pieText,type,Radius } = this.state
    if(type==='1'){
      this.setState({
        type:null
      })
      const series=[]
      if(data && JSON.stringify(data)!=='[]'){
        const arr=[['90%', '80%'],['70%', '60%'],['50%', '40%']]
        data.map((items,i) =>{
          const obj={
            radius:Radius && JSON.stringify(Radius)!=='[]'? Radius[i]:arr[i],
            data:items,
            itemStyle: {
              normal: {
                color(params) {
                  const colorList =items && (items[0]!==0 || items[1]!==0)? [['#6AE89D','#EDF2F9'],['#6FADFF','#EDF2F9'],['#8985FF','#EDF2F9']]:['#EDF2F9','#EDF2F9'];
                  return items && (items[0]!==0 || items[1]!==0)?colorList && colorList[i] && colorList[i][params.dataIndex]:colorList[params.dataIndex]
                },
              },
            },
          }
          series.push(obj)
        })
      }
        this.myChart.setOption({
          title:{
            text:pieText
          },
          series,
        })
    }
  }

  render() {
    const { width= 300,height= 300 } = this.props
    return (
      <div>
        <div ref={this.myRefPie} style={{width, height}} />
      </div>
    );
  }
}

export default Index;
