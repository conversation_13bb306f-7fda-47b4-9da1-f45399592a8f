import request from '@/utils/request';
//获取就诊记录
export async function findByUserIdAndSearchFiled(params) {
  return request('/api/emr/externalCall/findByUserIdAndSearchFiled', {
    method: 'POST',
    data: params,
  });
}

//写病历完成
export async function saveEmr(params) {
  return request('/api/emr/med/saveEmr', {
    method: 'POST',
    data: params,
  });
}

//词条联想查询
export async function findWordsByKeyWord(params) {
  return request('/api/emr/system/findWordsByKeyWord', {
    method: 'POST',
    data: params,
  });
}
//诊断词联想查询
export async function findDiagByKeyWord(params) {
  return request('/api/emr/system/findDiagByKeyWord', {
    method: 'POST',
    data: params,
  });
}
//基础治疗联想查询
export async function findTreatByKeyWord(params) {
  return request('/api/emr/system/findTreatByKeyWord', {
    method: 'POST',
    data: params,
  });
}
//根据预约获取emrSubId
export async function generateEmrSubId(params) {
  return request('/api/emr/med/generateEmrSubId', {
    method: 'POST',
    data: params,
  });
}
//查询单个病历详情
export async function getEmrInfoBySubId(params) {
  return request('/api/emr/med/getEmrInfoBySubId', {
    method: 'POST',
    data: params,
  });
}
//获取病历关联的一般检查
export async function findGenExamsByEmrSubId(params) {
  return request('/api/emr/med/findGenExamsByEmrSubId', {
    method: 'POST',
    data: params,
  });
}
//获取病历关联的牙周检查
export async function findPreExamsByEmrSubId(params) {
  return request('/api/emr/med/findPreExamsByEmrSubId', {
    method: 'POST',
    data: params,
  });
}

//获取一般检查配置项
export async function findToothExamCache(params) {
  return request('/api/emr/system/findToothExamCache', {
    method: 'POST',
    data: params,
  });
}

//获取影像库列表-按时间分组
export async function findCheckImgsByDate(params) {
  return request('/api/emr/check/findCheckImgsByDate', {
    method: 'POST',
    data: params,
  });
}

//单连接转换cdn方式访问图片
export async function dbPathTransform(params) {
  return request('/api/medical/aliyunOssApi/dbPathTransform', {
    method: 'POST',
    params: params,
  });
}

//查询关联同意书
export async function findLinkMrcs(params) {
  return request('/api/emr/med/findLinkMrcs', {
    method: 'POST',
    data: params,
  });
}

//查询关联影像
export async function findLinkImgs(params) {
  return request('/api/emr/med/findLinkImgs', {
    method: 'POST',
    data: params,
  });
}

//关联影像-全部病历里
export async function saveLinkImgs(params) {
  return request('/api/emr/med/saveLinkImgs', {
    method: 'POST',
    data: params,
  });
}

//关联知情同意书-全部病历里
export async function saveEmrMrc(params) {
  return request('/api/emr/med/saveLinkMrcs', {
    method: 'POST',
    data: params,
  });
}

//获取我的模板下面的模板分类
export async function findMyTempContentClass(params) {
  return request('/api/emr/med/findMyTempContentClass', {
    method: 'POST',
    data: params,
  });
}
