import React, {Component} from 'react';
import {Row, Col, Form, Select, Icon, message,} from 'antd';
import {connect} from "dva";
import styles from './index.less'
import PropTypes from 'prop-types';
const Option = Select.Option;
@connect(({ registerAndArrival, loading }) => ({
  registerAndArrival, loading,
}))
class Index extends Component {

  static propTypes = {
    parentBoxId:PropTypes.string,      // 患者id
  }
  static defaultProps = {
    parentBoxId: 'getPatientSourceId',
  }

  constructor() {
    super();
    this.state={
      sourceData:[]   //地区市场渠道和小牙医活动数据
    }
  }
  componentDidMount() {
    this.getList()
  }
  //获取地区市场渠道和小牙医活动数据
  getList = () => {
    const { dispatch,getOrganizationCity } = this.props;
    dispatch({
      type: 'registerAndArrival/getCustomerWayTree'
    }).then((res) => {
      let code = res.code;
      if (res && res.code===200) {
        const {content}=res
        this.setState({
          sourceData:content && content.firstVOList?content.firstVOList:[],
        })
        let city=content && content.city?content.city:null
        getOrganizationCity(city)
      }else{
        message.error('获知方式获取失败，请刷新浏览器重试！')
      }
    }).catch((error) => {
      message.error('获知方式获取失败，请刷新浏览器重试！')
      this.setState({
        CodeType: 0,
      })
    });
  };
  //地区市场渠道选择清空小牙医活动
  ChangePatientSource=()=>{
    const { setFields} = this.props.Form;
    setFields({
      getActivity: {
          value:undefined,
          errors: null,
        },
      })
  }
  render() {
    //defaultValue
    const { getFieldDecorator, getFieldValue } = this.props.Form;
    const { defaultValue,newCustomerInfo, height} = this.props;
    const {sourceData}=this.state
    const tenantId=localStorage.getItem('tenantId')
    console.log(tenantId=='77057aed269f4a14957ae0ad0eff359a','tenantId',tenantId)
    return (
      <Row className={height?styles.getPatientSourceBox:null} id='getPatientSourceId'>
        <Col span={newCustomerInfo?12:6}>
          <Form.Item label={newCustomerInfo?'获知方式':false} colon={!newCustomerInfo}>
            {getFieldDecorator('getPatientSource', {
              initialValue: defaultValue && defaultValue.getCustomerWayForm && defaultValue.getCustomerWayForm.firstName? defaultValue.getCustomerWayForm.firstName :defaultValue && defaultValue.informedFirstName?defaultValue.informedFirstName: undefined,
              rules: [{
                required: tenantId=='77057aed269f4a14957ae0ad0eff359a'?true:false, message: '请输入获知方式',
              },
              ],
            })(<Select
              showSearch
              placeholder="请选择"
              suffixIcon={<Icon type="caret-down" />}
              defaultActiveFirstOption={false}
              onChange={this.ChangePatientSource}
              getPopupContainer={() => document.getElementById(this.props.parentBoxId)}
              style={{width: '100%'}}
            >
               {sourceData &&JSON.stringify(sourceData)!='[]' &&  sourceData.map((item,i) => {
                 return (
                   <Option value={item.firstName} key={i}>{item.firstName}</Option>)
               })
              }
            </Select>)}
          </Form.Item>
        </Col>
        <Col span={newCustomerInfo?12:18} style={newCustomerInfo?{paddingLeft:30}:null}>
          <Form.Item>
            {getFieldDecorator('getActivity', {
              initialValue: defaultValue && defaultValue.getCustomerWayForm && defaultValue.getCustomerWayForm.secondName? defaultValue.getCustomerWayForm.secondName : defaultValue && defaultValue.informedSecondName?defaultValue.informedSecondName:undefined,
            })(<Select
              showSearch
              placeholder="请选择"
              suffixIcon={<Icon type="caret-down" />}
              defaultActiveFirstOption={false}
              getPopupContainer={() => document.getElementById(this.props.parentBoxId)}
              style={{width: '100%'}}
            >
               {sourceData &&JSON.stringify(sourceData)!='[]' &&  sourceData.map((item,i) => {
                 if(getFieldValue('getPatientSource')===item.firstName && item.secondVOList && JSON.stringify(item.secondVOList)!='[]' ){
                   return  item.secondVOList.map((val,indexs)=>{
                      return(
                        <Option value={val.secondName} key={indexs}>{val.secondName}</Option>
                      )
                   })
                 }
               })
              }
            </Select>)}
          </Form.Item>
        </Col>
      </Row>
    );
  }
}

export default Index;
