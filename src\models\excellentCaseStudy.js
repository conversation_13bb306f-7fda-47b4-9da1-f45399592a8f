import {
  getExcellentCaseList,
  getOrgExcellentCaseDict,
  getExcellentCaseInfo
} from '@/services/excellentCaseStudy';


export default {
  namespace: 'excellentCaseStudy',
  state: {
    searchKey: '', // 搜索关键字
    achievementList: [], // 病历成就
    depSubjectDictList: [], // 科室字典
    difficultLevelDictList: [], // 难度等级
    startDate: null, // 开始日期
    endDate: null, // 结束日期
  },

  effects: {
    // 分页获取优秀病历数据
    *getExcellentCaseList({ payload }, { call }) {
      const response = yield call(getExcellentCaseList, payload);
      return response;
    },
    // 获取字典项数据（筛选项数据）
    *getOrgExcellentCaseDict({ payload }, { call }) {
      const response = yield call(getOrgExcellentCaseDict, payload);
      return response;
    },


    // // 获取优秀病历信息详情
    // *getExcellentCaseInfo({ payload }, { call }) {
    //   const response = yield call(getExcellentCaseInfo, payload);
    //   return response;
    // },
  },

  reducers: {
    // 更新状态值数据
    setTaskListState(state, { payload }) {
      return {
        ...state,
        ...payload
      }
    },
    
    // 清空数据
    clean(state, { payload }){
      return {
        ...state,
        searchKey: '', // 搜索关键字
        achievementList: [], // 病历成就
        depSubjectDictList: [], // 科室字典
        difficultLevelDictList: [], // 难度等级
        startDate: null, // 开始日期
        endDate: null, // 结束日期
      }
    }
  },
  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, search }) => {
        if (!(/\/excellentCaseStudy\/index/.test(pathname) || /\/excellentCaseStudy\/details/.test(pathname))) {
          dispatch({
            type: "clean",
            payload: {}
          })
        }
      })
    }
  }
};
