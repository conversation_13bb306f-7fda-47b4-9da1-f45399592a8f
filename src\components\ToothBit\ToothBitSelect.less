@import '~antd/lib/style/themes/default.less';

.ToothBitEmpty{
  width: 20px;
  height: 20px;
}

.ToothBitL{
  width: 30px;
  background: #1296DB;
  line-height: 30px;
  text-align: center;
}

.ToothBit {
  :global {
    table {
      border-collapse: collapse;
      cursor: pointer;
    }
    table td {
      border-top: 0;
      //border-right: 1px solid black;
      border-bottom: 1px solid black;
      border-left: 0;
      padding: 1px;
      //.clearfix()
      /*min-width: 20px;
      min-height: 20px;*/
    }
    table tr:last-child td {
      border-bottom: 0;
      //.clearfix()
    }
    table tr td:last-child {
      border-right: 0;
      //.clearfix()
    }
  }
}

.teethNumberSelect {
  background: #4BA2FF;
  color: #fff;
  border-radius: 15px;
  border:1px soild #4BA2FF;
  :global {
    table td {
      border-bottom: 1px solid #fff;
    }
  }
}

.teethNumber {
  position: relative;
  left: 0.3px;
  //top:-0.6px;
}

.tableBorder {
  border-right:1px solid black;
}

.ToothBitS{
  text-align: center;
  line-height: 20px;
  width: 20px;
  height: 20px;
  float: left;
  margin: 2px 2px 2px 2px;
  display: inline-block;
  border-radius: 15px;

  .ToothBitS0NE{
    position: relative;
    top: -5px;
    left: -1px;
    -webkit-transform: scale(0.75);
    font-size:10px;
    display: inline-block;
    z-index: 999;
    div{
      flex: 1;
      width: 10px;
      height: 10px;
      font-size: 10px;
    }
  }
}



