import request from '@/utils/request';

/**医生列表**/
export async function findSynStatus(params) {
  return request(`/api/emr/system/findSynStatus`, {
    method: 'POST',
    data: params,
  });
}
/**同步诊断**/
export async function synSystemStdDiag(params) {
  return request(`/api/emr/system/synSystemStdDiag`, {
    method: 'POST',
    data: params,
  });
}
// /**平台诊断包**/
// export async function findSystemDiags(params) {
//   return request(`/api/emr/system/findSysStdDiags`, {
//     method: 'POST',
//     data: params,
//   });
// }
// /**平台诊断包**/
// export async function findSysStdWord(params) {
//   return request(`/api/emr/system/findSysStdWord`, {
//     method: 'POST',
//     data: params,
//   });
// }
/**同步词条**/
export async function synSystemStdWord(params) {
  return request(`/api/emr/system/synSystemStdWord`, {
    method: 'POST',
    data: params,
  });
}
/**同步书写模板**/
export async function synSystemEmrTmpt(params) {
  return request(`/api/emr/system/synSystemEmrTmpt`, {
    method: 'POST',
    data: params,
  });
}
/**同步同意书模板**/
export async function synSystemMrcTmpt(params) {
  return request(`/api/emr/system/synSystemMrcTmpt`, {
    method: 'POST',
    data: params,
  });
}


