import { Tabs } from 'antd';
import React, { useState, useRef, Component } from 'react';
import { useIntl, FormattedMessage } from 'umi';
import { GridContent } from '@ant-design/pro-layout';
import styles from './style.less';//样式
const { TabPane } = Tabs;
import MainSuit from "./components/MainSuit";//主诉
import HistoryPresent from "./components/HistoryPresent";//既往史
import NowPresent from "./components/NowPresent";//现病史
import WholeBody from "./components/WholeBody";//全身状况
import Examine from "./components/Examine";//检查
import Auxiliary from "./components/Auxiliary";//辅助检查
import Dispose from "./components/Dispose";//处置
class Entry extends Component {
  constructor(props) {
    super(props);
    this.state = {

    };
  }

  render() {
    return (
      <GridContent>
        <div className={styles.entryContent}>
          <Tabs defaultActiveKey="1">
            <TabPane tab="主诉" key="1">
              <MainSuit />
            </TabPane>
            <TabPane tab="现病史" key="2">
              <NowPresent />
            </TabPane>
            <TabPane tab="既往史" key="3">
              <HistoryPresent />
            </TabPane>
            <TabPane tab="全身状况" key="4">
              <WholeBody />
            </TabPane>
            <TabPane tab="检查" key="5">
              <Examine />
            </TabPane>
            <TabPane tab="辅助检查" key="6">
              <Auxiliary />
            </TabPane>
            <TabPane tab="处置" key="7">
              <Dispose />
            </TabPane>
          </Tabs>
        </div>
      </GridContent>
    );
  }
}

export default Entry;
