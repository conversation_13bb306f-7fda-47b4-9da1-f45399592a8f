import React, {Component} from 'react';
import {Card,Radio,Popover,Tooltip} from 'antd';
import PropTypes from 'prop-types';
import $ from 'jquery';
import moment from 'moment';
import styles from './MonthEventElement.less'
import classNames from 'classnames';
import LinesEllipsis from 'react-lines-ellipsis';
import Diamonds from '@/assets/RegisterImg/zuanshi.png'
import Insurance from '@/assets/RegisterImg/bao.png'
import walkin from '@/assets/RegisterImg/walkin.png'
import consultation from '@/assets/RegisterImg/huizhen.png'
import firstVisit from '@/assets/RegisterImg/chu.png'
import Consultant from '@/assets/RegisterImg/huizhenyisheng.png'
import Fu from '@/assets/registerAndArrival/fu.png';
import FurtherConsultation from '@/assets/RegisterImg/fu.png';
import referralIcon from '@/assets/W-img/t5.png';
import { getOrganizationInfo, screenData } from '@/utils/utils';
import jieya from "../../../assets/W-img/jieya.png";
import labelImg from '../../../assets/W-img/label.png'
import padiDui from "../../../assets/W-img/padiDui.png";
import Manualicon from './Manualicon'
import { formatTime } from '@/utils/CalendarUtils';

/**
 * @type 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
 *
 */
export default class EventElement extends Component {

  static propTypes = {
    eventObj:PropTypes.object
  }

  static defaultProps = {
    eventObj:{}
  }

  constructor(props) {
    super(props);
    /*this.state = {
      eventObj:props.eventObj
    };*/

    this.state = {
      isWaitStute:null,    // 是否是等待状态 0:不等待 1:等待 2:等待超时
      WaitFormatTime:null, // 格式化后的等待时间
      isHiddenElement:null,       // 是否需要隐藏元素 null:不隐藏 1:隐藏姓名和等待标签
    }
  }

  componentDidMount() {
    // 开启等待时长的正计时
    this.IntervalByformatTime()
  }

  componentWillUnmount(){
    if (this.IntervalId) { clearInterval(this.IntervalId) }
  }

  // 开启等待时长的正计时
  IntervalByformatTime=()=>{
    const { el,event,view,eventPropsChange } = this.props.eventObj;
    const {
      id,                   // 预约id
      start,                // 预约开始时间
      end,
      extendedProps,
    } = event || {}
    const {
      type,
      titleInfo,
      appointmentInfoOfTableDto,
      consultationInfoOfTableDto // 会诊信息
    } = extendedProps || {}

    let {
      isComeVisit,               // 是否已到诊
      isConsultationVisit,       // 0:未接诊 1:已接诊
      waitingTimes,              // 等待时长(秒)
      isWaitingOver,             // 等待超时(1是，0否)
    } = appointmentInfoOfTableDto || {}

    const OrganizationInfo = getOrganizationInfo()// 收费模式(1规范化，2简洁化，3无医生)
    if(
      isComeVisit == 1 &&
      isConsultationVisit == 0 &&
      OrganizationInfo.chargeModel == 1 &&
      waitingTimes != null
    ) {
      // 开始等待时长的计时
      this.setState({
        isWaitStute: isWaitingOver ? 2 : 1,  // 是否是等待状态 0:不等待 1:等待 2:等待超时
      },()=>{
        this.computerBoxWaitWarp()
        if (this.IntervalId) { clearInterval(this.IntervalId) }
        let seconds = waitingTimes ? waitingTimes : 0; // 开始的秒数
        this.IntervalId = setInterval(() => {
          this.setState({
            WaitFormatTime: formatTime(seconds) // 每秒输出一次格式化后的时间
          })
          seconds++;
        }, 1000);
      })
    }
  }

  // 根据预约卡片的宽高动态计算展示内容
  // ① 获取当前元素的clientWidth,clientHeight
  // ② 根据元素大小来判断是否需要展示名称或标签
  computerBoxWaitWarp=()=>{
    const { el, event, view, eventPropsChange,isfullScreen } = this.props.eventObj;
    console.log('this.props.eventObj; ',this.props.eventObj);
    /*
    clientHeight: 29
    clientLeft: 1
    clientTop: 1
    clientWidth: 103
    * */
    const {
      clientHeight,
      clientWidth,
    } = el || {}

    if(clientWidth < 75){
      this.setState({
        isHiddenElement:1,
      })
    }else {
      this.setState({
        isHiddenElement:null,
      })
    }

  }


  render() {
    const { el,event,view,eventPropsChange } = this.props.eventObj;
    const {
      id,                   // 预约id
      start,                // 预约开始时间
      end,
      extendedProps,
    } = event || {}
    const {
      type,
      titleInfo,
      appointmentInfoOfTableDto,
      consultationInfoOfTableDto // 会诊信息
    } = extendedProps || {}

    // 计算当前预约时长
    let diffMinutes = 0;
    if(start && end){
      let startMoment = moment(start);
      let endMoment = moment(end);
      diffMinutes = endMoment.diff(startMoment,'minutes');
    }

    if(eventPropsChange){
      diffMinutes = 100
    }

    let {
      patientInfoDto,          // 患者信息
      isInsurance,             // 是否使用保险 0不使用 1使用
      complaintList,           // 主诉与治疗
      remark,                  // 预约备注
      doctorNameOfReferral,    // 转诊医生
      doctorNameOfAppointment, // 当前预约医生
      doctorNameOfConsultation,  // 当前预约的会诊医生
      presentIllness,          // 患者标签
      isConsultation,          // 是否携带会诊 1有 0没有
      isReferral,              // 是否是转诊
      isFirstVisit,            // 是否初复诊 1: 为初诊 2:复诊
      appointmentIconDto,      // 治疗项目页标签
      isClean,                 // 添加洁牙标签
      isWaitingList,           // 是否在排队列表
      taskType,                  // 预约确认状态 已完成预约确认1/未完成预约确认0/已联系未确认2/撤销预约确认3
      toDirector,                // 咨询师到诊状态 0未到咨询师 1已到咨询师 2咨询师已分配 3接诊中
      appointmentStatus,        // 预约状态: 10 暂存预约(目前只有10)
      appointmentIconNewDtoList, // 预约手工标签
      timeEnd,
      timeStart,
    } = appointmentInfoOfTableDto||{}


    let {
      basicTreatment,   // 指定客服大客户
      bigCustomer,      // 指定客服大客户
      emergency,        // 急诊
      implantSurgery,   // 种植手术
      implantTwice,     // 种植二期
      lab,              // lab
      majorTreatment,   // 重大治疗
      orthodontic,      // 正畸
      platinumCard,     // 招商银行白金卡
      bankPlatinumCard, // 银行白金卡
      policyHolder,     // MSH保险客户
      repairCrown,      // 修复戴冠
      rootCanal,        // 根管
      teethWhitening,   // 冷光美白
      vipClient,        // 	VIP客户
      hitSupport,        //  打支抗
      toothExtraction,   //  拔牙
      firstOrthodontics, //  正畸初戴
      voucher,
      microscopicRootCanal, // 显微镜根管
      childrenBound,        // 儿童束缚
      toothWhitening,       // 牙齿美白
      regularCustomerVisit,   // 老客户回访
      regularInspection,      // 三个月定检
      comfortTreatment,       // 舒适治疗
      crown,                  // 戴冠
    } = appointmentIconDto || {}

    /**
     emergency,         //  急诊
     implantSurgery,    //  种植手术
     implantTwice,      //  种植二期
     lab,               //  lab
     orthodontic,       //  正畸
     platinumCard,      //  招商银行白金卡
     policyHolder,      //  MSH保险客户
     repairCrown,       //  修复戴冠
     rootCanal,         //  根管
     teethWhitening,    //  冷光美白
     hitSupport,        //  打支抗
     toothExtraction,   //  拔牙
     firstOrthodontics, //  正畸初戴
     * @type {Array}
     */

    // 是否有手工标签
    let isIcon = Array.isArray(appointmentIconNewDtoList) ? appointmentIconNewDtoList : []
    // 主诉与治疗信息
    let complaintListTextList = []
    if(Array.isArray(complaintList)){
      complaintList.map((res)=>{
        if(res.treatType == 1 || res.treatType == 2){
          complaintListTextList.push(res.complaintName)
        }else if (res.treatType == 3 || res.treatType == 4 || res.treatType == 5){
          complaintListTextList.push(res.medicalDictionaryName)
        }
      })
    }
    let complaintListText = complaintListTextList.join(',')

    let {
      age,
      sex,
      sexDescribe,
      name,
      patientName,            // 患者名称
      appointmentDate,
      appointmentDateList,
      doctor,
      doctorName,
      //isFirstVisit,     // 是否初复诊 1: 为初诊 2:复诊
      vipGrade
    } = patientInfoDto || {}

    let sexText = sex == 1 ? '男' : '女'

    /* 预约 */
    let appointmentDom = (
      <div className={classNames({
        [styles.waitByElement]:!!this.state.isWaitStute,
      })}>
        {!!this.state.isWaitStute &&
          <div className={classNames({
            [styles.boxWaitWarp]:!!this.state.isWaitStute,
            [styles.timeoutBorder]:this.state.isWaitStute == 2,
            [styles.timeoutBackgroundTransparency]:this.state.isWaitStute == 2,
          })}>
            <div className={classNames({
              [styles.stateByWait]:!!this.state.isWaitStute,
              [styles.timeoutBackground]:this.state.isWaitStute == 2,
              [styles.isHiddenElement]:!!this.state.isHiddenElement,
            })}>
              <i className={styles.stateByWaitIcon}/>
              <div>已等待</div>
            </div>
            <div className={classNames({
              [styles.TimeByText]:!!this.state.isWaitStute,
              [styles.timeoutColor]:this.state.isWaitStute == 2,
              [styles.isHiddenElement]:!!this.state.isHiddenElement,
            })}>
              {this.state.WaitFormatTime}
            </div>
          </div>
        }
        <div id={`event_${id}`}>
          {
            appointmentStatus == 10 &&
            <>
              <div onClick={()=>{}}
                   className={classNames(styles.ShutDownBtn, 'ShutDownBtn')}></div>
            </>
          }
          {/* 基础 重大
                  basicTreatment,   // 基础治疗
                  bigCustomer,      // 指定客服大客户
                  emergency,        // 急诊
                  lab,              // lab
                  majorTreatment,   // 重大治疗
                  vipClient,        // VIP客户
          */}
          {/* <i className={styles.jichu}></i> */}
          { basicTreatment == 1 && majorTreatment != 1 && <i className={styles.jichu}></i> }
          { majorTreatment == 1 && <i className={styles.zhongda}></i> }
          { !!(taskType && taskType == 1) &&  <i className={styles.appmentEnter}/>}


          <div className={classNames('eventElent',styles.eventElent)}>
            { diffMinutes <= 5 &&
            <div>

            </div>
            }

            {diffMinutes >5 &&
            <div>
              {/* 5分钟展示 */}
              <div className={styles.patienBox}>
                <div className={classNames({
                  [styles.EventTypeIcon]:true,
                  [styles.EventTypeSpan]:basicTreatment != 1 && majorTreatment != 1 ? false : true,
                })}>
                  <span className={styles.patientName} >{name}</span>
                  <span className={classNames({
                    [styles.warp_element_notName]: !!this.state.isWaitStute,
                  })}>
                  {
                   appointmentStatus != 10 && <>
                    { vipGrade == 1      && <span className={styles.patientEventicon}> <i className={styles.vipicon}></i></span>}
                    { isFirstVisit == 1  && <span className={styles.patientEventicon}><img width={18} height={18} src={firstVisit}/></span>}
                    { isFirstVisit == 2  && <span className={styles.patientEventicon}><img width={18} height={18}  src={Fu}/></span>}
                    { type == 2          && <span className={styles.patientEventicon}><img width={18} height={18}  src={consultation}/></span>}
                    { isInsurance == 1   && <span className={styles.patientEventicon}><img width={18} height={18}  src={Insurance}/></span>}
                    { isClean == 1       && <span className={styles.patientEventicon}> <img width={18} height={18}  src={jieya}/> </span>}
                    { isWaitingList == 1 && <span className={styles.patientEventicon}> <img width={20} height={20}  src={padiDui}/> </span>}
                    { toDirector == 1 && <span  className={styles.Eventicon}> <i className={styles.stayToDirectorWarp}>待接诊</i> </span>}
                    { toDirector == 3 && <span  className={styles.Eventicon}> <i className={styles.alreadyToDirectorWarp}>已接诊</i> </span>}
                  </>
                  }
                  </span>
                </div>
              </div>
              <div>
                <div className={classNames({
                  [styles.cureProjectIcon]:true,
                  [styles.warp_element_notName]: !!this.state.isWaitStute,
                })}>
                  {
                    appointmentStatus != 10 ? <>
                      <Manualicon appointmentIconNewDtoList={isIcon} />
                    </> : <><span className={'temporaryTime'}>{timeStart}-{timeEnd}</span></>
                  }
                  {/* { repairCrown     == 1 && <Tooltip title="修复戴冠"> <i className={styles.daiguan}></i></Tooltip>}      修复戴冠
                  { teethWhitening  == 1 && <Tooltip title="冷光美白"> <i className={styles.meibai}></i></Tooltip>}    冷光美白
                  { implantTwice    == 1 &&<Tooltip title="种植二期修复">  <i className={styles.yachixiufu}></i></Tooltip>}  种植二期修复
                  { rootCanal       == 1 && <Tooltip title="根管"><i className={styles.yagen}></i></Tooltip>}          根管
                  { orthodontic     == 1 && <Tooltip title="正畸"><i className={styles.zhengji}></i></Tooltip>}      正畸
                  { implantSurgery  == 1 && <Tooltip title="种植手术"><i className={styles.zhongzhi}></i></Tooltip>}  种植手术
                  { bigCustomer     == 1 && <Tooltip title="指定客户"> <i className={styles.zhiding}></i></Tooltip>}      指定客户
                  { platinumCard    == 1 && <i className={styles.zhaobaijin}></i>}  招商银行白金卡
                  { policyHolder    == 1 && <i className={styles.MSH}></i>}         MSH保险客户
                  { lab             == 1 && <i className={styles.LAB}></i>}
                  { emergency       == 1 &&<i className={styles.jizhen}></i>}          急诊 */}
                </div>
              </div>
            </div>
            }
          </div>
        </div>
      </div>
    )

    let otherHours = (<div></div>)

    /*
     * @type`
     * 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
     */
    if(type == 1 ){
      return appointmentDom
    }else if(type == 2) {
      let {
        patientInfoDto,          // 患者信息
        isInsurance,             // 是否使用保险 0不使用 1使用
        complaintList,           // 主诉与治疗
        remark,                  // 预约备注
        doctorNameOfReferral,    // 转诊医生
        doctorNameOfAppointment, // 当前预约医生
        doctorIdOfConsultation,  // 当前预约的会诊医生
        presentIllness,          // 患者标签
        isConsultation,          // 是否携带会诊 1有 0没有
      } = consultationInfoOfTableDto||{}

      let {
        age,
        sex,
        sexDescribe,
        name,
        patientName,            // 患者名称
        appointmentDate,
        appointmentDateList,
        doctor,
        doctorName,
        isFirstVisit,     // 是否初复诊 1: 为初诊 2:复诊
      } = patientInfoDto || {}

      let sexText = sex == 1 ? '男' : '女'

      let meetHours = (
        <div className={classNames('eventElent',styles.eventElent)}>
          {diffMinutes >5 &&
          <div>
            {/*姓名行*/}
            {/*<LinesEllipsis className={styles.patientName} text={name} maxLine={1} ellipsis='...' >{name}</LinesEllipsis>*/}
            { type == 2 &&  <span className={styles.huizhenText}>会诊</span>}
          </div>
          }
          { diffMinutes <= 5 &&
          <div>
            { type == 2 &&  <span className={styles.huizhenText}>会诊</span>}
          </div>
          }

          {
            diffMinutes >= 30  &&
            <div>
              {/*年龄行*/}
              {/*<span className={''}>{sexText}</span>
              <span className={"age"}>{screenData(age)}</span>*/}
            </div>
          }
        </div>
      )
      return meetHours
    }else if(type==4){
      /**
       *  start,                // 预约开始时间
          end,
       * @type {XML}
       */

      let start_date = moment(start,"YYYY-MM-DD HH:mm:ss");
      let end_date = moment(end,"YYYY-MM-DD HH:mm:ss");   //秒
      let diffMinutes = end_date.diff(start_date,"minutes");  //分钟

      let titleForEvent = titleInfo + ''
      if (diffMinutes >= 80){
        titleForEvent = titleForEvent + `(${diffMinutes}min)`
      }

      // 个人占用时间
      let otherHoursDiv = (
        <div id={`event_${id}`} className={classNames('eventElent',styles.eventElentOther)}>
          <LinesEllipsis text={titleForEvent} maxLine={1} ellipsis='...' />
        </div>)
      return otherHoursDiv
    }else if(type ==7){
      //走诊
      let otherHoursDiv = (
        <div  className={classNames('eventElent',styles.eventElentOther,styles.evnetOffTextColor)}>
          <LinesEllipsis text={titleInfo} maxLine={1} ellipsis='...' />
        </div>)
      return otherHoursDiv
    } else {
      return otherHours
    }
  }

}


