import React, { Component } from 'react';
import { connect } from 'dva';
import { CheckEmrEnvPath } from '@/utils/utils';
import styles from './index.less';




@connect(({ loading }) => ({
  loading
}))
class EmrPart extends Component {
  constructor(props) {
    super(props);
    this.state = {
    }
  }

  componentDidMount() {
    // 接收消息
    const { dispatch } = this.props;
    const that = this;
    window.onmessage = function(e){
      console.info(e.origin,"收到来自哪里的消息了|||||");
      console.info(e.data,"消息的内容是？？？？");
      console.info("=======接受只有一次么？");
      if(e.data !=''){
        // 接收消息的内容 0 打开新窗口，1 关闭窗口 2 跳转客户详情 3 退出登录
        if(e.data.funType==0) {
          let params = [];
          // 提发送过来的对应参数
          for(let val in e.data){
            if(!(val=='funType'||val=='emrMenuPath')){
              params.push(`${val}=${e.data[val]}`)
            }
          }
          console.info("====打开啦啦啦～～～～～");
          if(params.length>0){
            if(e.data.openPage){
              // 清除之前可能存在的本地存储页面
              localStorage.removeItem('EmrRefresh');
              window.open(`${location.origin}${e.data.emrMenuPath}?${params.join('&')}`)
            }else {
              if(e.data.newPage==true){
                window.location.replace(`${location.origin}${e.data.emrMenuPath}?${params.join('&')}`)
                return ;
              }
              localStorage.setItem('EmrRefresh',`${location.origin}${e.data.emrMenuPath}?${params.join('&')}`)
            }
          }else {
            if(e.data.openPage){
              // 清除之前可能存在的本地存储页面
              localStorage.removeItem('EmrRefresh');
              window.open(`${location.origin}${e.data.emrMenuPath}`)
            }else {
              if(e.data.newPage==true){
                window.location.replace(`${location.origin}${e.data.emrMenuPath}`);
                return ;
              }
              localStorage.setItem('EmrRefresh',`${location.origin}${e.data.emrMenuPath}`)
            }
          }
          console.info(params.join('&'),"传过来的参数是？？");
          // 打开新的窗口
          // window.open(`${location.origin}/diagnosis`)
          // 打开诊疗中心
          // window.open(`${location.origin}/emr/DiagnosisTreat/TreatmentCenter?key=3&appointmentId=544347&organizationId=${organizationId}`)
          // 病历列表
          // window.open(`${location.origin}/emr/MedicalShare/medical_list`)
          // 病历详情
          // window.open(`${location.origin}/emr/MedicalAdmin/medical?emrSubId=a050529dd69f465d8088ed70cf1084ee&tenantId=${tenantId}&emrId=BJ0S014381&createdGmtAt=2021-04-21%2009:47:53&organizationId=${organizationId}&patientId=1195142`)
          // 历史详情
          // window.open(`${location.origin}/emr/MedicalAdmin/full?emrSubId=a050529dd69f465d8088ed70cf1084ee&tenantId=${tenantId}&emrId=BJ0S014381&createdGmtAt=2021-04-21%2009:47:53&organizationId=${organizationId}&patientId=1195142`)
          // 转诊详情
          // window.open(`${location.origin}/emr/TransferAdmin/transfer_details?id=1&tenantId=${tenantId}&reqOrganizationId=${organizationId}&patientId=1195529&emrId=TS11000343&createdGmtAt=2021-04-22`)
        }else if(e.data.funType==1){
          // 关闭当前窗口
          window.close()
        }else if(e.data.funType==3){
          // 退出登录
          dispatch({
            type: 'login/logout',
          });
        }
      }
    }
  }

  // 离开当前路由
  componentWillUnmount(){

  }


  // 给电子病历发送消息
  Message = ()=>{
    const EmrEnvPath = CheckEmrEnvPath();  // 电子病历的当前环境
    const id = localStorage.getItem('id'); // 当前登录用户标识
    const tenantId = localStorage.getItem('tenantId'); // 租户id
    let organizationlnfo = localStorage.getItem('organizationInfoJson'); // 机构ID
    const roleCodes = localStorage.getItem('role'); // 用户角色集合
    const access_token = localStorage.getItem('access_token'); // access_token
    const userName = localStorage.getItem('userName'); //当前登录用户名称
    const accountNumber = localStorage.getItem('accountNumber'); // 账号
    const token_userName= localStorage.getItem('token_username'); // 登录输入框中的用户名

    let pathArr =  location.pathname.split('/')
    let emrMenuPath = '/emr'+`/${pathArr[1]}`+location.search;
    // let emrMenuPath = '/emr'+location.pathname.slice(0,-6)+location.search; // 子项目去向的地址

    let toEmrLoginInfo = {
      access_token:access_token,
      tenantId:tenantId,
      organizationlnfo:organizationlnfo,
      id:id,
      accountNumber:accountNumber,
      token_userName:token_userName,
      userName:userName,
      roleCodes:roleCodes,
      emrMenuPath:emrMenuPath,
    }

    console.info(toEmrLoginInfo,":给电子病历发送的消息");
    let Dom = document.getElementById('ErmPart').contentWindow;
    // 发送消息通知子项目去处
    if(emrMenuPath.indexOf('emr')>-1){
      // Dom.postMessage(toEmrLoginInfo,EmrEnvPath)
      Dom.postMessage(toEmrLoginInfo,'http://localhost:8080')
    }
  }



  render(){
    const  emrLoadingPath = CheckEmrEnvPath()+'/emr/loading';
    return (
      <div className={styles.ErmPartBody}>
        {/* <iframe onLoad={this.Message} className={styles.ErmPart} ref='ErmPart' id='ErmPart' src={emrLoadingPath}></iframe> */}
        <iframe onLoad={this.Message} className={styles.ErmPart} ref='ErmPart' id='ErmPart' src={'http://localhost:8080'}  height={800} width={'100%'}></iframe>
      </div>
    );
  }
}
export default EmrPart;


