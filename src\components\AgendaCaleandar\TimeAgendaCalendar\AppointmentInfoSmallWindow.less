.bigBox{
  background: #FFFFFF;
  width: 458px;
  //min-height: 417px;
  //max-height: 439px;
  box-shadow: 0px 12px 48px 16px rgba(0, 0, 0, 0.03), 0px 9px 28px 0px rgba(0, 0, 0, 0.05), 0px 6px 16px -8px rgba(0, 0, 0, 0.08);
  border-radius: 2px;
  position: relative;
  .bgLeft{
    background: #2C2C2C;
    width: 4px;
    border-radius: 2px 0 0 2px;
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
  }
  .topInfo{
    padding: 16px 24px 16px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    .top{
      display: flex;
      justify-content: space-between;
      img{
        width: 16px;
        height: 16px;
        vertical-align: super;
        margin-top: 2px;
      }
      .topLeft{
        img{
          margin-left: 4px;
        }
        .name{
          font-size: 18px;
          font-weight: 600;
          color: rgba(0, 0, 0, 0.85);
        }
        .confirmStatus{
          margin-left: 39px;
          margin-right: 8px;
          display: flex;
          line-height: 18px;
          img{
            margin-right: 4px;
          }
        }
      }
      .topRight{
        display: flex;
        img{
          margin-right: 4px;
          cursor: pointer;
        }
      }
    }
    .info{
      color: rgba(0, 0, 0, 0.65);
      font-size: 12px;
      margin-top: 8px;
      margin-bottom: 8px;
    }
    .tab{
      //margin-top: 9px;
      display: flex;
      justify-content: space-between;
      :global{
        .ant-popover-inner{
          background: rgba(0,0,0,0.6);
        }
        .ant-popover-placement-bottomLeft > .ant-popover-content > .ant-popover-arrow{
          box-sizing: border-box;
          border-top-color: rgba(0,0,0,0.6)!important;
          border-left-color: rgba(0,0,0,0.6)!important;
        }
        .ant-popover-placement-bottom > .ant-popover-content > .ant-popover-arrow{
          box-sizing: border-box;
          border-top-color: rgba(0,0,0,0.6)!important;
          border-left-color: rgba(0,0,0,0.6)!important;
        }
        .ant-popover-placement-top > .ant-popover-content > .ant-popover-arrow{
          box-sizing: border-box;
          border-right-color: rgba(0,0,0,0.6)!important;
          border-bottom-color: rgba(0,0,0,0.6)!important;
        }
        .ant-popover-placement-topLeft > .ant-popover-content > .ant-popover-arrow{
          box-sizing: border-box;
          border-right-color: rgba(0,0,0,0.6)!important;
          border-bottom-color: rgba(0,0,0,0.6)!important;
        }
        .ant-popover-inner-content{
          padding: 12px;
        }
      }
    }
  }
  .middleInfo{
    padding: 16px 24px 16px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    height: 233px;
    overflow: auto;
    .infoItem{
      display: flex;
      position: relative;
      line-height: 26px;
      div:nth-of-type(1):not(.inTime) {
        min-width: 75px;
        max-width: 75px;
        color: rgba(0, 0, 0, 0.45);
        text-align: right;
      }
      div:nth-of-type(2):not(.redContent) {
        color: rgba(0, 0, 0, 0.85);
      }
      .materialQRBodyIcon{
        position: absolute;
        right:24px;
        width: 16px;
        height: 16px;
        cursor: pointer;
        background: url("../../../assets/AppointmentRules/materialQRBodyIcon.png") no-repeat;
        background-size:100%;
      }
    }
    .infoItem1{
      display: flex;
      line-height: 26px;
      .left {
        min-width: 75px;
        max-width: 75px;
        color: rgba(0, 0, 0, 0.45);
        text-align: right;
      }
      .right{
        color: rgba(0, 0, 0, 0.85);
      }

    }
    .materialQRBody{
      display: flex;
      width: 210px;
      height:80px;
      background: #FFFFFF;
      h6{
        font-size: 14px;
      }
    }
    :global{
      .ant-popover-placement-bottomRight > .ant-popover-content > .ant-popover-arrow{
        display: none;
      }
    }
  }
  .rightInput {
   // height: 18px;
   // line-height: 18px;
    overflow:hidden;

    :global {
      .ant-input {
        width: 100%;
        // height: 17px;
        // padding: 4px 11px;
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
        // line-height: 17;
      }
    }
  }

  .rightInputBox {
    cursor: pointer;
    position: relative;
    display: flex;
  }

  .ellipsis_text {
    flex: 1;
    word-break: break-all;
  }

  .rightIcon {
    display: inline-block;
    //width: 14px;
    //height: 14px;
    font-size: 14px;
    color: #1890ff;
    // position: absolute;
    // right: 35px;
    // top: 0px;
    flex-shrink: 0;
    margin-left: 10px;
  }

  .bottomInfo{
    height: 64px;
    padding: 0 24px;
    //position: fixed;
    //bottom: 0;
    display: flex;
    justify-content: space-between;
    :global{
      .ant-btn{
        //margin-left: 8px;
        margin-top: 16px;
        padding: 0 12px;
      }
      .ant-btn-primary{
        background-color: #1890ff;
      }
      .ant-btn-primary[disabled]{
        background-color: #f5f5f5;
      }
    }
    div:nth-of-type(1){
      margin-right: 20px;
    }
  }
}
.appointmentConfirm{
  :global{
    .ant-popover-content .ant-popover-inner-content {
      padding: 0px;
      width: 316px;
    }
    textarea.ant-input{
      background: rgba(247, 248, 250, 1);
    }
  }
  .remarks{
    padding: 11px 16px;
  }
  .button{
    padding-bottom: 11px;
    overflow: hidden;
    :global{
      .ant-btn{
        margin-right: 16px;
        float: right;
      }
    }
  }
}

.infoItemContent {
  display: flex;
  .inTime {
    margin-right: 5px;
  }
  .redContent {
    padding: 4px 12px;
    box-sizing: border-box;
    color: #FF6F1B;
    background: #FFE8DB;
    border-radius: 14px;
    font-size: 12px;
    font-weight: 500;
    line-height: 17px;

    display: flex;
    align-items: center;

    img {
      width: 15px;
      height: 16px;
      margin-right: 5px;
    }
  }
  .timeoutContent {
    background: #FFDEDE;
    color: #FF4747;
  }
}
