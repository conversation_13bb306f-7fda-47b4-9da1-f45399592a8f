@import '~antd/es/style/themes/default.less';


.topStyle{
  border-bottom: 1px solid #e5e6eb;
  height:45px;
  line-height:45px;
  padding-left:16px;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
}
.cardHover {
  &:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    transform: translate3d(0, -3px, 0);
    cursor: pointer;
    border: 1px solid #4292FF;
  }
}
.titleRightFont{
  padding-right: 16px;
  color: #9a999c;
  font-size: 14px;
}
// 筛选就诊状态
// .siftImg {
//   width: 16px;
//   height: 16px;
//   margin-right: 5px;
//   line-height: 25px;
//   cursor: pointer;
// }

.tabsHidden {
  width: 0;
  height: auto;
  display: none;
}

.tabsShow {
  border-right: 1px solid #E5E6EB;
  //max-width: 342px;
  //height:720px;
}

// 折叠箭头
.tabButton {
  //border:1px solid #E6A23C;
  width: 16px;
  height: 56px;
  border-radius: 8px 0px 0px 8px;
  background: #E3E3E3;
  position: absolute;
  top: 345px;
  cursor: pointer;
}

.rightTabButton{
 border-radius:  0px 8px 8px 0px;
}
// 向左箭头按钮
.hideButton {
  float: right;
  right: 2px
}

// 向右箭头按钮
.showButton {
  float: left;
  left: 0;
}

// 左箭头
.arrowLeft {
  width: 0;
  border-left: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-top: 5px solid transparent;
  border-right: 5px solid #bfbfbf;
  font-size: 0;
  line-height: 56px;
  margin-top: 22px;
}

// 右箭头
.arrowRight {
  width: 0;
  border-right: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-top: 5px solid transparent;
  border-left: 5px solid #bfbfbf;
  display: inline-block;
  margin-top: 22px;
  margin-left: 6px;
}

:global {
  // .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  //   color: rgba(0,0,0 0.85);
  //   font-size: 16px;
  // }
.ant-card-bordered{
  border:0;
}
  .ant-card-body {
    padding: 8px !important;
  }
}

.statusFlag{
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 2px;
}
// 到诊
.status_bg1{
  background: #4292FF;
}

// 预约未到
.status_bg2 {
  background: #98CF5E;
}
// 结束就诊
.status_bg3 {
  background: #EECA5D;
}

.fontWeight {
  font-weight: bold;
  font-size: 16px;
}

.pd_lr {
  padding: 0 4px;
  color: #9a999c;
  font-size: 14px;
}

.icon_diamond {
  width: 16px;
  height: 14px;
  margin-top: 5px;
  margin-right: 4px;
}

.icon_boy {
  width: 14px;
  height: 14px;
  margin-top: -2px;
  margin-left: 2px;
  // margin-right: 4px;
}
.icon_girl {
  width: 19px;
  height: 19px;
  margin-top: 1px;
  // margin-right: 4px;
}
.line {
  width: 1px;
  height: 9px;
  margin: 0 4px;
  margin-top: 7px;
  background-color: #d8d8d8;
}
.topFlex {
  display: flex;
  justify-content: space-between;
}

.tagSuccess {
  padding: 0 2px;
  color: #00b42a !important;
  background: rgba(0, 180, 42, 0.1) !important;
  border-color: rgba(0, 180, 42, 0.3) !important;
  border-radius: 4px !important;
}

.tagRed {
  padding: 0 2px;
  color: rgba(245, 63, 63) !important;
  background: rgba(245, 63, 63, 0.1) !important;
  border-color: rgba(245, 63, 63, 0.3) !important;
  border-radius: 4px !important;
}
.tagWarn {
  padding: 0 2px;
  color: rgba(245, 63, 63) !important;
  background: rgba(255, 125, 0, 0.1) !important;
  border-color: rgba(255, 125, 0, 0.3) !important;
  border-radius: 4px !important;
}
.border_bot {
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.bottom {
  display: flex;
  justify-content: space-between;
  padding-top: 8px;
}

.word6 {
  display: block;
  width: 36px;
  height: 20px;
  margin: 1px 0 0 2px;
  color: rgba(0, 180, 42, 1);
  font-size: 12px;
  font-family: PingFang SC;
  line-height: 20px;
  white-space: nowrap;
  text-align: center;
  overflow-wrap: break-word;
}


.ellipse {
  min-width: 50px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.name {
  display: inline-block;
  max-width: 45%;
}
.marginSpan {
  margin-top: 2px;
}
.marginTag {
  margin-right: 0;
}
.colCard{

}

//加载中
.loading{
  position: absolute;
  top: 45%;
  left: 47%;
  z-index: 888;
}
.fontLeftSize{
  font-size: 14px;
  width:60%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
