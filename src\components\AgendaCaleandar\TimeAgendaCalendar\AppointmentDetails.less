.AppointmentDetails {
  :global {
    .ant-modal-content {
      width: 100%;
      height: 100vh;
    }
    .ant-modal-body {
      padding: 0;
    }
    .ant-btn{
      padding: 0 12px;
    }
    .ant-btn-primary{
      background-color: #1890ff;
    }
    .ant-btn-primary[disabled]{
      background-color: #f5f5f5;
    }
  }
  top: 0;
  margin: 0;
  padding-bottom: 0;
  .title{
    display: flex;
    justify-content: space-between;
    color: #8C8C8C;
    span{
      margin-left: 22px;
      cursor: pointer;
    }
    div:nth-of-type(1){
      font-size: 18px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
  .bigBox{
    display: flex;
    height: calc(100vh - 85px);
    .left{
      min-width: 340px;
      max-width: 340px;
      height: calc(100vh - 85px);
      border-right: 1px solid #EEEEEE;
      padding: 16px 24px;
      position: relative;
      overflow-y: auto;
    }
    .right{
      flex: 1;
      padding: 24px;
      .rightTitle{
        display: flex;
        justify-content: space-between;
        .title{
          font-size: 18px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: 600;
        }
        img{
          width: 16px;
          height: 16px;
          vertical-align: text-bottom;
          margin-right: 4px;
        }
        .confirmStatus{
          margin-right: 12px;
        }
      }
      .appointmentInfo{
        background: #F7F8FA;
        border-radius: 6px;
        .middleInfo{
          position: relative;
          padding: 16px 24px 16px 24px;
          margin-top: 17px;
          .infoItem{
            display: flex;
            line-height: 22px;
            margin-bottom: 4px;
            div:nth-of-type(1) {
              min-width: 62px;
              //max-width: 60px;
              color: rgba(0, 0, 0, 0.45);
              text-align: right;
              margin-right: 8px;
            }
            div:nth-of-type(2){
              flex: 1;
              color: rgba(0, 0, 0, 0.85);
            }
          }
          .infoItem1{
            display: flex;
            line-height: 22px;
            margin-bottom: 4px;
            .infoItemLeft {
              min-width: 62px;
              //max-width: 60px;
              color: rgba(0, 0, 0, 0.45);
              text-align: right;
              margin-right: 8px;
            }
            .infoItemRight{
              color: rgba(0, 0, 0, 0.85);
            }
          }
          .materialQRBody{
            position: absolute;
            top:16px;
            right: 24px;
            display: flex;
            width: 210px;
            height:80px;
            padding:4px 8px;
            background: #FFFFFF;
            img{
              width: 72px;
              height: 72px;
            }
            >div{
              flex: 1;
              h6{
                font-size:14px;
                height: 28px;
                line-height: 28px;
                margin-bottom: 0px;
              }
              p{
                font-size: 12px;
              }
            }
          }
        }
        .bottom{
          display: flex;
          justify-content: space-between;
          padding: 16px 24px;
          :global{
            .ant-btn{
              margin-right: 16px;
            }
            .ant-btn-primary{
              margin-right: 0;
            }
          }
        }
      }
    }
  }
}
.appointmentConfirm{
  :global{
    .ant-popover-content .ant-popover-inner-content {
      padding: 0px;
      width: 376px;
    }
    textarea.ant-input{
      background: rgba(247, 248, 250, 1);
    }

  }
  .remarks{
    padding: 8px 16px;
    div:nth-of-type(1){
      padding-bottom: 8px;
    }
  }
  .button{
    padding-bottom: 3px;
    overflow: hidden;
    :global{
      .ant-btn{
        margin-right: 16px;
        float: right;
        padding: 0 12px;
        border-radius: 2px;
      }
      .ant-btn-primary{
        background-color: #1890ff;
      }
    }
  }
}
.sendSMS{
  :global{
    .ant-btn{
      padding: 0 12px;
      border-radius: 2px;
    }
    .ant-btn-primary{
      background-color: #1890ff;
    }
    .ant-form-item {
      margin-bottom: 0px;
    }
  }
}

.contentsTextImg1 {
  margin-top: -2px
}

.Warp_AppintmentIcon {
  display: flex;
}

.AppintmentIcon {
  background: url('../../../assets/Type_A_AppointmentIcon.png');
  background-size: 24px 24px;
  width: 24px;
  height: 24px;
  display: inline-block;
  margin-right: 8px;
}


.PopoverWarp {
  width: 400px;

  .ItemMsg {
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    background: #FFF5ED;
    border-radius: 4px;
    padding-left: 8px;
    padding-right: 8px;
    margin-bottom: 8px;

    .iconByItemMsg {
      width: 16px;
      height: 16px;
      color: #ED6A09;
      border-radius: 50%;
      display: inline-block;
      margin-right: 5px;
      position: relative;
      top: 2px;
    }

    .TestByItemMsg {
      font-size: 14px;
      font-weight: 400;
      color: #ED6A09;
    }
  }

  .PopoverItem {
    display: flex;
    margin-bottom: 5px;

    .PopoverItem_Left {
      width: 80px;
      color: rgba(0,0,0,0.45);
      text-align: right;
      margin-right: 10px;
    }

    .PopoverItem_Right {
      width: calc(100% - 80px);
    }
  }
}

.SMSnumMsgBox {
  padding-left: 16px;
  padding-bottom: 16px;
  color: #FF2E2E;
}

.text_shortMessage {
  margin-left: 89px;
  margin-bottom: 9px;
  color: rgba(0, 0, 0, 0.45);
}

.Rigth_ShortMessage {
  width: 12px;
  height: 12px;
  color: rgba(0,0,0,0.45);
  border-radius: 50%;
  display: inline;
  cursor: pointer;
  user-select: none;
  margin-right: 5px;
}


.infoItem_WaitItem {
  display: flex;
  div:nth-of-type(1) {
    min-width: 62px;
    //max-width: 60px;
    color: rgba(0, 0, 0, 0.45);
    text-align: right;
    margin-right: 8px;
  }

  .inTime {
    margin-right: 5px;
    display: inline-block;
    color: rgba(0, 0, 0, 0.85);
  }
  .redContent {
    padding: 4px 12px;
    box-sizing: border-box;
    color: #FF6F1B;
    background: #FFE8DB;
    border-radius: 14px;
    font-size: 12px;
    font-weight: 500;
    line-height: 17px;
    display: flex;
    display: inline-block;
    align-items: center;

    img {
      width: 15px;
      height: 16px;
      margin-right: 5px;
    }
  }
  .timeoutContent {
    background: #FFDEDE;
    color: #FF4747;
  }
}
