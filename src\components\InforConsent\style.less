@import '~antd/es/style/themes/default.less';
.resource_tree{
  :global{
    .ant-select-tree-switcher {
      position: relative;
      z-index: 999;
    }

    .ant-select-tree-switcher_open::before, .ant-select-tree-switcher_close::before {
      content: '';
      position: absolute;
      right: -210px;
      top: 0;
      left: 0;
      bottom: 0;
    }
    .ant-select-tree .ant-select-tree-treenode{
      &:hover{
        background-color: rgba(0,0,0,0.0600);
      }
    }
  }

}
.AllStyle{
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
  .titleStyle{
    width: 25%;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    line-height: 32px;
    font-family: PingFang SC;
  }
  .chooseStyle{
    width: 75%;
    //margin-left: 12px;
    line-height: 30px;
  }
}
:global{
  .ant-modal-body{
    padding: 16px;
  }
}
.cardBorder{
  :global{
    .ant-image-mask{
      right:-30px;
    }
    .ant-btn-primary{
      border: 0px;
      color: rgba(66, 146, 255, 1);
      background: rgba(66, 146, 255, 0.12);
      border-radius: 2px;
      font-size: 14px;
      font-family: PingFang SC;
    }
  }
  float: right;
  width: 275px;
  height: 205px;
  margin-top: 16px;
  border: 1px solid #E5E6EB;
  padding: 16px;
  .imgStyle{
    width:100%;
    height: 120px;
  }
  .btnStyle{
    width: 100%;
    margin-top: 16px;
  }
}
.uploadStyle{
  margin-top: 16px;
  margin-left: 25%;
}
.cardBorderRight{
  :global{
    .ant-btn-primary{
      border: 0px;
      color: rgba(66, 146, 255, 1);
      background: rgba(66, 146, 255, 0.12);
      border-radius: 2px;
      font-size: 14px;
      font-family: PingFang SC;
    }
  }
  float: right;
  width: 275px;
  height: 205px;
  margin-top: 16px;
  border: 1px solid #E5E6EB;
  padding: 16px;
  .imgStyle{
    width:100%;
    height: 120px;
  }
  .btnStyle{
    width: 100%;
    margin-top: 16px;
  }
}
.ctimgdelete {
  cursor: pointer;
  color: #F0F0F0;
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 10;
  width: 242px;
  height: 120px;
  background: rgba(0, 0, 0, 0.45);
  text-align: center;
  align-items: center
}
.icon_delete{
  width: 20px;
  height: 20px;
}
.deleteFont{
  font-size: 14px;
  margin-left: 4px;
  margin-top: 1px;
}
.Choose{
  cursor: pointer;
  width: 24px;
  height: 24px;
  background: rgba(66, 146, 255, 0.12);
  border-radius: 2px;
  border: 1px solid #4292FF;
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 400;
  color: #4292FF;
  text-align: center;
}
.notChoose{
  cursor: pointer;
  width: 20px;
  height: 20px;
  background: #FFF;
  border-radius: 2px;
  border: 1px solid rgba(0, 0, 0, 0.45);
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.45);
  text-align: center;
  margin-top: 1px;
}
.Intertranslation{
  display: flex;
}
.contentTop{
  display: flex;
  justify-content: space-between;
}
.contentBook{
  margin-top: 16px;
  height: 500px;
  overflow: scroll;
}
