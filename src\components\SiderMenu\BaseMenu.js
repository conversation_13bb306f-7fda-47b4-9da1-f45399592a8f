import React, { Component } from 'react';
import { Menu, Icon, Row, Input, Button, Form, Spin, message } from 'antd';
import Link from 'umi/link';
import isEqual from 'lodash/isEqual';
import memoizeOne from 'memoize-one';
import pathToRegexp from 'path-to-regexp';
import { urlToList } from '../_utils/pathTools';
import styles from './index.less';
import addCommonMenu from '@/assets/menu/addCommonMenu.png';
import removeCommonMenu from '@/assets/menu/removeCommonMenu.png'
import { connect } from 'dva';

const { SubMenu } = Menu;
const pathByProcurementsystem = '/procurementsystem' // 采购系统path



// Allow menu.js config icon as string or ReactNode
//   icon: 'setting',
//   icon: 'http://demo.com/icon.png',
//   icon: <Icon type="setting" />,
const getIcon = icon => {
  if (typeof icon === 'string' && icon.indexOf('http') === 0) {
    return <img src={icon} alt="icon" className={styles.icon} />;
  }
  if (typeof icon === 'string') {
    return <Icon type={icon} />;
  }
  return icon;
};

export const getMenuMatches = (flatMenuKeys, path) =>
  flatMenuKeys.filter(item => {
    if (item) {
      return pathToRegexp(item).test(path);
    }
    return false;
  });

@Form.create()
@connect(({ global }) => ({
  global,
}))
export default class BaseMenu extends Component {
  constructor(props) {
    super(props);
    this.getSelectedMenuKeys = memoizeOne(this.getSelectedMenuKeys, isEqual);
    this.flatMenuKeys = this.getFlatMenuKeys(props.menuData);
    this.state = {
      menus: [], // 菜单
      editCommonState:false,  // 设置常用的状态
      addCommonMenuArr:[]        // 添加进来的菜单
    }
  }

  componentDidMount() {
    const { dispatch } = this.props;
    const  submitList = localStorage.getItem('submitList') // 获取到本地的存储的内容
    const tenantId = localStorage.getItem('tenantId')  // 获取租户id
    const id = localStorage.getItem('id') // 获取用户id
    if((submitList==null||submitList=='null')&&tenantId!=null){
      setTimeout(()=>{
        // 获取添加到常用菜单的项
        dispatch({
          type: 'menu/getAddCommonMenuArr',
        }).then((res)=>{
          if(res && res.code==200){
            this.setState({
              addCommonMenuArr:res.content!=null?res.content:[],
            },()=>{
              const { addCommonMenuArr } = this.state;
              localStorage.setItem('submitList',JSON.stringify(addCommonMenuArr))
              this.setState({
                menus: this.MenuSplitMerge()
              })
            })
          }else {
            message.error('获取菜单失败，请稍后重试～！')
          }
        })
      })
    }else {
      this.setState({
        addCommonMenuArr:JSON.parse(submitList)
      },()=>{
        this.setState({
          menus: this.MenuSplitMerge()
        })
      })
      if(id==null||tenantId==null||id=='null'||tenantId==null){
        alert(`tenantId:${tenantId},id:${id}`)
      }
    }
  }

  /**
   * Recursively flatten the data
   * [{path:string},{path:string}] => {path,path2}
   * @param  menus
   */
  getFlatMenuKeys(menus) {
    let keys = [];
    menus.forEach(item => {
      if (item.children) {
        keys = keys.concat(this.getFlatMenuKeys(item.children));
      }
      keys.push(item.path);
    });
    return keys;
  }

  /**
   * 获得菜单子节点
   * @memberof SiderMenu
   */
  getNavMenuItems = (menusData, parent) => {
    if (!menusData) {
      return [];
    }
    const Arrar = menusData
      .filter(item => item.name && !item.hideInMenu)
      .map(item => {
        // make dom
        const ItemDom = this.getSubMenuOrItem(item, parent);
        return this.checkPermissionItem(item.authority, ItemDom);
      })
      .filter(item => item);
    let obj = [];
    // 判断如果返回的数组中key 等于menusData数组中的path就添加到obj 新数组中
    for (let i = 0; i < menusData.length; i++) {
      const item = menusData[i];
      const path = menusData[i].path;
      for (let j = 0; j < Arrar.length; j++) {
        const key = Arrar[j].key;
        if (path == key) {
          obj.push(item)
        }
      }
    }
    return obj
  };

  // Get the currently selected menu
  getSelectedMenuKeys = pathname =>
    urlToList(pathname).map(itemPath => getMenuMatches(this.flatMenuKeys, itemPath).pop());

  /**
   * get SubMenu or Item
   */
  getSubMenuOrItem = item => {
    // doc: add hideChildrenInMenu
    if (item.children && !item.hideChildrenInMenu && item.children.some(child => child.name)) {
      const { name } = item;
      return (
        <SubMenu
          title={
            item.icon ? (
              <span>
                {getIcon(item.icon)}
                <span>{name}</span>
              </span>
            ) : (
              name
            )
          }
          key={item.path}
        >
          {this.getNavMenuItems(item.children)}
        </SubMenu>
      );
    }
    return <Menu.Item key={item.path}>{this.getMenuItemPath(item)}</Menu.Item>;
  };

  /**
   * 判断是否是http链接.返回 Link 或 a
   * Judge whether it is http link.return a or Link
   * @memberof SiderMenu
   */
  getMenuItemPath = item => {
    const { name } = item;
    const itemPath = this.conversionPath(item.path);
    const icon = getIcon(item.icon);
    const { target } = item;
    // Is it a http link
    if (/^https?:\/\//.test(itemPath)) {
      return (
        <a href={itemPath} target={target}>
          {icon}
          <span>{name}</span>
        </a>
      );
    }
    const { location, isMobile, onCollapse } = this.props;
    return (
      <Link
        to={itemPath}
        target={target}
        replace={itemPath === location.pathname}
        onClick={
          isMobile
            ? () => {
              onCollapse(true);
            }
            : undefined
        }
      >
        {icon}
        <span>{name}</span>
      </Link>
    );
  };
  // permission to check
  checkPermissionItem = (authority, ItemDom) => {

    const { Authorized } = this.props;
    if (Authorized && Authorized.check) {
      const { check } = Authorized;
      return check(authority, ItemDom);
    }
    return ItemDom;
  };

  conversionPath = path => {
    if (path && path.indexOf('http') === 0) {
      return path;
    }
    return `/${path || ''}`.replace(/\/+/g, '/');
  };

  //  菜单渲染规则分类
  MenuSplitMerge = () => {
    const { handleOpenChange, style, menuData,CurrentGroups } = this.props;
    const { addCommonMenuArr } = this.state;
    console.info(addCommonMenuArr,"有？？？？？");
    let menus = this.getNavMenuItems(menuData);
    // 获取添加到常用的权限
    if(addCommonMenuArr!=null&&addCommonMenuArr.length>0){
      menus.map((i)=>{
        addCommonMenuArr.map((y)=>{
          if((i.authority!=undefined&&i.authority ==y.authority)||(i.name==y.name)){
            let some = {...i};
            some.grouping = "common";
            some.groups = i.grouping;
            menus.push(some)
          }
        })
      })
    }
    // 根据grouping分组名字 按顺序分组
    const grouping = [
      { groups: 'common', name: '常用菜单' },
      { groups: 'statistics', name: '查询与统计' },
      { groups: 'setup', name: '设置与管理' },
      { groups: 'remind', name: '待办提醒' },
      { groups: 'medical', name: '医疗管理' },
      { groups: 'quality', name: '质检管理' },
      { groups: 'system', name: '系统配置' },
    ]
    let menuArr = [];
    grouping.map((itemGrouping, index) => {
      let newMenu = menus.filter(item => item.grouping == itemGrouping.groups)
      // 默认展开第一个文件夹  it's statistics；
      menuArr = [
        ...menuArr,
        {
          ...itemGrouping,
          menusCheck: itemGrouping.groups == CurrentGroups ? true : false, // 确认当前打开的是哪个文件夹
          childrens: [...newMenu]
        }
      ]
    })
    return menuArr
  }
  // 展开关闭菜单文件夹   当menusCheck为true时  展开默认的菜单
  menusChecked = (groups, menusCheck) => {
    const { ChangeCurrentGroups } = this.props;
    // 修改当前展开的是哪个文件夹
    menusCheck?ChangeCurrentGroups('statistics'):ChangeCurrentGroups(groups);
    let menus = this.state.menus;
    menus.map((item) => {
      if (menusCheck) {
        item.menusCheck = item.groups == 'statistics' ? true : false
      } else {
        item.menusCheck = item.groups == groups ? true : false
      }
    })
    this.setState({
      menus
    })
  }

  // 添加到常用菜单中
  addCommonMenu = (item) =>{
    const { menus } = this.state;
    let newMenu = menus;
    item.groups = "common";
    let result = menus[0].childrens.filter(one=>one.name==item.name);
    if(result.length>0){
      message.error('该菜单已被添加至常用菜单中，请勿重复添加～')
    }else {
      newMenu[0].childrens.push(item)
    }
    this.setState({
      menus:newMenu
    })
  }

  // 从常用菜单中移除
  removeCommonMenu = (item) =>{
    let { menus } = this.state;
    let newmenus = menus[0].childrens.filter(every=> every!=item);
    menus[0].childrens = [...newmenus];
    this.setState({
      menus:menus
    })
  }

  // 提交编辑添加到常用菜单的项
  changeCommonMenu=()=>{
    const { editCommonState ,menus } = this.state;
    const { dispatch } = this.props;
    let result = menus[0].childrens.filter(i=>i.groups)  //确认是添加进来的
    let submitList  = []
    if(result.length>0){
      result.map((item)=>{
        let every = {
          name:item.name,
          authority:item.authority
        }
        submitList.push(every)
      })
    }
    this.setState({
      editCommonState:!editCommonState
    })
    if(editCommonState){
      dispatch({
        type: 'menu/editCommonMenuArr',
        payload:{
          contentForms:submitList,
        }
      }).then((res)=>{
        if(res.code==200){
          this.setState({
            editCommonState:!editCommonState,
            addCommonMenuArr:submitList,
          },()=>{
            // 更新本地存储的菜单
            localStorage.setItem('submitList',JSON.stringify(submitList))
          })
        }else{
          message.error('配置常用菜单失败，请稍后重试～！')
        }
      })
    }
  }

  // getPathTo
  getPathTo=(path)=>{
    // localStorage.getItem()
    // 采购系统是否授权字段：procurementAuth  1已授权，其他未授权
    const procurementAuth = localStorage.getItem('procurementAuth')
    if(path == pathByProcurementsystem && procurementAuth == 1){
      return null
    }else {
      return path
    }
  }

  // onClickByLink
  onClickByLink=(item)=>{
    this.props.click()
    // "/procurementsystem" 采购系统是否授权字段：procurementAuth  1已授权，其他未授权
    const procurementAuth = localStorage.getItem('procurementAuth')
    if (item.path == pathByProcurementsystem && procurementAuth == 1) {
      const { dispatch, query } = this.props;
      dispatch({
        type: 'global/setVisibleByModalByGoJwsmed',
        payload: {
          visibleByModalByGoJwsmed: true,
          moduleIdByModalByGoJwsmed: null,
        },
      })
      return
    }
  }

  render() {
    const {
      openKeys,
      theme,
      mode,
      location: { pathname },
      loading
    } = this.props;
    const addCommonMenuLoading = loading.effects['menu/getAddCommonMenuArr'];
    const { getFieldDecorator } = this.props.form;
    // if pathname can't match, use the nearest parent's key
    let selectedKeys = this.getSelectedMenuKeys(pathname);
    if (!selectedKeys.length && openKeys) {
      selectedKeys = [openKeys[openKeys.length - 1]];
    }
    let props = {};
    if (openKeys) {
      props = {
        openKeys,
      };
    }
    const { menus, editCommonState } = this.state;
    const { handleOpenChange, style, menuData } = this.props;
    const StyleArr = [
      { width: '245px', height: '75px', fontSize: '14px', borderRadius: '10px' },
      { width: '160px', height: '75px', fontSize: '14px', borderRadius: '10px' },
      { width: '75px', height: '75px', fontSize: '14px', borderRadius: '10px' },
    ]
    const ColorArr = [
      { background: 'linear-gradient(180deg, #53ADFF 0%, #5492FB 100%)', },
      { background: 'linear-gradient(180deg, #FE7B29 0%, #FF7242 100%)' },
      { background: 'linear-gradient(180deg, #3DC8CC 0%, #1BAEB3 100%)', },
      { background: 'linear-gradient(360deg, #5250EA 0%, #6685FF 100%)', },
      { background: 'linear-gradient(180deg, #A4C1EF 0%, #7490C5 100%)', }
    ]
    return (
      <div className={styles.parent}>
          <div style={{ marginLeft: 30, marginRight: 15, width: 510 }}>
            <Spin spinning={!!addCommonMenuLoading}>
            {menus && menus.map((item, index) => (
              item.childrens&&item.childrens.length>0?
                <div key={index}>
                  {/* item.groups为common的就是常用菜单  常用菜单没有展开收起控制开关 */}
                  {item.groups == 'common' ? (
                    <div>
                      <p style={{ fontSize: '16px', fontWeight: '400', color: '#333333', marginBottom: 14, paddingTop: 20 }}>{item.name}<Button className={styles.editCommonBtn} onClick={()=>this.changeCommonMenu()}>{editCommonState?'完成常用菜单设置':'设置常用菜单'}</Button></p>
                      <Row type='flex'>
                        {item.childrens && item.childrens
                        .map((item, index) => (
                          <div className={styles.everyMenuBody}>
                            {
                              editCommonState&&item.groups&&item.groups !=item.grouping?<img src={removeCommonMenu} className={styles.removeCommon} onClick={()=>this.removeCommonMenu(item)} />:<></>
                            }
                            <div className={styles.menu} key={index} style={
                              { ...StyleArr[item.styleColor], ...ColorArr[item.backgroundColor] }
                            }>
                              <Link
                                to={this.getPathTo(item.path)}
                                onClick={()=>{this.onClickByLink(item)}}
                              >
                                <Icon className={styles[item.icon]} type={item.icon} style={{ marginBottom: 3, marginTop: 3 }} />
                                <div
                                  className={styles.textName}
                                >
                                  {item.name}
                                </div>
                              </Link>
                            </div>
                          </div>
                        ))}
                      </Row>
                    </div>
                  ) : (
                    <div>
                      <p
                        style={{
                          fontSize: '14px', fontWeight: '400', color: '#333333',
                          height: 25, lineHeight: "25px", marginBottom: 10, cursor: 'pointer'
                        }}
                        className={item.menusCheck ? styles.openFloder : styles.closeFloder}
                        onClick={() => this.menusChecked(item.groups, item.menusCheck)}
                      >
                        <sapn style={{ marginLeft: 30 }}>{item.name}</sapn>
                      </p>
                      {item.menusCheck && <Row type='flex'>
                        {item.childrens && item.childrens
                        .map((item, index) => (
                          <div  className={styles.everyMenuBody}>
                            {
                              editCommonState?<img src={addCommonMenu} className={styles.addCommon} onClick={()=>this.addCommonMenu(item)} />:<></>
                            }
                            <div className={styles.menu} key={index} style={
                              { ...StyleArr[item.styleColor], ...ColorArr[item.backgroundColor] }
                            }>
                              <Link
                                to={this.getPathTo(item.path)}
                                onClick={()=>{this.onClickByLink(item)}}>
                                <Icon className={styles[item.icon]} type={item.icon} style={{ marginBottom: 5, marginTop: 3 }} />
                                <div
                                  className={styles.textName}
                                >
                                  {item.name}
                                </div>
                              </Link>
                            </div>
                          </div>
                        ))}
                      </Row>}
                    </div>
                  )}
                </div>:<></>
            ))}
            </Spin>
          </div>
      </div >
    );
  }
}
