import React, { Component } from 'react';
import classNames from 'classnames';
import { Tag, Icon } from 'antd';

import styles from './index.less';

const { CheckableTag } = Tag;

const TagSelectOption = ({ children, checked, onChange, value,type }) => (
  <CheckableTag type={type} checked={checked} key={value} onChange={state => onChange(value,state,type)}>
    {children}
  </CheckableTag>
);

TagSelectOption.isTagSelectOption = true;

class TagSelect extends Component {
  static defaultProps = {
    hideCheckAll: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      expand: false,
      value: props.value || props.defaultValue || [],
    };
  }

  static getDerivedStateFromProps(nextProps) {
    if ('value' in nextProps && nextProps.value) {
      return { value: nextProps.value };
    }
    return null;
  }

  onChange = value => {
    const { onChange } = this.props;
    if (!('value' in this.props)) {
      this.setState({ value });
    }

    if (onChange) {
      onChange(value);
    }
  };

  onSelectAll = checked => {
    let checkedTags = [];
    if (checked) {
      checkedTags = this.getAllTags();
    }
    this.onChange(checkedTags);
  };

  getAllTags() {
    let { children } = this.props;
    children = React.Children.toArray(children);
    const checkedTags = children
      .filter(child => this.isTagSelectOption(child))
      .map(child => child.props.value);
    return checkedTags || [];
  }

  handleTagChange = (value, checked,type) => {
    const { value: StateValue } = this.state;
    let checkedTags = [...StateValue];
    let checkValueList = checkedTags.filter((res)=>{
      if(res.toothNumber == value && res.teethType == type){
        return res
      }
    });
    const statues = checkValueList.length == 0
    if (checked && statues) {
      checkedTags.push({toothNumber:value,teethType:type});
    } else if (!checked && !statues) {
      //let checkedTagstemp = checkedTags
      checkedTags = checkedTags.filter((res)=>{
        if(res.toothNumber == value && res.teethType == type){
        }else {
          return res
        }
      })
    }
    this.onChange(checkedTags);
  };

  handleExpand = () => {
    const { expand } = this.state;
    this.setState({
      expand: !expand,
    });
  };

  isTagSelectOption = node =>
    node &&
    node.type &&
    (node.type.isTagSelectOption || node.type.displayName === 'TagSelectOption');

  render() {
    const { value, expand } = this.state;
    const { children, hideCheckAll, className, style, expandable } = this.props;

    const checkedAll = this.getAllTags().length === value.length;

    const cls = classNames(styles.tagSelect, className, {
      [styles.hasExpandTag]: expandable,
      [styles.expanded]: expand,
    });


    return (
      <div className={cls} style={style}>
        {hideCheckAll ? null : (
          <CheckableTag checked={checkedAll} key="tag-select-__all__" onChange={this.onSelectAll}>
            全部
          </CheckableTag>
        )}
        {value &&
          React.Children.map(children, child => {
            if (this.isTagSelectOption(child)) {
              let checkValueList = value.filter((res)=>{
                if(child.props.value == res.toothNumber && child.props.type == res.teethType){
                  return res
                }
              });
              const statues = checkValueList.length != 0
              return React.cloneElement(child, {
                key: `tag-select-${child.props.value}`,
                value: child.props.value,
                checked: statues,
                onChange: this.handleTagChange,
              });
            }
            return child;
          })}
        {expandable && (
          <a className={styles.trigger} onClick={this.handleExpand}>
            {expand ? '收起' : '展开'} <Icon type={expand ? 'up' : 'down'} />
          </a>
        )}
      </div>
    );
  }
}

TagSelect.Option = TagSelectOption;

export default TagSelect;
