import {Row, Col, Form, Input, Modal, Button, AutoComplete, Checkbox, message, Tooltip, Image, Select} from 'antd';
import React, { Component, useState } from 'react';
import { useIntl, FormattedMessage, history } from 'umi';
import { GridContent } from '@ant-design/pro-layout';
import styles from './style.less';
import {StringUtils} from "@/utils/StringUtils";
// 引入图片
import Delete from '@/assets/<EMAIL>'
import blueDelete from '@/assets/<EMAIL>'
import Add from '@/assets/<EMAIL>'
import Preview from "@/assets/<EMAIL>";
import ybImg from "@/assets/<EMAIL>";
import yzImg from "@/assets/<EMAIL>";

import ToothSelect from "@/components/ToothSelect";  //牙位选择
import {toothUtils} from "@/utils/toothUtils";//牙位选择事件
import InforConsent from "@/components/InforConsent"//关联知情同意书

import Screenage from "@/components/Screenage"//关联影像
import TemplateList from "./components/TemplateList"//模板列表
import PopupPeriodontal from "@/components/PopupPeriodontal"//牙周检查
import LookPopupPeriodontal from "@/components/LookPopupPeriodontal"//牙周检查预览
import PopupCommon from "@/components/PopupCommon"//一般检查
import LookPopupCommon from "@/components/LookPopupCommon"//一般检查预览
import { connect } from 'dva';
import CdnImgs from "@/components/CdnImgs";//图片转换
import ToothShow from "@/components/ToothShow";//十字牙位显示组件
import moment from "moment";
import {getAppointmentOpinion} from "@/services/ClinicMornMeet";


const { TextArea } = Input;
const { Option } = Select;

// 表单布局
const layout = {
  labelCol: {
    span: 3,
  },
  wrapperCol: {
    span: 19,
  },
};
// 表单验证
const validateMessages = {
  required: '请填写${label}',
};

class WriteMedical extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      myfileInfo:[],
      clientHeight: document.documentElement.clientHeight, // 屏幕高度
      examine:[{
        toothPosition:"",
        value:"",
      }],//检查
      diag:[{
        toothPosition:"",
        value:"",
        linkCode:"",
      }],//诊断
      treat:[{
        toothPosition:"",
        value:"",
        linkCode:"",
      }],//本次治疗
      dispose:[{
        toothPosition:"",
        value:"",
      }],//处置
      key:"",
      index:"",
      visibleToothBit2:false,//选择牙位弹窗
      inforStatus:false,//关联知情同意书弹窗状态
      photoStatus:false,//关联影像弹窗状态
      periodontalStatus:false,//牙周检查
      pagination6: {...props.state},//患者信息
      item2: {},
      tdData: {},//牙周检查
      ordinaryStatus:false,//一般检查状态
      checkResult: [],//口腔检查数组普通检查
      setModelStatus:false,//是否将病历保存为模板
      saveModel:false,//保存为模板自定义名称弹窗
      sendMedicalParams:{
        illnessDesc:"",//主诉
        preIllness:"",//现病史
        pastHist:"",//既往史
        genCond:"",//全身状况
        auxiExam:"",//辅助检查
        docOrder:"",//医嘱
        isSaveTmpt:0,//是否保存为模板 0否，1是
        tmptName:"",//模板名称
        classCode:"",//模板分类
      },
      genCheck:null,//获取关联的一般检查
      preCheck:null,//获取关联的牙周检查
      linkImgs:[],//辅助检查图片列表
      mrcs:[],//知情同意书列表
      ModelParams:{},//点击模板使用
      // scrollPage:1,
      newKey: new Date() + Math.random(),//form表单key值
      MsgKey: new Date() + Math.random(),//知情同意书key值
      LookperiodontalStatus:false,//牙周检查预览状态
      LookordinaryStatus:false,//一般检查预览状态
      lenovoEntry:[],//联想词条
      treatEntry:[],//本次治疗联想词
      diagEntry:[],//诊断联想词
      diagKey:new Date()+ Math.random(),// 诊断key
      peopleInfo:{},
      rightPatientInfos:{},//患者信息
      chooseImgs:[],//查询关联的影像
      Imgkey: new Date() + Math.random(),//影像资料key值
      yzkey:new Date() + Math.random(),//牙周检查显示key值
      MedicalStatus:3,//病历状态 2编辑中；3已完成；4已归档
      Imgvisible:false,//查看大图打开状态
      bigImgurl:"",//查看大图图片转换后的地址
      uploadingStatus:true,//上传影像判断图片是否正在上传
      MrcuploadingStatus:false,//上传知情同意书判断图片是否正在上传
      finishStatus:false,//保存病历按钮状态
      patientInfoDtos:{
        labelList:[]
      }, //患者标签显示
      DataByGetAppointmentOpinion:null,  // 当前请求的晨会意见
    };
    this.resize = this.resize.bind(this);//屏幕高度
  }
  //初始化
  componentDidMount() {
    this.props.triggerRef(this)
    console.log('propspropsprops123123 :: ',this.props);
    window.addEventListener("resize", this.resize); //增加
    if(this.props.emrSubId){
      this.getEmrMessage(this.props.emrSubId)//获取单个病历详情
      this.findLinkMrcs(this.props.emrSubId)//查询关联同意书
      this.findLinkImages(this.props.emrSubId)//查询关联影像
      this.getGenExamList(this.props.emrSubId)//获取关联一般检查
      this.getyzList(this.props.emrSubId)//获取关联牙周检查
    }
  }
  //在组件卸载及销毁之前直接调用
  componentWillUnmount() {
    window.removeEventListener("resize", this.resize); //取消
  }
  //监听屏幕高度变化
  resize() {
    this.setState({ clientHeight: document.documentElement.clientHeight }); //监听
  }
  //获取单个病历详情
  getEmrMessage=(emrSubId)=>{
    const { dispatch } = this.props;
    let params = {
      tenantId: localStorage.getItem('tenantId'),
      emrId: this.props.emrId,
      emrSubId:emrSubId  //小病历号 保存病历时电子病历系统自动生成的标识
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/getEmrInfoService',
        payload: params,
        callback: (res) => {
          console.log('resresres123123 :: ',res);
          if (res.code == 200) {
            if(res.content!==null){
              // console.log("单个病历详情===",JSON.stringify(res.content))
              this.setState({
                sendMedicalParams:res.content,
                diag:res.content.diag,
                treat:res.content.treat,
                dispose:res.content.dispose,
                examine:res.content.examine,
                newKey: new Date() + Math.random(),
              },()=>{
                this.getAppointmentOpinion()
              });
              if(res.content.status==4){
                history.push({
                  pathname:'/emr/MedicalCenter/AllPanel',
                  state:{
                    patientData:{
                      fileNumber: res.content.emrId?res.content.emrId:this.props.emrId,
                      patientId:res.content.patientId?res.content.patientId:this.props.patientId,
                      id: this.props.id?this.props.id:res.content.appointmentId,
                      name:res.content.patientName?res.content.patientName:this.props.name
                    },
                    patientId:res.content.patientId?res.content.patientId:this.props.patientId,
                    tenantId: localStorage.getItem("tenantId")
                  }
                })
              }
            }else {
              this.getAppointmentOpinion()
            }
            this.getTopPatientInfo()//患者信息
          }
        },
      });
    }
  }

  // 根据预约ID查询预约建议
  getAppointmentOpinion= async ()=>{
   const { appointmentId } =  this.state.sendMedicalParams
    let DataByGetAppointmentOpinion =  await getAppointmentOpinion({
      appointmentId:appointmentId ? appointmentId : this.props.id,
      tenantId:localStorage.getItem('tenantId'),
      organizationId: localStorage.getItem("organizationId")
    })

    console.log('DataByGetAppointmentOpinion1231 :: ',DataByGetAppointmentOpinion);
    if(
      !!DataByGetAppointmentOpinion &&
      DataByGetAppointmentOpinion.code == 200
    ) {
      this.setState({
        DataByGetAppointmentOpinion:DataByGetAppointmentOpinion.content
      },()=>{
        console.log('DataByGetAppointmentOpinion1231 :: ',this.state.DataByGetAppointmentOpinion);
      })
    }else {
      this.setState({
        DataByGetAppointmentOpinion:null
      })
    }
  }


  //获取关联一般检查
  getGenExamList=(emrSubId)=>{
    const { dispatch } = this.props;
    let params = {
      emrSubId:emrSubId  //小病历号 保存病历时电子病历系统自动生成的标识
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/findGenExamsByEmrSubIdService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            if(res.content!==null){
              this.setState({
                genCheck:res.rows,
                newKey: new Date() + Math.random(),
              });
              // console.log("一般检查详情===",this.state.genCheck);
            }
          }
        },
      });
    }
  }

  //获取关联牙周检查
  getyzList=(emrSubId)=>{
    const { dispatch } = this.props;
    let params = {
      emrSubId:emrSubId  //小病历号 保存病历时电子病历系统自动生成的标识
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/findPreExamsByEmrSubIdService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            // console.log("res.content==",res.content)
            if(res.content!==null){
              this.setState({
                preCheck:res.content,
                yztoothPosition:res.content.toothDesc
              });
            }
          }
        },
      });
    }
  }

  // 鼠标移入移出影像图片
  onMouseIn = (mouseIn,index) => {
    this.setState({
      ['mouseIn'+index]: mouseIn
    })
  }
  // 鼠标移入移出影像图片
  chooseonMouseIn=(mouseIn,index) => {
    this.setState({
      ['chooseonMouseIn'+index]: mouseIn
    })
  }
  //知情同意书移入移出
  onMouseIns= (mouseIn,index) => {
    this.setState({
      ['mouseIns'+index]: mouseIn
    })
  }
  //展示牙位
  showModalCheckTooth = (index,key,toothPosition) => {
    this.setState({
      hoverIndex:'',
      toothPosition: toothPosition,
      index:index,
      key:key,
      visibleToothBit2: true,
    });
  }
  // 选择牙位确定
  handleOkSel = (e) => {
    let key = this.state.key;
    let index = this.state.index;
    if(key!='plan'){
      this.state[key][index] = {
        ... this.state[key][index],
        toothPosition: this.tooth.getTooth()
      }
      this.setState({
        [key]: this.state[key],
        visibleToothBit2: false,
      });
    }else{
      this.state.params.plan[index].toothPosition = this.tooth.getTooth();
      this.setState({
        plan: this.state.params.plan,
        visibleToothBit2: false,
      });
    }
  };
  // 关闭选择牙位弹窗
  handleCancelSelTooth = () => {
    this.setState({
      visibleToothBit2: false,
    });
  };
  /**添加新的一项**/
  addRowSelect = key => {
    let arr = this.state[key];
    arr.push({
      value:null,
    });
    this.setState({
      [key]: arr
    });
  }
  /**删除新一项**/
  delRowSelect = (key,index,arrkey) => {
    this.state[key].splice(index,1);
    let arr = this.state[arrkey]
    for(let c in this.state[arrkey]){
      if(c==index){
        arr.splice(c,1);
        break;
      }
    }
    this.setState({
      newKey: new Date() + Math.random(),
      [key]:this.state[key],
    });
  }
  // 关联知情同意书弹窗
  relevance=()=>{
    this.setState({
      inforStatus:true,
    });
  }
  //保存关联知情同意书
  AssisInfo=()=>{
    let arr=this.mrcsListMsg.state.fileList;
    let otherCheck=this.mrcsListMsg.state.MrcMessage;
    // console.log("arrarraar==",arr)
    // console.log("otherCheckotherCheck==",otherCheck)
    if(arr.length>0 || otherCheck){
      if(arr.length==0){
        message.warning({
          content: '请上传文件',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        return;
      }
      if(!otherCheck.tmptName && !otherCheck.tmptCode){
        message.warning({
          content: '请选择类型',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        return;
      }
    }
    arr.forEach((text,index)=>{
      if(text.status=="uploading"){
        message.warning({
          content: '图片正在上传',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        this.state.MrcuploadingStatus=false;
        return;
      }else{
        this.state.MrcuploadingStatus=true;
      }
    })
    if(this.state.MrcuploadingStatus){
      let mrcs=this.state.mrcs;
      arr.forEach((text,index)=>{
        if(text.response&&text.response.content) {
          mrcs.push({
            id: "",
            tmptName: otherCheck.tmptName,
            tmptCode: otherCheck.tmptCode,
            url: text.response.content.fileUrl,
            fileUrlView: text.response.content.fileUrlView,
            createdGmtAt: "",
          })
        }
      })
      // console.log("mrcsmrcsmrcsmrcs",JSON.stringify(mrcs))
      this.setState({
        mrcs:mrcs,
        inforStatus:false,
        MrcuploadingStatus:false
      });
    }
  }
  // 关闭关联知情同意书弹窗
  CancelAssisInfo=()=>{
    this.setState({
      inforStatus:false,
    });
  }
  //查询关联同意书
  findLinkMrcs=(emrSubId)=>{
    const { dispatch } = this.props;
    let params = {
      tenantId: localStorage.getItem('tenantId'),
      emrSubId:emrSubId  //小病历号 保存病历时电子病历系统自动生成的标识
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/findLinkMrcsService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              mrcs:res.rows
            });
          }
        },
      });
    }
  }
  //查询关联影像
  findLinkImages=(emrSubId)=>{
    const { dispatch } = this.props;
    let params = {
      tenantId: localStorage.getItem('tenantId'),
      emrSubId:emrSubId  //小病历号 保存病历时电子病历系统自动生成的标识
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/findLinkImgsService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              chooseImgs:res.rows
            });
          }
        },
      });
    }
  }
  //关联影像弹窗
  relevphoto=()=>{
    this.setState({
      photoStatus:true,
    });
  }
  //保存关联影像
  AssisPhoto=()=>{
    let ImageLibrarys=this.ImageListMsg.state.ImageLibrary;
    let arr=this.ImageListMsg.state.fileList;
    let otherCheck=this.ImageListMsg.state.uploadParams;
    let linkImgss=this.state.linkImgs;
    let chooseImgs=[];
    if(arr.length>0 || otherCheck.uploadDate ||otherCheck.uploadType.length>0 || otherCheck.fileDesc){
      if(otherCheck.uploadType.length==0){
        message.warning({
          content: '请选择类型',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        return;
      }
      if(arr.length==0){
        message.warning({
          content: '请上传文件',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        return;
      }
      arr.forEach((text,index)=>{
        if(text.status=="uploading"){
          message.warning({
            content: '图片正在上传',
            className: 'custom-class',
            style: {
              marginTop: '20vh',
            },
          });
          this.state.uploadingStatus=false;
          return;
        }else{
          this.state.uploadingStatus=true;
        }
      })
      if(!otherCheck.uploadDate){
        message.warning({
          content: '请选择日期',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        return;
      }
    }
    if(this.state.uploadingStatus){
      arr.forEach((text,index)=>{
        if(text.response&&text.response.content) {
          linkImgss.push({
            checkId: "",
            className: otherCheck.uploadType[0].key.className,
            classCode: otherCheck.uploadType[0].key.classCode,
            url: text.response.content.fileUrl,
            fileUrlView: text.response.content.fileUrlView,
            fileDesc: otherCheck.fileDesc,
            shootingTime: otherCheck.uploadDate,//拍摄时间
            createdGmtAt: ""
          })
        }
      })
      if(linkImgss.length==0 && ImageLibrarys.length==0){
        message.warning({
          content: '请选择影像',
          className: 'custom-class',
          style: {
            marginTop: '20vh',
          },
        });
        return;
      }
      this.setState({
        Imgkey: Math.random(),
        linkImgs:linkImgss,//本地上传的
        chooseImgs:ImageLibrarys,
        photoStatus:false,
        uploadingStatus:true
      })
      // console.log("本地上传==",this.state.linkImgs)
    }
  }
  //删除影像
  deleteLinkImgs=(index)=>{
    this.state.linkImgs.splice( index, 1 );
    this.setState({
      linkImgs:this.state.linkImgs,
      Imgkey: new Date() + Math.random(),
    })
  }
  //删除影像，从影像库选择
  deletechooseImgs=(index)=>{
    this.state.chooseImgs.splice( index, 1 );
    this.setState({
      chooseImgs:this.state.chooseImgs,
      Imgkey: new Date() + Math.random(),
    })
  }
  //删除知情同意书
  deleteMsgs=(index)=>{
    this.state.mrcs.splice( index, 1 );
    this.setState({
      mrcs:this.state.mrcs,
      MsgKey: new Date() + Math.random(),
    })
    // console.log("删除后",this.state.mrcs)
  }
  //取消弹框状态
  CancelPhoto=()=>{
    this.setState({
      photoStatus:false,
    });
  }
  //牙周检查
  periodontal=()=>{
    this.setState({
      periodontalStatus:true,
    });
  }
  //预览点击事件
  popupCommon=()=>{
    this.setState({
      LookordinaryStatus:true,
    });
  }
  // 保存牙周检查数据
  handelperiodontal=()=>{
    this.setState({
      // preCheck:this.pPre.getResult(),
      periodontalStatus:false,
    });
    this.state.preCheck=this.pPre.getResult();
    // console.log("牙周检查---",JSON.stringify(this.state.preCheck))
    // console.log("%%%%",this.state.preCheck.toothDesc)
    this.setState({
      yztoothPosition:this.state.preCheck.toothDesc,
      yzkey:new Date() + Math.random(),
    })
  }
  //取消牙周检查点击事件
  CancelPeriodontal=()=>{
    this.setState({
      periodontalStatus:false,
    });
  }

  //一般检查
  ordinary=()=>{
    this.setState({
      ordinaryStatus:true
    })
  }
  //一般检查
  handeordinary=()=>{
    this.setState({
      genCheck: this.pCommon.state.checkResult,
      ordinaryStatus:false
    })
    this.state.genCheck=this.pCommon.state.checkResult;
    // console.log("一般检查#####",this.state.genCheck);
  }
  //取消一般检查点击事件
  Cancelordinary=()=>{
    this.setState({
      ordinaryStatus:false
    })
  }
  //获取牙位检查结果数据
  getCheckResult(position) {
    this.state.genCheck = position;
    this.setState({
      genCheck: position
    })
  }
  //查看牙周检查
  LookExamin=()=>{
    this.setState({
      LookperiodontalStatus:true,
    });
  }
  //查看牙周检查关闭
  cancelLookperiodontal=()=>{
    this.setState({
      LookperiodontalStatus:false,
    });
  }
  //一般检查关闭
  CancelLookordinary=()=>{
    this.setState({
      LookordinaryStatus:false,
    });
  }
  //牙周检查结果删除
  deletePreCheck=()=>{
    this.setState({
      preCheck:null
    })
  }
  // 将病历保存为模板
  savaModel=(e)=>{
    this.setState({
      setModelStatus:e.target.checked
    })
    if(e.target.checked){
      this.state.sendMedicalParams.isSaveTmpt=1;
    }else{
      this.state.sendMedicalParams.isSaveTmpt=0;
      this.state.sendMedicalParams.tmptName="";
    }
    // console.log(`checked = ${e.target.checked}`);
  }
  //保存病历完成按钮
  saveMedical=()=>{
    const { sendMedicalParams,examine,dispose,diag,treat } = this.state;
    // console.log("examineexamine=",examine)
    if(!this.props.currentCure && !sendMedicalParams.illnessDesc){
      message.warning("主诉不能为空");
      return false;
    }
    if(!sendMedicalParams.preIllness||sendMedicalParams.preIllness==""){
      message.warning("现病史不能为空");
      return false;
    }
    if(!sendMedicalParams.pastHist||sendMedicalParams.pastHist==""){
      message.warning("既往史不能为空");
      return false;
    }
    if(!sendMedicalParams.genCond||sendMedicalParams.genCond==""){
      message.warning("全身状况不能为空");
      return false;
    }
    if(!examine[0].value){
      message.warning("检查不能为空");
      return false;
    }
    if(!diag[0].value){
      message.warning("诊断不能为空");
      return false;
    }
    if(!treat[0].value){
      message.warning("本次治疗不能为空");
      return false;
    }
    if(!dispose[0].value){
      message.warning("处置不能为空");
      return false;
    }
    //保存为病历模板
    if(sendMedicalParams.isSaveTmpt==1){
      this.setState({
        saveModel:true
      })
      this.getMyfileInfo()
    }else{
      this.saveMedicalModel()
    }
  }
  //暂存病历
  TsMedical=()=>{
    const {sendMedicalParams } = this.state;
    this.state.MedicalStatus=2;//病历状态 2编辑中；3已完成；4已归档
    this.state.sendMedicalParams.isSaveTmpt=0;
    this.saveMedicalModel()
  }
  //取消病历
  cancelMedical=()=>{
    const {sendMedicalParams } = this.state;
    history.push({
      pathname:'/emr/MedicalCenter/AllPanel',
      state:{
        patientData:{
          fileNumber: sendMedicalParams.emrId?sendMedicalParams.emrId:this.props.emrId,
          patientId:sendMedicalParams.patientId?sendMedicalParams.patientId:this.props.patientId,
          id: this.props.id?this.props.id:sendMedicalParams.appointmentId,
          name:sendMedicalParams.patientName?sendMedicalParams.patientName:this.props.name
        },
        patientId:sendMedicalParams.patientId?sendMedicalParams.patientId:this.props.patientId,
        tenantId: localStorage.getItem("tenantId")
      }
    })
  }
  //保存病历
  saveMedicalModel=()=>{
    const {sendMedicalParams } = this.state;
    const {dispatch} = this.props
    // console.log("参数==",this.state.sendMedicalParams)
    // console.log("检查==",this.state.examine)
    // console.log("诊断==",this.state.diag)
    // console.log("本次治疗==",this.state.treat)
    // console.log("处置==",this.state.dispose)
    let linkImgs=[...this.state.linkImgs,...this.state.chooseImgs];
    // this.state.chooseImgs.forEach((value,index)=>{
    //   linkImgs.push(value)
    // })
    this.setState({
      finishStatus:true
    })
    let params = {
      patientName: sendMedicalParams.patientName?sendMedicalParams.patientName:this.props.name, //患者姓名
      userId: localStorage.getItem('userId'), //医生标识
      userName: localStorage.getItem('userName'), //医生姓名
      tenantId: localStorage.getItem('tenantId'), //平台标识
      organizationId: localStorage.getItem('organizationId'),//机构id
      organizationName: localStorage.getItem('organizationName'), //机构名称
      patientId: sendMedicalParams.patientId?sendMedicalParams.patientId:this.props.patientId, //患者标识
      emrId: sendMedicalParams.emrId?sendMedicalParams.emrId:this.props.emrId, //患者大病历号
      emrSubId: sendMedicalParams.emrSubId?sendMedicalParams.emrSubId:this.props.emrSubId, //患者小病历号 新增时未空，编辑时有值
      appointmentId: sendMedicalParams.appointmentId?sendMedicalParams.appointmentId:this.props.id, //预约标识
      illnessDesc: sendMedicalParams.illnessDesc?sendMedicalParams.illnessDesc:this.props.currentCure, //主诉
      visitIndicator: sendMedicalParams.visitIndicator?sendMedicalParams.visitIndicator:this.props.isFirstVisit, //初复诊标识 0初诊，1复诊
      status:this.state.MedicalStatus, //病历状态 2编辑中；3已完成；4已归档
      emrStatus: sendMedicalParams.emrStatus?sendMedicalParams.emrStatus:0, //病历状态 代表病历是否暂存过 0否1是
      auxiExam: sendMedicalParams.auxiExam, //辅助检查
      diag: this.state.diag,//诊断列表
      dispose: this.state.dispose,//处置
      docOrder: sendMedicalParams.docOrder, //医嘱
      examine: this.state.examine,//检查
      genCond: sendMedicalParams.genCond, //全身状况
      pastHist: sendMedicalParams.pastHist, //既往史
      preIllness: sendMedicalParams.preIllness, //现病史
      treat: this.state.treat,//本次治疗
      clinicTime: this.props.inTime, //到诊时间 必传
      versionStamp: sendMedicalParams.versionStamp?sendMedicalParams.versionStamp:"",   //编辑时必传
      linkImgs: linkImgs,//辅助检查图片列表，处理url后，页面展示用此列表
      mrcs:this.state.mrcs,//知情同意书列表
      genCheck:this.state.genCheck,//一般检查列表
      preCheck: this.state.preCheck,
      isSaveTmpt:sendMedicalParams.isSaveTmpt?sendMedicalParams.isSaveTmpt:0,//是否保存为模板 0否，1是
      tmptName:sendMedicalParams.tmptName?sendMedicalParams.tmptName:"",//模板名称
      classCode:sendMedicalParams.classCode?sendMedicalParams.classCode:"",//模板分类
    };
    // console.log("提交病历参数====",params);
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/saveEmrService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            history.push({
              pathname:'/emr/MedicalCenter/AllPanel',
              state:{
                patientData:{
                  fileNumber: sendMedicalParams.emrId?sendMedicalParams.emrId:this.props.emrId,
                  patientId:sendMedicalParams.patientId?sendMedicalParams.patientId:this.props.patientId,
                  id: this.props.id?this.props.id:sendMedicalParams.appointmentId,
                  name:sendMedicalParams.patientName?sendMedicalParams.patientName:this.props.name
                },
                patientId:sendMedicalParams.patientId?sendMedicalParams.patientId:this.props.patientId,
                tenantId: localStorage.getItem("tenantId")
              }
            })
            this.setState({
              finishStatus:false
            })
          } else {
            message.error({
              content: res.msg,
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              finishStatus:false
            })
          }
        }
      });
    }
  }
  //保存新建病历模板名称
  handleModelOk=()=>{
    if(!this.state.sendMedicalParams.classCode){
      message.warning({
        content: '请选择文件夹名称',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if(!this.state.sendMedicalParams.tmptName){
      message.warning({
        content: '请填写模板名称',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    this.setState({
      saveModel:false
    })
    this.saveMedicalModel()
  }
  //取消新建病历模板名称事件
  handleModelCancel=()=>{
    this.state.sendMedicalParams.tmptName="";
    this.state.sendMedicalParams.classCode="";
    this.setState({
      saveModel:false
    })
  }
  //下拉滑动事件
  // handelscroll=e=>{
  //   e.persist();
  //   const { target } = e;
  //   if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
  //     const { scrollPage } = this.state;
  //     const nextScrollPage = scrollPage + 1;
  //     this.setState({ scrollPage: nextScrollPage });
  //     this.getProposedMsg(nextScrollPage); // 调用api方法
  //   }
  // }
  //联想词条
  getProposedMsg=(nextScrollPage,wordType,value)=>{
    if(value&&value!==""){
      this.getlenovoEntry(nextScrollPage,wordType,value)
    }
  }
  //联想词条
  getlenovoEntry=(nextScrollPage,wordType,value)=>{
    // console.log("value11==",value)
    // console.log("nextScrollPage==",nextScrollPage)
    const {dispatch} = this.props;
    let params = {
      tenantId: localStorage.getItem('tenantId'),//平台标识
      keyWord: value,//关键词,
      wordType:wordType
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/KeyWordService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            let arr=[]
            res.rows.forEach((val,index)=>{
              arr.push({
                key:val.id,
                id:val.id,
                wordCode:val.wordCode,
                wordContent:val.wordContent,
                value:val.wordContent,
              })
            })
            let key = 'lenovoEntry'+wordType;
            this.setState({
              [key]:arr
            })
          } else {
            console.log("查询失败")
          }
        }
      });
    }
  }
  //诊断联想词查询
  DiagByKeyWordMsg=(value)=>{
    if(value){
      this.getDiagByKeyWord(value)
    }
  }
  //获取诊断数据
  getDiagByKeyWord=(value)=>{
    const {dispatch} = this.props;
    let params = {
      tenantId: localStorage.getItem('tenantId'),//平台标识
      keyWord: value,//关键词,
      status:0,//0正常 1停用
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/DiagByKeyWordService',
        payload: params,
        callback: (res) => {
          // console.log("诊断==",JSON.stringify(res))
          if (res.code == 200) {
            let arr=[]
            res.rows.forEach((val,index)=>{
              arr.push({
                key:val.id,
                id:val.id,
                wordCode:val.diagCode,
                stdCode:val.stdCode,
                wordContent:val.diagName,
                value:val.diagName,
              })
            })
            this.setState({
              diagEntry:arr
            })
          } else {
            console.log("查询失败")
          }
        }
      });
    }
  }
  //基础治疗联想查询
  TreatByKeyWordMsg=(value)=>{
    if(value){
      this.getTreatByKeyWord(value)
    }
  }
  //获取本次治疗
  getTreatByKeyWord=(value)=>{
    const {dispatch} = this.props;
    let params = {
      tenantId: localStorage.getItem('tenantId'),//平台标识
      keyWord: value,//关键词,
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/findTreatByKeyWordService',
        payload: params,
        callback: (res) => {
          // console.log("本次治疗==",JSON.stringify(res))
          if (res.code == 200) {
            let arr=[]
            res.rows.forEach((val,index)=>{
              arr.push({
                id:val.id,
                wordCode:val.treatCode,
                wordContent:val.treatName,
                value:val.treatName,
                stdCode:val.stdCode,
              })
            })
            this.setState({
              treatEntry:arr
            })
          } else {
            console.log("查询失败")
          }
        }
      });
    }
  }
  //牙周检查结果鼠标进入进出样式
  preview= (previewStatus) => {
    this.setState({
      ['previewStatus']: previewStatus
    })
  }
  //一般检查结果鼠标进入进出样式
  preview1= (previewStatus1) => {
    this.setState({
      ['previewStatus1']: previewStatus1
    })
  }
  //病历模板详情
  getModelMsg=(id)=>{
    const {dispatch} = this.props
    let params = {
      id:id,//模板代码
      tenantId:localStorage.getItem('tenantId')//平台标识
    };
    if (dispatch) {
      dispatch({
        type: 'medicalModel/EmrMessageService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              ModelParams:res.content,
            })
            if(this.state.ModelParams){
              this.addTo()
            }
          }
        }
      });
    }
  }
  //追加模板
  addTo(){
    const { sendMedicalParams,ModelParams,examine,diag,dispose } = this.state;
    //主诉
    if(ModelParams.illnessDesc){
      // sendMedicalParams.illnessDesc=sendMedicalParams.illnessDesc +" "+ModelParams.illnessDesc;
      sendMedicalParams.illnessDesc=ModelParams.illnessDesc;
    }
    //现病史
    if(ModelParams.preIllnessDesc){
      // sendMedicalParams.preIllness=sendMedicalParams.preIllness +" "+ModelParams.preIllnessDesc;
      sendMedicalParams.preIllness=ModelParams.preIllnessDesc;
    }
    //既往史
    if(ModelParams.pastHistDesc){
      // sendMedicalParams.pastHist=sendMedicalParams.pastHist +" "+ModelParams.pastHistDesc;
      sendMedicalParams.pastHist=ModelParams.pastHistDesc;
    }
    //全身状况
    if(ModelParams.genCondDesc){
      // sendMedicalParams.genCond=sendMedicalParams.genCond +" "+ModelParams.genCondDesc;
      sendMedicalParams.genCond=ModelParams.genCondDesc;
    }
    //检查
    if(ModelParams.examineDesc){
      // examine[0].value=examine[0].value +" "+ModelParams.examineDesc;
      examine[0].value=ModelParams.examineDesc;
    }
    //辅助检查
    if(ModelParams.auxiExamDesc){
      // sendMedicalParams.auxiExam=sendMedicalParams.auxiExam +" "+ModelParams.auxiExamDesc;
      sendMedicalParams.auxiExam=ModelParams.auxiExamDesc;
    }
    //诊断
    if(ModelParams.diagDesc){
      // diag[0].value=diag[0].value +" "+ModelParams.diagDesc;
      diag[0].value=ModelParams.diagDesc;
    }
    //处置
    if(ModelParams.treatDesc){
      // dispose[0].value=dispose[0].value +" "+ModelParams.treatDesc;
      dispose[0].value=ModelParams.treatDesc;
    }
    //医嘱
    if(ModelParams.adviceDesc){
      // sendMedicalParams.docOrder=sendMedicalParams.docOrder+" "+ModelParams.adviceDesc;
      sendMedicalParams.docOrder=ModelParams.adviceDesc;
    }

    this.setState({
      newKey: new Date() + Math.random(),
    })
  }
  //查询患者信息
  getTopPatientInfo = () => {
    const { dispatch } = this.props;
    const { sendMedicalParams } = this.state;
    let params = {
      tenantId:localStorage.getItem('tenantId'),//平台标识
      patientId:sendMedicalParams.patientId?sendMedicalParams.patientId:this.props.patientId,
      organizationId:localStorage.getItem('organizationId'),
    };
    if (dispatch) {
      dispatch({
        type: 'homePage/rightPatientInfo',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              rightPatientInfos: res.content.patientSignBaseDto.baseInfo,
              patientInfoDtos: res.content.patientInfoDto,
            });
          }
        },
      });
    }
  };
  //一般检查结果删除
  deletegenCheck=()=>{
    this.setState({
      genCheck:[]
    })
  }
  //查看大图
  LookImg=(url)=>{
    const { dispatch } = this.props;
    let params = {
      filePath: url,
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/dbPathTransformService',
        payload: params,
        callback: (res) => {
          this.setState({
            Imgvisible:true,
            bigImgurl: res,
          });
        },
      });
    }
  }
  //添加为个人模板的文件夹选择
  handleChange=(value)=>{
    const {myfileInfo}=this.state;
    let arrs="";
    myfileInfo.forEach((key,index)=>{
      if(key.classCode==value){
        arrs=key.classCode;
      }
    })
    this.state.sendMedicalParams.classCode=arrs;
  }
  //获取我的模板下面的模板分类
  getMyfileInfo = () => {
    const { dispatch } = this.props;
    let params = {
      tenantId:localStorage.getItem('tenantId'),//平台标识
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/findMyTempContentService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              myfileInfo:res.rows
            });
          }
        },
      });
    }
  };
  render() {
    const {examine,diag,treat,dispose,genCheck,preCheck,saveModel,sendMedicalParams,newKey,MsgKey,Imgkey,lenovoEntry,treatEntry,ModelParams,diagEntry,diagKey,linkImgs,mrcs,rightPatientInfos,patientInfoDtos,yztoothPosition,yzkey,bigImgurl} = this.state;
    return (
      <GridContent>
        <div className={styles.writeHeight} style={{height:(patientInfoDtos.labelList && patientInfoDtos.labelList.length>0)?this.state.clientHeight-166 : this.state.clientHeight-142}}>
          <div style={{paddingLeft:16}}>
            <Row gutter={16}>
              <Col span={18}>
                <div className={styles.peopleInfo_border}>
                  <div className={styles.peopleInfo}>
                    {/*<div className={styles.group6}>*/}
                    {/*  <span className={styles.word6}>已收费</span>*/}
                    {/*</div>*/}
                    {this.props.emrStatus!==3?
                      <div className={styles.group7}>
                        <span className={styles.txt8}>未完成</span>
                      </div>
                      :''}
                    <span className={styles.fontgray}>
                      {this.props.appointmentDate?this.props.appointmentDate:sendMedicalParams.updatedGmtAt}
                    </span>
                    <span className={`${styles.fontgray} ${styles.nameMaxwidth}`}>
                      {this.props.name?this.props.name:sendMedicalParams.patientName}
                    </span>
                    <span className={styles.fontgray}>{this.props.isFirstVisit==1?'初诊':this.props.isFirstVisit==2?'复诊':''}</span>
                    <span className={styles.fontgray}>{sendMedicalParams.visitIndicator==1?'初诊':sendMedicalParams.visitIndicator==2?'复诊':''}</span>

                  </div>
                  <div style={{width:'50%',textAlign:'right'}}>
                    <span className={styles.assistant} onClick={this.relevphoto}>关联影像</span>
                    <Modal
                      title="关联影像"
                      visible={this.state.photoStatus}
                      destroyOnClose={true}
                      onOk={this.AssisPhoto}
                      onCancel={this.CancelPhoto}
                      okText="确认上传"
                      cancelText="取消"
                      width={900}
                    >
                      <Screenage
                        emrId={sendMedicalParams.emrId?sendMedicalParams.emrId:this.props.emrId}
                        chooseImgs={this.state.chooseImgs}
                        linkImgs={linkImgs}
                        onRef={(ref)=>this.ImageListMsg=ref}
                      />
                    </Modal>
                    <span className={styles.assistant} onClick={this.relevance}>知情同意书</span>
                    <Modal
                      title="关联知情同意书"
                      visible={this.state.inforStatus}
                      destroyOnClose={true}
                      onOk={this.AssisInfo}
                      onCancel={this.CancelAssisInfo}
                      okText="确认上传"
                      cancelText="取消"
                      width={800}
                    >
                      <InforConsent
                        onRef={(ref)=>this.mrcsListMsg=ref}
                      />
                    </Modal>
                    <span className={styles.assistant} onClick={this.periodontal}>牙周检查</span>
                    <Modal
                      title="牙周检查"
                      visible={this.state.periodontalStatus}
                      destroyOnClose={true}
                      onOk={this.handelperiodontal}
                      onCancel={this.CancelPeriodontal}
                      okText="确认"
                      cancelText="取消"
                      width={1400}
                      closable={false}
                      maskClosable={false}
                      className={styles.build}
                    >
                      <PopupPeriodontal
                        emrId={this.props.emrId}
                        patientInfoDtos={patientInfoDtos}
                        rightPatientInfos={rightPatientInfos}
                        state={this.state.pagination6}
                        onRef={(e)=>this.pPre=e}
                        data={preCheck}
                        patientData={this.state.item2}
                        key={this.state.item2.id}
                      />
                    </Modal>
                    {/*牙周检查预览*/}
                    <Modal
                      title="查看牙周检查"
                      visible={this.state.LookperiodontalStatus}
                      destroyOnClose={true}
                      onCancel={this.cancelLookperiodontal}
                      width={1400}
                      footer={[]}
                    >
                      <LookPopupPeriodontal
                        state={this.state.pagination6}
                        data={preCheck}
                        patientData={this.state.item2}
                        key={this.state.item2.id}
                      />
                    </Modal>
                    <span className={styles.assistant} onClick={this.ordinary}>一般检查</span>
                    <Modal
                      title="一般检查"
                      visible={this.state.ordinaryStatus}
                      destroyOnClose={true}
                      onOk={this.handeordinary}
                      onCancel={this.Cancelordinary}
                      okText="确认"
                      cancelText="取消"
                      width={1087}
                    >
                      <PopupCommon
                        emrId={this.props.emrId}
                        patientInfoDtos={patientInfoDtos}
                        rightPatientInfos={rightPatientInfos}
                        onRef={(e)=>this.pCommon=e}
                        state={this.state.pagination6}
                        data={genCheck}
                        patientData={this.state.item2}
                        key={this.state.item2.id}
                      />
                    </Modal>
                    {/*查看一般检查*/}
                    <Modal
                      title="查看一般检查"
                      visible={this.state.LookordinaryStatus}
                      destroyOnClose={true}
                      onCancel={this.CancelLookordinary}
                      width={1087}
                      footer={[]}
                    >
                      <LookPopupCommon
                        data={genCheck}
                        state={this.state.pagination6}
                        patientData={this.state.item2}
                        key={this.state.item2.id}
                      />
                    </Modal>
                  </div>
                </div>
                <Form
                  {...layout}
                  name="nest-messages"
                  validateMessages={validateMessages}
                  colon={false}
                  className={styles.formStyle}
                  style={{height:(patientInfoDtos.labelList && patientInfoDtos.labelList.length>0)?this.state.clientHeight-271 : this.state.clientHeight-249}}
                  autoComplete="off"
                  key={newKey}
                >
                  <Form.Item
                    name='illnessDesc'
                    label="主诉"
                    rules={[{required: true}]}
                    key={this.state['illnessDescKey']}
                  >
                    <div id="illnessDesc">
                      <AutoComplete
                        getPopupContainer={() => document.getElementById('illnessDesc')}
                        maxLength={100}
                        defaultValue={sendMedicalParams.illnessDesc?sendMedicalParams.illnessDesc:this.props.currentCure}
                        onBlur = {()=>{this.setState({['lenovoEntry'+1]: []})}}
                        onFocus = {()=>{this.setState({['lenovoEntry'+1]: []})}}
                        // options={lenovoEntry}
                        options={this.state['lenovoEntry'+1]}
                        onSelect={value => {
                          sendMedicalParams.illnessDesc=value;this.setState({['lenovoEntry'+1]: [],['illnessDescKey']:Math.random()})}}
                        // filterOption={(inputValue, option) =>
                        //   option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                        // }
                      >
                        <TextArea
                          onChange={e=>{sendMedicalParams.illnessDesc=e.target.value;this.getProposedMsg(1,1,e.target.value)}}
                          placeholder="请输入主诉"
                          className="custom"
                          autoSize={{ minRows: 1,maxRows:10}}
                        />
                      </AutoComplete>
                    </div>
                  </Form.Item>
                  <Form.Item
                    name='preIllness'
                    label="现病史"
                    rules={[{required: true}]}
                    key={this.state['preIllnessKey']}
                  >
                    <div id="preIllness">
                      <AutoComplete
                        getPopupContainer={() => document.getElementById('preIllness')}
                        defaultValue={sendMedicalParams.preIllness}
                        onBlur = {()=>{this.setState({['lenovoEntry'+2]: []})}}
                        onFocus = {()=>{this.setState({['lenovoEntry'+2]: []})}}
                        // options={lenovoEntry}
                        options={this.state['lenovoEntry'+2]}
                        onSelect={value=>{sendMedicalParams.preIllness=value;this.setState({['lenovoEntry'+2]: [],['preIllnessKey']:Math.random()})}}
                        filterOption={(inputValue, option) =>
                          option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                        }
                      >
                        <TextArea
                          onChange={e=>{sendMedicalParams.preIllness=e.target.value;this.getProposedMsg(1,2,e.target.value)}}
                          placeholder="请输入现病史"
                          className="custom"
                          autoSize={{ minRows: 1,maxRows:10}}
                        />
                      </AutoComplete>
                    </div>
                  </Form.Item>
                  <Form.Item
                    name='pastHist'
                    label="既往史"
                    rules={[{required: true}]}
                    key={this.state['pastHistKey']}
                  >
                    <div id="pastHist">
                      <AutoComplete
                        getPopupContainer={() => document.getElementById('pastHist')}
                        defaultValue={sendMedicalParams.pastHist}
                        onBlur = {()=>{this.setState({['lenovoEntry'+3]: []})}}
                        onFocus = {()=>{this.setState({['lenovoEntry'+3]: []})}}
                        // options={lenovoEntry}
                        options={this.state['lenovoEntry'+3]}
                        onSelect={value=>{sendMedicalParams.pastHist=value;this.setState({['lenovoEntry'+3]: [],['pastHistKey']:Math.random()})}}
                        filterOption={(inputValue, option) =>
                          option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                        }
                      >
                        <TextArea
                          onChange={e=>{sendMedicalParams.pastHist=e.target.value;this.getProposedMsg(1,3,e.target.value)}}
                          placeholder="请输入既往史"
                          className="custom"
                          autoSize={{ minRows: 1,maxRows:10}}
                        />
                      </AutoComplete>
                    </div>
                  </Form.Item>
                  <Form.Item
                    name='genCond'
                    label="全身状况"
                    rules={[{required: true}]}
                    key={this.state['genCondKey']}
                  >
                    <div id="genCond">
                      <AutoComplete
                        getPopupContainer={() => document.getElementById('genCond')}
                        defaultValue={sendMedicalParams.genCond}
                        onBlur = {()=>{this.setState({['lenovoEntry'+4]: []})}}
                        onFocus = {()=>{this.setState({['lenovoEntry'+4]: []})}}
                        // options={lenovoEntry}
                        options={this.state['lenovoEntry'+4]}
                        onSelect={value=>{sendMedicalParams.genCond=value;this.setState({['lenovoEntry'+4]: [],['genCondKey']:Math.random()})}}
                        filterOption={(inputValue, option) =>
                          option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                        }
                      >
                        <TextArea
                          onChange={e=>{sendMedicalParams.genCond=e.target.value;this.getProposedMsg(1,4,e.target.value)}}
                          placeholder="请输入全身状况"
                          autoSize={{ minRows: 4,maxRows:10}} />
                      </AutoComplete>
                    </div>
                  </Form.Item>
                  <Form.Item
                    name='examine'
                    label="检查"
                    rules={[{required: true}]}
                  >
                    <div className={styles.AllchooseLine}>
                      <table className={styles.table_row} onClick={() => this.showModalCheckTooth(0, "examine",examine[0].toothPosition)}>
                        <tbody>
                        <tr>
                          <td className={styles.line_th_row}>{toothUtils.showTooth(1,examine[0].toothPosition)}</td>
                          <td className={styles.line_th_col}>{toothUtils.showTooth(2,examine[0].toothPosition)}</td>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row}>{toothUtils.showTooth(4,examine[0].toothPosition)}</td>
                          <td className={styles.line_col}>{toothUtils.showTooth(3,examine[0].toothPosition)}</td>
                        </tr>
                        </tbody>
                      </table>
                      <div className={styles.inputStyle} id={examine+0}>
                        <AutoComplete
                          getPopupContainer={() => document.getElementById(examine+0)}
                          onBlur = {()=>{this.setState({['lenovoEntry'+5]: []})}}
                          onFocus = {()=>{this.setState({['lenovoEntry'+5]: []})}}
                          defaultValue={examine[0].value}
                          // options={lenovoEntry}
                          options={this.state['lenovoEntry'+5]}
                          onSelect={value=>{examine[0].value=value;this.setState({['lenovoEntry'+5]: []})}}
                          filterOption={(inputValue, option) =>
                            option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                          }
                        >
                          <TextArea
                            onChange={e=>{examine[0].value=e.target.value;this.getProposedMsg(1,5,e.target.value)}}
                            placeholder="请输入"
                            autoSize={{ minRows: 1,maxRows:10}} />
                        </AutoComplete>
                      </div>
                      <div className={styles.operation}>
                        {this.state.examine.length > 1 ?
                          <img src={blueDelete} className={styles.add} onClick={() => {
                            this.delRowSelect("examine", 0, "checkArr1")
                          }} alt=""/>
                          :
                          <img src={Add} className={styles.add} onClick={() => this.addRowSelect("examine")} alt=""/>
                        }
                      </div>
                    </div>

                    <div>
                      {examine.map((item, index) =>
                        index != 0 ? <div key={index}>
                          <div style={{marginTop:10}} className={styles.AllchooseLine}>
                            <table className={styles.table_row} onClick={() => this.showModalCheckTooth(index, "examine",item.toothPosition)}>
                              <tbody>
                              <tr>
                                <td className={styles.line_th_row}>{toothUtils.showTooth(1,examine[index].toothPosition)}</td>
                                <td className={styles.line_th_col}>{toothUtils.showTooth(2,examine[index].toothPosition)}</td>
                              </tr>
                              </tbody>
                              <tbody>
                              <tr>
                                <td className={styles.line_row}>{toothUtils.showTooth(4,examine[index].toothPosition)}</td>
                                <td className={styles.line_col}>{toothUtils.showTooth(3,examine[index].toothPosition)}</td>
                              </tr>
                              </tbody>
                            </table>
                            <div className={styles.inputStyle} id={examine+index}>
                              <AutoComplete
                                getPopupContainer={() => document.getElementById(examine+index)}
                                onBlur = {()=>{this.setState({['lenovoEntry'+5]: []})}}
                                onFocus = {()=>{this.setState({['lenovoEntry'+5]: []})}}
                                defaultValue={examine[index].value}
                                // options={lenovoEntry}
                                options={this.state['lenovoEntry'+5]}
                                onSelect={value=>{examine[index].value=value;this.setState({['lenovoEntry'+5]: []})}}
                                filterOption={(inputValue, option) =>
                                  option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                                }
                              >
                                <TextArea
                                  onChange={e=>{examine[index].value=e.target.value;this.getProposedMsg(1,5,e.target.value)}}
                                  placeholder="请输入"
                                  autoSize={{ minRows: 1,maxRows:10}} />
                              </AutoComplete>
                            </div>
                            {this.state.examine.length - 1==index?
                              <div className={styles.operation} style={{right:-48}}>
                                <img src={Add} className={styles.add} onClick={() => this.addRowSelect("examine")} alt=""/>
                                <img src={blueDelete} className={styles.add} onClick={() => {
                                  this.delRowSelect("examine", index, "checkArr1")
                                }} alt=""/>
                              </div>
                              :
                              <div className={styles.operation}>
                                <img src={blueDelete} className={styles.add} onClick={() => {
                                  this.delRowSelect("examine", index, "checkArr1")
                                }} alt=""/>
                              </div>
                            }
                          </div>
                        </div> : null
                      )}
                    </div>
                    <div  className={styles.examineLookAll}>
                      <div key={yzkey}>
                        {preCheck?
                          <div className={styles.examineLooks}>
                            <div className={styles.examines}>
                              <div>牙周检查结果</div>
                              <div className={styles.right}><span onClick={this.periodontal}>编辑</span> | <span onClick={this.deletePreCheck}>删除</span></div>
                            </div>
                            <div className={styles.examineBottom}>
                              <div
                                style={{position:'relative'}}
                                onMouseOver={()=>this.preview(true)}
                                onMouseOut={()=>this.preview(false)}
                              >
                                <img className={styles.ctimgStyle} style={{height:120}} src={yzImg} alt=""/>
                                <div
                                  hidden={!this.state['previewStatus']}
                                  className={styles.ctimgdelete}
                                >
                                  <div style={{marginTop:'35%'}}>
                                    <div style={{cursor:'pointer'}} onClick={this.LookExamin}>
                                      <img src={Preview} className={styles.icon_delete} style={{marginLeft:5}} alt=""/>
                                      <span className={styles.deleteFont}>预览</span>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div style={{maxHeight:120,overflowY:"hidden",padding:5}}>
                                <div>
                                  {yztoothPosition? Object.keys((yztoothPosition.occRelation||{})).map(key=>
                                    <Form.Item
                                      name={key}
                                      hidden={StringUtils.isBlank(yztoothPosition.occRelation[key].toothPosition)&&StringUtils.isBlank(yztoothPosition.occRelation[key].value)}
                                    >
                                      <div className={styles.fontLineStyle} style={{display:'flex',minWidth:0}}>
                                        <div style={{width:120}}>
                                          <ToothShow tooth={yztoothPosition.occRelation[key].toothPosition}/>
                                        </div>
                                        <Tooltip title={yztoothPosition.occRelation[key].name+'：'+(yztoothPosition.occRelation[key].value?yztoothPosition.occRelation[key].value:'')}>
                                          <div style={{width:30,marginTop:15}} className={styles.fontLineStyle}>{yztoothPosition.occRelation[key].name}：{yztoothPosition.occRelation[key].value}</div>
                                        </Tooltip>
                                      </div>
                                    </Form.Item>
                                  ):null}
                                  {yztoothPosition? Object.keys((yztoothPosition.other||{})).map(key=>
                                    <Form.Item
                                      name={key}
                                      hidden={StringUtils.isBlank(yztoothPosition.other[key].toothPosition)&&StringUtils.isBlank(yztoothPosition.other[key].value)}
                                    >
                                      <div className={styles.fontLineStyle} style={{display:'flex',minWidth:0}}>
                                        <div style={{width:120}}>
                                          <ToothShow tooth={yztoothPosition.other[key].toothPosition}/>
                                        </div>
                                        <Tooltip title={yztoothPosition.other[key].name+'：'+(yztoothPosition.other[key].value?yztoothPosition.other[key].value:'')}>
                                          <div style={{width:30,marginTop:15}} className={styles.fontLineStyle}>{yztoothPosition.other[key].name}：{yztoothPosition.other[key].value}</div>
                                        </Tooltip>
                                      </div>
                                    </Form.Item>
                                  ):null}
                                </div>
                                <Tooltip title={preCheck.examRemark}>
                                  <div className={styles.fontLineStyle}>检查描述：{preCheck.examRemark}</div>
                                </Tooltip>
                              </div>
                            </div>
                          </div>
                          :null}
                      </div>

                      {genCheck&&genCheck.length>0?
                        <div className={styles.examineLooks}>
                          <div className={styles.examines}>
                            <div>一般检查结果</div>
                            <div className={styles.right}><span onClick={this.ordinary}>编辑</span> | <span onClick={this.deletegenCheck}>删除</span></div>
                          </div>
                          <div className={styles.examineBottom}>
                            <div
                              style={{position:'relative'}}
                              onMouseOver={()=>this.preview1(true)}
                              onMouseOut={()=>this.preview1(false)}
                            >
                              <img className={styles.ctimgStyle} style={{height:120}} src={ybImg} alt=""/>
                              <div
                                hidden={!this.state['previewStatus1']}
                                className={styles.ctimgdelete}
                              >
                                <div style={{marginTop:'35%'}}>
                                  <div style={{cursor:'pointer'}} onClick={this.popupCommon}>
                                    <img src={Preview} className={styles.icon_delete} style={{marginLeft:5}} alt=""/>
                                    <span className={styles.deleteFont}>预览</span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div style={{maxHeight:120,overflowY:"hidden",padding:5}}>
                              {(genCheck||[]).map((item,index)=>
                                <div key={index} style={{display:'flex',marginBottom:8}}>
                                  <ToothShow tooth={item.toothPosition}/>
                                  <Tooltip title={item.examName}>
                                    <div style={{width:30,marginTop:15}} className={styles.fontLineStyle}>
                                      <span>{item.examName}</span>
                                      { item.toothDesc?<span>{item.toothDesc.BSelected==true?",颊侧":""}
                                        {item.toothDesc.MSelected==true?",近中":""}
                                        {item.toothDesc.DSelected==true?",远中":""}
                                        {item.toothDesc.OSelected == true ?",颌面":""}
                                        {item.toothDesc.LSelected==true?",舌侧":""}</span>:null}
                                    </div>
                                  </Tooltip>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        :null
                      }
                    </div>
                  </Form.Item>

                  <Modal
                    title="选择牙位"
                    visible={this.state.visibleToothBit2}
                    destroyOnClose={true}
                    onOk={this.handleOkSel}
                    onCancel={this.handleCancelSelTooth}
                    okText="保存"
                    cancelText="取消"
                    width={1070}
                  >
                    <ToothSelect toothPosition = {this.state.toothPosition} onRef={(ref) => this.tooth = ref}></ToothSelect>
                  </Modal>
                  <Form.Item
                    name='auxiExam'
                    label="辅助检查"
                  >
                    <div id="auxiExam">
                      <AutoComplete
                        getPopupContainer={() => document.getElementById('auxiExam')}
                        defaultValue={sendMedicalParams.auxiExam}
                        onBlur = {()=>{this.setState({['lenovoEntry'+6]: []})}}
                        onFocus = {()=>{this.setState({['lenovoEntry'+6]: []})}}
                        // options={lenovoEntry}
                        options={this.state['lenovoEntry'+6]}
                        onSelect={value=>{sendMedicalParams.auxiExam=value;this.setState({['lenovoEntry'+6]: []})}}
                        filterOption={(inputValue, option) =>
                          option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                        }
                      >
                        <TextArea
                          onChange={e=>{sendMedicalParams.auxiExam=e.target.value;this.getProposedMsg(1,6,e.target.value)}}
                          placeholder="请输入全身状况"
                          autoSize={{ minRows: 4,maxRows:10}} />
                      </AutoComplete>
                    </div>
                  </Form.Item>
                  {this.state.chooseImgs&&this.state.chooseImgs.length>0 || linkImgs&&linkImgs.length>0?
                    <Form.Item
                      name='linkImgs'
                      label="影像资料"
                      key={Imgkey}
                    >

                      <div className={styles.videoImg}>
                        {this.state.chooseImgs.map((item,index)=>(
                          <div className={styles.videomargin} key={index}>
                            <div>{item.className}</div>
                            <div className={styles.images}>
                              <div
                                onMouseOver={()=>this.chooseonMouseIn(true,index)}
                                onMouseOut={()=>this.chooseonMouseIn(false,index)}
                                style={{width:118}}
                                className={styles.imgborder}>
                                {item.fileUrlView?
                                  <img style={{ width: '100%', height:88 }} src={item.fileUrlView} />
                                  :
                                  <CdnImgs
                                    onRef={(ref)=>this.LookImgMsg=ref}
                                    height={88}
                                    fileUrl={item.url}
                                  />
                                }
                                {/*<img className={styles.ctimgStyle} src={item.fileUrlView} alt=""/>*/}
                                <div
                                  hidden={!this.state['chooseonMouseIn'+index]}
                                  className={styles.ctimgdelete}
                                >
                                  <div style={{display:'flex',marginTop:'24%'}}>
                                    <div style={{cursor:'pointer'}} onClick={this.LookImg.bind(this,item.url)}>
                                      <img src={Preview} className={styles.icon_delete} style={{marginLeft:5}} alt=""/>
                                      <span className={styles.deleteFont}>预览</span>
                                    </div>
                                    <div style={{cursor:'pointer',marginLeft:5}} onClick={this.deletechooseImgs.bind(this,index)}>
                                      <img src={Delete} className={styles.icon_delete} alt=""/>
                                      <span className={styles.deleteFont}>删除</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className={styles.ctimgInfo}>
                                <div className={styles.lineHeightStyle}>拍摄时间：{item.shootingTime}</div>
                                <div className={styles.lineHeightStyle}>上传时间：{item.createdGmtAt}</div>
                                <Tooltip title={item.fileDesc}>
                                  <div className={`${styles.fontLineStyle} ${styles.lineHeightStyle}`}>影像分析：{item.fileDesc}</div>
                                </Tooltip>
                              </div>
                            </div>
                          </div>
                        ))}
                        {linkImgs.map((item1,index1)=>(
                          <div className={styles.videomargin} key={index1}>
                            <div>{item1.className}</div>
                            <div className={styles.images}>
                              <div
                                onMouseOver={()=>this.onMouseIn(true,index1)}
                                onMouseOut={()=>this.onMouseIn(false,index1)}
                                style={{width:118}}
                                className={styles.imgborder}>
                                {item1.fileUrlView?
                                  <img style={{ width: '100%', height:88 }} src={item1.fileUrlView} />
                                  :
                                  <CdnImgs
                                    onRef={(ref)=>this.LookImgMsg=ref}
                                    height={88}
                                    fileUrl={item1.url}
                                  />
                                }
                                {/*<img className={styles.ctimgStyle} src={item.fileUrlView} alt=""/>*/}
                                <div
                                  hidden={!this.state['mouseIn'+index1]}
                                  className={styles.ctimgdelete}
                                >
                                  <div style={{display:'flex',marginTop:'24%'}}>
                                    <div style={{cursor:'pointer'}} onClick={this.LookImg.bind(this,item1.url)}>
                                      <img src={Preview} className={styles.icon_delete} style={{marginLeft:5}} alt=""/>
                                      <span className={styles.deleteFont}>预览</span>
                                    </div>
                                    <div style={{cursor:'pointer',marginLeft:5}} onClick={this.deleteLinkImgs.bind(this,index1)}>
                                      <img src={Delete} className={styles.icon_delete} alt=""/>
                                      <span className={styles.deleteFont}>删除</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className={styles.ctimgInfo}>
                                <div className={styles.lineHeightStyle}>拍摄时间：{item1.shootingTime}</div>
                                <div className={styles.lineHeightStyle}>上传时间：{item1.createdGmtAt&&item1.createdGmtAt!==""?item1.createdGmtAt:moment(new Date()).format('YYYY-MM-DD')}</div>
                                <Tooltip title={item1.fileDesc}>
                                  <div className={`${styles.fontLineStyle} ${styles.lineHeightStyle}`}>影像分析：{item1.fileDesc}</div>
                                </Tooltip>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </Form.Item>:""
                  }
                  <Form.Item
                    name='diag'
                    label="诊断"
                    rules={[{required: true}]}
                  >
                    <div className={styles.AllchooseLine}>
                      <table className={styles.table_row} onClick={() => this.showModalCheckTooth(0, "diag",diag[0].toothPosition)}>
                        <tbody>
                        <tr>
                          <td className={styles.line_th_row}>{toothUtils.showTooth(1,diag[0].toothPosition)}</td>
                          <td className={styles.line_th_col}>{toothUtils.showTooth(2,diag[0].toothPosition)}</td>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row}>{toothUtils.showTooth(4,diag[0].toothPosition)}</td>
                          <td className={styles.line_col}>{toothUtils.showTooth(3,diag[0].toothPosition)}</td>
                        </tr>
                        </tbody>
                      </table>
                      <div className={styles.inputStyle} id={diag+0}>
                        <AutoComplete
                          getPopupContainer={() => document.getElementById(diag+0)}
                          onBlur = {()=>{this.setState({diagEntry: []})}}
                          onFocus = {()=>{this.setState({diagEntry: []})}}
                          defaultValue={diag[0].value}
                          options={diagEntry}
                          // onSelect={value=>{diag[0].value=value;this.setState({diagEntry: []})}}
                          onSelect={(value, option)=> {
                            diag[0].value=value;
                            diag[0].linkCode=option.wordCode;
                            diag[0].stdCode=option.stdCode;
                            this.setState({diagEntry: []})
                          }}
                          filterOption={(inputValue, option) =>
                            option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                          }
                        >
                          <TextArea
                            onChange={e=>{diag[0].value=e.target.value;this.DiagByKeyWordMsg(e.target.value)}}
                            placeholder="请输入"
                            autoSize={{ minRows: 1,maxRows:10}} />
                        </AutoComplete>
                      </div>
                      <div className={styles.operation}>
                        {this.state.diag.length > 1 ?
                          <img src={blueDelete} className={styles.add} onClick={() => {
                            this.delRowSelect("diag", 0, "checkArr1")
                          }} alt=""/>
                          :
                          <img src={Add} className={styles.add} onClick={() => this.addRowSelect("diag")} alt=""/>
                        }
                      </div>
                    </div>

                    <div>
                      {diag.map((item, index) =>
                        index != 0 ? <div key={index}>
                          <div style={{marginTop:10}} className={styles.AllchooseLine}>
                            <table className={styles.table_row} onClick={() => this.showModalCheckTooth(index, "diag",item.toothPosition)}>
                              <tbody>
                              <tr>
                                <td className={styles.line_th_row}>{toothUtils.showTooth(1,diag[index].toothPosition)}</td>
                                <td className={styles.line_th_col}>{toothUtils.showTooth(2,diag[index].toothPosition)}</td>
                              </tr>
                              </tbody>
                              <tbody>
                              <tr>
                                <td className={styles.line_row}>{toothUtils.showTooth(4,diag[index].toothPosition)}</td>
                                <td className={styles.line_col}>{toothUtils.showTooth(3,diag[index].toothPosition)}</td>
                              </tr>
                              </tbody>
                            </table>
                            <div className={styles.inputStyle} id={diag+index}>
                              <AutoComplete
                                getPopupContainer={() => document.getElementById(diag+index)}
                                onBlur = {()=>{this.setState({diagEntry: []})}}
                                onFocus = {()=>{this.setState({diagEntry: []})}}
                                defaultValue={diag[index].value}
                                options={diagEntry}
                                onSelect={(value, option)=> {
                                  diag[index].value=value;
                                  diag[index].linkCode=option.wordCode;
                                  diag[index].stdCode=option.stdCode;
                                  this.setState({diagEntry: []})
                                }}
                                filterOption={(inputValue, option) =>
                                  option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                                }
                              >
                                <TextArea
                                  onChange={e=>{diag[index].value=e.target.value;this.DiagByKeyWordMsg(e.target.value)}}
                                  placeholder="请输入"
                                  autoSize={{ minRows: 1,maxRows:10}} />
                              </AutoComplete>
                            </div>
                            {this.state.diag.length - 1==index?
                              <div className={styles.operation} style={{right:-48}}>
                                <img src={Add} className={styles.add} onClick={() => this.addRowSelect("diag")} alt=""/>
                                <img src={blueDelete} className={styles.add} onClick={() => {
                                  this.delRowSelect("diag", index, "checkArr1")
                                }} alt=""/>
                              </div>
                              :
                              <div className={styles.operation}>
                                <img src={blueDelete} className={styles.add} onClick={() => {
                                  this.delRowSelect("diag", index, "checkArr1")
                                }} alt=""/>
                              </div>
                            }
                          </div>
                        </div> : null
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item
                    name='treat'
                    label="本次治疗"
                    rules={[{required: true}]}
                  >
                    <div className={styles.AllchooseLine}>
                      <table className={styles.table_row} onClick={() => this.showModalCheckTooth(0, "treat",treat[0].toothPosition)}>
                        <tbody>
                        <tr>
                          <td className={styles.line_th_row}>{toothUtils.showTooth(1,treat[0].toothPosition)}</td>
                          <td className={styles.line_th_col}>{toothUtils.showTooth(2,treat[0].toothPosition)}</td>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row}>{toothUtils.showTooth(4,treat[0].toothPosition)}</td>
                          <td className={styles.line_col}>{toothUtils.showTooth(3,treat[0].toothPosition)}</td>
                        </tr>
                        </tbody>
                      </table>
                      <div className={styles.inputStyle} id={treat+0}>
                        <AutoComplete
                          getPopupContainer={() => document.getElementById(treat+0)}
                          onBlur = {()=>{this.setState({treatEntry: []})}}
                          onFocus = {()=>{this.setState({treatEntry: []})}}
                          defaultValue={treat[0].value}
                          options={treatEntry}
                          onSelect={(value, option)=> {
                            treat[0].value=value;
                            treat[0].linkCode=option.wordCode;
                            treat[0].stdCode=option.stdCode;
                            this.setState({treatEntry: []})
                          }}
                          filterOption={(inputValue, option) =>
                            option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                          }
                        >
                          <TextArea
                            onChange={e=>{treat[0].value=e.target.value;this.TreatByKeyWordMsg(e.target.value)}}
                            placeholder="请输入"
                            autoSize={{ minRows: 1,maxRows:10}} />
                        </AutoComplete>
                      </div>
                      <div className={styles.operation}>
                        {this.state.treat.length > 1 ?
                          <img src={blueDelete} className={styles.add} onClick={() => {
                            this.delRowSelect("treat", 0, "checkArr1")
                          }} alt=""/>
                          :
                          <img src={Add} className={styles.add} onClick={() => this.addRowSelect("treat")} alt=""/>
                        }
                      </div>
                    </div>

                    <div>
                      {treat.map((item, index) =>
                        index != 0 ? <div key={index}>
                          <div style={{marginTop:10}} className={styles.AllchooseLine}>
                            <table className={styles.table_row} onClick={() => this.showModalCheckTooth(index, "treat",item.toothPosition)}>
                              <tbody>
                              <tr>
                                <td className={styles.line_th_row}>{toothUtils.showTooth(1,treat[index].toothPosition)}</td>
                                <td className={styles.line_th_col}>{toothUtils.showTooth(2,treat[index].toothPosition)}</td>
                              </tr>
                              </tbody>
                              <tbody>
                              <tr>
                                <td className={styles.line_row}>{toothUtils.showTooth(4,treat[index].toothPosition)}</td>
                                <td className={styles.line_col}>{toothUtils.showTooth(3,treat[index].toothPosition)}</td>
                              </tr>
                              </tbody>
                            </table>
                            <div className={styles.inputStyle} id={treat+index}>
                              <AutoComplete
                                getPopupContainer={() => document.getElementById(treat+index)}
                                onBlur = {()=>{this.setState({treatEntry: []})}}
                                onFocus = {()=>{this.setState({treatEntry: []})}}
                                defaultValue={treat[index].value}
                                options={treatEntry}
                                onSelect={(value, option)=> {
                                  treat[index].value=value;
                                  treat[index].linkCode=option.wordCode;
                                  treat[index].stdCode=option.stdCode;
                                  this.setState({treatEntry: []})
                                }}
                                filterOption={(inputValue, option) =>
                                  option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                                }
                              >
                                <TextArea
                                  onChange={e=>{treat[index].value=e.target.value;this.TreatByKeyWordMsg(e.target.value)}}
                                  placeholder="请输入"
                                  autoSize={{ minRows: 1,maxRows:10}} />
                              </AutoComplete>
                            </div>
                            {this.state.treat.length - 1==index?
                              <div className={styles.operation} style={{right:-48}}>
                                <img src={Add} className={styles.add} onClick={() => this.addRowSelect("treat")} alt=""/>
                                <img src={blueDelete} className={styles.add} onClick={() => {
                                  this.delRowSelect("treat", index, "checkArr1")
                                }} alt=""/>
                              </div>
                              :
                              <div className={styles.operation}>
                                <img src={blueDelete} className={styles.add} onClick={() => {
                                  this.delRowSelect("treat", index, "checkArr1")
                                }} alt=""/>
                              </div>
                            }
                          </div>
                        </div> : null
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item
                    name='dispose'
                    label="处置"
                    rules={[{required: true}]}
                  >
                    <div className={styles.AllchooseLine}>
                      <table className={styles.table_row} onClick={() => this.showModalCheckTooth(0, "dispose",dispose[0].toothPosition)}>
                        <tbody>
                        <tr>
                          <td className={styles.line_th_row}>{toothUtils.showTooth(1,dispose[0].toothPosition)}</td>
                          <td className={styles.line_th_col}>{toothUtils.showTooth(2,dispose[0].toothPosition)}</td>
                        </tr>
                        </tbody>
                        <tbody>
                        <tr>
                          <td className={styles.line_row}>{toothUtils.showTooth(4,dispose[0].toothPosition)}</td>
                          <td className={styles.line_col}>{toothUtils.showTooth(3,dispose[0].toothPosition)}</td>
                        </tr>
                        </tbody>
                      </table>
                      <div className={styles.inputStyle} id={dispose+0}>
                        <AutoComplete
                          getPopupContainer={() => document.getElementById(dispose+0)}
                          onBlur = {()=>{this.setState({['lenovoEntry'+7]: []})}}
                          onFocus = {()=>{this.setState({['lenovoEntry'+7]: []})}}
                          defaultValue={dispose[0].value}
                          // options={lenovoEntry}
                          options={this.state['lenovoEntry'+7]}
                          onSelect={value=>{dispose[0].value=value;this.setState({['lenovoEntry'+7]: []})}}
                          filterOption={(inputValue, option) =>
                            option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                          }
                        >
                          <TextArea
                            onChange={e=>{dispose[0].value=e.target.value;this.getProposedMsg(1,7,e.target.value)}}
                            placeholder="请输入"
                            autoSize={{ minRows: 1,maxRows:10}} />
                        </AutoComplete>
                      </div>
                      <div className={styles.operation}>
                        {this.state.dispose.length > 1 ?
                          <img src={blueDelete} className={styles.add} onClick={() => {
                            this.delRowSelect("dispose", 0, "checkArr1")
                          }} alt=""/>
                          :
                          <img src={Add} className={styles.add} onClick={() => this.addRowSelect("dispose")} alt=""/>
                        }
                      </div>
                    </div>

                    <div>
                      {dispose.map((item, index) =>
                        index != 0 ? <div key={index}>
                          <div style={{marginTop:10}} className={styles.AllchooseLine}>
                            <table className={styles.table_row} onClick={() => this.showModalCheckTooth(index, "dispose",item.toothPosition)}>
                              <tbody>
                              <tr>
                                <td className={styles.line_th_row}>{toothUtils.showTooth(1,dispose[index].toothPosition)}</td>
                                <td className={styles.line_th_col}>{toothUtils.showTooth(2,dispose[index].toothPosition)}</td>
                              </tr>
                              </tbody>
                              <tbody>
                              <tr>
                                <td className={styles.line_row}>{toothUtils.showTooth(4,dispose[index].toothPosition)}</td>
                                <td className={styles.line_col}>{toothUtils.showTooth(3,dispose[index].toothPosition)}</td>
                              </tr>
                              </tbody>
                            </table>
                            <div className={styles.inputStyle} id={dispose+index}>
                              <AutoComplete
                                getPopupContainer={() => document.getElementById(dispose+index)}
                                onBlur = {()=>{this.setState({['lenovoEntry'+7]: []})}}
                                onFocus = {()=>{this.setState({['lenovoEntry'+7]: []})}}
                                defaultValue={dispose[index].value}
                                // options={lenovoEntry}
                                options={this.state['lenovoEntry'+7]}
                                onSelect={value=>{dispose[index].value=value;this.setState({['lenovoEntry'+7]: []})}}
                                filterOption={(inputValue, option) =>
                                  option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                                }
                              >
                                <TextArea
                                  onChange={e=>{dispose[index].value=e.target.value;this.getProposedMsg(1,7,e.target.value)}}
                                  placeholder="请输入"
                                  autoSize={{ minRows: 1,maxRows:10}} />
                              </AutoComplete>
                            </div>
                            {this.state.dispose.length - 1==index?
                              <div className={styles.operation} style={{right:-48}}>
                                <img src={Add} className={styles.add} onClick={() => this.addRowSelect("dispose")} alt=""/>
                                <img src={blueDelete} className={styles.add} onClick={() => {
                                  this.delRowSelect("dispose", index, "checkArr1")
                                }} alt=""/>
                              </div>
                              :
                              <div className={styles.operation}>
                                <img src={blueDelete} className={styles.add} onClick={() => {
                                  this.delRowSelect("dispose", index, "checkArr1")
                                }} alt=""/>
                              </div>
                            }
                          </div>
                        </div> : null
                      )}
                    </div>
                  </Form.Item>
                  <Form.Item
                    name='docOrder'
                    label="医嘱"
                  >
                    <TextArea
                      defaultValue={sendMedicalParams.docOrder}
                      onChange={e=>{sendMedicalParams.docOrder=e.target.value;}}
                      placeholder="请输入医嘱"
                      rows={4} />
                  </Form.Item>
                  {mrcs && mrcs.length>0?
                    <Form.Item
                      name='mrcs'
                      label="知情同意书"
                      key={MsgKey}
                    >
                      <div className={styles.agressBook}>
                        {mrcs.map((item,index)=>(
                          <div className={styles.agressMargin} key={index} >
                            <div>{item.tmptName}</div>
                            <div>
                              <div
                                className={styles.imgborder}
                                style={{width: 132,position: 'relative'}}
                                onMouseOver={()=>this.onMouseIns(true,index)}
                                onMouseOut={()=>this.onMouseIns(false,index)}
                              >
                                {item.fileUrlView?
                                  <img style={{ width: '100%', height:105 }} src={item.fileUrlView} />
                                  :
                                  <CdnImgs
                                    height={105}
                                    fileUrl={item.url}
                                    onRef={(ref)=>this.LookImgMsg=ref}
                                  />
                                }
                                <div
                                  hidden={!this.state['mouseIns'+index]}
                                  className={styles.ctimgdelete}
                                >
                                  <div style={{display:'flex',marginTop:'28%'}}>
                                    <div style={{cursor:'pointer'}} onClick={this.LookImg.bind(this,item.url)}>
                                      <img src={Preview} className={styles.icon_delete} style={{marginLeft:7}} alt=""/>
                                      <span className={styles.deleteFont}>预览</span>
                                    </div>
                                    <div style={{cursor:'pointer',marginLeft:10}} onClick={this.deleteMsgs.bind(this,index)}>
                                      <img src={Delete} className={styles.icon_delete} alt=""/>
                                      <span className={styles.deleteFont}>删除</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </Form.Item>:""
                  }
                  {/*<Form.Item wrapperCol={{ ...layout.wrapperCol, offset: 8 }}>*/}
                  {/*  <Button type="primary" htmlType="submit">*/}
                  {/*    Submit*/}
                  {/*  </Button>*/}
                  {/*</Form.Item>*/}
                </Form>
                <Image
                  width={200}
                  style={{
                    display: 'none',
                  }}
                  preview={{
                    visible:this.state.Imgvisible,
                    src:bigImgurl,
                    onVisibleChange: (value) => {
                      this.setState({
                        Imgvisible:false
                      })
                    },
                  }}
                />
              </Col>
              <Col span={6}>
                <div style={{border:'1px solid #E5E6EB',height:'100%'}}>
                  <TemplateList
                    patientInfoDtos={patientInfoDtos}
                    getModelMsg={this.getModelMsg.bind(this)}
                  />
                </div>
              </Col>
            </Row>
          </div>
          <div className={styles.bottomBtns}>
            <div className={styles.left_boxBtns}>
              {this.state.DataByGetAppointmentOpinion &&
                <div className={styles.AppointmentOpinionWarp}>
                  <div className={styles.AppointmentOpinionTitle}>
                    晨会意见:
                  </div>
                  <Tooltip placement="top" title={this.state.DataByGetAppointmentOpinion}>
                    <div className={styles.AppointmentOpinionContent}>
                      {this.state.DataByGetAppointmentOpinion}
                    </div>
                  </Tooltip>
                </div>
              }
            </div>
            <div className={styles.btn_position}>
              <Checkbox className={styles.checked} onChange={this.savaModel}>将此份病历保存为模板</Checkbox>
              <Button
                className={styles.mL_16}
                onClick={this.cancelMedical}
              >取消</Button>
              <Button
                disabled={this.state.finishStatus ||sendMedicalParams.status==4}
                className={styles.mL_16}
                onClick={this.TsMedical}
              >暂存</Button>
              <Button
                disabled={this.state.finishStatus ||sendMedicalParams.status==4}
                onClick={this.saveMedical}
                className={styles.mL_16}
                type="primary">完成</Button>
            </div>
          </div>
        </div>
        <Modal
          title="保存病历模板"
          visible={saveModel}
          destroyOnClose={true}
          onOk={this.handleModelOk}
          onCancel={this.handleModelCancel}
          okText="保存"
          cancelText="取消"
          width={600}
          maskClosable={false}
        >
          <Form
            name="basic"
            labelCol={{
              span: 4,
            }}
            wrapperCol={{
              span: 20,
            }}
            initialValues={{
              remember: true,
            }}
            autoComplete="off"
          >
            <Form.Item
              label="二级目录"
              name="filename"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Select placeholder="请选择" style={{ width: '100%' }} onChange={this.handleChange} >
                {this.state.myfileInfo.map((item,index)=>(
                  <Option key={index} value={item.classCode}>{item.className}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item
              label="模板名称"
              name="username"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Input
                onChange={e=>{sendMedicalParams.tmptName=e.target.value}}
                placeholder="请输入模板名称"/>
            </Form.Item>
          </Form>
        </Modal>
      </GridContent>
    );
  }
}

export default connect(({ Classification,loading }) => ({}))(WriteMedical);
