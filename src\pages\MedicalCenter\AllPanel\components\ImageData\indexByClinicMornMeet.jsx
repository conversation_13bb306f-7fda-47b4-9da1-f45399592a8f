// import { PlusOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import { UploadOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  DatePicker,
  Form,
  Image,
  Input,
  message,
  Modal,
  Row,
  Select,
  Spin,
  Upload,
} from 'antd';
import moment from 'moment';
import React, { Component } from 'react';
// import { useIntl, FormattedMessage } from 'umi';
import Delete from '@/assets/<EMAIL>';
import Preview from '@/assets/<EMAIL>';
import LoadingOutlined from '@ant-design/icons/lib/icons/LoadingOutlined';
import { GridContent } from '@ant-design/pro-layout';
import { connect } from 'dva';
import styles from './style.less';//引入样式
import TimeRecord from './TimeRecordByClinicMornMeet'; //按时间顺序
import InforConsent from './InforConsent/index'
import PropTypes from 'prop-types';
//Base64图片

const { TextArea } = Input;
/**form表单控制布局**/
const modalLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};

class ImageData extends Component {
  /*
  ImageData: undefined
  callback: ƒ (value)
  childKey: 1
  dispatch: ƒ (action)
  emrLoading: false
  imageListParams: undefined
  imgByClass: []
  imgByClassCount: 10
  imgByDate: []
  imgByDateCount: 10
  loading: undefined
  patientData: {
    fileNumber: "JY0A000051"
    name: "测试预约流程"
    patientId: 1386
  }
  tabKey: "2"
  * */

  static propsType = {
    ImageData:PropTypes.any,
    callback:PropTypes.any,
    dispatch:PropTypes.any,
    emrLoading: PropTypes.any,
    imageListParams: PropTypes.any,
    imgByClass: PropTypes.any,
    imgByClassCount: PropTypes.any,
    imgByDate: PropTypes.any,
    imgByDateCount: PropTypes.any,
    loading: PropTypes.any,
    patientData: PropTypes.any,
    tabKey: PropTypes.any,
  }

  static defaultProps = {
    callback: ()=>{},
    dispatch: ()=>{},
    ImageData: null,
    emrLoading: false,
    imageListParams: null,
    imgByClass: [],
    imgByClassCount: 10,
    imgByDate: [],
    imgByDateCount: 10,
    loading: null,
    patientData: null,
    tabKey: "2",
  }

  infoFormRef = React.createRef();

  constructor(props) {
    super(props);
    this.state = {
      fileList: [], //上传文件
      imageClass: [], //影像类型列表
      uploadModalVisible: false, //上传影像弹窗
      currentType: '', // 当前展示的类型  分类&时间
      patientData: props.patientData,//患者详情
      formData: {
        tenantId: localStorage.getItem('tenantId'), //平台标识
        emrId: props.patientData?props.patientData.fileNumber:null, //大病历号
        patientId: props.patientData?props.patientData.patientId:null, //患者标识
        patientName: props.patientData?props.patientData.name:null, //患者姓名
        userId: localStorage.getItem('userId'), // 医生标识
        userName: localStorage.getItem('userName'), //医生姓名
        classCode: '', //分类标识
        fileName: '', //文件名称
        fileType: 'img', //文件类型
        fileUrl: '', //文件存储地址  阿里云OSS地址
        fileDesc: '', //文件描述
        filesSize: '', //文件大小  kb
        organizationId: localStorage.getItem('organizationId'), //机构ID
        organizationName: localStorage.getItem('organizationName'), //机构名称
        shootingTime: '', //拍摄时间
      },
      clientHeight: document.documentElement.clientHeight, // 屏幕高度
      previewVisible: false, // 预览图片弹窗
      previewImage: '', //预览图片url
    };
    this.resize = this.resize.bind(this);//监听屏幕高度
  }
  //初始化
  componentDidMount() {
    this.showAll(1); // 按时间排序1 & 按类型排序2
    window.addEventListener('resize', this.resize); //监听屏幕高度
  }
  //在组件卸载及销毁之前直接调用
  componentWillUnmount() {
    window.removeEventListener('resize', this.resize); //监听屏幕高度
  }

  //监听数据变化
  componentWillReceiveProps(nextProps) {
    if (nextProps.patientData) {
      if (!this.state.patientData || nextProps.patientData.patientId != this.state.patientData.patientId) {
        // 页面中的操作都初始化一下
        this.setState({
          fileList: [], //上传文件
          imageClass: [], //影像类型列表
          uploadModalVisible: false, //上传影像弹窗
          currentType: '', // 当前展示的类型  分类&时间
          patientData: nextProps.patientData,//患者信息
          formData: {
            tenantId: localStorage.getItem('tenantId'), //平台标识
            emrId: nextProps.patientData?nextProps.patientData.fileNumber:null, //大病历号
            patientId: nextProps.patientData?nextProps.patientData.patientId:null, //患者标识
            patientName: nextProps.patientData?nextProps.patientData.name:null, //患者姓名
            userId: localStorage.getItem('userId'), // 医生标识
            userName: localStorage.getItem('userName'), //医生姓名
            classCode: '', //分类标识
            fileName: '', //文件名称
            fileType: 'img', //文件类型
            fileUrl: '', //文件存储地址  阿里云OSS地址
            fileDesc: '', //文件描述
            filesSize: '', //文件大小  kb
            organizationId: localStorage.getItem('organizationId'), //机构ID
            organizationName: localStorage.getItem('organizationName'), //机构名称
            shootingTime: '', //拍摄时间
          },
          clientHeight: document.documentElement.clientHeight, // 屏幕高度
          previewVisible: false, // 预览图片弹窗
          previewImage: '', //预览图片url
        });
      }
    }
  }

//监听屏幕高度
  resize() {
    this.setState({ clientHeight: document.documentElement.clientHeight }); //监听
  }

  // 按时间排序1 & 按类型排序2
  showAll = (key = 1) => {
    this.props.callback(key);
    if (key == 1) {
      this.setState({
        imageKey: Math.random(),
        currentType: 'dateType',
      });
      // 请求按时间排序
    } else if (key == 2) {
      // 请求按类型排序
      this.setState({
        imageKey: Math.random(),
        currentType: 'classType',
      });
    }
  };

  // 关闭影像上传弹窗
  hideModal = () => {
    this.setState({
      uploadModalVisible: false,
      fileList: [],
      formData: {
        tenantId: localStorage.getItem('tenantId'), //平台标识
        emrId: this.state.patientData.fileNumber, //大病历号
        patientId: this.state.patientData.patientId, //患者标识
        patientName: this.state.patientData.name, //患者姓名
        userId: localStorage.getItem('userId'), // 医生标识
        userName: localStorage.getItem('userName'), //医生姓名
        classCode: '', //分类标识
        fileName: '', //文件名称
        fileType: 'img', //文件类型
        fileUrl: '', //文件存储地址  阿里云OSS地址
        fileDesc: '', //文件描述
        filesSize: '', //文件大小  kb
        organizationId: localStorage.getItem('organizationId'), //机构ID
        organizationName: localStorage.getItem('organizationName'), //机构名称
        shootingTime: '', //拍摄时间
      },
    });
    this.onReset();
  };

  // 表单重置
  onReset = () => {
    this.infoFormRef.current.resetFields();
  };

  // 鼠标移入移出影像图片
  onMouseIn = (mouseIn,uid) => {
    this.setState({
      ['mouseIn'+uid]: mouseIn,
    });
  };

  render() {
    const {
      imageKey,
      patientData,
      formData,
      uploadModalVisible,
      fileList,
      imageClass,
      previewVisible,
      previewImage,
      saveLoading,
      currentType
    } = this.state;
    const { childKey } = this.props;
    //上传属性及参数数据
    const props = {
      action:
        `/api/medical/ossFile/uploadFile?tenantId=` +
        localStorage.getItem('tenantId') +
        `&fileType=1`, //接口路径
      multiple: true, //支持多个文件
      maxCount:10,
      showUploadList:false,
      headers: {
        access_token: localStorage.getItem('access_token'),
        client: 'PC',
        username: localStorage.getItem('username'),
      },
    };
    const antIcon = (
      <LoadingOutlined
        style={{
          fontSize: 24,
        }}
        spin
      />
    );
    return (
      <GridContent >
        <Row
          style={{
            paddingLeft: '16px',
            paddingBottom: '12px',
            borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <div className={styles.operationBox}>
            <div
              className={currentType == 'dateType' ? styles.tagChecked : styles.tagNoCheck}
              style={{width: '90px', marginRight: '10px'}}
              onClick={() => this.showAll(1)}
            >
                影像资料
            </div>

            <div
              className={ currentType == 'classType' ? styles.tagChecked : styles.tagNoCheck }
              style={{ width: '90px' }}
              onClick={() => this.showAll(2)}
            >
              知情同意书
            </div>
          </div>
        </Row>
        <Image
          width={200}
          style={{
            display: 'none',
          }}
          preview={{
            visible: previewVisible,
            src: previewImage,
            onVisibleChange: (value) => {
              this.setState({
                previewVisible: false,
              });
            },
          }}
        />
        <div style={{ overflowY: 'auto', height: this.state.clientHeight - 340 }}>
          {/*// 按时间顺序*/}
          {currentType == 'dateType' &&
            <Row className={`${styles.block9} `}>
              <TimeRecord patientData={patientData} onRef={(ref) => (this.timeRecord = ref)} />
            </Row>
          }
          {
            currentType == 'classType' &&
            <Row className={`${styles.block9} `}>
              <InforConsent patientData={patientData} onRef={(ref) => (this.timeRecord = ref)} />
            </Row>
          }
        </div>
      </GridContent>
    );
  }
}
export default connect(({ ImageData, loading }) => ({
  ImageData,
  loading: loading.models.ImageData,
}))(ImageData);
