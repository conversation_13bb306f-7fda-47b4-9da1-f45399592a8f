import React, {Component} from 'react';
import * as echarts from 'echarts/core';
import {GridComponent, DataZoomComponent} from 'echarts/components';
import {<PERSON><PERSON><PERSON>} from 'echarts/charts';
import {CanvasRenderer} from 'echarts/renderers';
import Immutable from 'immutable';
import $ from 'jquery'

echarts.use(
  [ GridComponent, DataZoomComponent, BarChart, CanvasRenderer]
);

class Index extends Component {
   constructor(props) {
     super(props);
     this.state={
       data:[],
       nameList:[],
       type:'',
     }
     this.myChart = null;
     this.myRef = React.createRef()
   }

 static getDerivedStateFromProps( props,state ) {
   if(!Immutable.is(Immutable.fromJS(props.data), Immutable.fromJS(state.data)) || !Immutable.is(Immutable.fromJS(props.nameList), Immutable.fromJS(state.nameList))){
     return {
       data:props.data,
       nameList:props.nameList,
       type:'1'
     }
   }
   return null
 }

 componentDidMount() {
   const { nameList} = this.state
   const option = {
     top:0,
     xAxis: {
       type: 'category',
       axisLabel: {
         interval: 0,
         rotate: 50,
         formatter: (params) =>{
           let val = "";
           if (params.length > 8) {
             val = `${params.substr(0, 8)  }...`;
             return val;
           }
             return params;
         }
       },
       data: nameList,
       triggerEvent:true,
       axisTick: {
         show: false
       },
       axisLine: {
         show:false
       },
       splitLine : {
         lineStyle: {
           type: 'dashed'
         },
       },
     },
     yAxis: {
        type: 'value',
        show:true,
         // min:0,
         // max:100,
         // minInterval:25,
        boundaryGap: [0, 0.01],
        splitLine : {
         lineStyle: {
           type: 'dashed'
         },
       }
     },
     // dataZoom: [
     //   {
     //     type: 'inside',
     //     // type: 'slider',
     //     xAxisIndex: [0],
     //     start: 1,
     //     end: 100,
     //     // bottom: 10
     //   }
     // ],
     grid:{
       show:true,
       top:20,
       bottom:20,
       left:20,
       right:20,
       containLabel: true,
     },
     series:this.setSeries(),
   };
   const chartDom = this.myRef.current ;
   this.myChart=echarts.init(chartDom)
   this.myChart.clear();
   option && this.myChart.setOption(option);
   this.myChart.on('click', (params)=> {
     this.props.eventClick(params)
   });

   // 页面监控宽度的变化
   window.addEventListener("resize",  () =>{
     this.myChart.resize();
   });
     // this.myChart.on('mouseover', (params) =>{
     //   this.getDiv()
     //   // 注意这里，我是以X轴显示内容过长为例，如果是y轴的话，需要改为yAxis
     //   if (params.componentType === "xAxis") {
     //     // 设置悬浮文本的位置以及样式
     //     $('#extension').css({
     //       "position": "absolute",
     //       "color": "#333",
     //       "font-size": "12px",
     //       "padding": "5px",
     //       "display": "inline",
     //       'border-radius': '4px',
     //       'background-color': 'rgba(255, 255, 255, 0.5)',
     //       'box-shadow': 'rgba(0, 0, 0, 0.3) 2px 2px 8px'
     //     }).text(params.value);
     //     $("html").mousemove((event) =>{
     //       const X = event.pageX - 10;
     //       const Y = event.pageY + 15;
     //       $('#extension').css('top', Y).css('left', X);
     //     });
     //   }
     // });
     // this.myChart.on('mouseout', (params) =>{
     //   // 注意这里，我是以X轴显示内容过长为例，如果是y轴的话，需要改为yAxis
     //   if (params.componentType === "xAxis") {
     //     $('#extension').remove();
     //   }
     // });
 }

  componentDidUpdate() {
     const { nameList,type } = this.state
     if(type==='1'){
       this.setState({
         type:''
       })
       this.myChart.setOption({
         xAxis: {
           data: nameList,
         },
         series:this.setSeries()
       })
     }
 }

 // 判断是否有div
  getDiv = () =>{
    const  id = document.getElementById('extension') ;
    if (!id) {
      const div = "<div id = 'extension' sytle=\"display:block\"></div>";
      $("html").append(div);
    }
  }

 //  重新设置series
  setSeries = () =>{
    const { data } = this.state
    const {dataType} = this.props
    const series = []
    const arr=['#6AE89D','#BDF4D3','#6FADFF','#BDD9FF']
    const Arr=['#6FADFF','#B468FF','#D4BEEC']
    if(data && JSON.stringify(data)!=='[]'){
      data.map(( items,i ) => {
        const obj={
          type: 'bar',
          barWidth: 5,
          stack: items.type,
          silent:true,
          data:items.data,
          itemStyle: {
            normal: {
              barBorderRadius:[50,50,0,0],
              color:dataType?Arr[i]:arr[i],
            }
          },
        }
        series.push(obj)
      })
    }

    return series
  }

  render() {
     const { width='100%' ,height= 300 } = this.props
    return (
      <div>
        <div ref={this.myRef} style={{width, height , minWidth:300}} />
      </div>
    );
  }
}

export default Index;
