@import '~antd/lib/style/themes/default.less';

@nav-header-height: 64px;

.logo {
  height: @nav-header-height;
  position: relative;
  line-height: @nav-header-height;
  padding-left: (@menu-collapsed-width - 32px) / 2;
  transition: all 0.3s;
  background: #002140;
  overflow: hidden;

  img {
    display: inline-block;
    vertical-align: middle;
    height: 32px;
  }

  h1 {
    color: white;
    display: inline-block;
    vertical-align: middle;
    font-size: 20px;
    margin: 0 0 0 12px;
    font-family: Avenir, Arial, Helvetica, sans-serif;
    font-weight: 600;
  }
}

.sider {
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  position: relative;
  z-index: 10;

  &.fixSiderbar {
    position: fixed;
    top: 0;
    left: 0;

    :global(.ant-menu-root) {
      overflow-y: auto;
      height:~'calc(100vh - @{nav-header-height})';
    }
  }

  &.light {
    box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
    background-color: white;

    .logo {
      background: white;
      box-shadow: 1px 1px 0 0 @border-color-split;

      h1 {
        color: @primary-color;
      }
    }

    :global(.ant-menu-light) {
      border-right-color: transparent;
    }
  }
}

.icon {
  width: 30px;
  height: 30px;
}

:global {
  ::-webkit-scrollbar-track {
    background: rgba(0,0,0,0)
  }
  .top-nav-menu li.ant-menu-item {
    height: @nav-header-height;
    line-height: @nav-header-height;
  }

  .drawer .drawer-content {
    background: #001529;
  }

  .ant-menu-inline-collapsed {

    &>.ant-menu-item .sider-menu-item-img+span,
    &>.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-item .sider-menu-item-img+span,
    &>.ant-menu-submenu>.ant-menu-submenu-title .sider-menu-item-img+span {
      max-width: 0;
      display: inline-block;
      opacity: 0;
    }
  }

  .ant-menu-item .sider-menu-item-img+span,
  .ant-menu-submenu-title .sider-menu-item-img+span {
    transition: opacity 0.3s @ease-in-out, width 0.3s @ease-in-out;
    opacity: 1;
  }

  .ant-layout-sider {
    background-color: rgba(0, 0, 0, 0);
  }
}

.box {
  :global {
    .ant-drawer-content-wrapper {
      width: 560px !important;

      .ant-layout-sider {
        max-width: 100% !important;
        width: 100% !important;
        min-width: 100% !important;
        height: 100% !important;
        flex: 0 0 560px !important;
      }

      .ant-drawer-content {
        background: rgba(0, 0, 0, 0);
      }
    }

    .anticon {
      vertical-align: -0.45em;
    }

    .anticon-Home {
      width: 36.68px;
      height: 34.1px;
      background: url("../../assets/menu/Home.png") no-repeat center;
      background-size: 36.68px 34.1px;
    }

    .anticon-Subscribe {
      width: 34px;
      height: 32.07px;
      background: url("../../assets/menu/Subscribe.png") no-repeat center;
      background-size: 34px 32.07px;
    }

    .anticon-Defcustom {
      width: 34.46px;
      height: 32.32px;
      background: url("../../assets/menu/Defcustom.png") no-repeat center;
      background-size: 34.46px 32.32px;
    }

    .anticon-ExclusiveCustomerService {
      width: 36px;
      height: 31.46px;
      background: url("../../assets/menu/CustomerService.png") no-repeat center;
      background-size: 36px 31.46px;
    }

    .anticon-Points {
      width: 36px;
      height: 32.4px;
      background: url("../../assets/menu/Points.png") no-repeat center;
      background-size: 36px 32.4px;
    }

    .anticon-Repairfees {
      width: 30.26px;
      height: 39.55px;
      background: url("../../assets/menu/Repairfees.png") no-repeat center;
      background-size: 30.26px 39.55px;
    }

    .anticon-DailyJob {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/DailyJob.png") no-repeat center;
      background-size: 36px;
    }

    .anticon-Customerfile {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/Customerfile.png") no-repeat center;
      background-size: 36px 36px;
    }

    .anticon-SearchChargeItem {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/SearchChargeItem.png") no-repeat center;
      background-size: 36px 36px;
    }


    .anticon-Settlement {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/Settlement.png") no-repeat center;
      background-size: 36px;
    }

    .anticon-ReportForms {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/ReportForms.png") no-repeat center;
      background-size: 36px 36px;
    }

    .anticon-Scheduling {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/Scheduling.png") no-repeat center;
      background-size: 36px 36px;
    }

    .anticon-Gestational {
      width: 30.64px;
      height: 34.81px;
      background: url("../../assets/menu/Gestational.png") no-repeat center;
      background-size: 30.64px 34.81px;
    }

    .anticon-AppointmentRuleIcon {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/AppointmentRuleIcon.png") no-repeat center;
      background-size: 36px 36px;
    }

    .anticon-ResourceModel {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/ResourceModel.png") no-repeat center;
      background-size: 36px 36px;
    }

    .anticon-customerArchives {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/customerArchives.png") no-repeat center;
      background-size: 36px 36px;
    }

    .anticon-Setting {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/Setting.png") no-repeat center;
      background-size: 36px 36px;
    }

    .anticon-TaskStatus {
      width: 29.45px;
      height: 36px;
      background: url("../../assets/menu/TaskStatus.png") no-repeat center;
      background-size: 29.45px 36px;
    }

    .anticon-Custom {
      width: 29.45px;
      height: 36px;
      background: url("../../assets/menu/Custom.png") no-repeat center;
      background-size: 29.45px 36px;
    }

    .anticon-TaskHistory {
      width: 29.45px;
      height: 36px;
      background: url("../../assets/menu/TaskHistory.png") no-repeat center;
      background-size: 29.45px 36px;
    }

    .anticon-TreatmentRemarks {
      width: 31.32px;
      height: 36px;
      background: url("../../assets/menu/TreatmentRemarks.png") no-repeat center;
      background-size: 31.32px 36px;
    }

    .anticon-Complaint {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/Complaint.png") no-repeat center;
      background-size: 36px 36px;
    }

    .anticon-Yanbaopart {
      width: 35.92px;
      height: 36px;
      background: url("../../assets/menu/Yanbaopart.png") no-repeat center;
      background-size: 35.92px 36px;
    }

    .anticon-GarworthIcon {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/GarworthIcon.png") no-repeat center;
      background-size: 36px 36px;
    }

    .anticon-WelfareCenter {
      width: 38px;
      height: 37px;
      background: url("../../assets/menu/WelfareCenter.png") no-repeat center;
      background-size: 38px 37px;
    }

    .anticon-Diagnosis {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/Diagnosis.png") no-repeat center;
      background-size: 36px 36px;
    }

    .anticon-Consultation {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/Consultation.png") no-repeat center;
      background-size: 36px 36px;
    }

    .anticon-RecordManagement {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/RecordManagement.png") no-repeat center;
      background-size: 36px 36px;
    }

    .anticon-DoctorManagement {
      width: 36px;
      height: 36px;
      background: url("../../assets/menu/DoctorManagement.png") no-repeat center;
      background-size: 36px 36px;
    }

    .anticon-InformedConsent {
      width: 32px;
      height: 36px;
      background: url("../../assets/menu/InformedConsent.png") no-repeat center;
      background-size: 32px 36px;
    }

    .anticon-PracticePermissions {
      width: 34px;
      height: 36px;
      background: url("../../assets/menu/PracticePermissions.png") no-repeat center;
      background-size: 34px 36px;
    }

    .anticon-Pharmacy {
      width: 20px;
      height: 27px;
      background: url("../../assets/menu/Pharmacy.png") no-repeat center;
      background-size: 20px 27px;
    }

    .anticon-Paygopulya {
      width: 24px;
      height: 31px;
      background: url("../../assets/menu/Paygopulya.png") no-repeat center;
      background-size: 24px 31px;
    }

    .anticon-CaseStudy{
      width: 32px;
      height: 32px;
      background:url("../../assets/menu/CaseStudy.png") no-repeat center;
      background-size: 32px 32px;
    }
    .anticon-MeetingDiscuss{
      width: 32px;
      height: 32px;
      background:url("../../assets/menu/MeetingDiscuss.png") no-repeat center;
      background-size: 32px 32px;
    }
    .anticon-ExpertConsultation{
      width: 36px;
      height: 36px;
      background:url("../../assets/menu/ExpertConsultation.png") no-repeat center;
      background-size: 36px 36px;
    }
    .anticon-MyConsultation{
      width: 36px;
      height: 36px;
      background:url("../../assets/menu/MyConsultation.png") no-repeat center;
      background-size: 36px 36px;
    }
    .anticon-StatisticsQuery{
      width: 36px;
      height: 36px;
      background:url("../../assets/menu/StatisticsQuery.png") no-repeat center;
      background-size: 36px 36px;
    }
    .ant-input {
      background: #E8EBED;
    }

  }
}

// 文件夹 打开关闭 样式
.openFloder {
  background: url("../../assets/menu/OpenFolder.png") no-repeat left;
  background-size: 22px 23px;
}

.closeFloder {
  background: url("../../assets/menu/CloseFolder.png") no-repeat left center;
  background-size: 20px 18px;
}

.parent {
  // margin: 24px 20px 0px 28px;
  flex-wrap: wrap;
  // background: rgba(222, 228, 232, 0.5);
  // height: 100%;
  position: relative;
  min-height: 100%;

  // margin-left: 30px;
  // margin-right: 15px;
  // padding-bottom: 20px;
  // 首页
  .everyMenuBody {
    position: relative;
  }

  .addCommon,
  .removeCommon {
    position: absolute;
    width: 20px;
    height: 20px;
    cursor: pointer;
    left: -7px;
    top: -7px;
    background: #fff;
    border-radius: 50%;
  }

  .editCommonBtn {
    margin-left: 20px;
  }

  .menu {
    margin-right: 10px;
    margin-bottom: 10px;
    float: left;

    // position: relative;
    &>a {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #FFFFFFFF;

      &>i {
        // margin-bottom: 15px;
      }

      .textName {
        letter-spacing: 0.5px;
      }
    }
  }

  .BrandMenu {
    padding-bottom: 20px;

    a {
      display: flex;
      margin-bottom: 10px;
    }

    a:hover {
      background: rgba(27, 49, 64, 0.06);

      .BrandMenuIcon {
        opacity: 0.8;
      }
    }

    .BrandMenuIcon {
      width: 75px;
      height: 75px;
      border-radius: 10px;
      margin-right: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      opacity: 1;
    }
  }
}

.parent::before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  //backdrop-filter: blur(20px);
  //background: rgba(222, 228, 232, 0.5);
  //background: url("../../assets/menu/MenuIndexBg.png") repeat 100% 100%;
  background: url("../../assets/menu/IndexBGmenu.png") repeat 100% 100%;
  z-index: -1;
}


.subMenu {

  background: linear-gradient(180deg, #5E5D5D 0%, #737373 51%, #5E5D5D 100%);
  position: fixed;
  top: 50px;
  bottom: 0px;
  z-index: 100;

  :global {

    .ant-menu-inline-collapsed {
      width: 56px;
    }

    .ant-menu-inline-collapsed>.ant-menu-item,
    .ant-menu-inline-collapsed>.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-item,
    .ant-menu-inline-collapsed>.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-submenu>.ant-menu-submenu-title,
    .ant-menu-inline-collapsed>.ant-menu-submenu>.ant-menu-submenu-title {
      left: 0;
      padding: 0 20px !important;
      text-overflow: clip;
      margin: 2px 8px;
      display: flex;
      justify-content: center;
    }

    .antd-pro-components-sider-menu-index-sider .ant-layout-sider-children .ant-menu-inline {

      background: none;
    }

    .ant-layout-sider {

      background: none;
    }

    .ant-menu-dark,
    .ant-menu-dark .ant-menu-sub {

      background: none !important;
    }

    .ant-menu-dark .ant-menu-inline.ant-menu-sub {

      background: none !important;
      box-shadow: none;
    }

    // .ant-menu-dark .ant-menu-submenu-open .ant-menu-submenu-title {
    //   color: #FFFFFF !important;
    //   font-size: 13px;
    // }

    .antd-pro-components-sider-menu-index-sider .ant-layout-sider-children .ant-menu-inline .ant-menu-submenu-title span {
      color: #FFFFFF;
      font-size: 14px;
      font-weight: 400;
    }

    .ant-menu-dark.ant-menu-inline .ant-menu-submenu-title {
      color: #FBFBFB;
      font-size: 14px;
      padding: 0px 8px;

      &>span {
        margin-left: 8px;
      }
    }

    .ant-menu-dark .ant-menu-item:hover>a,
    .ant-menu-dark .ant-menu-item-active>a,
    .ant-menu-dark .ant-menu-submenu-active>a,
    .ant-menu-dark .ant-menu-submenu-open>a,
    .ant-menu-dark .ant-menu-submenu-selected>a,
    .ant-menu-dark .ant-menu-submenu-title:hover>a {
      color: #FFFFFF;
    }

    .ant-menu-dark .ant-menu-item,
    .ant-menu-dark .ant-menu-item-group-title,
    .ant-menu-dark .ant-menu-item>a {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #FBFBFB;
      font-size: 14px !important;
      font-weight: 400 !important;
      margin: 2px 8px;
    }


    .ant-menu-inline .ant-menu-item,
    .ant-menu-inline .ant-menu-submenu-title {
      width: auto !important;
      padding-left: 0px !important;
      margin: 2px 8px !important;
      justify-content: start;
    }

    // .ant-menu-inline .ant-menu-submenu {
    //   margin: 0px 8px;
    // }
    .ant-menu-submenu>.ant-menu-sub>.ant-menu-item {
      margin-left: 35px !important;
    }

    .ant-menu-dark .ant-menu-item:hover,
    .ant-menu.ant-menu-dark .ant-menu-item-selected,
    .ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected {
      border-radius: 8px;
      border-left: none !important;
      background: #898787 !important;
      // box-shadow: inset 0px 1px 0px 0px #989696;
    }

    .ant-menu-submenu-inline>.ant-menu-submenu-title:hover,
    .ant-menu-submenu-vertical>.ant-menu-submenu-title:hover,
    .ant-menu-submenu-selected>.ant-menu-submenu-title {
      color: #FBFBFB;
      border-radius: 8px;
      border-left: none !important;
      background: #898787 !important;
      box-shadow: inset 0px 1px 0px 0px #989696;
    }

    .ant-menu-submenu-open>.ant-menu-sub>.ant-menu-item-selected {
      margin: 0px 0px 0px 32px;
      border-left: none !important;
      background: none !important;
      box-shadow: none;

      &>a {
        color: #FFFFFF !important;
      }
    }

    .ant-menu-submenu-open>.ant-menu-sub>.ant-menu-item {
      margin: 0px 0px 0px 35px !important;
      border-left: none !important;
      background: none !important;
      box-shadow: none;

      &>a {
        color: #C5C8CA;
      }
    }



    .ant-menu-dark .ant-menu-item-selected>a {
      display: flex;
      align-items: center;
      color: #FFFFFF !important;
      font-weight: 400 !important;

      &:hover {
        color: #FFFFFF !important;
      }
    }

    .ant-menu-dark .ant-menu-item:hover>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-item-active>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-submenu-active>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-submenu-open>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-submenu-selected>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-submenu-title:hover>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-item:hover>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-item-active>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-submenu-active>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-submenu-open>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-submenu-selected>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-submenu-title:hover>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-item:hover>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-item-active>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-submenu-active>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-submenu-open>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-submenu-selected>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-submenu-title:hover>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-item:hover>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-item-active>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-submenu-active>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-submenu-open>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-submenu-selected>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-submenu-title:hover>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-submenu-title .ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-sub .ant-menu-submenu-title .ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-submenu-title .ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-sub .ant-menu-submenu-title .ant-menu-submenu-arrow::before {
      background: #BBBABA;
    }

    .ant-menu-dark .ant-menu-item:hover>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-item-active>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-submenu-active>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-submenu-open>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-item:hover>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-item-active>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-submenu-active>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::after,
    .ant-menu-dark .ant-menu-item:hover>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-item-active>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-submenu-active>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-submenu-open>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-item:hover>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-item-active>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before,
    .ant-menu-dark .ant-menu-submenu-active>.ant-menu-submenu-title:hover>.ant-menu-submenu-arrow::before {
      background: #BBBABA;
    }

    .ant-menu-submenu-inline>.ant-menu-submenu-title .ant-menu-submenu-arrow::before,
    .ant-menu-submenu-inline>.ant-menu-submenu-title .ant-menu-submenu-arrow::after {
      width: 8px;
    }

    .ant-menu-dark .ant-menu-submenu-title .ant-menu-submenu-arrow {
      opacity: 1;
    }

    .ant-menu-submenu-inline>.ant-menu-submenu-title .ant-menu-submenu-arrow::before {
      transform: rotate(-45deg) translateX(4px);
    }

    .ant-menu-submenu-open.ant-menu-submenu-inline>.ant-menu-submenu-title .ant-menu-submenu-arrow::before {
      transform: rotate(45deg) translateX(4px);
    }

    .ant-menu-submenu-inline>.ant-menu-submenu-title .ant-menu-submenu-arrow::after {
      transform: rotate(45deg) translateX(-4px);
    }

    .ant-menu-submenu-open.ant-menu-submenu-inline>.ant-menu-submenu-title .ant-menu-submenu-arrow::after {
      transform: rotate(-45deg) translateX(-4px);
    }

    .ant-menu-vertical .ant-menu-submenu-title {
      overflow: visible;
    }

    // 全部任务
    .anticon-menu_task {
      width: 16px;
      height: 16px;
      background: url("../../assets/submenu/menu_task.png") no-repeat center;
      background-size: 16px 16px;
    }

    // 服务中心-预约看板
    .anticon-menu_service {
      width: 16px;
      height: 16px;
      background: url("../../assets/submenu/menu_service.png") no-repeat center;
      background-size: 16px 16px;
    }

    // 医疗中心
    .anticon-menu_medical {
      width: 16px;
      height: 16px;
      background: url("../../assets/submenu/menu_medical.png") no-repeat center;
      background-size: 16px 16px;
    }

    // 患者中心
    .anticon-menu_customer {
      width: 16px;
      height: 16px;
      background: url("../../assets/submenu/menu_customer.png") no-repeat center;
      background-size: 16px 16px;
    }

    // 运营中心
    .anticon-menu_run {
      width: 16px;
      height: 16px;
      background: url("../../assets/submenu/menu_run.png") no-repeat center;
      background-size: 16px 16px;
    }

    // 支持中心
    .anticon-menu_stock {
      width: 16px;
      height: 16px;
      background: url("../../assets/submenu/menu_stock.png") no-repeat center;
      background-size: 16px 16px;
    }

    // 学习中心
    .anticon-menu_study {
      width: 16px;
      height: 16px;
      background: url("../../assets/submenu/menu_study.png") no-repeat center;
      background-size: 16px 16px;
    }

    // 数据中心
    .anticon-menu_data {
      width: 16px;
      height: 16px;
      background: url("../../assets/submenu/menu_data.png") no-repeat center;
      background-size: 16px 16px;
    }

    // 管理中心
    .anticon-menu_manage {
      width: 16px;
      height: 16px;
      background: url("../../assets/submenu/menu_manage.png") no-repeat center;
      background-size: 16px 16px;
    }

    // 商城
    .anticon-menu_shop {
      width: 16px;
      height: 16px;
      background: url("../../assets/submenu/menu_shop.png") no-repeat center;
      background-size: 16px 16px;
    }
  }
}

.antMenuTriangle {
  position: absolute;
  top: 20px;
  left: -8px;
  width: 0px;
  height: 0px;
  border-width: 4px;
  border-style: solid;
  border-color: transparent rgba(0, 0, 0, 0.75) transparent transparent;
}

:global {
  .ant-menu-dark.ant-menu-submenu-popup {
    left: 60px !important;
  }

  .ant-menu-dark,
  .ant-menu-dark .ant-menu-sub {
    background-color: rgba(0, 0, 0, 0.75);
    box-shadow: 0 2px 8px rgba(0 0 0 / 15%);
  }

  .ant-menu-dark .ant-menu-item>a {
    color: rgba(255, 255, 255, 0.85);
  }
}
