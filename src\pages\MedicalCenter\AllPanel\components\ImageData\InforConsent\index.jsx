import { connect } from 'dva';

const { GridContent } = require('@ant-design/pro-layout');
import React, { Component, useState } from 'react';
import { Row, Col, Spin } from 'antd';
import ImageCard from '@/components/ImageCard/InformedconsentByClinicMornMeet.js';  //单个影像卡片组件
// 样式文件
import styles from './../style.less';
import commonStyle from '@/components/common.less';
// 图片
import upArrow from '@/assets/<EMAIL>';
import downArrow from '@/assets/downArrow.png';
import noData from '@/assets/<EMAIL>';
import {StringUtils} from "@/utils/StringUtils";
import {
  findAllMedicalRecords,   // 全部病历
} from '@/services/homePage';
import moment from "moment";


class InforConsent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isShow: 'up', // 显示隐藏的小箭头
      key: '1', //点击箭头的key
      patientData:props.patientData,
      imageListParams: {
        //影像列表参数
        tenantId: localStorage.getItem('tenantId'), //平台标识
        emrId: props.patientData?props.patientData.fileNumber:null, //大病历号
        pageNum: 1,
        pageSize: 10,
        patientId: null, // 患者标识
        beginTime: '',   // 就诊时间筛选开始时间
        endTime: '',     // 就诊时间筛选结束时间
        sort: 'DESC',    // 排序：正序是ASC 倒叙是DESC
        status: null,    // 就诊状态 状态：：2编辑中；3已完成；4归档
      },
      newImgByDate: [], // 新的数据列表
      emrLoading: false, //分页加载数据
      emrNextStatus: false, //分页加载数据
      total: 0,          // 总条数
      allPanelList: [],  // 全部病历
      allPanelCount:0,   // 病历总数
      allPanelParams: {
        //全部病历入参
        tenantId: localStorage.getItem('tenantId'),//品牌id
        pageNum: 1,      // 当前页
        pageSize: 10,    // 限制页
        patientId: null, // 患者标识
        emrId:  null,    // 大病历号
        beginTime: '',   // 就诊时间筛选开始时间
        endTime: '',     // 就诊时间筛选结束时间
        sort: 'DESC',    // 排序：正序是ASC 倒叙是DESC
        status: null,    // 就诊状态 状态：：2编辑中；3已完成；4归档
      }
    };
  }

  // 初始化调用
  componentDidMount() {
    this.props.onRef(this);
    if(this.state.patientData){
      this.getCheckImgByDate();// 请求影像资料--按时间
    }
  }




  //监听患者信息数据变化
  componentWillReceiveProps(nextProps) {
    if (nextProps.patientData) {
      if (!this.state.patientData || nextProps.patientData.patientId != this.state.patientData.patientId) {
        // 页面中的操作都初始化一下
        this.setState( {
          isShow: 'up', // 显示隐藏的小箭头
          key: '1', //点击箭头的key
          imageListParams: {
            //影像列表参数
            tenantId: localStorage.getItem('tenantId'),
            emrId: nextProps.patientData?nextProps.patientData.fileNumber:null,
            pageNum: 1,
            pageSize: 10,
          },
          newImgByDate: [], // 新的数据列表
          allPanelList: [], // 新的数据列表
          emrLoading: false, //分页加载数据
          emrNextStatus: false, //分页加载数据
          total: 0,
        },()=>this.getCheckImgByDate(1));// 请求影像资料--按时间
      }
    }
  }

  // 请求影像资料--按时间
  getCheckImgByDate = (pageNum) => {
    const { dispatch } = this.props;
    const { imageListParams } = this.state;
    if (pageNum) { imageListParams.pageNum = pageNum; }
    if(StringUtils.isBlank(imageListParams.emrId)){
      this.setState({
        allPanelList: [],
        total: 0,
      });
      return false;
    }
    this.setState({
      emrLoading: true,
    });
    this.findAllMedicalRecords(imageListParams);
  };

  // 请求全部病历
  findAllMedicalRecords= async (imageListParams)=> {
    if(!!this.props.patientData) {
      const {
        patientId,
        fileNumber,
      } = this.props.patientData || {}

      const res = await findAllMedicalRecords({
        ...imageListParams,
        patientId:patientId,
      });
      const {
        code,
        msg,
        rows
      } = res || {}

      if (res && code == 200 && Array.isArray(rows)) {
        let rowsArr = (rows || []).map((item)=>{
          const { mrcs,clinicTime } = item || {}
          return {
            isShow:'up',
            createdGmtAt:clinicTime,
            classes:mrcs,
          }
        });

        let arr;
        if (imageListParams.pageNum == 1) {
          arr = rowsArr;
        } else {
          arr = [...this.state.allPanelList, ...rowsArr];
        }
        arr = arr.filter((item)=>{ return Array.isArray(item.classes) && item.classes.length > 0 ? true : false })
        this.setState({
          allPanelList: arr,
          // total: res.total,
          emrLoading: false,
        },()=>{
          console.log('res123123  :: ',this.state.allPanelList);
        });
      }else {

      }
    }
  }

  // 加载更多
  emrNext = () => {
    this.setState({
      emrLoading: true,
    });
    const { imageListParams, total } = this.state;
    if (imageListParams.pageNum <= total / imageListParams.pageSize) {
      imageListParams.pageNum++;
      this.getCheckImgByDate();
    }
  };
  // 箭头展开折起事件
  isShow = (key, value) => {
    const { allPanelList } = this.state;

    this.setState({
      key: key,
      allPanelList: allPanelList.map((item, index) =>
        index == key ? { ...item, isShow: value } : item,
      ),
    });
  };

  render() {
    const {
      isShow,
      key,
      newImgByDate,
      emrLoading,
      imageListParams,
      total,
      emrNextStatus,
      allPanelList,  // 全部病例
    } = this.state;
    return (
      <GridContent>
        <div>
          {allPanelList.length > 0 ? (
            <>
              {allPanelList.map((item, index) => {
                console.log('item123123 :: ',item);
                return (
                  <div key={index}>
                    <Row className={`${styles.width100}`}>
                      <Col span={20} className={styles.displayFlex}>
                        <h3 style={{fontWeight: 900, fontSize: '16', marginRight: '8px', display: 'inline-block'}}>
                          {item.createdGmtAt}
                        </h3>
                        {item.isShow == 'up' ? (
                          <img
                            src={upArrow}
                            alt=""
                            className={styles.icon_boy}
                            onClick={() => this.isShow(index, 'down')}
                          />
                        ) : (
                          <img
                            src={downArrow}
                            alt=""
                            className={styles.icon_boy}
                            onClick={() => this.isShow(index, 'up')}
                          />
                        )}
                      </Col>
                    </Row>
                    {item.isShow == 'up' ? (
                      <div>
                        <Row style={{display: 'flex'}}>
                          {item.classes &&
                          item.classes.map((childItem, childIndex) => {
                            /*
                              createdGmtAt: "2023-01-29 14:32:17"
                              emrSubId: "fb5141ccc48d42f4b59062ab1b9bb256"
                              id: 19
                              tmptCode: "a329f0f637ef43ae9756f5e0657a6ea6"
                              tmptName: "儿童拔牙知情同意书"
                              url: "tenant/3a9d9c680c364df9a84bfd5d
                            * */
                            const {
                              id,
                              createdGmtAt,
                              emrSubId,
                              tmptCode,
                              tmptName,
                              url,
                            } = childItem || {}

                            return (
                              <div>
                                <ImageCard
                                  type={'dateType'}
                                  disableDelete={true}
                                  key={childItem.id}
                                  connected={childItem.isRelation}
                                  imageData={{
                                    ...childItem,
                                    fileDesc: childItem.tmptName,
                                    fileUrl: childItem.url,
                                    createdGmtAt: moment(createdGmtAt, 'YYYY-MM-DD').format('YYYY-MM-DD')
                                  }}
                                  getList={this.getCheckImgByDate}
                                />
                              </div>
                            )
                            /*<Row key={childIndex}>
                              <Row className={styles.width100}>
                                <p className={styles.typeTitle}>
                                  {childItem.className || 'CT的影像'}
                                </p>
                              </Row>
                              <Row style={{ display: 'flex' }}>
                                {/!* {console.log('子项里都有啥',childItem)} *!/}
                                {childItem.checkList &&
                                childItem.checkList.map((secondItem, secondIndex) => {
                                  return (
                                    <ImageCard
                                      type={'dateType'}
                                      disableDelete={true}
                                      key={secondItem.id}
                                      connected={secondItem.isRelation}
                                      imageData={secondItem}
                                      getList={this.getCheckImgByDate}
                                    />
                                  )
                                })}

                                {/!* <ImageCard type="dateType"
                                // data={item}
                                getCheckImgByDate={getCheckImgByDate} />
                              <ImageCard type="dateType"
                                // data={item}
                                getCheckImgByDate={getCheckImgByDate} /> *!/}
                              </Row>
                            </Row>*/
                          })}
                        </Row>
                      </div>
                    ) : (
                      <></>
                    )}
                  </div>
                )
              })}

              <Spin spinning={emrLoading} tip="加载中...">
                {imageListParams.pageNum >= total / imageListParams.pageSize ?
                  <div
                    className={styles.loading_more}
                    hidden={emrLoading}
                  >
                    <Row className={styles.width100}>
                      <p className={styles.typeTitle}>
                        没有更多啦！
                      </p>
                    </Row>
                  </div>
                  : <div
                    className={styles.loading_more}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                    hidden={emrLoading}
                    onClick={this.emrNext.bind(this) }
                  >
                    加载更多
                  </div>}
              </Spin>
            </>

          ) : (
            <Spin spinning={emrLoading} tip="加载中...">
              <div className={commonStyle.nodataContent} style={{ marginTop: '25%' }}>
                <img src={noData} className={commonStyle.imgStyle} alt="" />
                <div className={commonStyle.fontStyle}>暂无数据</div>
              </div>
            </Spin>
          )}
        </div>
      </GridContent>
    );
  }
}
export default connect(({ TimeRecord, loading }) => ({
  TimeRecord,
  loading: loading.models.TimeRecord,
}))(InforConsent);
