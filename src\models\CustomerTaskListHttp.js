//引用services里路径
import {
  CustomerTaskListHttpID,
  consultationResourceDoctorList,
  queryPersonnelByAuthList,
  distributeTaskHttp,
  directorCheckinHttp,
  doTaskRecord,
} from '@/services/InsurancePayment';

const CustomerTaskHttpsModels = {
namespace: 'textHttpsModels',
state: {
  receptionSearchVal: '', // 接待搜索内容
  visitSearchVal: '', // 回访搜索内容
  receptionSelectRadioStatus: null, // 接待初、复诊选中的状态
  visitSelectRadioStatus: null, // 回访初、复诊选中的状态
  receptionPersonnelSelectStatus: [], // 接待人员选中的状态
  visitPersonnelSelectStatus: [], // 回访人员选中的状态
  receptionAllSelectStatus: [], // 接待全部选中状态
  visitAllSelectStatus: [], // 回访全部选中状态
  visitCacheData: {}, // 回访选中的数据
  receptionCatcheData: {}, // 接待选中的数据
},
effects: {
*CustomerTaskListHttpID({ payload }, { call }) {
  const res = yield call(CustomerTaskListHttpID, payload);
  return res
},
*consultationResourceDoctorList({ payload }, { call }) {
  const res = yield call(consultationResourceDoctorList, payload);
  return res
},
*queryPersonnelByAuthList({ payload }, { call }) {
  const res = yield call(queryPersonnelByAuthList, payload);
  return res
},
*distributeTaskHttp({ payload }, { call }) {
  const res = yield call(distributeTaskHttp, payload);
  return res
},
*directorCheckinHttp({ payload }, { call }) {
  const res = yield call(directorCheckinHttp, payload);
  return res
},
*doTaskRecord({ payload }, { call }) {
  const res = yield call(doTaskRecord, payload);
  return res
}
},
reducers: {
  // 更新状态值数据
  setStatus(state, { payload }) {
    return {
      ...state,
      ...payload
    };
  },
  
  // 清空数据
  clean(state, { payload }){
    return {
      ...state,
      receptionSearchVal: '', // 接待搜索内容
      visitSearchVal: '', // 回访搜索内容
      receptionSelectRadioStatus: null, // 接待初、复诊选中的状态
      visitSelectRadioStatus: null, // 回访初、复诊选中的状态
      receptionPersonnelSelectStatus: [], // 接待人员选中的状态
      visitPersonnelSelectStatus: [], // 回访人员选中的状态
      receptionAllSelectStatus: [], // 接待全部选中状态
      visitAllSelectStatus: [], // 回访全部选中状态
      visitCacheData: {}, // 回访选中的数据
      receptionCatcheData: {}, // 接待选中的数据
    }
  }
},
subscriptions: {
  setup({ dispatch, history }) {
    return history.listen(({ pathname, search }) => {
      if (!((/\/consult/.test(pathname)) || (/\/customerfollow/.test(pathname)) || (/\/homeIndex/.test(pathname)))) {
        dispatch({
          type: "clean",
          payload: {}
        })
      }
    })
  }
}
};
export default CustomerTaskHttpsModels;