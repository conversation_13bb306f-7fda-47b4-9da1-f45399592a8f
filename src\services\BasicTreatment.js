import request from '@/utils/request';

//获取基础治疗分组列表
export async function findTreatClass(params) {
  return request('/api/emr/system/findTreatClass', {
    method: 'POST',
    data: params,
  });
}
//新建文件夹
export async function saveContentClass(params) {
  return request('/api/emr/system/saveContentClass', {
    method: 'POST',
    data: params,
  });
}
//获取基础治疗列表
export async function findTreatsByClass(params) {
  return request('/api/emr/system/findTreatsByClass', {
    method: 'POST',
    data: params,
  });
}
//删除基础治疗
export async function deleteTreat(params) {
  return request('/api/emr/system/deleteTreat', {
    method: 'POST',
    data: params,
  });
}
//新增/编辑基础治疗
export async function saveTreat(params) {
  return request('/api/emr/system/saveTreat', {
    method: 'POST',
    data: params,
  });
}
//获取当前分类和上级分类
export async function getTreatClasses(params) {
  return request('/api/emr/system/getTreatClasses', {
    method: 'POST',
    data: params,
  });
}
//基础治疗分组列表搜索
export async function findTreatClassSearch(params) {
  return request('/api/emr/system/findTreatClassSearch', {
    method: 'POST',
    data: params,
  });
}
//删除文件夹
export async function deleteContentClass(params) {
  return request('/api/emr/system/deleteContentClass', {
    method: 'POST',
    data: params,
  });
}
//编辑模板文件夹
export async function updateContentClass(params) {
  return request('/api/emr/system/updateContentClass', {
    method: 'POST',
    data: params,
  });
}
