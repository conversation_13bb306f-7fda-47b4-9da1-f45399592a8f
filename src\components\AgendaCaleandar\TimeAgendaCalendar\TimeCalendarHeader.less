.HeaderTableWeap {
  position: relative;
  .goToLast {
    float:right;
    width: 22px;
    height: 22px;
    display: inline-block;
    position:absolute;
    left: -10px;
    top: 50px;
    vertical-align: text-bottom;
    background: url('../../../../src/assets/calendar/next.png');
    cursor: pointer;
    transform:scaleX(-1);
    z-index: 20;
  }

  .goToNext {
    float:left;
    width: 22px;
    height: 22px;
    position:absolute;
    right: -10px;
    top: 50px;
    display: inline-block;
    vertical-align: text-bottom;
    background: url('../../../../src/assets/calendar/next.png');
    cursor: pointer;
    z-index:20;
  }

  :global {
    table {
      width:100%;
      height: 78px;
      table-layout:fixed;
      margin-top: -1px;
      margin-bottom: -1px;
      border-color: #ccc;
    }

    .fc-axis {
      background: #E0E2E7;
    }

    .DoctorNameTr th {
      text-align: center;
      //padding-left: 16px;
      font-weight: normal;
      border-color: #ccc;
      font-size: 16px;
      font-family: "Microsoft YaHei";
      background: #E0E2E7;
      cursor: pointer;
    }

    .fc-resource-cell {
      background: #F7F7F7;
      font-weight: normal;
      font-size: 14px;
      // height: 25px;
    }

    th, td {
      border-style: solid;
      border-width: 1px;
      padding: 0;
      vertical-align: top;
      border-color: #ccc;
    }

    th {
      //height: 40px;
      //line-height: 40px;
      padding-top: 2px;
      padding-bottom: 4px;
      text-align: center;
      border-color: #ccc;
    }
  }
}

.lableTitleHead {
  //margin-top: 12px;
}

.labelTdsTitle {
  line-height: 18px;
  font-size: 14px;
  font-weight: bold;
}

.doctorIcon {
  width: 18px;
  height: 22px;
}

.chairIcon {
  width: 24px;
  height: 20px;
}

.doctorIconTh {
  padding-left: 0px;
}

.AgendaHeadName {
  font-family:'MicrosoftYaHei';
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-weight: 700;
  font-size: 16px;
}

.AgnedaTextOnClick {
  cursor: pointer;
}

.suplusHeader {
  background: #E0E2E7!important;
  //border:#E0E2E7;
}

.HeaderTableDisClickWeap {
  :global {
    .DoctorNameTr th {
      text-align: center;
      //padding-left: 16px;
      font-weight: normal;
      border-color: #ccc;
      font-size: 16px;
      font-family: "Microsoft YaHei";
      background: #F7F7F7;
      cursor: default;
    }
  }
}

.AgendaExtraInfo {
  line-height: 14px;
  font-size:12px;
  font-weight:400;
  // color: #888888;
  cursor: pointer;
}

.extraNumRed {
  color: #E24C4D;
}
