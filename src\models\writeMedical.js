import {
  findByUserIdAndSearchFiled,
  saveEmr,
  findWordsByKeyWord,
  findDiagByKeyWord,
  findTreatByKeyWord,
  generateEmrSubId,
  getEmrInfoBySubId,
  findToothExamCache,
  findCheckImgsByDate,
  dbPathTransform,
  findLinkMrcs,
  findLinkImgs,
  findGenExamsByEmrSubId,
  findPreExamsByEmrSubId,
  saveLinkImgs,
  saveEmrMrc,
  findMyTempContentClass
} from '@/services/writeMedical';//接口
import {notification} from "antd";
const WriteMedicalModel = {
  namespace: 'WriteMedical',
  state: {
    saveEmrData:[],//保存病历返回信息
    KeyWordData:[],//词条联想数据
    DiagByKeyWordData:[],//诊断联想查询
    TreatByKeyWordData:[],//基础治疗联想查询
    findByUserIdAndSearchData:[],//获取就诊记录
    generateEmrData:[],//根据预约获取emrSubId
    getEmrInfoData:[],//获取单个病历详情
    findCheckImgsByDate:[],//获取影像库列表-按时间分组
    dbPathTransformData:[],//单连接转换cdn方式访问图片
    findLinkMrcsData:[],//查询关联同意书
    findLinkImgsData:[],//查询关联影像
    saveLinkImgsData:[],//关联影像-全部病历
    saveEmrMrcData:[],//关联知情同意书-全部病历
    findMyTempContentClassData:[],//获取我的模板下面的模板分类
    loading:false,
    loadTip:"加载中",
  },
  //异步
  effects: {
    //获取我的模板下面的模板分类
    *findMyTempContentService({payload, callback} , { call, put }) {
      const response = yield call(findMyTempContentClass ,payload);
      yield put({
        type: 'findMyTempContentInfo',
        payload: response,
      });
      if (response) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      }
    },

    //查询关联知情同意书
    *saveEmrMrcService({payload, callback} , { call, put }) {
      const response = yield call(saveEmrMrc ,payload);
      yield put({
        type: 'saveEmrMrcInfo',
        payload: response,
      });
      if (response) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      }
    },

    //查询关联影像
    *saveLinkImgsDataService({payload, callback} , { call, put }) {
      const response = yield call(saveLinkImgs ,payload);
      yield put({
        type: 'saveLinkImgsInfo',
        payload: response,
      });
      if (response) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      }
    },
    //查询关联影像
    *findLinkImgsService({payload, callback} , { call, put }) {
      const response = yield call(findLinkImgs ,payload);
      yield put({
        type: 'findLinkImgsInfo',
        payload: response,
      });
      if (response) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      }
    },
    //查询关联同意书
    *findLinkMrcsService({payload, callback} , { call, put }) {
      const response = yield call(findLinkMrcs ,payload);
      yield put({
        type: 'findLinkMrcsInfo',
        payload: response,
      });
      if (response) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      }
    },
    // 单连接转换cdn方式访问图片
    *dbPathTransformService({payload, callback} , { call, put }) {
      const response = yield call(dbPathTransform ,payload);
      yield put({
        type: 'dbPathTransformInfo',
        payload: response,
      });
      if (response) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      }
    },
    // 获取影像库列表-按时间分组
    *findCheckImgsService({payload, callback} , { call, put }) {
      const response = yield call(findCheckImgsByDate ,payload);
      yield put({
        type: 'findCheckImgsInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    // 获取单个病历详情
    *getEmrInfoService({payload, callback} , { call, put }) {
      const response = yield call(getEmrInfoBySubId ,payload);
      yield put({
        type: 'getEmrsInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    *findGenExamsByEmrSubIdService({payload, callback} , { call, put }) {
      const response = yield call(findGenExamsByEmrSubId ,payload);
      yield put({
        type: 'findGenExamsByEmrSubIdInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    *findPreExamsByEmrSubIdService({payload, callback} , { call, put }) {
      const response = yield call(findPreExamsByEmrSubId ,payload);
      yield put({
        type: 'findPreExamsByEmrSubIdInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    // 获取就诊记录
    *generateEmrSubIdService({payload, callback} , { call, put }) {
      const response = yield call(generateEmrSubId ,payload);
      yield put({
        type: 'generateEmrSubIdInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    // 获取就诊记录
    *findByUserIdAndSearchService({payload, callback} , { call, put }) {
      const response = yield call(findByUserIdAndSearchFiled ,payload);
      yield put({
        type: 'findByUserIdAndSearchInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    // 保存病历
    *saveEmrService({payload, callback} , { call, put }) {
      const response = yield call(saveEmr ,payload);
      yield put({
        type: 'saveEmrInfo',
        payload: response,
      });
      if (response.code === 200 ||response.code === 500) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //词条联想查询
    *KeyWordService({payload, callback} , { call, put }) {
      const response = yield call(findWordsByKeyWord ,payload);
      yield put({
        type: 'KeyWordInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //诊断联想查询
    *DiagByKeyWordService({payload, callback} , { call, put }) {
      const response = yield call(findDiagByKeyWord ,payload);
      yield put({
        type: 'DiagByKeyWordInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //基础治疗联想查询
    *findTreatByKeyWordService({payload, callback} , { call, put }) {
      const response = yield call(findTreatByKeyWord ,payload);
      yield put({
        type: 'findTreatByKeyWordInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
    //获取一般检查配置项
    *findToothExamCacheService({payload, callback} , { call, put }) {
      const response = yield call(findToothExamCache ,payload);
      yield put({
        type: 'findToothExamCacheInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
  },

  //同步
  reducers: {
    findMyTempContentInfo(state, action) {
      return {
        ...state,
        findMyTempContentClassData: action.payload || {},
      };
    },
    saveEmrMrcInfo(state, action) {
      return {
        ...state,
        saveEmrMrcData: action.payload || {},
      };
    },
    saveLinkImgsInfo(state, action) {
      return {
        ...state,
        saveLinkImgsData: action.payload || {},
      };
    },
    findLinkImgsInfo(state, action) {
      return {
        ...state,
        findLinkImgsData: action.payload || {},
      };
    },
    findLinkMrcsInfo(state, action) {
      return {
        ...state,
        findLinkMrcsData: action.payload || {},
      };
    },
    dbPathTransformInfo(state, action) {
      return {
        ...state,
        dbPathTransformData: action.payload || {},
      };
    },
    findCheckImgsInfo(state, action) {
      return {
        ...state,
        findCheckImgsByDate: action.payload || {},
      };
    },
    getEmrsInfo(state, action) {
      return {
        ...state,
        getEmrInfoData: action.payload || {},
      };
    },
    generateEmrSubIdInfo(state, action) {
      return {
        ...state,
        generateEmrData: action.payload || {},
      };
    },
    findByUserIdAndSearchInfo(state, action) {
      return {
        ...state,
        findByUserIdAndSearchData: action.payload || {},
      };
    },
    findTreatByKeyWordInfo(state, action) {
      return {
        ...state,
        TreatByKeyWordData: action.payload || {},
      };
    },
    DiagByKeyWordInfo(state, action) {
      return {
        ...state,
        DiagByKeyWordData: action.payload || {},
      };
    },
    saveEmrInfo(state, action) {
      return {
        ...state,
        saveEmrData: action.payload || {},
      };
    },
    KeyWordInfo(state, action) {
      return {
        ...state,
        KeyWordData: action.payload || {},
      };
    },
    findToothExamCacheInfo(state, action) {
      return {
        ...state,
        ToothExamData: action.payload || {},
      };
    },
  },
};
export default WriteMedicalModel;
