.tableSearch{
  background-color: #108de9!important;
  color: #fff!important;
}
.tooth_top{
  padding-bottom: 20px;
  :global{
    .ant-tag{
      font-size:14px;
      line-height:1.5;
    }
  }
}
.tableAll{
  .table{
    width: 100%;
    height: 168px;
    td {
      height: 100px;
    }
  }
  .bottom_td {
    width: 22px;
    border: 1.6px solid #DEDEDE;
    border-right: 0;
    border-bottom: 0;
  }
  .td {
    width: 22px;
    border: 1.6px solid #DEDEDE;
    border-top: 0;
    border-left: 0;
  }
  .td_box{
    padding: 11px 4px;
    display: inline-flex;
  }
  .btn_box{
    height: 63px;
    width: 63px;
    position: relative;
    left: 0;
    top: 0;
    border: 1px solid #B5B8B8;
    cursor: pointer;
  }
  .tooth_box1{
    height: 0px;
    width: 63px;
    position: absolute;
    top: 0px;
    left: -2px;
    z-index: 100;
    border-width: 17px 21px 0 22px;
    border-style: solid;
    border-color: #ffffff transparent transparent transparent;
    span{
      position: absolute;
      top: -19px;
      left: 9px;
      user-select:none;
    }
  }
  .tooth_box1_hover{
    height: 0px;
    width: 63px;
    position: absolute;
    top: 0px;
    left: -2px;
    z-index: 100;
    border-width: 17px 21px 0 22px;
    border-style: solid;
    border-color: #1890FF transparent transparent transparent;
    span{
      position: absolute;
      top: -19px;
      left: 9px;
      color: #ffffff;
      user-select:none;
    }
  }
  .tooth_box1_d{
    height: 0px;
    width: 63px;
    position: absolute;
    top: 0px;
    left: -2px;
    z-index: 100;
    border-width: 27px 20px 0 22px;//27px ​20px 0 22p
    border-style: solid;
    border-color: #ffffff transparent transparent transparent;
    span{
      position: absolute;
      top: -25px;
      left: 7px;
      user-select:none;
    }
  }
  .tooth_box1_s{
    position: absolute;
    top: 0px;
    left: -2px;
    height: 0px;
    width: 63px;
    border-width: 18px 20px 0 22px;
    border-style: solid;
    border-color: #939496 transparent transparent transparent;
    z-index: 1;
  }
  .tooth_box1_s_d{
    position: absolute;
    top: 0px;
    left: -2px;
    height: 0px;
    width: 63px;
    border-width: 28px 20px 0 22px;
    border-style: solid;
    border-color: #939496 transparent transparent transparent;
    z-index: 1;
  }
  .tooth_box2 {
    height: 60px;
    border-width: 18px 1px 23px 20px;
    border-style: solid;
    border-color: transparent transparent transparent #ffffff;
    position: absolute;
    left: 0px;
    top: 1px;
    z-index: 100;
    span{
      position: absolute;
      left: -16px;
      top: -2px;
      user-select:none;
    }
  }
  .tooth_box2_s{
    position: absolute;
    left: -1px;
    top: 1px;
    height: 60px;
    width: 0px;
    border-width: 16px 0px 22px 21px;
    border-style: solid;
    border-color: transparent transparent transparent #939496;
    z-index: 1;
  }
  .tooth_box2_hover {
    height: 60px;
    border-width: 17px 0px 22px 19px;
    border-style: solid;
    border-color: transparent transparent transparent #1890FF;
    position: absolute;
    left: 0px;
    top: 1px;
    z-index: 100;
    span{
      position: absolute;
      left: -16px;
      top: -2px;
      color: #ffffff;
      user-select:none;
    }
  }
  .tooth_box3 {
    width: 21px;
    height: 21px;
    border-style: solid;
    border-color: #B5B8B8;
    border-width: 0px 1px 1px 1px;
    user-select:none;
    text-align: center;
    position: absolute;
    left: 20px;
    top: 17px;
  }
  .tooth_box3_hover {
    width: 21px;
    height: 21px;
    border-style: solid;
    border-color: #B5B8B8;
    background-color: #1890FF;
    border-width: 0px 1px 1px 1px;
    color: #ffffff;
    text-align: center;
    position: absolute;
    left: 20px;
    top: 17px;
  }
  .tooth_box4 {
    height: 0px;
    width: 63px;
    top: 38px;
    left: -1px;
    border-width: 0px 23px 23px 23px;
    border-style: solid;
    border-color: transparent transparent #ffffff transparent;
    position: absolute;
    z-index: 2;
    span{
      padding: 0px 0 0 5px;
      user-select:none;
    }
  }
  .tooth_box4_hover {
    height: 0px;
    width: 63px;
    top: 39px;
    left: 0px;
    border-width: 0px 24px 22px 23px;
    border-style: solid;
    border-color: transparent transparent #1890FF transparent;
    position: absolute;
    z-index: 2;
    span{
      padding: 0px 0 0 5px;
      color:#ffffff;
      user-select:none;
    }
  }
  .tooth_box4_d {
    width: 62px;
    top: 28px;
    left: 0px;
    border-width: 0px 22px 33px 21px;
    border-style: solid;
    border-color: transparent transparent #ffffff transparent;
    position: absolute;
    z-index: 2;
    span{
      position: absolute;
      top: 7px;
      left: 9px;
      user-select:none;
    }
  }
  .tooth_box4_s{
    position: absolute;
    top: 38px;
    left: -1px;
    height: 0px;
    width: 64px;
    border-width: 0px 23px 24px 22px;
    border-style: solid;
    border-color: transparent transparent #939496 transparent;
    z-index: 1;
  }
  .tooth_box4_s_d{
    position: absolute;
    top: 28px;
    left: -1px;
    height: 0px;
    width: 64px;
    border-width: 0px 22px 34px 21px;
    border-style: solid;
    border-color: transparent transparent #939496 transparent;
    z-index: 1;
  }
  .tooth_box5{
    height: 60px;
    border-width: 16px 20px 20px 3px;
    border-style: solid;
    border-color: transparent #ffffff transparent transparent;
    position: absolute;
    left: 38.5px;
    top: 0px;
    width: 0;
    z-index: 100;
    span{
      position: absolute;
      top: 1.2px;
      left: 6px;
      user-select:none;
    }
  }
  .tooth_box5_s{
    position: absolute;
    left: 39px;
    top: 0px;
    height: 60px;
    width: 0;
    border-width: 16px 20px 20px 3px;
    border-style: solid;
    border-color: transparent #939496 transparent transparent;
    z-index: 1;
  }
  .tooth_box5_hover{
    height: 59px;
    border-width: 18px 20px 22px 2px;
    border-style: solid;
    border-color: transparent #1890FF transparent transparent;
    position: absolute;
    width: 0;
    left: 40px;
    top: 0px;
    z-index: 100;
    span{
      position: absolute;
      top: 1.2px;
      left: 6px;
      color: #ffffff;
      user-select:none;
    }
  }
  .btn_tooth_Eng{
    text-align: right;
  }
  .btn_tooth_Eng_left{
    text-align: left;
  }
  :global{
    .ant-btn{
      height: 21px;
      line-height: 0.1;
      font-size: 16px;
      margin: 3px 21px;
    }
    .ant-btn-circle, .ant-btn-circle-outline {
      min-width: 21px;
    }
  }
}
.tableSearchBtns{
  background-color: #ffffff!important;
  color: #333!important;
}



