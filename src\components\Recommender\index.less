  .container{
    :global{
        .ant-modal-header{
            padding: 12px 20px 12px 12px !important;
        }
      .ant-btn{
        padding: 0 12px;
      }
      .ant-btn-primary{
        background-color: #1890ff;
      }
    }
  }
 .contentContainer{
     min-height: 115px;
     .search{
         margin-bottom: 20px;
     }
    .ReferrerRelationship{
        background:rgba(224,226,231,1);
        border-radius:3px;
        padding: 9px 9px 0 9px;
        margin-bottom: 20px;
        margin-left: 20px;
        :global{
            .ant-tag-checkable{
                background:rgba(255,255,255,1);
                color:rgba(68,68,68,1);
                height: 28px;
                line-height: 25px;
                border: 1px solid rgba(204,204,204,1);
                margin-bottom: 12px;
                transition:none;
            }
            .ant-tag-checkable-checked{
                color:#fff;
                background-color:#4BA2FF;
            }
        }
    }
    .ReferrerRelationshipHide{
        display: none;
    }
    .referrerList{
      height: 423px;
      overflow: hidden;
      overflow-y: auto;
      padding-right: 10px;
    }
    .patientCard{
        min-height: 162px;
        margin-bottom: 20px;
    }
    .CardContent{
        padding: 12px 12px 12px 6px;
        border:1px solid rgba(187, 187, 187, 1);
        border-radius:3px;
    }
    .CurrentReferences{
        width:59px;
        height:52px;
        position: absolute;
        left: 11px;
        top:-2px;
        z-index: 10;
        transform:rotate(-1deg);
    }
    .checkedCardContent{
        padding:12px 12px 12px 6px;
        border:1px solid rgba(187, 187, 187, 1);
        border-radius:3px;
        background: rgba(216,231,245,1);
    }
    .leftAvatar{
        width: 90px;
        text-align: center;
        overflow: hidden;
        .Vip {
                width: 24px;
                height: 24px;
                right: 5px;
                top:51px;
            }
        .fileNumber{
                font-weight: 600;
                font-size: 16;
                color: #444;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                margin-bottom: 0;
        }
    }
    .rightCheckedContent{
        flex: 1;
        padding-left:6px;
        background: url('./../../assets/checkMark.png') no-repeat right center;
        background-size: 27px 27px;
    }
    .rightContent{
        flex: 1;
        padding-left:6px;
    }
    .rightName{
            font-weight: 600;
            font-size: 16;
            color: #444;
            margin-bottom: 6px;
    }
 }
  .getCustomerWay{
    max-width: 200px;
    word-break: break-all;
  }
