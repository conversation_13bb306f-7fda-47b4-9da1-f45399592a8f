import BasicTreatmentModel from "@/models/BasicTreatment";
import {getTreatClasses} from "@/services/BasicTreatment";
import { appointmentList } from '@/services/ClinicMornMeet.js';
import {notification} from "antd";

/**
 * 诊所-晨会模块
 * /emr/ClinicMornMeet
 * */
const ClinicMornMeetModel = {
  namespace: 'ClinicMornMeetModel',
  state: {
    // 晨会列表的的筛选项 和搜索条件已经当前页数
    paramsByClinicMornMeetList: {
      pageSize:10,             // 获取条数
      pageNow:1,               // 获取页数
      searchValue:null,        // 搜索值resultList
      searchFiled:null,        // 患者姓名、病历号、主诉
      queryType:'1',          // null 全部 1今日病历 2.既往病历 3.未来病历 默认今日病例
      doctorId:null,           // 搜索医生用户ID
    },
    dataByAppointmentList:null,  // 晨会列表数据
    // 晨会详情页面的参数
    paramsByClinicMornMeetDetails: {

    },
  },
  //异步
  effects: {
    *appointmentList({payload, callback} , { call, put,select }) {
      const paramsByClinicMornMeetList =  yield select((state)=>{
        console.log('state123123 :: ',state.ClinicMornMeetModel.paramsByClinicMornMeetList);
        return state.ClinicMornMeetModel.paramsByClinicMornMeetList
      });

      console.log('paramsByClinicMornMeetList :: ',paramsByClinicMornMeetList);
      const response = yield call(appointmentList ,{...paramsByClinicMornMeetList,...payload});
      yield put({
        type: 'save',
        payload: {
          paramsByClinicMornMeetList:{...paramsByClinicMornMeetList,...payload}
        },
      });
      yield put({
        type: 'saveAppointmentList',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    }
  },
  // 同步
  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    saveAppointmentList(state, { payload }) {
      const { code,content } = payload || {}
      const { paramsByClinicMornMeetList } = state || {}
      if(code == 200) {
        const {
          pageNum,
          pageSize,
          resultList,
          total,
        } = content || {}

        return {
          ...state,
          paramsByClinicMornMeetList: {
            ...paramsByClinicMornMeetList,
            pageNow: pageNum,
            pageSize: pageSize,
          },
          dataByAppointmentList:payload,
        }
      }else {
        return {
          ...state,
          dataByAppointmentList:null,
        }
      }
    }
  }
}
export default ClinicMornMeetModel;
