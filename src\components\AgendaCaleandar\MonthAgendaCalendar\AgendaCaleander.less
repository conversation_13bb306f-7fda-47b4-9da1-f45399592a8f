@import '~@/utils/utils.less';

.scrollBox{
  max-height: ~"calc(70vh)";
  min-height: 258px;
  overflow: hidden;
  border-bottom: 1px solid #ccc;
  border-top: 1px solid #ccc;
  cursor: pointer;
  .clearfix()
}

.scrollContentInfo {
  height: 101%;
  width: 100%;
  max-width: 1980px;
  clear: both;
  .clearfix();
}

.scrollSpan{
  height: 40px;
  width: 100%;
  //background: #4ca2ff;
  display: none;
  .clearfix();
}

.scrollSpanContent {
  //margin-bottom: 4px;
  text-align: center;
  margin: 0 auto;
  margin-top: 4px;
}

.spanContentTitle {
  font-size: 16px;
  font-weight: 700;
  margin-right: 7px;
}


.scrollSpanBottom {
  height: 30px;
  width: 100%;
  //background: #4ca2ff;
  display: none;
  .clearfix();
}


.scroll {
  //overflow-y: auto;
  //overflow-x: auto;
  min-height: 100.5%;
  .clearfix();

  :global {
    html {
      /*隐藏滚动条，当IE下溢出，仍然可以滚动*/
      -ms-overflow-style:none;
      /*火狐下隐藏滚动条*/
      overflow:-moz-scrollbars-none;
    }

    /*Chrome下隐藏滚动条，溢出可以透明滚动*/
    html::-webkit-scrollbar{width:0px}
  }
}

.calendar {
  :global {
    font-size: 14px;
    background: #fff;
    margin-top: -1px;
    .fc-widget-content:not(.fc-axis .fc-time) {
      cursor:pointer;
    }

    .fc-widget-content {
      border-bottom: none;
    }

    .fc-head {
      display:none;
    }



    .fc-ltr .fc-axis {
      vertical-align: middle;
      padding: 0 12px;
      white-space: nowrap;
      width: 41px!important;
    }

    /*.fc th, .fc td {
      border-style: solid;
      border-width: 1px;
      padding: 0;
      vertical-align: top;
    }*/

    .fc-time-grid .fc-slats td {
      //height: 2.0em;
      height: 58px!important;
      box-sizing: border-box;
      //border-color: #dddddd;
    }
    .fc-unthemed tbody {
      //background: #fff;
    }
    .fc-v-event{
      border: 1px solid #ccd;
      //min-height: 16px;
      overflow: unset;
    }

    .fc-view .fc-head-container tr {
      background: #E0E2E7;
      height: 60px;
      line-height: 60px;
      font-family: MicrosoftYaHei;
      font-weight: normal;
      border-bottom: 1px solid #000000;
      font-size: 14px;
      color: #444444;
    }

    .fc-body .fc-axis {
      background: #E0E2E7;
      padding: 0 13px;
      border-color: #ccc;
      box-sizing: content-box;
    }


    .fc-head .fc-widget-content {
      //background: #fff;
      //border-color: #ddd;
    }

    .fc-unthemed th,
    .fc-unthemed td,
    .fc-unthemed thead,
    .fc-unthemed tbody,
    .fc-unthemed .fc-divider,
    .fc-unthemed .fc-row,
    .fc-unthemed .fc-content,
    .fc-unthemed .fc-popover,
    .fc-unthemed .fc-list-view,
    .fc-unthemed .fc-list-heading td{
      border-color: #ccc;
    }

    .fc-unthemed  .fc-resource-cell {
      background: #ECECF1;
      font-weight: normal;
      font-size: 14px;
    }
    .surplusBackgroundEvent {
      opacity: 1!important;
    }
    .inverseWorkbackground {
      opacity: 0.6!important;
    }
    .doctorName {
      margin-left: 10px;
    }

    .onterEventTile {
      text-align: center;
    }

    .DoctorNameTr th {
      text-align: left;
      padding-left: 16px;
      font-weight: normal;
      font-size: 16px;
      font-family: "Microsoft YaHei";
    }

    .DoctorNameTr th.DoctorIconTr {
      padding: 0 12px;
      text-align: center;
    }

    .labelTdsTitle{
      line-height: 0px;
      margin-top: 20px;
      font-weight: 400;
      color: #444444;
    }

    .titleLable {
      line-height:0px;
      margin-top: 20px;
      color: #444444;
      font-weight: 600;
    }

    .fc-event-container .fc-event{
      border-radius:10px;
      overflow: hidden;
    }

    .expirationTime.fc-bgevent{
      opacity:0.5;
      border-bottom: 2px solid #F43D3D;
    }

    .fc-event-container .fc-event.unset {
      overflow: unset;
    }

    .fc-event-container .fc-event.unset .antd-pro-components-agenda-caleandar-elements-event-element-patientEventicon{
      margin-top: -7px;
    }

    /*已结算*/
    .fc-event-container .fc-event.finishType:not(.unset):before {
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #9E9E9E;
    }
    /*已经迟到*/
    .fc-event-container .fc-event.lateType:not(.unset):before {
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #F28A19;
    }
    /*未结算 未迟到 未到诊*/
    .fc-event-container .fc-event.notClinicType:not(.unset):before {
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #27AE6B;
    }
    /*未结算 未迟到 已经到诊*/
    .fc-event-container .fc-event.comeClinicType:not(.unset):before {
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: #2796F3;
    }
    /*搜索到的event*/
    .fc-event-container .fc-event.checkedEvent{
      border:2px solid blue!important;
    }

    .eventOtherEvent{
      border-radius: 10px;
      border: 1px solid #5B6AFF;
      margin: 1px;
      text-align: center;
      opacity: 1;
    }

    .otherWalkingTime.fc-bgevent{
      border-radius: 10px;
      border: 1px solid #5B6AFF;
      margin: 1px;
      text-align: center;
      opacity: 1;
    }

    .checkedBackEvent.fc-bgevent {
      border:2px solid blue!important;
      opacity: 1;
    }

    /*6: 工作时间*/
    /*.fc-event-container .fc-event.officeHoursClass:before{
      content:"";
      display: inline-block;
      width: 10px;
      height: 100%;
      background: rgba(255,0,0,.3);
    }*/

    /*.fc-event-container .fc-v-event.eventTypeClass:hover {
      border-color: #22ff22!important;
    }*/
  }
}

/*.popperContentArrows:after {
  content:'';
  position:absolute;
  //height:20px;
  //width:20px;
  //background:yellow;
  left: 300px;
  top: 1px;
  //border:10px solid gray;
  border:10px solid transparent;
  //border-top-color:gray,
  border-left-color:gray
}*/




.popperContent{
  z-index: 99;
  position: relative;
  background: #ffc107;
  color: black;
  width: 300px;
  border-radius: 3px;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
  text-align: center;

  :global {
    [x-arrow] {
      position: absolute;
     /* left: -8px;
      border-right: 8px solid blue;
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      */

      border:10px solid transparent;
      border-left-color:gray
    }
  }
}
