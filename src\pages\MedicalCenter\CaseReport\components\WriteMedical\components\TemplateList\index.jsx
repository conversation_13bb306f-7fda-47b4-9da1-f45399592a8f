import { Mo<PERSON>, <PERSON><PERSON>, Spin } from 'antd';
import React, { useState, useRef, Component } from 'react';
import { useIntl, FormattedMessage } from 'umi';
import { GridContent } from '@ant-design/pro-layout';
import styles from './style.less';//引入样式
//引入图片
import Fold from '@/assets/<EMAIL>';
import Unfold from '@/assets/<EMAIL>';
import Folder from '@/assets/<EMAIL>';
import FileUnselected from '@/assets/<EMAIL>';
import FileSelected from '@/assets/<EMAIL>';
import Download from '@/assets/<EMAIL>';
import chakan from '@/assets/chakan.png';
import PreviewHover from '@/assets/<EMAIL>';
import DownBlue from '@/assets/xiazai_blue.png';
import diamond from '@/assets/<EMAIL>';
import boy from '@/assets/<EMAIL>';
import noData from "@/assets/<EMAIL>";

import commonStyle from '@/pages/common.less';//引入公共样式
import FolderSelected from '@/assets/<EMAIL>';
import { connect } from 'dva';
const { TabPane } = Tabs;

class templateList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      show1: null,//点击一级文件夹状态
      show2: null,//点击二级文件夹状态
      show3: null,//点击三级文件夹状态
      clientHeight: document.documentElement.clientHeight, // 屏幕高度
      EmrTmp: [], //病历文件夹
      fileLists: [], //病历模板列表
      EmrMsgs: {}, //模板详情
      InforList: [], //知情同意书文件夹
      ModelListName: [], //知情同意书模板列表
      MrcMessage: {}, //知情同意书详情数据
      BookStatus: false,//知情同意书预览弹窗状态
      tabChinese: true,//知情同意书默认显示中文状态
      tabEnglish: false,//知情同意书默认显示英文状态
      MrcmessageName: '',//知情同意书详情名称
      MsgStatus: false,//查看知情同意书loading状态
      onefirstCode:"",//存一下病历一级的classcode
      patientInfoDtos:{...props.patientInfoDtos}//患者标签
    };
    this.resize = this.resize.bind(this);//屏幕高度
  }
  //初始化
  componentDidMount() {
    this.getModelList();//获取病历模板列表
    this.InforList();//知情同意书模板列表
    window.addEventListener('resize', this.resize); //监听屏幕高度
  }
  //在组件卸载及销毁之前直接调用
  componentWillUnmount() {
    window.removeEventListener('resize', this.resize); //取消
  }
  //获取病历模板列表
  getModelList = () => {
    const { dispatch } = this.props;
    let params = {
      tenantId: localStorage.getItem('tenantId'), //平台标识
    };
    if (dispatch) {
      dispatch({
        type: 'medicalModel/findEmrTmptService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              EmrTmp: res.rows,
            });
          }
        },
      });
    }
  };
  //知情同意书模板列表
  InforList = () => {
    const { dispatch } = this.props;
    let params = {
      // tenantId:localStorage.getItem('tenantId')
    };
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/findCheckService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              InforList: res.rows,
            });
          }
        },
      });
    }
  };
  //监听屏幕高度
  resize() {
    this.setState({ clientHeight: document.documentElement.clientHeight });
  }
   //点击病历模板一级文件夹
  openfirst = (item, index, classCode) => {
    this.state.onefirstCode=classCode;
    if (this.state.show1 == classCode) {
      this.setState({
        show1: null,
        show2: null,
        show3: null,
      });
    } else {
      this.setState({
        show1: classCode,
        show2: null,
        show3: null,
      });
    }
  };
  //知情同意书模板列表点击第一级文件夹
  openfirst1 = (item, index, id) => {
    if (this.state.show1 == id) {
      this.setState({
        show1: null,
        show2: null,
        show3: null,
        ModelListName:[]
      });
    } else {
      this.setState({
        show1: id,
        show2: null,
        show3: null,
        ModelListName:[]
      });
    }
    if (item.classCode) {
      this.getModelName(item.classCode);
    }
  };
  //根据知情同意书模板分类获取模板名
  getModelName = (classCode) => {
    const { dispatch } = this.props;
    let params = {
      classCode: classCode,
      tenantId: localStorage.getItem('tenantId'),
    };
    // console.log('params', params);
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/findMrcTmptNamesService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              ModelListName: res.rows,
            });
          }
        },
      });
    }
  };
  //一级文件夹hover状态
  hoverFirst = (hoverFirst, classCode) => {
    this.setState({
      ['hoverFirst' + classCode]: hoverFirst,
    });
  };
  //二级文件夹hover状态
  hoverTwo = (hoverTwo, id) => {
    this.setState({
      ['hoverTwo' + id]: hoverTwo,
    });
  };
  //三级文件hover状态
  hoverThree = (hoverThree, id) => {
    this.setState({
      ['hoverThree' + id]: hoverThree,
    });
  };
  //病历模板点击二级文件夹
  opentwo = (item1, index1, id) => {
    this.setState({
      fileLists: [],
    });
    this.ModelLists(item1.classCode);
    if (this.state.show2 == id) {
      this.setState({
        show2: null,
      });
    } else {
      this.setState({
        show2: id,
        show3: null,
      });
    }
  };
  //知情同意书模板第二级点击事件
  opentwo1 = (item1, index1, id) => {
    if (this.state.show2 == id) {
      this.setState({
        show2: null,
      });
    } else {
      this.setState({
        show2: id,
      });
    }
  };
  //病历模板列表
  ModelLists = (classCode) => {
    const { dispatch } = this.props;
    let param;
    if(this.state.onefirstCode=="0"){
      param = {
        isSystem:2,
        classCode: classCode, //分类编码
        tenantId: localStorage.getItem('tenantId'), //平台标识
      };
    }else{
      param = {
        classCode: classCode, //分类编码
        tenantId: localStorage.getItem('tenantId'), //平台标识
      };
    }
    if (dispatch) {
      dispatch({
        type: 'medicalModel/findEmrTmptNameService',
        payload: param,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              fileLists: res.rows,
            });
          }
        },
      });
    }
  };
  //鼠标离开状态
  closetwo = () => {
    // console.log('88888');
    this.setState({
      show2: null,
    });
  };
  //点击三级模板文件
  openthree = (item2, index2, id) => {
    if (this.state.show3 == id) {
      this.setState({
        show3: null,
      });
    } else {
      this.setState({
        show3: id,
      });
    }
    this.setState({
      EmrMsgs: {},
    });
    this.getEmrMsg(id);
  };
  //获取病历详情
  getEmrMsg = (id) => {
    const { dispatch } = this.props;
    let params = {
      id: id, //模板代码
      tenantId: localStorage.getItem('tenantId'), //平台标识
    };
    if (dispatch) {
      dispatch({
        type: 'medicalModel/EmrMessageService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              EmrMsgs: res.content,
            });
          }
        },
      });
    }
  };
  //点击使用模板
  userModel = (item2) => {
    this.props.getModelMsg(item2.id);
  };
  // 鼠标移入移出影像图片
  onMouseIn = (mouseIn, id) => {
    this.setState({
      ['mouseIn' + id]: mouseIn,
    });
  };
  //下载图片状态显示
  downLoads = (mouseIn, id) => {
    this.setState({
      ['downLoads' + id]: mouseIn,
    });
  };
  // tab切换的时候重赋初始值
  onChangeTabs = (key) => {
    this.setState({
      show1: null,
      show2: null,
      show3: null,
    });
  };
  //知情同意书下载
  downMrcByCode = (item) => {
    // alert(item.tmptName);
    const { dispatch } = this.props;
    let params = {
      id: item.id, //模板id
      language: null,
    };
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/downMrcByCodeService',
        payload: params,
        callback: (blob) => {
          let fileName = item.tmptName + '.zip';
          if (window.navigator.msSaveOrOpenBlob) {
            navigator.msSaveBlob(blob, fileName);
          } else {
            const link = document.createElement('a');
            const evt = document.createEvent('MouseEvents');
            link.style.display = 'none';
            link.href = window.URL.createObjectURL(blob);
            link.download = fileName;
            document.body.appendChild(link); // 此写法兼容可火狐浏览器
            evt.initEvent('click', false, false);
            link.dispatchEvent(evt);
            document.body.removeChild(link);
          }
        },
      });
    }
  };
  //查看知情同意书详情
  LookMrcByCode = (item1) => {
    const { dispatch } = this.props;
    this.setState({
      MrcMessage: {},
      BookStatus: true,
      MsgStatus: true,
      tabChinese:true,
      tabEnglish:false,
      MrcmessageName: item1.tmptName,
    });
    let params = {
      id: item1.id,
      tenantId: localStorage.getItem('tenantId'),
    };
    // console.log('params', params);
    if (dispatch) {
      dispatch({
        type: 'InforConsentModel/getMrcTempInfoService',
        payload: params,
        callback: (res) => {
          // console.log('详情==', JSON.stringify(res));
          if (res.code == 200) {
            this.setState({
              MrcMessage: res.content,
              MsgStatus: false,
            });
          } else {
            this.setState({
              MsgStatus: false,
            });
          }
        },
      });
    }
  };
  //查看知情同意书关闭弹窗
  ModalCancel = () => {
    this.setState({
      BookStatus: false,
    });
  };
  // 中英切换-点击中文
  tabChinese = (tabChinese) => {
    this.setState({
      tabChinese: true,
      tabEnglish: false,
    });
    document.getElementById('pageView').scrollTop = 0;
  };
  // 中英切换-点击英文
  tabEnglish = (tabEnglish) => {
    this.setState({
      tabEnglish: true,
      tabChinese: false,
    });
    document.getElementById('pageView').scrollTop = 0;
  };
  render() {
    const {
      EmrTmp,
      fileLists,
      EmrMsgs,
      InforList,
      ModelListName,
      MrcMessage,
      BookStatus,
      MrcmessageName,
      MsgStatus,
    } = this.state;
    return (
      <GridContent>
        <div className={styles.templateListContent}>
          <Tabs defaultActiveKey="1" onChange={this.onChangeTabs}>
            <TabPane tab="病历模板" key="1">
              <div
                className={styles.modelContent}
                style={{height:(this.state.patientInfoDtos.labelList && this.state.patientInfoDtos.labelList.length>0)?this.state.clientHeight-283 : this.state.clientHeight-262}}
              >
                {EmrTmp.map((item, index) => (
                  <div className={styles.menuFirst} key={index}>
                    <div
                      className={
                        this.state['hoverFirst' + item.classCode]
                          ? `${styles.pointer} ${styles.chooseBgcolor}`
                          : `${styles.pointer}`
                      }
                      style={{ cursor: 'pointer', marginTop: 12 }}
                      onClick={() => this.openfirst(item, index, item.classCode)}
                      onMouseEnter={() => this.hoverFirst(true, item.classCode)}
                      onMouseLeave={() => this.hoverFirst(false, item.classCode)}
                    >
                      {this.state.show1 == item.classCode ? (
                        <img
                          onMouseEnter={() => this.hoverFirst(true, item.classCode)}
                          onMouseLeave={() => this.hoverFirst(false, item.classCode)}
                          src={Unfold}
                          className={styles.arrows}
                          alt=""
                        />
                      ) : (
                        <img
                          onMouseEnter={() => this.hoverFirst(true, item.classCode)}
                          onMouseLeave={() => this.hoverFirst(false, item.classCode)}
                          src={Fold}
                          className={styles.arrows}
                          alt=""
                        />
                      )}
                      {this.state['hoverFirst' + item.classCode] ? (
                        <img
                          onMouseEnter={() => this.hoverFirst(true, item.classCode)}
                          onMouseLeave={() => this.hoverFirst(false, item.classCode)}
                          src={FolderSelected}
                          className={styles.fileIcon}
                          alt=""
                        />
                      ) : (
                        <img
                          onMouseEnter={() => this.hoverFirst(true, item.classCode)}
                          onMouseLeave={() => this.hoverFirst(false, item.classCode)}
                          src={Folder}
                          className={styles.fileIcon}
                          alt=""
                        />
                      )}
                      <div
                        className={
                          this.state['hoverFirst' + item.classCode]
                            ? `${styles.filetitle} ${styles.chooseFontcolor}`
                            : `${styles.filetitle}`
                        }
                        style={{ marginLeft: 8 }}
                      >
                        {item.className}
                      </div>
                    </div>
                      <>
                        {item.children ? (
                          <>
                            {item.children.map((item1, index1) => (
                              <div
                                style={{ marginLeft: 15 }}
                                key={index1}
                                className={
                                  this.state.show1 == item.classCode ? styles.show : styles.hidden
                                }
                              >
                                <div
                                  className={
                                    this.state['hoverTwo' + item1.id]
                                      ? `${styles.pointer} ${styles.chooseBgcolor}`
                                      : `${styles.pointer}`
                                  }
                                  style={{ cursor: 'pointer', marginTop: 12 }}
                                  onMouseEnter={() => this.hoverTwo(true, item1.id)}
                                  onMouseLeave={() => this.hoverTwo(false, item1.id)}
                                  onClick={() => this.opentwo(item1, index1, item1.id)}
                                >
                                  {this.state.show2 == item1.id ? (
                                    <img src={Unfold} className={styles.arrows} alt="" />
                                  ) : (
                                    <img src={Fold} className={styles.arrows} alt="" />
                                  )}
                                  {this.state['hoverTwo' + item1.id] ? (
                                    <img
                                      onMouseEnter={() => this.hoverTwo(true, item1.id)}
                                      onMouseLeave={() => this.hoverTwo(false, item1.id)}
                                      src={FolderSelected}
                                      className={styles.fileIcon}
                                      alt=""
                                    />
                                  ) : (
                                    <img
                                      onMouseEnter={() => this.hoverTwo(true, item1.id)}
                                      onMouseLeave={() => this.hoverTwo(false, item1.id)}
                                      src={Folder}
                                      className={styles.fileIcon}
                                      alt=""
                                    />
                                  )}
                                  <div
                                    className={
                                      this.state['hoverTwo' + item1.id]
                                        ? `${styles.filetitle} ${styles.chooseFontcolor}`
                                        : `${styles.filetitle}`
                                    }
                                    style={{ marginLeft: 8 }}
                                  >
                                    {item1.className}
                                  </div>
                                </div>

                                {fileLists ? (
                                  <>
                                    {fileLists.map((item2, index2) => (
                                      <div
                                        style={{ marginLeft: 15 }}
                                        key={index2}
                                        className={
                                          this.state.show2 == item1.id ? styles.show : styles.hidden
                                        }
                                      >
                                        <div
                                          className={
                                            this.state['hoverThree' + item2.id]
                                              ? `${styles.fileName} ${styles.chooseBgcolor}`
                                              : `${styles.fileName}`
                                          }
                                          style={{ justifyContent: 'space-between', marginTop: 12 }}
                                          onMouseEnter={() => this.hoverThree(true, item2.id)}
                                          onMouseLeave={() => this.hoverThree(false, item2.id)}
                                        >
                                          <div
                                            style={{
                                              display: 'flex',
                                              cursor: 'pointer',
                                              width: '80%',
                                            }}
                                            onClick={() => this.openthree(item2, index2, item2.id)}
                                          >
                                            {this.state.show3 == item2.id ||
                                            this.state['hoverThree' + item2.id] ? (
                                              <img
                                                src={FileSelected}
                                                className={styles.Unselected}
                                                alt=""
                                              />
                                            ) : (
                                              <img
                                                src={FileUnselected}
                                                className={styles.Unselected}
                                                alt=""
                                              />
                                            )}
                                            <div
                                              className={
                                                this.state.show3 == item2.id ||
                                                this.state['hoverThree' + item2.id]
                                                  ? styles.SelectedStyle
                                                  : styles.UnselectedStyle
                                              }
                                            >
                                              {item2.tmptName}
                                            </div>
                                          </div>
                                          <div
                                            onClick={this.userModel.bind(this, item2)}
                                            className={styles.filetitle}
                                            style={{ float: 'right', cursor: 'pointer' }}
                                          >
                                            使用
                                          </div>
                                        </div>
                                        <div
                                          className={
                                            this.state.show3 == item2.id
                                              ? styles.show
                                              : styles.hidden
                                          }
                                        >
                                          <div className={styles.fileContent}>
                                            <div>
                                              <div className={styles.ContentLine}>
                                                <div className={styles.title}>主诉：</div>
                                                <div className={styles.content}>
                                                  {EmrMsgs.illnessDesc ? EmrMsgs.illnessDesc : ''}
                                                </div>
                                              </div>
                                              <div className={styles.ContentLine}>
                                                <div className={styles.title}>现病史：</div>
                                                <div className={styles.content}>
                                                  {EmrMsgs.preIllnessDesc
                                                    ? EmrMsgs.preIllnessDesc
                                                    : ''}
                                                </div>
                                              </div>
                                              <div className={styles.ContentLine}>
                                                <div className={styles.title}>既往史：</div>
                                                <div className={styles.content}>
                                                  {EmrMsgs.pastHistDesc ? EmrMsgs.pastHistDesc : ''}
                                                </div>
                                              </div>
                                              <div className={styles.ContentLine}>
                                                <div className={styles.title}>全身情况：</div>
                                                <div className={styles.content}>
                                                  {EmrMsgs.genCondDesc ? EmrMsgs.genCondDesc : ''}
                                                </div>
                                              </div>
                                              <div className={styles.ContentLine}>
                                                <div className={styles.title}>检查：</div>
                                                <div className={styles.content}>
                                                  {EmrMsgs.examineDesc ? EmrMsgs.examineDesc : ''}
                                                </div>
                                              </div>
                                              <div className={styles.ContentLine}>
                                                <div className={styles.title}>辅助检查：</div>
                                                <div className={styles.content}>
                                                  {EmrMsgs.auxiExamDesc ? EmrMsgs.auxiExamDesc : ''}
                                                </div>
                                              </div>
                                              <div className={styles.ContentLine}>
                                                <div className={styles.title}>诊断：</div>
                                                <div className={styles.content}>
                                                  {EmrMsgs.diagDesc ? EmrMsgs.diagDesc : ''}
                                                </div>
                                              </div>
                                              <div className={styles.ContentLine}>
                                                <div className={styles.title}>处置：</div>
                                                <div className={styles.content}>
                                                  {EmrMsgs.treatDesc ? EmrMsgs.treatDesc : ''}
                                                </div>
                                              </div>
                                              <div className={styles.ContentLine}>
                                                <div className={styles.title}>医嘱：</div>
                                                <div className={styles.content}>
                                                  {EmrMsgs.adviceDesc ? EmrMsgs.adviceDesc : ''}
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </>
                                ) : (
                                  ''
                                )}
                              </div>
                            ))}
                          </>
                        ) : (
                          ''
                        )}
                      </>
                  </div>
                ))}
              </div>
            </TabPane>
            <TabPane tab="同意书模板" key="2">
              <div
                className={styles.modelContent}
                style={{height:(this.state.patientInfoDtos.labelList && this.state.patientInfoDtos.labelList.length>0)?this.state.clientHeight-283 : this.state.clientHeight-262}}
              >
                {InforList.map((item, index) => (
                  <div className={styles.menuFirst} key={index}>
                    <div
                      className={
                        this.state['hoverFirst' + item.id]
                          ? `${styles.pointer} ${styles.chooseBgcolor}`
                          : `${styles.pointer}`
                      }
                      style={{ cursor: 'pointer', marginTop: 12 }}
                      onMouseEnter={() => this.hoverFirst(true, item.id)}
                      onMouseLeave={() => this.hoverFirst(false, item.id)}
                      onClick={() => this.openfirst1(item, index, item.id)}
                    >
                      {this.state.show1 == item.id ? (
                        <img src={Unfold} className={styles.arrows} alt="" />
                      ) : (
                        <img src={Fold} className={styles.arrows} alt="" />
                      )}
                      {this.state['hoverFirst' + item.id] ? (
                        <img
                          onMouseEnter={() => this.hoverFirst(true, item.id)}
                          onMouseLeave={() => this.hoverFirst(false, item.id)}
                          src={FolderSelected}
                          className={styles.fileIcon}
                          alt=""
                        />
                      ) : (
                        <img
                          onMouseEnter={() => this.hoverFirst(true, item.id)}
                          onMouseLeave={() => this.hoverFirst(false, item.id)}
                          src={Folder}
                          className={styles.fileIcon}
                          alt=""
                        />
                      )}
                      <div
                        className={
                          this.state['hoverFirst' + item.id]
                            ? `${styles.filetitle} ${styles.chooseFontcolor}`
                            : `${styles.filetitle}`
                        }
                        style={{ marginLeft: 8 }}
                      >
                        {item.className}
                      </div>
                    </div>
                    {ModelListName ? (
                      <>
                        {ModelListName.map((item1, index1) => (
                          <div
                            style={{paddingLeft:16}}
                            key={index1}
                            className={this.state.show1 == item.id ? styles.show : styles.hidden}
                            onMouseEnter={() => this.opentwo1(item1, index1, item1.id)}
                            onMouseLeave={() => this.closetwo()}
                          >
                            <div
                              className={
                                this.state.show2 == item1.id
                                  ? `${styles.pointer} ${styles.chooseBgcolor}`
                                  : `${styles.pointer}`
                              }
                              style={{
                                cursor: 'pointer',
                                marginTop: 12,
                                justifyContent: 'space-between',
                              }}
                            >
                              <div style={{ display: 'flex' }}>
                                {this.state.show2 == item1.id ? (
                                  <img src={FileSelected} className={styles.Unselected} alt="" />
                                ) : (
                                  <img src={FileUnselected} className={styles.Unselected} alt="" />
                                )}
                                <div
                                  className={
                                    this.state.show2 == item1.id
                                      ? styles.SelectedStyle
                                      : styles.UnselectedStyle
                                  }
                                  style={{ marginLeft: 8}}
                                >
                                  {item1.tmptName}
                                </div>
                              </div>
                              <div style={{ display: 'flex' }}>
                                {this.state.show2 == item1.id ? (
                                  <>
                                    {this.state['mouseIn' + item1.id] ? (
                                      <img
                                        onClick={this.LookMrcByCode.bind(this, item1)}
                                        onMouseEnter={() => this.onMouseIn(true, item1.id)}
                                        onMouseLeave={() => this.onMouseIn(false, item1.id)}
                                        src={PreviewHover}
                                        className={styles.checkagress}
                                        alt=""
                                      />
                                    ) : (
                                      <img
                                        onMouseEnter={() => this.onMouseIn(true, item1.id)}
                                        onMouseLeave={() => this.onMouseIn(false, item1.id)}
                                        src={chakan}
                                        className={styles.checkagress}
                                        alt=""
                                      />
                                    )}
                                    {this.state['downLoads' + item1.id] ? (
                                      <img
                                        onClick={this.downMrcByCode.bind(this, item1)}
                                        onMouseEnter={() => this.downLoads(true, item1.id)}
                                        onMouseLeave={() => this.downLoads(false, item1.id)}
                                        src={DownBlue}
                                        className={styles.checkagress}
                                        style={{ marginLeft: 8 }}
                                        alt=""
                                      />
                                    ) : (
                                      <img
                                        onMouseEnter={() => this.downLoads(true, item1.id)}
                                        onMouseLeave={() => this.downLoads(false, item1.id)}
                                        src={Download}
                                        className={styles.checkagress}
                                        style={{ marginLeft: 8 }}
                                        alt=""
                                      />
                                    )}
                                  </>
                                ) : null}
                              </div>
                            </div>
                          </div>
                        ))}
                      </>
                    ) : (
                      ''
                    )}
                  </div>
                ))}
              </div>
            </TabPane>
          </Tabs>
        </div>
        <Modal
          width={790}
          title="知情同意书预览"
          visible={BookStatus}
          onOk={this.ModalOK}
          onCancel={this.ModalCancel}
          okText="确认上传"
          cancelText="取消"
          maskClosable={false}
          footer={[]}
        >
          <div>
            <div className={styles.contentTop}>
              <div>{MrcmessageName ? MrcmessageName : ''}</div>
              <div className={styles.Intertranslation}>
                {this.state.tabChinese ? (
                  <div onClick={() => this.tabChinese(false)} className={styles.Choose}>
                    中
                  </div>
                ) : (
                  <div onClick={() => this.tabChinese(true)} className={styles.notChoose}>
                    中
                  </div>
                )}
                {this.state.tabEnglish ? (
                  <div onClick={() => this.tabEnglish(false)} className={styles.Choose}>
                    英
                  </div>
                ) : (
                  <div onClick={() => this.tabEnglish(true)} className={styles.notChoose}>
                    英
                  </div>
                )}
              </div>
            </div>
            <Spin spinning={MsgStatus}>
              <div id="pageView" className={styles.contentBook}>
                { this.state.tabChinese && MrcMessage.tmptCont&& MrcMessage.tmptCont!==" "?
                  <div
                    dangerouslySetInnerHTML={{
                      __html: MrcMessage.tmptCont,
                    }}
                  ></div>:this.state.tabEnglish && MrcMessage.tmptContEn && MrcMessage.tmptContEn!==" "?
                    <div
                      dangerouslySetInnerHTML={{
                        __html: MrcMessage.tmptContEn,
                      }}
                    ></div>:
                      <div className={commonStyle.nodataContent} style={{marginTop:'25%'}}>
                        <img src={noData} className={commonStyle.imgStyle} alt=""/>
                        <div className={commonStyle.fontStyle}>暂无数据</div>
                      </div>
                }
              </div>
            </Spin>
          </div>
        </Modal>
      </GridContent>
    );
  }
}

export default connect(({ EmrMessageData }) => ({
  EmrMessageData,
}))(templateList);
