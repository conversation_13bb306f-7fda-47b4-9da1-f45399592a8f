import {Pagination, Table} from 'antd';
import React, { Component } from 'react';
import { GridContent } from '@ant-design/pro-layout';
import styles from './style.less';//引入样式
import {connect} from "dva";

class MajorManage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      Mangage:[],//专业字典维护列表
      params:{
        tenantId:localStorage.getItem('tenantId'),//平台标识
        pageNum:1,
        pageSize:10
      },
      loading:false//专业字典维护列表loading
    };
  }
  //初始化
  componentDidMount() {
    this.getMangageList()
  }
  //专业字典维护列表
  getMangageList=()=>{
    const {params}=this.state;
    this.setState({
      loading:true
    })
    const {dispatch} = this.props
    if (dispatch) {
      dispatch({
        type: 'findMajoresModel/findMajoresService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              totals:res.total,
              Mangage:res.rows,
              loading:false
            })
          }else{
            this.setState({
              loading:false
            })
          }
        }
      });
    }
  }
  //分页数据
  pageNumberOnChange = (pageNum,pageSize) => {
    this.state.params.pageNum=pageNum;
    this.state.params.pageSize=pageSize;
    this.getMangageList()
  };
  render() {
    const {Mangage,loading,params,totals}=this.state;
    const columns = [
      {
        width: '30%',
        title: '专业代码',
        dataIndex: 'stdCode',
        key: 'stdCode',
      },
      {
        width: '25%',
        title: '中文名称',
        dataIndex: 'majorName',
        key: 'majorName',
      },
      {
        width: '25%',
        title: '英文名称',
        dataIndex: 'majorNameEn',
        key: 'majorNameEn',
      },
      {
        width: '20%',
        title: '描述',
        dataIndex: 'majorDesc',
        key: 'majorDesc',
      },
    ];

    return (
      <GridContent>
        <div className={styles.imageMagecontent}>
          <Table
            rowKey={Mangage => Mangage.id}
            loading={loading}
            columns={columns}
            dataSource={Mangage}
            pagination={false}
          />
          <Pagination
            style={{ float: 'right',marginTop:16 }}
            total={totals}
            showTotal={(totals) => `共 ${totals} 条记录`}
            defaultPageSize={params.pageSize}
            defaultCurrent={params.pageNum}
            onChange={(pageNum,pageSize) => this.pageNumberOnChange(pageNum,pageSize)}
          />
        </div>
      </GridContent>
    );
  }
}

export default connect(({ findMajoresData, }) => ({
  findMajoresData
}))(MajorManage);
