.CustomerArchivesBox{
  background: #FFFFFF;
  min-height: calc(100vh - 50px);
  padding: 16px;
  .headerSearch{
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-bottom: 16px;

    .screenBox {
      display: flex;
    }

    .screenBoxBtn {
      // margin-right: 15px;
      cursor: pointer;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      color: #4292FF;
      line-height: 20px;

      .ClinicMornMeetFiltrateIcon {
        width: 16px;
        height: 16px;
        background: url('~@/assets/ClinicMornMeet_filtrate_Icon.png');
        background-size: 16px 16px;
        display: inline-block;
      }

      :global {
        .ant-select-single.ant-select-show-arrow .ant-select-selection-item, .ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
          padding-right: 18px;
          font-size: 14px;
          font-weight: 400;
          color: #4292FF;
        }

        .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
          padding: 0 8px;
        }
        .ant-select-arrow {
          display: none;
        }
      }

    }

  }
}

.tagSuccess {
  padding: 0 2px;
  color: #00b42a !important;
  background: rgba(0, 180, 42, 0.1) !important;
  border-color: rgba(0, 180, 42, 0.3) !important;
  border-radius: 4px !important;
}

.tagRed {
  padding: 0 2px;
  color: rgba(245, 63, 63) !important;
  background: rgba(245, 63, 63, 0.1) !important;
  border-color: rgba(245, 63, 63, 0.3) !important;
  border-radius: 4px !important;
}

.tagWarn {
  padding: 0 2px;
  color: rgba(245, 63, 63) !important;
  background: rgba(255, 125, 0, 0.1) !important;
  border-color: rgba(255, 125, 0, 0.3) !important;
  border-radius: 4px !important;
}

.TableWarp {
  :global{
    .ant-table {
      height: 80vh;
    }
    .ant-table-thead>tr>th{
      background:rgba(0,0,0,0.02);
      font-size: 14px;
      color: #3B3E40;
      padding: 10px 16px;
    }
    .ant-table-header table {
      border-radius: 0
    }
    .ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row) > td{
      background: #DFECFE;
    }
    .ant-table-tbody>tr:hover:not(.ant-table-expanded-row)>td {
      background: #DFECFE;
    }
    .ant-table-tbody > tr > td{
      font-size: 12px;
      color: #5E6E82;
      cursor: pointer;
    }
    .ant-popover-inner-content{
      font-size: 12px !important;
      padding: 6px 16px !important;
    }

    .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > thead > tr > th {
      border-right: 0px none;
    }

    .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > td {
      border-right: 0px none;
    }

    .ant-table.ant-table-bordered > .ant-table-container {
      border-left: 0px none;
    }
  }
}

.dotRed {
  width: 8px;
  height: 8px;
  background: #FF3B2E;
  border-radius: 50%;
  display: inline-block;
}

.dotGreen {
  width: 8px;
  height: 8px;
  background: #54BB00;
  border-radius: 50%;
  display: inline-block;
}



