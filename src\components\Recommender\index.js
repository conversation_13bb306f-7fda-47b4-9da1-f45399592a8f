import React, { Component } from 'react';
import styles from './index.less';
import { Modal, Button, Row, Col, Form, Input, Icon, Radio, Spin, message, Avatar, Popover, Badge, Empty } from 'antd';
import { connect } from 'dva';
import InfiniteScroll from 'react-infinite-scroller';
//默认头像
import UserAvatar from '@/components/Avatar/index';
import vip from '@/assets/Repairfees/BvipX.png';
import Ellipsis from '@/components/Ellipsis';
import { screenData } from '@/utils/utils';
import birthday from '@/assets/user/Birthday.png';
import placeholder1 from '@/assets/Nopatient.png';
import phone from '@/assets/registerAndArrival/phone.png';
import TagSelect from '@/components/TagSelect';
import { thisExpression } from '@babel/types';
import DataFailure from '@/components/DataFailure';
import CurrentReferences from '@/assets/registerAndArrival/CurrentReferences.png'
const Search = Input.Search
@Form.create()
@connect(({ registerAndArrival, loading }) => ({
    registerAndArrival,
    loading
}))
class index extends Component {
    constructor() {
        super()
        this.state = {
            pageNum: 1,                         // 请求页数
            searchValue: '',                    // 搜索值
            resultList: [],                     // 推荐人列表
            expandable: false,                  // 点击推荐人Iocn展示样式
            ReferrerRelationship: false,        // 判断推荐人关系是否可操作
            loading: false,                     // 滚动加载条件
            hasMore: true,                      // 滚动加载条件
            RecommenderId: null,                // 选择推荐人id
            relation: '',                        // 推荐人关系关系
            CodeType: null,                     //判断后台是否为500提示图片显示
            recommendId: null,                   //有推荐人的id
            visible: false,                       //推荐人弹窗展示
            Recommendershow: null,                //判断左上角推荐人图标展示
            TemporaryRelationship: null,          //暂存推荐人关系
            RecommendName: '',
        }
    }
    componentDidMount() {
        this.props.onRef(this)
    }
    //有推荐人获取推荐人
    getRecommenderId = () => {
        const { RecommenderId, RecommendName, form, visitRecommendName } = this.props
        const arr = ['空', "配偶", "爸爸", '子女', '兄弟', "爷爷", '孙子女', '朋友', '其他亲属', '姊妹', "妈妈", "奶奶", "姐弟", "兄妹", "其他", '外公', '外婆', '外孙子女'];
        let relationArr = []
        if (visitRecommendName) {
            let index = isNaN(visitRecommendName) && arr.findIndex(item => item == visitRecommendName) > -1 ? arr.findIndex(item => item == visitRecommendName) + '' : visitRecommendName
            relationArr.push(index)
        }
        localStorage.setItem('RecommenderId', RecommenderId)
        this.setState({
            RecommenderId: RecommenderId,
            recommendId: RecommenderId,
            relation: relationArr,
            Recommendershow: RecommenderId,
            TemporaryRelationship: relationArr,
            RecommendName,
        }, () => {
            this.getRecommenderInfo()
        })
    }
    //显示推荐人modal
    showOrHideModal = () => {
        this.setState({
            visible: !this.state.visible
        })
    }
    //获取推荐人列表
    getRecommenderInfo = (type) => {
        const { dispatch, } = this.props
        dispatch({
            type: 'appointment/searchPatient',
            payload: {
                current: this.state.pageNum,
                pageSize: 8,
                search: this.state.searchValue,
                appointment: '6',
                patientId: this.props.patientId,
                recommendId: this.state.RecommenderId ? this.state.RecommenderId : null
            }
        }).then((res) => {
            let code = res.code;
            if (res.code == 200) {
                console.log(res)
                const { content } = res;
                const { resultList } = content ? content : {}
                let resultLists = this.state.resultList
                let RecommenderId = localStorage.getItem('RecommenderId')
                if (content) {
                    if (type) {
                        console.log('555555')
                        this.setState({
                            total: content.total,
                            resultList: resultList,
                            loading: false,
                            RecommenderId: resultList && resultList.findIndex(item => item.patientId == RecommenderId) > -1 ? RecommenderId : null
                        })
                    } else {
                        console.log('89898989898989')
                        this.setState({
                            total: content.total,
                            resultList: resultLists.concat(resultList),
                            loading: false,
                            RecommenderId: resultList && resultLists.concat(resultList).findIndex(item => item.patientId == RecommenderId) > -1 ? RecommenderId : null
                        })
                    }
                } else {
                    this.setState({
                        CodeType: 1,
                        resultList: null,
                        total: 0,
                        loading: false,
                    })
                }
            }
        }).catch(() => {
            this.setState({
                CodeType: 0
            })
            message.error('数据加载失败，请刷新浏览器重试！')
        })
    }
    //取消
    Cancel = () => {
        const { form } = this.props
        form.resetFields('SearchA')
        localStorage.removeItem('RecommenderId')
        this.setState({
            pageNum: 1,                         // 请求页数
            searchValue: '',                    // 搜索值
            resultList: [],                     // 推荐人列表
            expandable: false,                  // 点击推荐人Iocn展示样式
            ReferrerRelationship: false,        // 判断推荐人关系是否可操作
            loading: false,                     // 滚动加载条件
            hasMore: true,                      // 滚动加载条件
            RecommenderId: null,                // 选择推荐人id
            relation: [],                        // 推荐人关系关系
            CodeType: null,
            recommendId: null,
            Recommendershow: null,
            TemporaryRelationship: null,
            RecommendName: '',
        })
        this.showOrHideModal()
        this.props.detailsPopup && this.props.detailsPopup()
    }
    //推荐人搜索
    Search = (searchValue) => {
        let divScroll = document.getElementById('InfiniteScroll')
        divScroll.scrollTop = 0
        this.setState({
            searchValue,
            loading: false,
            hasMore: true,
            pageNum: 1,
            RecommenderId: null,
            ReferrerRelationship: false,
            CodeType: null,
        }, () => {
            this.getRecommenderInfo(1)
        })
    }
    //点击推荐人展示关系
    expandableRelationship = () => {
        this.setState({
            ReferrerRelationship: !this.state.ReferrerRelationship,
            expandable: !this.state.ReferrerRelationship
        })
    }
    //选择推荐人关系
    chengeRelation = (value) => {
        if (value.length >= 2) {
            value.splice(value, 1)
            this.setState({
                relation: value,
            })
        } else {
            this.setState({
                relation: value,
            })
        }
    }
    //选择推荐人卡片
    checkedPatientCard = (id) => {
        const { recommendId, TemporaryRelationship, Recommendershow } = this.state
        localStorage.setItem('RecommenderId', id)
        if (recommendId != id) {
            this.setState({
                recommendId: null,
                relation: [],
            })
        }
        if (id == Recommendershow) {
            this.setState({
                relation: TemporaryRelationship
            })
        }
        this.setState({
            RecommenderId: id
        })
    }
    //推荐人滚动加载
    handleInfiniteOnLoad = () => {
        if (this.state.total == this.state.resultList.length) {
            this.setState({
                loading: false,
                hasMore: false,
            })
            message.warning('加载完成')
            return
        }
        this.setState({
            pageNum: this.state.pageNum + 1,
            loading: true,
            RecommenderId: null,
        }, () => {
            this.getRecommenderInfo()
        })
    }
    //确认选择推荐人
    SearchConfirm = () => {
        const { resultList, RecommenderId, relation, recommendId } = this.state
        const { patientId } = this.props
        let index = resultList.findIndex(item => item.patientId == RecommenderId)
        let referrer = null
        if (index > -1) {
            let obj = {}
            obj.name = resultList[index].name ? resultList[index].name : null;
            obj.tel = resultList[index].oftenTel ? resultList[index].oftenTel : null
            obj.patientCircleId = resultList[index].patientId ? resultList[index].patientId : null
            obj.patientId = patientId ? patientId : null
            obj.circleRel = relation && JSON.stringify(relation) != '[]' ? relation[0] : ''
            referrer = obj
        }
        if (!RecommenderId) {
            message.warning('请选择推荐人')
        } else if (this.props.RecommenderType) {
            const { dispatch } = this.props
            dispatch({
                type: 'registerAndArrival/getAddRecommend',
                payload: {
                    tenantId: localStorage.getItem('tenantId'),
                    organizationId: localStorage.getItem('organizationInfoId'),
                    circleRel: relation && JSON.stringify(relation) != '[]' ? relation[0] : '',
                    patientId: patientId ? patientId : null,
                    patientCircleId: resultList[index].patientId ? resultList[index].patientId : null
                }
            }).then(res => {
                this.Cancel()
                if (res.code == 200) {
                    this.props.notarizeRecommender('1')
                }
            }).catch(err => {
                message.error('数据加载失败，请刷新浏览器重试！')
            })
        } else {
            this.props.notarizeRecommender(referrer)
            this.Cancel()
        }
    }

    render() {
        const { getFieldDecorator, getFieldValue } = this.props.form;
        const { loading } = this.props
        const { resultList, RecommenderId, relation, CodeType, visible, recommendId, Recommendershow, RecommendName } = this.state
        const arr = ['空', "配偶", "爸爸", '子女', '兄弟', "爷爷", '孙子女', '朋友', '其他亲属', '姊妹', "妈妈", "奶奶", "姐弟", "兄妹", "其他", '外公', '外婆', '外孙子女'];
        return (
            <div>
                <Modal
                    visible={visible}
                    zIndex={1001}
                    // style={{ top: 80, right: -100 }}
                    wrapClassName={styles.container}
                    width='700px'
                    closable={false}
                    bodyStyle={{ padding: '20px 20px 20px 0', }}
                    footer={false}
                    destroyOnClose={true}
                    title={
                        <Row type='flex' justify='space-between' align='middle'>
                            <Col>
                                <span>推荐人</span>
                            </Col>
                            <Col>
                                <Button style={{ marginRight: 20, background: '#ccc' }} onClick={this.Cancel}> 取消</Button>
                                <Button type='primary' onClick={this.SearchConfirm} loading={!!loading.effects['registerAndArrival/getAddRecommend']}>确定</Button>
                            </Col>
                        </Row>
                    }
                >
                    <div className={styles.contentContainer}>
                        <Row type='flex' align='middle' className={styles.search} >
                            <Col span={16} style={{ paddingLeft: 20, paddingRight: 12 }}>
                                {
                                    getFieldDecorator('SearchA', {
                                        initialValue: RecommendName,
                                    })
                                        (<Search
                                            placeholder="搜索推荐人"
                                            onSearch={value => this.Search(value)}
                                            autoComplete="off"
                                        />)
                                }
                            </Col>
                            <Col
                                span={8}
                                style={{ color: RecommenderId || recommendId ? 'rgba(75, 162, 255, 1)' : 'rgba(186, 186, 186, 1)', cursor: RecommenderId || recommendId ? 'pointer' : 'null' }}
                                onClick={RecommenderId || recommendId ? this.expandableRelationship : null}
                            >
                                <Icon type={this.state.expandable ? 'up' : 'down'} />&nbsp;&nbsp;添加与推荐人关系
                            </Col>
                        </Row>
                        {
                            <div className={this.state.ReferrerRelationship ? styles.ReferrerRelationship : styles.ReferrerRelationshipHide}>

                                {

                                    < TagSelect onChange={(e) => { this.chengeRelation(e) }} value={relation} hideCheckAll={true}>
                                        推荐人关系：
                                        <TagSelect.Option value="7">朋友 {relation && relation[0] == '7' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="1">配偶 {relation && relation[0] == '1' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="2">爸爸 {relation && relation[0] == '2' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="3">子女 {relation && relation[0] == '3' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="4">兄弟 {relation && relation[0] == '4' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="9">姊妹 {relation && relation[0] == '9' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="5">爷爷 {relation && relation[0] == '5' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="6">孙子女 {relation && relation[0] == '6' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="8">其他亲属 {relation && relation[0] == '8' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="10">妈妈 {relation && relation[0] == '10' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="11">奶奶 {relation && relation[0] == '11' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="12">姐弟 {relation && relation[0] == '12' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="13">兄妹 {relation && relation[0] == '13' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="15">外公 {relation && relation[0] == '15' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="16">外婆 {relation && relation[0] == '16' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="17">外孙子女 {relation && relation[0] == '17' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                        <TagSelect.Option value="14">其他 {relation && relation[0] == '14' ? <Icon type="check-circle" /> : null}</TagSelect.Option>
                                    </TagSelect>
                                }
                            </div>
                        }

                        <Spin spinning={!!loading.effects['appointment/searchPatient']}>
                            <div className={resultList && JSON.stringify(resultList) != '[]' ? styles.referrerList : null} id='InfiniteScroll'>
                                <InfiniteScroll
                                    initialLoad={false}
                                    pageStart={0}
                                    loadMore={this.handleInfiniteOnLoad}
                                    hasMore={!this.state.loading && this.state.hasMore}
                                    useWindow={false}
                                >
                                    <Row>
                                        {
                                            resultList && JSON.stringify(resultList) != '[]' ?
                                                resultList.map((item, index) => {
                                                    return (
                                                        <Col
                                                            span={12}
                                                            key={index}
                                                            className={styles.patientCard}
                                                            onClick={() => { this.checkedPatientCard(item.patientId) }}
                                                        >
                                                            <Row style={{ paddingLeft: 20, paddingTop: 1 }}>
                                                                {item.patientId == Recommendershow && <img src={CurrentReferences} className={styles.CurrentReferences} />}
                                                                <Row type='flex' align='middle' className={item.patientId == RecommenderId || item.patientId == recommendId ? styles.checkedCardContent : styles.CardContent}>
                                                                    <Col className={styles.leftAvatar}>
                                                                        <UserAvatar sex={item.sex} age={item.age} isVip={  item.vipGrade == 1 ? 1 : 0} />
                                                                        {
                                                                            item.fileNumber ?
                                                                                <Popover content={item.fileNumber}>
                                                                                    <p className={styles.fileNumber}>
                                                                                        {item.fileNumber}
                                                                                    </p>
                                                                                </Popover>
                                                                                : null
                                                                        }
                                                                    </Col>
                                                                    <Col className={item.patientId == RecommenderId || item.patientId == recommendId ? styles.rightCheckedContent : styles.rightContent}>
                                                                        {
                                                                            item.name ?
                                                                                <Popover content={item.name}>
                                                                                    <Ellipsis lines={1} className={styles.rightName}>
                                                                                        {item.name}
                                                                                        {RecommenderId && recommendId && item.patientId == recommendId && this.props.visitRecommendName && isNaN(this.props.visitRecommendName) && `(${this.props.visitRecommendName})`}
                                                                                        {RecommenderId && recommendId && item.patientId == recommendId && this.props.visitRecommendName && !isNaN(this.props.visitRecommendName) && `(${arr[this.props.visitRecommendName]})`}
                                                                                    </Ellipsis>
                                                                                </Popover>
                                                                                : null
                                                                        }
                                                                        {
                                                                            item.name || item.sex || item.age ?
                                                                                <Popover content={item.name}>
                                                                                    <Ellipsis lines={1} style={{ marginBottom: 6 }}>
                                                                                        {item.sex == '1' ? '男' : item.sex == '2' ? '女' : '未知'}&nbsp;&nbsp;
                                                                                        {item.age ? screenData(item.age) : null}&nbsp;&nbsp;
                                                                                        {item.birthday}&nbsp;&nbsp;
                                                                                        {item.birthdayTips == '1' ? <img src={birthday} style={{ marginTop: -4 }} /> : null}
                                                                                    </Ellipsis>
                                                                                </Popover>
                                                                                : null
                                                                        }
                                                                        {item.oftenTel ?
                                                                            <Popover content={item.oftenTel}>
                                                                                <Ellipsis lines={1} style={{ marginBottom: 6 }}>
                                                                                    <img src={phone} style={{ width: 13, height: 13 }} />&nbsp;
                                                                                    {item.oftenTel} {item.oftenTelRelation ? `(${item.oftenTelRelation})` : null}
                                                                                </Ellipsis>
                                                                            </Popover> :
                                                                            <Ellipsis lines={1} style={{ marginBottom: 6 }}>
                                                                                <img src={phone} style={{ width: 13, height: 13 }} />
                                                                            </Ellipsis>
                                                                        }
                                                                        {item.visitSource ?
                                                                            <Popover content={item.visitSource}>
                                                                                <Ellipsis lines={1} style={{ marginBottom: 6 }}>
                                                                                    初诊来源：<span style={{ color: '#4BA2FF' }}>{item.visitSource}</span>
                                                                                </Ellipsis>
                                                                            </Popover> :
                                                                            <Ellipsis lines={1} style={{ marginBottom: 6 }}>
                                                                                初诊来源：无
                                                                            </Ellipsis>
                                                                        }
                                                                        {item.getCustomerWay ?
                                                                            <Popover content={<div className={styles.getCustomerWay}>{item.getCustomerWay}</div>}>
                                                                                <Ellipsis lines={1} style={{ marginBottom: 6 }}>
                                                                                    获知方式：<span style={{ color: '#4BA2FF' }}>{item.getCustomerWay}</span>
                                                                                </Ellipsis>
                                                                            </Popover> :
                                                                            <Ellipsis lines={1} style={{ marginBottom: 6 }}>
                                                                                获知方式：无
                                                                            </Ellipsis>
                                                                        }
                                                                    </Col>
                                                                </Row>
                                                            </Row>
                                                        </Col>
                                                    )
                                                })

                                                : CodeType ? <DataFailure
                                                    key={CodeType}
                                                    // width={'100%'}
                                                    margin={'50px 0px 50px 280px'}
                                                    // height={`calc(100vh - 300px)`}
                                                    DataFailMsg={CodeType == 1 ? '暂无数据' : '数据加载失败'}
                                                    CodeType={CodeType}
                                                />
                                                    //  : < Empty image={placeholder1} style={{ marginTop: 80, }} description={<span></span>} />
                                                    : null
                                        }
                                    </Row>
                                </InfiniteScroll>
                            </div>
                        </Spin>
                    </div>
                </Modal>
            </div>
        );
    }
}

export default index;
