import {Input, Button, Table, Form, Space, Modal, message, Popconfirm, Pagination} from 'antd';
import React, {Component} from 'react';
import {GridContent} from '@ant-design/pro-layout';
import styles from './style.less';//样式
import {connect} from "dva";
import {StringUtils} from "@/utils/StringUtils";//公共验证

const {Search} = Input;
const {TextArea} = Input;

class ImageManage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      saveBtnLoading:false, //新增、编辑影像保存按钮是否loading
      ImgTypeStatus: false,//新增/编辑影像类型状态
      ImageList: [],//影像类型列表
      params: {
        tenantId: localStorage.getItem('tenantId'),//租户标识
        className: "",//类型（类型名称）
        pageNum: 1,//当前页码
        pageSize: 10//页码数量
      },
      ImageMsg: {},//影像详情
      loading: false,//加载
      totals: 1,//列表总数
    };
  }
  //初始化
  componentDidMount() {
    this.getImageList()//获取影像类型列表
  }

  //获取影像类型列表
  getImageList = () => {
    const {params} = this.state;
    this.setState({
      loading: true
    })
    const {dispatch} = this.props
    if (dispatch) {
      dispatch({
        type: 'ImageManageModel/ImageManageService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              totals: res.total,
              ImageList: res.rows,
              loading: false
            })
          } else {
            this.setState({
              loading: false
            })
          }
        }
      });
    }
  }
  //搜索事件
  onSearch = (value) => {
    this.state.params.className = StringUtils.trimToEmpty(value);
    this.getImageList()
  };
  //新增弹窗
  openAdd = () => {
    this.setState({
      ImgTypeStatus: true,
      ImageMsg: {}
    });
  };
  //编辑
  openEdit = (record) => {
    // console.log("record==",record)
    this.setState({
      ImgTypeStatus: true,
      ImageMsg: record
    });
  }
  //新增/编辑影像类型保存数据
  handleImgTypeOk = () => {
    const {ImageMsg} = this.state;
    let param;
    if (!ImageMsg.classCode) {
      param = {
        className: ImageMsg.className,//类型（类型名称）
        classDesc: ImageMsg.classDesc,//描述
        tenantId: localStorage.getItem('tenantId'),//租户标识
        createName: localStorage.getItem('userName'),
        createId: localStorage.getItem('userId')
      }
    } else {
      param = {
        id: ImageMsg.id,
        classCode: ImageMsg.classCode,//类型代码
        className: ImageMsg.className,//类型（类型名称）
        classDesc: ImageMsg.classDesc,//描述
        tenantId: localStorage.getItem('tenantId'),//租户标识
        updateName: localStorage.getItem('userName'),
        updateId: localStorage.getItem('userId')
      }
    }
    if (!param.className) {
      message.warning({
        content: '请填写类型',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if(param.className.length>100){
      message.warning({
        content: '类型最多可输入100个字',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return
    }
    if(ImageMsg.classDesc && ImageMsg.classDesc.length>100){
      message.warning({
        content: '描述最多可输入100个字',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return
    }
    const {dispatch} = this.props
    this.setState({
      saveBtnLoading:true
    })
    if (dispatch) {
      dispatch({
        type: 'ImageManageModel/saveCheckClassService',
        payload: param,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              ImgTypeStatus: false,
              saveBtnLoading:false
            })
            this.getImageList()
          } else {
            message.error({
              content: res.msg,
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.setState({
              saveBtnLoading: false
            })
          }
        }
      });
    }
  };
  //新增/编辑影像类型取消事件
  handleImgTypeCancel = () => {
    this.getImageList()
    this.setState({
      ImgTypeStatus: false,
      saveBtnLoading:false,
    });
  };
  onFinish = (values) => {
    // console.log('Success:', values);
  };
  //删除点击事件
  sureDelete = (record) => {
    const {dispatch} = this.props
    let param = {
      id: record.id
    }
    this.setState({
      saveBtnLoading:true,
    })
    if (dispatch) {
      dispatch({
        type: 'ImageManageModel/deleteCheckService',
        payload: param,
        callback: (res) => {
          if (res.code == 200) {
            message.success({
              content: '操作成功',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
            this.getImageList()
          } else {
            message.error({
              content: '操作失败',
              className: 'custom-class',
              style: {
                marginTop: '20vh',
              },
            });
          }
          this.setState({
            saveBtnLoading: false,
          })
        }
      });
    }
  };
  //取消点击事件
  noDelete = () => {
    // console.log('22');
  };
  //分页数据
  pageNumberOnChange = (pageNum, pageSize) => {
    this.state.params.pageNum = pageNum;
    this.state.params.pageSize = pageSize;
    this.getImageList()
  };

  render() {
    const { ImageList, loading, ImageMsg, totals, params,saveBtnLoading} = this.state;
    //表格题
    const columns = [
      {
        width: '40%',
        title: '类型',
        dataIndex: 'className',
        key: 'className',
      },
      {
        width: '45%',
        title: '类型描述',
        dataIndex: 'classDesc',
        key: 'classDesc',
      },
      {
        width: '15%',
        title: '操作',
        key: 'action',
        render: (text, record) => (
          <Space size="middle">
            <a
              onClick={() => {
                this.openEdit(record);
              }}
            >
              编辑
            </a>
            <Popconfirm
              title="确定删除?"
              okButtonProps={{ loading: saveBtnLoading }}
              onConfirm={() => {
                this.sureDelete(record);
              }}
              onCancel={() => {
                this.noDelete;
              }}
              okText="是"
              cancelText="否"
            >
              <a>删除</a>
            </Popconfirm>
          </Space>
        ),
      },
    ];
    return (
      <GridContent>
        <div className={styles.imageMagecontent}>
          <div className={styles.searchBtn}>
            <Search placeholder="搜索" onSearch={this.onSearch} style={{width: 200}}/>
            <Button type="primary" onClick={this.openAdd}>
              新增
            </Button>
          </div>
          <Table
            rowKey={ImageList => ImageList.id}
            columns={columns}
            dataSource={ImageList}
            loading={loading}
            style={{marginTop: 16}}
            pagination={false}
          />
          <Pagination
            style={{float: 'right', marginTop: 16}}
            total={totals}
            showTotal={(totals) => `共 ${totals} 条记录`}
            defaultPageSize={params.pageSize}
            defaultCurrent={params.pageNum}
            onChange={(pageNum, pageSize) => this.pageNumberOnChange(pageNum, pageSize)}
          />
        </div>
        <Modal
          title="新增/编辑影像类型"
          visible={this.state.ImgTypeStatus}
          destroyOnClose={true}
          onOk={this.handleImgTypeOk}
          onCancel={this.handleImgTypeCancel}
          okText="保存"
          cancelText="取消"
          width={600}
          maskClosable={false}
          confirmLoading={saveBtnLoading}
        >
          <Form
            name="basic"
            labelCol={{
              span: 3,
            }}
            wrapperCol={{
              span: 21,
            }}
            initialValues={{
              remember: true,
              classDesc: ImageMsg.classDesc,
              className: ImageMsg.className
            }}
            onFinish={this.onFinish}
            autoComplete="off"
          >
            <Form.Item
              label="类型"
              name="className"
              rules={[
                {
                  required: true,
                  message: '请输入类型',
                },
                {
                  max: 100,
                  message: '最多可输入100个字',
                },
              ]}
            >
              <TextArea
                autoSize={{minRows:1,maxRows:10}}
                onChange={e => {
                  ImageMsg.className = e.target.value
                }}
                placeholder="请输入类型"/>
            </Form.Item>

            <Form.Item
              label="描述"
              name="classDesc"
              rules={[
                {
                  max: 100,
                  message: '最多可输入100个字',
                },
              ]}
            >
              <TextArea
                autoSize={{minRows:4,maxRows:10}}
                onChange={e => {
                  ImageMsg.classDesc = e.target.value
                }}
                placeholder="请输入"/>
            </Form.Item>
          </Form>
        </Modal>
      </GridContent>
    );
  }
}

export default connect(({ImageManageData,}) => ({
  ImageManageData
}))(ImageManage);
