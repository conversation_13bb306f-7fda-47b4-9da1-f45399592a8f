import { findDiags,findDiag, saveDiag, deleteDiag, diagInfo } from '@/services/standard';
import { notification } from 'antd';
//诊断词联想、新增编辑诊断、删除诊断、获取详情

const Model = {
  namespace: 'standard',
  state: {
    findDiags:{},//标准诊断列表
    findDiag: {},//获取筛选类型的类型列表数据
    saveDiag: {},//新增编辑诊断返回结果
    deleteDiag: {},//删除接口返回
    diagInfoData: {},//获取单个标准诊断详情
  },
  effects: {
    //标准诊断列表
    *findDiagsService({ payload, callback }, { call, put }) {
      const response = yield call(findDiags, payload);
      yield put({
        type: 'findDiagsInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },

    /**获取筛选类型的类型列表数据 **/
    *fetchFindDiag({ payload, callback }, { call, put }) {
      const response = yield call(findDiag, payload);
      yield put({
        type: 'getFindDiag',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },

    /**新增编辑
     * 参数：tenantId， diagCode， diagName， id, status **/
    *fetchSaveDiag({ payload, callback }, { call, put }) {
      const response = yield call(saveDiag, payload);
      yield put({
        type: 'getSaveDiag',
        payload: response,
      });
      if (response.code === 200 ||response.code === 500) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },

    /**删除诊断
     * 参数： 诊断标识id,  **/
    *fetchDeleteDiag({ payload, callback }, { call, put }) {
      const response = yield call(deleteDiag, payload);
      yield put({
        type: 'getDeleteDiag',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },

    /**获取详情
     * 参数： id,  **/
    *fetchDiagInfo({ payload, callback }, { call, put }) {
      const response = yield call(diagInfo, payload);
      yield put({
        type: 'getDiagInfo',
        payload: response,
      });
      if (response.code === 200) {
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      } else {
        notification.error({
          message: response.msg,
        });
      }
    },
  },

  reducers: {
    findDiagsInfo(state, action) {
      return {
        ...state,
        findDiags: action.payload || {},
      };
    },
    getFindDiag(state, action) {
      return {
        ...state,
        findDiag: action.payload || {},
      };
    },
    getSaveDiag(state, action) {
      return {
        ...state,
        saveDiag: action.payload || {},
      };
    },
    getDeleteDiag(state, action) {
      return {
        ...state,
        deleteDiag: action.payload || {},
      };
    },
    getDiagInfo(state, action) {
      return {
        ...state,
        diagInfoData: action.payload || {},
      };
    },
  },
};

export default Model;
