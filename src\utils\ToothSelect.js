import React from "react";
import styles from "@/utils/ToothUtils.less";

export const toothUtils = {
  //选中牙位
  selectTooth: function (tooth, toothSelect, key, index, value, suffix) {
    let key1 = suffix;
    let key2 = 0;

    if (suffix === 1) {
      key2 = 2;
    } else {
      key2 = 1;
    }

    if (milkToothToPermanentTooth[value]) {
      let row = tooth[key].indexOf(milkToothToPermanentTooth[value]);
      if (row != -1) {
        toothSelect[key + key2][index] = false;
        toothSelect[key + key1][index] = true;
        tooth[key][row] = value;
      } else {
        toothSelect[key + key1][index] = true;
        tooth[key].push(value);
      }

    } else {
      toothSelect[key + key1][index] = true;
      tooth[key].push(value);
    }

    return {
      tooth, toothSelect
    }
  },
  //取消选中牙位
  noSelectTooth: function (tooth, toothSelect, key, index, value, suffix) {
    toothSelect[key + suffix][index] = false;
    for (let l in tooth[key]) {
      if (tooth[key][l] === value) {
        tooth[key].splice(l, 1);
        return{
          tooth, toothSelect
        }
      }
    }
  },
  //牙位显示
  showTooth: function(e,toothPosition){
    let arr = [];
    let toothArr = toothPosition?toothPosition.split(";"):[];

    for(let t of toothArr){
      if(t[0]===e+""){
        arr.push(t.substring(1,t.length));
      }
    }
    if(e==1||e==4){
      return [].concat(arr)
        .sort((a, b) => toothSort[a[0]] < toothSort[b[0]] ? 1 : -1)
        .map((item,i) =>
          item.length===1?item:item.trim().split(" ").map((d,ti)=>
            ti===0?d:<span key={ti} className={styles.tooth}>{d}</span>
          )
        );
    }else {
      return [].concat(arr)
        .sort((a, b) => toothSort[a[0]] > toothSort[b[0]] ? 1 : -1)
        .map((item,i) =>
          item.length===1?item:item.trim().split(" ").map((d,ti)=>
            ti===0?d:<span key={ti} className={styles.tooth} >{d}</span>
          )
        );
    }
    return arr;
  }
}

//牙位取值顺序标记
export const milkToothToPermanentTooth = {
  "A": "1",
  "B": "2",
  "C": "3",
  "D": "4",
  "E": "5",
  "1": "A",
  "2": "B",
  "3": "C",
  "4": "D",
  "5": "E"
};
//牙位取值顺序标记
export const toothSort = {
  "A": 1,
  "B": 2,
  "C": 3,
  "D": 4,
  "E": 5,
  "1": 1,
  "2": 2,
  "3": 3,
  "4": 4,
  "5": 5,
  "6": 6,
  "7": 7,
  "8": 8
};

