import Menu, { <PERSON>, Col, Tabs, Breadcrumb, Space,Spin,Popconfirm,Button, Modal } from 'antd';
import React, { useState, useRef, Component } from 'react';
import { GridContent } from '@ant-design/pro-layout';
import styles from './style.less';
import VisitToday from '@/pages/MedicalCenter/AllPanel/components/visitToday'; //今日就诊左侧列表
import WriteMedical from './components/WriteMedical'; //写病历页面
import Menus from '@/pages/Menu';
import {connect} from "dva";
import moment from 'moment';
import PatientInfo from "@/components/PatientInfo";//右上方患者详情
import {ExclamationCircleOutlined} from "@ant-design/icons";
import {history} from "umi";
const { confirm } = Modal;


class CaseReport extends Component {
  constructor(props) {
    super(props);
    this.state = {
      listHidden: false, //左侧隐藏显示状态变化
      // isModalVisible: false,
      loading: false,//加载状态
      clientHeight: document.documentElement.clientHeight, // 屏幕高度
      todyWaite: {
    // tenantId: localStorage.getItem('tenantId'),//品牌id
        pageNum: 1, //当前页
        pageSize: 9999, //限制页
        tenantId: localStorage.getItem("tenantId"), //平台标识
        userId: localStorage.getItem("userId"), //当前登录医生标识
        visitStatus:4, // props.location.state.visitStatus, //就诊状态1未到诊、2到诊、3已接诊、4全部有效预约
        organizationId: localStorage.getItem("organizationId"),//机构id
        appointmentDates: [moment().format('YYYY-MM-DD')], //预约日期 date,当list.size=1时查询天数据，当list.size=2时按顺序安排start和end,前后包含
        sortRule: {sort: 2, rule: 2}//排序 正序
      },
      patientParams: {
        organizationId: localStorage.getItem("organizationId"), //机构ID
        patientId: this.props.location.state.patientData?this.props.location.state.patientData.patientId:this.props.location.state.patientId, //患者标识
        tenantId: localStorage.getItem("tenantId"), //平台标识
      },
      rightPatientInfos:{},//患者信息基本数据
      waitTodayData: [], //今日就诊列表
      waitCount: 0, //今日就诊数
      patientInfo:{}, //点击card的所有信息
      patientData:this.props.location.state.patientData,//从上个页面传过来的患者信息
      positions:2//判断是从哪个页面引入的右上方患者信息组件，修饰样式
    };
    this.resize = this.resize.bind(this);//屏幕高度
  }
  //初始化
  componentDidMount() {
    window.addEventListener('resize', this.resize); //监听屏幕高度
    this.getTodayWaitInfo();//今日就诊列表
    this.getTopPatientInfo();// 查询右上方患者信息接口

  }
  // 查询右上方患者信息接口
  getTopPatientInfo = () => {
    const { dispatch } = this.props;
    let params = {
      tenantId: localStorage.getItem("tenantId"), //平台标识
      organizationId: localStorage.getItem("organizationId"), //机构ID
      patientId:this.props.location.state.patientId//患者id
    };
    if (dispatch) {
      dispatch({
        type: 'homePage/rightPatientInfo',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              rightPatientInfos: res.content.patientSignBaseDto.baseInfo,
              patientInfoDto: res.content.patientInfoDto,
            });
            // console.log("hhhhhhhhhhh===",JSON.stringify(this.state.patientInfoDto))
          }
        },
      });
    }
  };
  /**今日就诊列表**/
  getTodayWaitInfo = () => {
    const { dispatch } = this.props;
    this.setState({loading:true})
    let params = this.state.todyWaite;
    if (dispatch) {
      dispatch({
        type: 'homePage/todaySeePending',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              loading:false,
              waitTodayData: res.content.resultList,
              waitCount: res.content.total,
            });
          }
        },
      });
    }
  };
  //在组件卸载及销毁之前直接调用
  componentWillUnmount() {
    window.removeEventListener('resize', this.resize); //取消
  }
  //监听屏幕高度
  resize() {
    this.setState({ clientHeight: document.documentElement.clientHeight }); //监听
  }
  // 获取今日列表点击card的信息
  getCardItem = (info) => {
    // console.log("infoinfo===",info)
    if(info.id!==this.props.location.state.id && info.patientId!==this.props.location.state.patientId){
      this.showConfirm(info)
    }
    // 点击左侧卡片时 tab切换到病历的全部病历 页面中的操作都初始化
    // this.setState({
    //     clickPatientInfo: info,
    //     tabKey: '1',
    //     patientInfo: info,
    //     patientData: info
    //   }
    // );
  };
  //提示框
  showConfirm = (info) => {
    confirm({
      title: '当前页面有未保存数据，是否保存?',
      icon: <ExclamationCircleOutlined/>,
      onOk: () => {
        this.child.TsMedical()
      },
      onCancel: () => {
        history.push({
          pathname:'/emr/MedicalCenter/AllPanel',
          state:{
            patientData: info,
            patientId:info.patientId,
            tenantId: localStorage.getItem("tenantId")
          }
        })
      },
    });
  };
  //左侧隐藏显示状态变化功能事件
  setLeftHidden = () => {
    const { listHidden } = this.state;
    this.setState({
      listHidden: !listHidden,
    });
  };
  //获取子组件写病历页面数据
  bindRef = ref => { this.child = ref }
  render() {
    const { listHidden, waitTodayData, rightPatientInfos, patientInfo, loading, patientData, patientInfoDto
      // , todyWaite
     } = this.state;
    return (
      <GridContent className={styles.allcontentStyle} style={{ height: this.state.clientHeight }}>
        {/*<div style={{ position: 'absolute', top: 0, left: 0 }}>*/}
        {/*  <Menus />*/}
        {/*</div>*/}
        <Row>
          <Col span={listHidden ? 1 : 6} style={{ maxWidth: listHidden ? '1.16667%' : '25%' }}>
            <Space size="middle" className={styles.loading}>
              <Spin spinning={loading}/>
            </Space>
            <VisitToday
              // todyWaite={todyWaite}
              getTodayWaitInfo={this.getTodayWaitInfo}
              appointmentId={this.props.location.state.appointmentId}
              waitTodayData={waitTodayData}
              setHidden={this.setLeftHidden}
              allPanelClick={this.getCardItem}
              patientData={patientData}
              // patientInfo={patientInfo}
              onRef={(ref) => (this.child = ref)}
            />
          </Col>
          <Col span={listHidden ? 23 : 18} style={{ maxWidth: listHidden ? '98.833%' : '75%', flex: 1 }}>
            <div style={{ paddingTop: 16 }}>
              <div style={{ paddingLeft: 16 }}>
                <Breadcrumb>
                  <Breadcrumb.Item>医疗中心</Breadcrumb.Item>
                  <Breadcrumb.Item>患者列表</Breadcrumb.Item>
                  <Breadcrumb.Item>当前患者</Breadcrumb.Item>
                  <Breadcrumb.Item>写病历</Breadcrumb.Item>
                </Breadcrumb>
              </div>
              <div className={styles.topborder}>
                <div style={{ paddingLeft: 16 }}>
                  <PatientInfo
                    position={this.state.positions}
                    patientId={this.props.location.state.patientData?this.props.location.state.patientData.patientId:this.props.location.state.patientId}
                    rightPatientInfos={rightPatientInfos}
                    patientInfos={patientInfoDto}
                    onRef={(ref) => (this.child = ref)}
                  />
                </div>
                <div style={{ marginTop: 16,height: this.state.clientHeight }}>
                  <WriteMedical
                    triggerRef={this.bindRef}
                    inTime={this.props.location.state.inTime}             // 到诊时间
                    currentCure={this.props.location.state.currentCure}   // 预约事项 == 预约主诉 String
                    id={this.props.location.state.id}                     // 预约ID
                    emrSubId={this.props.location.state.emrSubId}         // 小病历号
                    emrId={this.props.location.state.emrId}               // 主病历号
                    emrStatus={this.props.location.state.emrStatus}       // 病历状态 1无病历，2未完成，3已完成，4已归档
                    appointmentDate={this.props.location.state.appointmentDate} // 预约日期
                    name={this.props.location.state.name}                 // 患者姓名
                    isFirstVisit={this.props.location.state.isFirstVisit} // 初诊1/复诊2,
                    patientId={this.props.location.state.patientId}       // 患者ID
                  />
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </GridContent>
    );
  }
}

export default  connect(
  ({findByUserIdAndSearchData,}) => ({
    findByUserIdAndSearchData, //病历首页- 患者信息
  }),
)(CaseReport);
