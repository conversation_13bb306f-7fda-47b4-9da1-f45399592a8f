@import '~antd/es/style/themes/default.less';

html,
body,
#root {
  height: 100%;
  font-size: 16px;
  font-family: PingFang SC;
}

.ant-pro-grid-content {
  padding: 0 2px;
}

.ant-timeline-item-content {
  font-size: 18px !important;
}
.ant-table-thead > tr > th {
  font-weight: 600 !important;
}

.ant-pro-basicLayout-content {
  margin: 0 !important;
  //padding: 24px;
  background-color: #fff !important;
}
.ant-pro-setting-drawer-handle {
  display: none;
}

.ant-card-body {
  padding: 0 !important;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }

  .ant-pro-grid-content {
    background: #fff;
  }
}
//定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸
::-webkit-scrollbar {
  width: 5px;
  height: 10px;
}
//定义滑块 内阴影+圆角
::-webkit-scrollbar-thumb {
  background: rgba(0,0,0,0.2);
  border-radius: 10px;
}
//定义滚动条轨道 内阴影+圆角
::-webkit-scrollbar-track {
  border-radius: 10px;
}
// 兼容IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }

  .ant-pro-grid-content {
    background: #fff;
  }
}

.ant-modal {
  top: 20px !important;
}

.ant-layout-header {
  display: none;
}
@font-face {
  font-family: 'RoboData';
  src: local('RoboData'), url(./fonts/AaHuanLeBao-2.ttf) format('truetype');
}

.ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before{
    font-weight: 600;
  }
.ant-form-item{
  margin-bottom: 12px !important;
}
