import React, { Component } from 'react';
import { connect } from 'dva';
import { Col, message, Row, Spin, Tag,Timeline } from 'antd';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import { divide } from '@/utils/calc';
import styles from './index.less'

const {CheckableTag} = Tag;

@connect(({Getscreen, loading}) => ({
  loading,
  Getscreen,
  ComplaintData: Getscreen.Complaintlist,// 主诉数据
  appointmentCacheShowData: Getscreen.appointmentCacheShowData
}))
class ComplaintRecord extends Component {
  static propTypes = {

  }
  static defaultProps = {

  }
  constructor(props) {
    super(props)
    this.state = {
      ComplaintRecordData:[
        { type:'跟进', time:'2019-01-31 17:40:32', name:'李紫薇', content:'今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者' },
        { type:'跟进跟进跟进跟进跟进', time:'2019-01-31 17:40:32', name:'李紫薇', content:'今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进' },
        { type:'跟进', time:'2019-01-31 17:40:32', name:'李紫薇', content:'今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟进患者今日已打电话跟' }
      ]
    }
  }

  getComputeTypeWidth=(str)=>{
    if(typeof(str)=='string'){
      if(str.length * 20 > 105){ return 105 }
      else if(str.length < 50) { return 50 }
    } return 50
  }

  render() {
    return (
      <div className={styles.TimeLineBox}>
        <Timeline
          mode={'left'}
        >
          {this.state.ComplaintRecordData.map((res,idx)=>{
            if (res) {
              return (
                <Timeline.Item
                  key={`item${idx}`}
                  color={idx == 0? 'blue' : 'gray'}
                >
                  <p>
                    <span
                      style={{left:`-${this.getComputeTypeWidth(res.type)}px`}}
                      className={classNames({
                        [styles.TimeLineLabel]:true,
                        [styles.TimeLineLabelCurrent]:idx == 0,
                      })}
                    >{res.type}</span>
                    <span className={styles.ComplaintTime}>{res.time}</span>
                    <span className={styles.ComplaintName}>{res.name}</span>
                  </p>
                  <p className={styles.ComplaintContent}>{res.content}</p>
                </Timeline.Item>
              )
            }
          })}
        </Timeline>
      </div>
    )
  }
}
export default ComplaintRecord


