import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import styles from './phoneServiceEventElement.less';
import classNames from 'classnames';
import LinesEllipsis from 'react-lines-ellipsis';
import Insurance from '@/assets/RegisterImg/bao.png';
import consultation from '@/assets/RegisterImg/huizhen.png';
import firstVisit from '@/assets/RegisterImg/chu.png';
import Consultant from '@/assets/RegisterImg/huizhenyisheng.png';
import Fu from '@/assets/registerAndArrival/fu.png';
import Jie from '@/assets/customerServiceNew/legend1.png';
import referralIcon from '@/assets/W-img/t5.png';
import Manualicon from './Manualicon';

const time5 = {
  '01:00:00':10,
  '00:30:00':7,
  '00:15:00':5,
}

const time10 = {
  '01:00:00':20,
  '00:30:00':15,
  '00:15:00':10,
}

const time15 = {
  '01:00:00':25,
  '00:30:00':20,
  '00:15:00':15,
}

const time30 = {
  '01:00:00':40,
  '00:30:00':35,
  '00:15:00':30,
}





/**
 * @type 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
 *
 */
export default class EventElement extends Component {

  static propTypes = {
    eventObj:PropTypes.object,
    eventPersonalTimeClose:PropTypes.func,
    slotLabelInterval:PropTypes.string
  }

  static defaultProps = {
    eventObj:{},
    eventPersonalTimeClose:null,
    slotLabelInterval:'01:00:00'
  }

  /**
   * [commonStyles.calendarBy60min]:this.props.Option.slotLabelInterval == '01:00:00',
     [commonStyles.calendarBy30min]:this.props.Option.slotLabelInterval == '00:30:00',
     [commonStyles.calendarBy15min]:this.props.Option.slotLabelInterval == '00:15:00',
   * @param props
   */


  constructor(props) {
    super(props);
    /*this.state = {
      eventObj:props.eventObj
    };*/
  }

  render() {
    const { el,event,view,eventPropsChange } = this.props.eventObj;
    const {
      id,                   // 预约id
      start,                // 预约开始时间
      end,
      extendedProps,
    } = event || {}
    const {
      type,
      titleInfo,
      appointmentInfoOfTableDto,
      consultationInfoOfTableDto // 会诊信息
    } = extendedProps || {}

    // 计算当前预约时长
    let diffMinutes = 0;
    if(start && end){
      let startMoment = moment(start);
      let endMoment = moment(end);
      diffMinutes = endMoment.diff(startMoment,'minutes');
    }

    if(eventPropsChange){
      diffMinutes = 100
    }

    let {
      patientInfoDto,            // 患者信息
      isInsurance,               // 是否使用保险 0不使用 1使用
      complaintList,             // 主诉与治疗
      remark,                    // 预约备注
      doctorNameOfReferral,      // 转诊医生
      doctorNameOfAppointment,   // 当前预约医生
      doctorNameOfConsultation,    // 当前预约的会诊医生
      presentIllness,            // 患者标签
      isConsultation,            // 是否携带会诊 1有 0没有
      isReferral,                // 是否是转诊
      isFirstVisit,              // 是否初复诊 1: 为初诊 2:复诊
      isClean,
      appointmentIconDto,        // 治疗项目页标签
      taskType,                  // 预约确认状态 已完成预约确认1/未完成预约确认0/已联系未确认2/撤销预约确认3
      toDirector,
    } = appointmentInfoOfTableDto||{}
    //isClean = 1


    let {
      basicTreatment,   // 指定客服大客户
      bigCustomer,      // 指定客服大客户
      emergency,        // 急诊
      implantSurgery,   // 种植手术
      implantTwice,     // 种植二期
      lab,              // lab
      majorTreatment,   // 重大治疗
      orthodontic,      // 正畸
      platinumCard,     // 招商银行白金卡
      bankPlatinumCard, // 银行白金卡
      policyHolder,     // MSH保险客户
      repairCrown,      // 修复戴冠
      rootCanal,        // 根管
      teethWhitening,   // 冷光美白
      vipClient,        // 	VIP客户
      hitSupport,        //  打支抗
      toothExtraction,   //  拔牙
      firstOrthodontics, //  正畸初戴
      voucher,
      microscopicRootCanal, // 显微镜根管
      childrenBound,        // 儿童束缚
      toothWhitening,       // 牙齿美白
      regularCustomerVisit,   // 老客户回访
      regularInspection,      // 三个月定检
      comfortTreatment,       // 舒适治疗
      crown,                  // 戴冠
    } = appointmentIconDto || {}




    // 是否有手工标签
    let isIcon = []
    if (comfortTreatment == 1) { isIcon.push('comfortTreatment') }      // 舒适治疗
    if (implantSurgery == 1)   { isIcon.push('implantSurgery')}         // 种植手术
    if (repairCrown == 1)      { isIcon.push('repairCrown') }           // 修复
    if (crown == 1)            { isIcon.push('crown')}                  // 戴冠
    if (teethWhitening == 1)   { isIcon.push('teethWhitening') }        // 冷光美白
    if (emergency == 1)        { isIcon.push('emergency')}              // 急诊
    if (platinumCard == 1)     { isIcon.push('platinumCard')}           // 招商银行白金卡
    if (bankPlatinumCard == 1) { isIcon.push('bankPlatinumCard')}       // 银行白金卡
    if (microscopicRootCanal == 1){ isIcon.push('microscopicRootCanal')}// 显微镜根管
    if (childrenBound == 1){ isIcon.push('childrenBound')}              // 儿童束缚
    if (toothWhitening == 1){ isIcon.push('toothWhitening')}            // 牙齿美白

    if (implantTwice == 1)     { isIcon.push('implantTwice') }          // 种植二期
    if (rootCanal == 1)        { isIcon.push('rootCanal') }             // 根管
    if (orthodontic == 1)      { isIcon.push('orthodontic')}            // 正畸
    //if (bigCustomer == 1)      { isIcon.push('bigCustomer')}
    if (policyHolder == 1)     { isIcon.push('policyHolder')}           // MSH保险客户
    if (lab == 1)              { isIcon.push('lab')}                    // lab
    if (hitSupport == 1)       { isIcon.push('hitSupport')}             // 打支抗
    if (toothExtraction == 1)  { isIcon.push('toothExtraction')}        // 拔牙
    if (firstOrthodontics == 1){ isIcon.push('firstOrthodontics')}      // 正畸初戴
    if (regularCustomerVisit == 1){ isIcon.push('regularCustomerVisit')}// 老客户回访
    if (regularInspection == 1){ isIcon.push('regularInspection')}      // 三个月定检




    // 主诉与治疗信息
    let complaintListTextList = []
      if(Array.isArray(complaintList)){
      complaintList.map((res)=>{
        if(res.treatType == 1 || res.treatType == 2){
          complaintListTextList.push(res.complaintMergeName ? res.complaintMergeName : res.complaintName)
        }else if (res.treatType == 3 || res.treatType == 4 || res.treatType == 5){
          complaintListTextList.push(res.medicalDictionaryName)
        }
      })
    }
    let complaintListText = complaintListTextList.join(',')

    let {
      age,
      sex,
      sexDescribe,
      name,
      patientName,            // 患者名称
      appointmentDate,
      appointmentDateList,
      doctor,
      doctorName,
      //isFirstVisit,     // 是否初复诊 1: 为初诊 2:复诊
      vipGrade,

    } = patientInfoDto || {}

    let sexText = sex == 1 ? '男' : '女'

    let  typeIconList = []
    if (isFirstVisit == 1) { typeIconList.push('firstVisit') }
    if (isFirstVisit == 2) { typeIconList.push('fuVisit')    }
    if (isInsurance == 1)  { typeIconList.push('Insurance')  }
    if (isReferral == 1)   { typeIconList.push('isReferral') }
    if (isClean == 1)      { typeIconList.push('isClean') }
    if (vipGrade == 1)     { typeIconList.push('vipGrade') }






    // 是否左移名称
    let patienBoxMarginleft = false
    if(diffMinutes >= (time10[this.props.slotLabelInterval] + 5) && (basicTreatment == 1 || majorTreatment == 1) && diffMinutes < 45){
      patienBoxMarginleft = true
    }


    /*预约*/
    let appointmentDom = (
      <div id={`event_${id}`}>
        {/*基础 重大
                basicTreatment,   //  基础治疗
                bigCustomer,      //  指定客服大客户
                emergency,        //  急诊
                lab,              //  lab
                majorTreatment,   //  重大治疗
                vipClient,        // 	VIP客户
        */}
        {/*<i className={styles.jichu}></i>*/}
        { diffMinutes >= (time10[this.props.slotLabelInterval] + 5) && basicTreatment == 1 && majorTreatment != 1 && <i className={styles.jichu}></i>}
        { diffMinutes >= (time10[this.props.slotLabelInterval] + 5) && majorTreatment == 1 && <i className={styles.zhongda}></i>}
        { !!(taskType && taskType == 1) &&  <i className={styles.appmentEnter}/> }

        <div className={classNames('eventElent',styles.eventElent)}>
          {/* 5分钟一行会诊显示样例：显示“患者姓名+客户身份标识+初/复诊标识+会诊标识+转诊标识+保险客户标识 */}
          { diffMinutes <= 5 &&
          <div>
            {/*姓名行*/}
            {/*<span className={styles.patientName} >{name}</span>*/}
            <div className={classNames({
              [styles.patienBox]:true,
              [styles.patienBoxMarginleft]:patienBoxMarginleft,
            })}>
              <div>
                {vipGrade == 1 && <i className={classNames({
                  [styles.vipicon]:true,

                })}></i>}
                <div
                  className={classNames({
                    [styles.patientName]:true,
                    [styles.patientNameWidth1]:typeIconList.length == 1,
                    [styles.patientNameWidth2]:typeIconList.length == 2,
                    [styles.patientNameWidth3]:typeIconList.length == 3,
                    [styles.patientNameWidth4]:typeIconList.length >= 4,
                  })}
                >
                  {name}
                </div>
                {/*<LinesEllipsis className={styles.patientName} text={name} maxLine={1} ellipsis='...' >{name}</LinesEllipsis>*/}
              </div>
              <span className={styles.PatienticonRigth}>
                { toDirector == 1 &&  <i className={styles.stayToDirectorWarp}>待接待</i>}
                { toDirector == 3 &&  <i className={styles.alreadyToDirectorWarp}>接待中</i>}
                { isFirstVisit == 1 && <span className={styles.patientEventicon}><img width={18} height={18} src={firstVisit}/></span>}
                { isFirstVisit == 2 && <span className={styles.patientEventicon}><img width={18} height={18}  src={Fu}/></span>}
                { type == 2 && <span className={styles.patientEventicon}><img width={18} height={18}  src={consultation}/></span>}
                { isInsurance == 1 && <span className={styles.patientEventicon}><img width={18} height={18}  src={Insurance}/></span>}
                { /*<span className={classNames(styles.patientEventicon,styles.Diamonds)}><img src={Diamonds}/></span>*/}
                { isReferral == 1 && <span className={styles.patientEventicon}><div className={styles.dingwei5}></div></span>}
                { /*{ isConsultation == 1 && <span className={styles.patientEventicon}><img width={18} height={18}  src={consultation}/></span>}*/}
                { isClean == 1 && <span className={styles.patientEventicon}><img width={18} height={18} src={Jie}/></span>}
              </span>
            </div>
          </div>
          }

          {/* 5分钟一行会诊显示样例：显示“患者姓名+客户身份标识+初/复诊标识+会诊标识+转诊标识+保险客户标识 */}
          {diffMinutes > 5 &&
          <div>
            {/*姓名行*/}
            {/*<span className={styles.patientName} >{name}</span>*/}
            <div className={classNames({
              [styles.patienBox]:true,
              [styles.patienBoxMarginleft]:patienBoxMarginleft,
            })}>
              <div>
                {vipGrade == 1 && <i className={styles.vipicon}></i>}
                <div className={classNames({
                  [styles.patientName]:true,
                  [styles.patientNameWidth1]:typeIconList.length == 1,
                  [styles.patientNameWidth2]:typeIconList.length == 2,
                  [styles.patientNameWidth3]:typeIconList.length == 3,
                  [styles.patientNameWidth4]:typeIconList.length >= 4,
                })}>{name}</div>
                {/*<LinesEllipsis className={styles.patientName} text={name} maxLine={1} ellipsis='...' >{name}</LinesEllipsis>*/}
              </div>
              <span className={styles.PatienticonRigth}>
                { toDirector == 1 &&  <i className={styles.stayToDirectorWarp}>待接待</i>}
                { toDirector == 3 &&  <i className={styles.alreadyToDirectorWarp}>接待中</i>}
                { isFirstVisit == 1 && <span className={styles.patientEventicon}><img width={18} height={18} src={firstVisit}/></span>}
                { isFirstVisit == 2 && <span className={styles.patientEventicon}><img width={18} height={18}  src={Fu}/></span>}
                { type == 2 && <span className={styles.patientEventicon}><img width={18} height={18}  src={consultation}/></span>}
                { isInsurance == 1 && <span className={styles.patientEventicon}><img width={18} height={18}  src={Insurance}/></span>}
                {/*<span className={classNames(styles.patientEventicon,styles.Diamonds)}><img src={Diamonds}/></span>*/}
                { isReferral == 1 && <span className={styles.patientEventicon}><div className={styles.dingwei5}></div></span>}
                {/*{ isConsultation == 1 && <span className={styles.patientEventicon}><img width={18} height={18}  src={consultation}/></span>}*/}
                { isClean == 1 && <span className={styles.patientEventicon}><img width={18} height={18} src={Jie}/></span>}
              </span>
            </div>
          </div>
          }

          {/* 10分钟两行显示样例 第二行显示本次患者预约主诉，显示不全...代替 diffMinutes >= 10 && diffMinutes < 15 &&*/}
          {diffMinutes >= time10[this.props.slotLabelInterval]  && diffMinutes < time15[this.props.slotLabelInterval] &&
          <div>
            {/*主诉行*/}
            <div>
              {isIcon.length != 0 &&
                <div className={styles.cureProjectIcon}>
                  {isIcon.map((valStr,idx)=>{
                    return (<Manualicon key={idx} shortcutField={valStr}/>)
                  })}
                  {/*{ repairCrown == 1     && <Tooltip title="修复戴冠"> <i className={styles.daiguan}></i></Tooltip>}      修复戴冠
                  { teethWhitening == 1  && <Tooltip title="冷光美白"> <i className={styles.meibai}></i></Tooltip>}    冷光美白
                  { implantTwice == 1    &&<Tooltip title="种植二期修复">  <i className={styles.yachixiufu}></i></Tooltip>}  种植二期修复
                  { rootCanal == 1       && <Tooltip title="根管"><i className={styles.yagen}></i></Tooltip>}          根管
                  { orthodontic == 1     && <Tooltip title="正畸"><i className={styles.zhengji}></i></Tooltip>}      正畸
                  { implantSurgery == 1  && <Tooltip title="种植手术"><i className={styles.zhongzhi}></i></Tooltip>}  种植手术
                  { bigCustomer == 1     && <Tooltip title="指定客户"> <i className={styles.zhiding}></i></Tooltip>}      指定客户
                  { platinumCard == 1    && <i className={styles.zhaobaijin}></i>}  招商银行白金卡
                  { policyHolder == 1    && <i className={styles.MSH}></i>}         MSH保险客户
                  { lab == 1             && <i className={styles.LAB}></i>}
                  { emergency == 1       &&<i className={styles.jizhen}></i>}          急诊*/}
                </div>
              }
            </div>
          </div>
          }

          {/*15分钟三行显示样例：第三行显示手工标签，，当本次无手工标签时显示预约备注信息，当既没有预约备注也没有手工标签时不显示*/}
          {diffMinutes >= time15[this.props.slotLabelInterval] && diffMinutes < time30[this.props.slotLabelInterval] &&
          <div>
            {/*备注行 或者 手工标签行*/}
            <div>
                <div className={styles.cureProjectIcon}>
                  {isIcon.map((valStr)=>{
                    return (<Manualicon shortcutField={valStr}/>)
                  })}
                  {/*{repairCrown == 1 && <Tooltip title="修复戴冠"> <i className={styles.daiguan}></i></Tooltip>}      修复戴冠
                  {teethWhitening == 1 && <Tooltip title="冷光美白"> <i className={styles.meibai}></i></Tooltip>}    冷光美白
                  {implantTwice == 1 &&<Tooltip title="种植二期修复">  <i className={styles.yachixiufu}></i></Tooltip>}  种植二期修复
                  {rootCanal == 1 && <Tooltip title="根管"><i className={styles.yagen}></i></Tooltip>}          根管
                  {orthodontic == 1 && <Tooltip title="正畸"><i className={styles.zhengji}></i></Tooltip>}      正畸
                  {implantSurgery == 1 && <Tooltip title="种植手术"><i className={styles.zhongzhi}></i></Tooltip>}  种植手术
                  {bigCustomer == 1 && <Tooltip title="指定客户"> <i className={styles.zhiding}></i></Tooltip>}      指定客户
                  {platinumCard == 1 && <i className={styles.zhaobaijin}></i>}  招商银行白金卡
                  {policyHolder == 1 && <i className={styles.MSH}></i>}         MSH保险客户
                  {lab == 1 && <i className={styles.LAB}></i>}
                  {emergency == 1 &&<i className={styles.jizhen}></i>}          急诊*/}
                </div>
              {/*主诉行*/}
              { complaintListText &&
                <div className={styles.complaintList}>{complaintListText}</div>
              }
            </div>
          </div>
          }


          {/*
          大于三行显示样例：第一行显示“患者姓名+客户身份标识+初/复诊标识+会诊标识+转诊标识+保险客户标识”
                         第二行显示患者本次预约主诉，显示不全...代替
                         第三行显示预约备注信息，当既没有预约备注时显示手工标签，两者都没有时不显示
                         第四行显示手工标签，无手工标签时不显示
          */}
          { diffMinutes >= time30[this.props.slotLabelInterval] &&
          <div>
            <div className={styles.cureProjectIcon}>
              {isIcon.map((valStr)=>{
                return (<Manualicon shortcutField={valStr}/>)
              })}
              {/*{repairCrown == 1 && <Tooltip title="修复戴冠"> <i className={styles.daiguan}></i></Tooltip>}      修复戴冠
              {teethWhitening == 1 && <Tooltip title="冷光美白"> <i className={styles.meibai}></i></Tooltip>}    冷光美白
              {implantTwice == 1 &&<Tooltip title="种植二期修复">  <i className={styles.yachixiufu}></i></Tooltip>}  种植二期修复
              {rootCanal == 1 && <Tooltip title="根管"><i className={styles.yagen}></i></Tooltip>}          根管
              {orthodontic == 1 && <Tooltip title="正畸"><i className={styles.zhengji}></i></Tooltip>}      正畸
              {implantSurgery == 1 && <Tooltip title="种植手术"><i className={styles.zhongzhi}></i></Tooltip>}  种植手术
              {bigCustomer == 1 && <Tooltip title="指定客户"> <i className={styles.zhiding}></i></Tooltip>}      指定客户
              {platinumCard == 1 && <i className={styles.zhaobaijin}></i>}  招商银行白金卡
              {policyHolder == 1 && <i className={styles.MSH}></i>}         MSH保险客户
              {lab == 1 && <i className={styles.LAB}></i>}
              {emergency == 1 &&<i className={styles.jizhen}></i>}          急诊*/}
            </div>
            {/*主诉行*/}
            { complaintListText &&
              <div className={styles.complaintList}>{complaintListText}</div>
            }
            {/*备注行*/}
            {remark &&
              <div>
                {/*<Ellipsis lines={1} className={""}>备注: {remark}</Ellipsis>*/}
                {/* <LinesEllipsis text={remark} maxLine={1} ellipsis='...' />*/}
                <div
                  style={this.getEventRemarkStyle(diffMinutes,complaintListText,isIcon)}
                  className={styles.remark}>{ remark }
                  </div>
              </div>
            }
            {/*转诊行*/}
            {doctorNameOfReferral &&
            <div>
              <img className={styles.referralIcon} src={referralIcon} alt=""/> <span className={""}>{doctorNameOfReferral}医生转</span>
            </div>
            }

          </div>
          }

          {diffMinutes >= 60 &&
          <div>
            {/*会诊医生信息*/}
            {doctorNameOfConsultation &&
            <div>
              <img src={Consultant} alt=""/> <span className={''}>{doctorNameOfConsultation}</span>
            </div>
            }
          </div>
          }
        </div>
      </div>
    )

    let otherHours = (<div></div>)

    /*
     * @type
     * 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
     */
    if(type == 1 ){
      return appointmentDom
    }else if(type == 2) {
      let {
        patientInfoDto,          // 患者信息
        isInsurance,             // 是否使用保险 0不使用 1使用
        complaintList,           // 主诉与治疗
        remark,                  // 预约备注
        doctorNameOfReferral,    // 转诊医生
        doctorNameOfAppointment, // 当前预约医生
        doctorIdOfConsultation,  // 当前预约的会诊医生
        presentIllness,          // 患者标签
        isConsultation,          // 是否携带会诊 1有 0没有
      } = consultationInfoOfTableDto||{}

      let {
        age,
        sex,
        sexDescribe,
        name,
        patientName,            // 患者名称
        appointmentDate,
        appointmentDateList,
        doctor,
        doctorName,
        isFirstVisit,     // 是否初复诊 1: 为初诊 2:复诊
      } = patientInfoDto || {}

      let sexText = sex == 1 ? '男' : '女'

      let meetHours = (
        <div className={classNames('eventElent',styles.eventElent)}>
          {diffMinutes >=5 &&
          <div>
            {/*姓名行*/}
            <LinesEllipsis className={styles.patientName} text={name} maxLine={1} ellipsis='...' >{name}</LinesEllipsis>
            { type == 2 && <span className={styles.huizhenText}>会诊</span>}
          </div>
          }
          { diffMinutes < 5 &&
          <div>
            { type == 2 && <span className={styles.huizhenText}>会诊</span>}
          </div>
          }

          {/*{
            diffMinutes >= 30  &&
            <div>
              年龄行
              <span className={''}>{sexText}</span>
              <span className={"age"}>{screenData(age)}</span>
            </div>
          }*/}
        </div>
      )
      return meetHours
    }else if(type==4){
      // 个人占用时间
      let otherHoursDiv = (
        <div id={`event_${id}`} >
          {/*{this.props.eventPersonalTimeClose && <div className={styles.closeWarp} onClick={(e)=>{
            e.stopPropagation()
            this.props.eventPersonalTimeClose && this.props.eventPersonalTimeClose(event)
          }}><i className={styles.closeIcon}></i></div>}*/}
          <div className={classNames('eventElent',styles.eventElentOther)}>
            <LinesEllipsis text={titleInfo} maxLine={1} ellipsis='...' />
          </div>
        </div>)
      return otherHoursDiv
    }else if(type ==7){
      //走诊
      let otherHoursDiv = (
        <div id={`event_${id}`} className={classNames('eventElent',styles.eventElentOther,styles.evnetOffTextColor)}>
          <LinesEllipsis text={titleInfo} maxLine={1} ellipsis='...' />
        </div>)
      return otherHoursDiv
    } else {
      return otherHours
    }
  }

  getEventRemarkStyle=(diffMinutes,complaintListText,isIcon)=>{
    // 计算占用行数
    let occupyCull = 1; // 名称行
    if (Array.isArray(isIcon) && isIcon.length != 0) { occupyCull++ }
    if(complaintListText){ occupyCull++ }

    let num = 1;
    if(diffMinutes){
      num = Math.floor(diffMinutes / time5[this.props.slotLabelInterval])
      if(num > occupyCull){
        num = num - occupyCull
      }
    }
    return {
      display : '-webkit-box',
      WebkitBoxOrient: 'vertical',
      WebkitLineClamp : num + '',
      overflow: 'hidden',
      wordBreak :'break-all'
    }
  }

}


