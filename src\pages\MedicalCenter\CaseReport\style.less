@import '~antd/es/style/themes/default.less';

.allcontentStyle {
  padding: 0 16px 0 0;
  //margin: 24px;
  border-top: 1px solid #E5E6EB;
  border-bottom: 1px solid #E5E6EB;
  overflow: hidden;
}
:global {
  .ant-tabs-top > .ant-tabs-nav::before,
  .ant-tabs-bottom > .ant-tabs-nav::before,
  .ant-tabs-top > div > .ant-tabs-nav::before,
  .ant-tabs-bottom > div > .ant-tabs-nav::before {
    border-bottom: 1px solid #e5e6eb;
  }
  .ant-tabs > .ant-tabs-nav .ant-tabs-nav-wrap,
  .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-wrap {
    margin-left: 10px;
  }
}
.topborder{
  margin-top: 16px;
}
//加载中
.loading{
  position: absolute;
  top: 45%;
  left: 47%;
  z-index: 888;
}
