@import '~@/utils/utils.less';

@keyframes hoverBtn
{
  0%   {background: #fff;}
  100% {background: #f4f4f9;}
}

@keyframes DownToUp {
  0%   {  transform:rotate(90deg); }
  100% {  transform:rotate(-90deg); }
}

@keyframes UpToDown {
  0%   {  transform:rotate(-90deg);  }
  100% {  transform:rotate(90deg);  }
}

.calendar {
  :global {
    font-size: 14px;
    background: #fff;

    .fc-widget-content:not(.fc-axis .fc-time) {
      cursor:pointer;
    }

    .eventElent  .patientName{
      margin-right: 10px;
    }

    .eventElent .womanIcon {
      margin-right: 7px;
    }

    .eventElent .manIcon {
      margin-right: 7px;
    }

    .eventElent  .age{
      //margin-left: 7px;
    }

    /* .fc th, .fc td {
      border-style: solid;
      border-width: 1px;
      padding: 0;
      vertical-align: top;
    } */

    .fc-time-grid .fc-slats td {
      height: 3.5em;
      //border-color: #dddddd;
    }
    .fc-unthemed tbody {
       //background: #fff;
     }
    .fc-v-event{
      //border: 1px solid #ccd;
      min-height: 16px;
      overflow: unset;
    }



    .fc-view .fc-head-container tr {
      background: #E0E2E7;
      height: 60px;
      line-height: 60px;
      font-family: MicrosoftYaHei;
      font-weight: normal;
      border-bottom: 1px solid #000000;
      font-size: 14px;
      color: #444444;
    }

    .fc-body .fc-axis {
      background: #E0E2E7;
      border-color: #ccc;
    }


    .fc-head .fc-widget-content {
      //background: #fff;
      //border-color: #ddd;
    }

    .fc-unthemed th,
    .fc-unthemed td,
    .fc-unthemed thead,
    .fc-unthemed tbody,
    .fc-unthemed .fc-divider,
    .fc-unthemed .fc-row,
    .fc-unthemed .fc-content,
    .fc-unthemed .fc-popover,
    .fc-unthemed .fc-list-view,
    .fc-unthemed .fc-list-heading td{
      border-color: #ccc;
    }

    .fc-unthemed  .fc-resource-cell {
      background: #ECECF1;
    }

    .eventElent {
      font-size: 14px;
      color: #444444;
      font-family: 'MicrosoftYaHei';
      padding-left: 3%;
      position: absolute;
      width: 100%;
      top: 50%;
      transform: translate(0, -50%);

      p {
        margin-top: 0;
        margin-bottom: 0;
      }

    }

    .doctorName {
      margin-left: 10px;
    }

    .onterEventTile {
      text-align: center;
    }

    .removeIcon {
      position: absolute;
      z-index: 99;
      right: -5px;
      top: -6px;
      width: 14px;
      height: 14px;
      display: inline-block;
      background-image: url('../../../../src/assets/calendar/guanbi.png');
      background-size: 14px 14px;
    }
    .manIcon {
      width: 12px;
      height: 12px;
      display: inline-block;

      background: url('../../../../src/assets/calendar/man.png');
    }
    .womanIcon {
      width: 8px;
      height: 14px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/woman.png');
    }
    .doctorIcon {
      width: 14px;
      height: 18px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/doctorIcon.png')
    }
    .bigDoctorIcon {
      width: 22px;
      height: 28px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/bigDoctorIcon.png')
    }
    .insuranceIcon {
      width: 18px;
      height: 18px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/InsuranceIcon.png');
    }
    .inIcon {
      width: 18px;
      height: 18px;
      display: inline-block;
      vertical-align: middle;
      background: url('../../../../src/assets/calendar/IN.png');
    }
    .goNextIcon {
      width: 22px;
      height: 22px;
      display: inline-block;
      position:relative;
      left:10px;
      vertical-align: text-bottom;
      background: url('../../../../src/assets/calendar/next.png');
      cursor: pointer;
    }

    .goDisableNextIcon {
      width: 22px;
      height: 22px;
      display: inline-block;
      position:relative;
      left:10px;
      vertical-align: text-bottom;
      background: url('../../../../src/assets/calendar/disableNext.png');
    }

    .goLastIcon {
      width: 22px;
      height: 22px;
      position:relative;
      right: 10px;
      display: inline-block;
      vertical-align: text-bottom;
      transform:scaleX(-1);
      background: url('../../../../src/assets/calendar/next.png');
      cursor: pointer;
    }

    .goDisableLastIcon {
      width: 22px;
      height: 22px;
      position:relative;
      right: 10px;
      display: inline-block;
      vertical-align: text-bottom;
      transform:scaleX(-1);
      background: url('../../../../src/assets/calendar/disableNext.png');
    }
    .labelTdsTitle{
      line-height: 0px;
      margin-top: 20px;
      font-weight: 400;
      color: #444444;
    }

    .titleLable {
      line-height:0px;
      margin-top: 20px;
      color: #444444;
      font-weight: 600;
    }

    /*实现样式*/
    .fc-event-container .fc-v-event.eventTypeClass {
      background: #002140;
    }
  }
}

.poppperWarp {
  display: none;
}

.demo {
  width: 100px;
  height: 100px;
  background: #22ff22;
}

.chiarItemWarp {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 25px;
}

.chairIconWarp {
  display: inline-block;
  vertical-align: middle;
  margin-right: 7px;
  margin-top: 2px;
}

.chairIcon {
  width: 21px;
  height: 16px;
  display: inline-block;
  background: url('../../../../src/assets/registerAndArrival/chair.png') no-repeat;
  background-size: 20px 16px;
}

.chiarText {
  font-size: 12px;
  font-weight: 400;
  color: #444444;
  position: relative;
  margin-right: 2px;
}

.chiarState {
  font-size:14px;
  font-family:'MicrosoftYaHei-Bold';
  font-weight:bold;
  color:rgba(68,68,68,1);
  height: 15px;
  line-height: 15px;
}

.redChiarNum {
  color: #E24C4D;
}

.otherUp {
  height:35px;
  background:rgba(255,255,255,1);
  border:1px solid rgba(204,204,204,1);
  box-shadow:0px 6px 8px 0px rgba(0, 0, 0, 0.34);
  position: relative;
  z-index: 19;
  cursor: pointer;
}

.otherUp:hover {
  animation: hoverBtn 0.4s;
  background: #f4f4f9;
}


.otherDown {
  height:35px;
  background:rgba(255,255,255,1);
  border:1px solid rgba(204, 204, 204, 1);
  box-shadow: 0px -6px 8px 0px rgba(0, 0, 0, 0.34);
  position: relative;
  z-index: 19;
  cursor: pointer;
}


.otherDown:hover {
  animation: hoverBtn 0.4s;
  background: #f4f4f9;
}

.otherActive {
  background: #f4f4f9;
}


.otherContent {
  display: inline-block;
  line-height: 35px;
  height:16px;
  font-size:16px;
  font-family:'MicrosoftYaHei-Bold';
  font-weight:bold;
  color:#666666;
  margin-left:20px;
  user-select: none;
}
.redText {
  color: #E24C4D;
}

.doubleDown {
  display: inline-block;
  position: absolute;
  left: 50%;
  top:50%;
  font-size: 19px;
  margin-top: -12px;
  margin-left: -12px;
  //transform:rotate(90deg);
}

.doubleUp {
  display: inline-block;
  position: absolute;
  left: 50%;
  top:50%;
  font-size: 19px;
  margin-top: -12px;
  margin-left: -12px;
  //transform:rotate(-90deg);
}

.doubleDownToUp {
  animation: DownToUp 0.4s;
  transform: rotate(-90deg);
}

.doubleUpToDown {
  animation: UpToDown 0.4s;
  transform: rotate(90deg);
}


.TimeAgendaCalendar {
  position: relative;
  /* height: 100px; */
  background: #F7F7F7;
  /* border: 1px solid #ccc; */
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  // border-left: 1px solid #ccc;
  // border-right: 1px solid #ccc;
  .clearfix()
}

.contentList {
  float: left;
  display: block;
  width: 48px;
  // margin-right: -1px;
}

.contentMiddle {
  float: left;
  display: block;
  width:calc(100% - 77px - 77px);
  height:100%;
  background: #fff;
}

.contentRight {
  float: left;
  display: block;
  width: 76px;
  margin-left: -1px;
}

.titleList {
  float: left;
  display: block;
  width: 48px;
  // height: 5px;
  height: 76px;
  text-align: center;
  position: relative;
  border-bottom: 1px solid #cccccc;
  border-left: 1px solid #cccccc;
}

.titleMiddle {
  float: left;
  display: block;
  width:calc(100% - 76px - 77px);
  min-height:75px;
}

.middleNotRight {
  // width:95%;
  height:75px;
  width:calc(100% - 48px);
  // width:calc(100% - 48px);
}

.titleRight {
  float: left;
  display: block;
  width: 76px;
  height:75px;
  text-align: center;
  position: relative;
}


.doctorIcon {
  width: 16px;
  height: 16px;
  margin-top: 23%;
}
.seatIcon{
  width: 16px;
  height: 16px;
  margin-top: 10%;
}

.doctorNotDataIcon {
  width: 18px;
  height: 22px;
  margin-top: 7px;
}


.TimeAgnedaNotData {
  height: 40px;
  width: 100%;
  text-align: center;
  //padding-top: 10%;
}

.chairFireTime {
  font-size: 12px;
  line-height: 13px;
  font-weight: 400;
}

.DoctorNameTrBy38 {
  height: 38px;
}

.DoctorNameTrBy45 {
  height: 45px;
}

.LineAppointmentWarp {
  width: calc(100% - 2px);
  height: 2px;
  background: transparent;
  display: flex;
  .lineAppintTimeBar {
    width: 49px;
    height: 2px;
    background: transparent;
  }

  .lineAppintByRes {
    flex: 1;
    height: 2px;
    background: transparent;
  }
}

.BoxWarp {
  position: relative;
}

.goToLast {
  float:right;
  width: 16px;
  height: 56px;
  display: inline-block;
  position:absolute;
  left: -15px;
  top: 30%;
  vertical-align: text-bottom;
  background: url('../../../../src/assets/calendar/TimeAgendaCalendar_Left.png');
  background-size: 16px 56px;
  cursor: pointer;
  // transform:scaleX(-1);
  z-index: 20;
}

.goToNext {
  float:left;
  width: 16px;
  height: 56px;
  position:absolute;
  right: -15px;
  top: 30%;
  display: inline-block;
  vertical-align: text-bottom;
  background: url('../../../../src/assets/calendar/TimeAgendaCalendar_Right.png');
  background-size: 16px 56px;
  cursor: pointer;
  z-index:20;
}

