import React, { Component } from 'react';
import { GridContent } from '@ant-design/pro-layout';
import { Row, Col, Form } from 'antd';
//引入样式
import styles from './style.less';
import commonStyles from '@/components/common.less';
//图标
import head from  '@/assets/head.png';
//引入公共验证
import {StringUtils} from "@/utils/StringUtils";
/**form表单控制布局**/
const modalLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 10 },
};

class ModalContent extends Component {
  infoFormRef = React.createRef();
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    const { doctorInfo } = this.props;
    return (
      <GridContent>
        <Row className={styles.borderBottom}>
          <Col className={commonStyles.width100}>
            <img
              className={styles.img}
              src={StringUtils.isNotBlank(doctorInfo.headUrlView)?doctorInfo.headUrlView:head}
            />
          </Col>

          <Row className={`${commonStyles.width100} ${styles.padding20}`}>
            <Form {...modalLayout} className={commonStyles.width100} ref={this.infoFormRef}>
              <Row className={styles.displayFlex}>
                <Col span={12} className={styles.col8}>
                  <Form.Item labelAlign="right" label="姓名">
                    {doctorInfo.userName }
                  </Form.Item>
                </Col>

                <Col span={12} className={styles.col8}>
                  <Form.Item labelAlign="right" label="性别">
                    {doctorInfo.sex}
                  </Form.Item>
                </Col>
              </Row>
              <Row className={styles.displayFlex}>
                <Col span={12} className={styles.col8}>
                  <Form.Item labelAlign="right" label="出生日期">
                    { doctorInfo.birthday}
                  </Form.Item>
                </Col>
                <Col span={12} className={styles.col8}>
                  <Form.Item labelAlign="right" label="电话">
                    {doctorInfo.phone}
                  </Form.Item>
                </Col>
              </Row>
              <Row className={styles.displayFlex}>
                <Col span={12} className={styles.col8}>
                  <Form.Item labelAlign="right" label="邮箱">
                    {doctorInfo.email}
                  </Form.Item>
                </Col>
                {/* <Col span={12} className={styles.col8}>
                  <Form.Item labelAlign="right" label="国籍">
                    {doctorInfo.nationality || '中国'}
                  </Form.Item>
                </Col> */}
              </Row>
            </Form>
            {/* <Row> */}
              <div  style={{  marginTop: '5px', marginLeft: '11.2%' }}>
                <span className={styles.label}>简介：</span>
                <div>{doctorInfo.intro }</div>
              </div>
            {/* </Row> */}
          </Row>
        </Row>
      </GridContent>
    );
  }
}
export default ModalContent;
