
.tableHome .siteBox {
  height: 40px;
  line-height: 40px;
}

.tableHome{
  width: 100%;
  height: auto;
  border-top: 1px solid gainsboro;

}
.tableHome .tableDialg{
  width: 100% !important;
}
.tableHome th{

  padding: 5px !important;

}
.tableHome td{
  padding: 0 !important;
  text-align: center;
}
//table
.tableHome .notesadd{
  display: inline-block;
  width: 100%;
  border-bottom: 1px solid #f3f1f1;
}
.tableHome .notesadd>span:nth-of-type(1) {
  display: inline-block;
  width: 15%;
  text-align: center;
  padding: 4px;
  background-color: #fafafa;
  border-right: 1px solid #f3f1f1;
  line-height: 30px;
}


.tableHome .notes>span:nth-of-type(1) {
  display: inline-block;
  width: 15%;
  text-align: center;
  line-height: 30px;
  background-color: #fafafa;
  border-right: 1px solid #f3f1f1;
  height: 30px;
  margin: 0;
}
.tableHome .notes>input{
  display: inline-block;
  width: 85%;
}
/* 扇形 */
.tableHome .sector{
  margin-left: 20%;
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 100%;
  background-color: gray;
}
.tableHome .sectorchild1{
  position: absolute;
  border: 100px solid transparent;
  width: 0;
  border-radius: 100px;
  border-top-color: white;
  z-index: 0;
}

.tableHome .sectorchild2{
  position: absolute;
  border: 100px solid transparent;
  width: 0;
  border-radius: 100px;
  border-left-color: white;
}
.tableHome .sectorchild3{
  position: absolute;
  border: 100px solid transparent;
  width: 0;
  border-radius: 100px;
  border-right-color: white;
}
.tableHome .sectorchild4{
  position: absolute;
  border: 100px solid transparent;
  width: 0;
  border-radius: 100px;
  border-bottom-color: white;
}
.tableHome .sectorchild5{
  position: absolute;
  top: 57px;
  left: 57px;
  width: 85px;
  height: 85px;
  border: 1px solid  gainsboro;
  z-index: 1;
  border-radius: 100%;
  background-color: white;
}
.tableHome .sector>span:nth-of-type(1){
  position: absolute;
  top: 20px;
  left: 43%;
  z-index: 1;
}
.tableHome .sector>span:nth-of-type(2){
  position: absolute;
  top: 90px;
  left: 75%;
  z-index: 1;
}
.tableHome .sector>span:nth-of-type(3){
  position: absolute;
  top: 90px;
  left: 5%;
  z-index: 1;
}
.tableHome .sector>span:nth-of-type(4){
  position: absolute;
  top: 160px;
  left: 43%;
  z-index: 1;
}
.tableHome .sector>span:nth-of-type(5){
  position: absolute;
  top: 90px;
  left: 43%;
  z-index: 1;
}
.tableHome .hide{
  display: none;
}
.tableHome .red{
  background-color: red;
  color: white;
}
.tableHome .gray{
  background-color: gray;
  color: white;
}
.tableHome .grays{
  text-align: center;
}
//
.tableHome .customSelect {
  :global {
    .ant-tooltip-inner {
      min-width: 495px !important;
      min-height: 32px;
      padding: 0px 0px !important;
      color: #130c0c !important;
      text-align: left;
      text-decoration: none;
      word-wrap: break-word;
      background-color:white !important;
      border-radius: 2px;
      box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
    }
  }
}
// table
.tableHome .tablenav{
  background-color: #fafafa;
  height: 34px;
  line-height: 34px;
}
.tableHome .tablenav>span{
  display: inline-block;
  width: 14.25%;
  height: 34px;
  line-height: 34px;
  text-align: center;
  border-top: 1px solid #f1f1f1;
  border-right: 1px solid #f1f1f1;
  border-bottom: 1px solid #f1f1f1;

}
//table
.tableHome .datasoure{
  height: 34px;
  background: #f3f9fe;
}
.tableHome .datasoure>span{
  display: inline-block;
  width: 14.25%;
  height: 34px;
  line-height: 34px;
  text-align: center;
  border-right: 1px solid #f1f1f1;
  border-bottom: 1px solid #f1f1f1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tableHome .datasoure>:last-child{
  color: #1890ff;
  cursor: pointer;
}
.tableHome .tablenav>div:nth-child(odd) {
  background-color: #fafafa;
}



