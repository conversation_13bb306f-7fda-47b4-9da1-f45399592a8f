
# Ant Design Pro

This project is initialized with [Ant Design Pro](https://pro.ant.design). Follow is the quick guide for how to use.

## Environment Prepare

Install `node_modules`:

```bash
npm install
```

or

```bash
yarn
```

## Provided Scripts

Ant Design Pro provides some useful script to help you quick start and build with web project, code style check and test.

Scripts provided in `package.json`. It's safe to modify or add additional script:

### Start project

```bash
npm start
```

### Build project

```bash 
默认：npm run build
```

### Check code style

```bash
npm run lint
```

You can also use script to auto fix some lint error:

```bash
npm run lint:fix
```

### Test code

```bash
npm test
```

## More

You can view full document on our [official website](https://pro.ant.design). And welcome any feedback in our [github](https://github.com/ant-design/ant-design-pro).



├─public #公共资源文件
│ ├─assets #口腔检查中使用的牙齿，牙齿对应治疗项的图片，及牙齿鼠标悬浮图片高亮等图像
│ ├─favicon.png #浏览器显示的icon图标
│ ├─home_bg.png #登录页背景图
│ ├─pro_icon.png #系统logo
└─src #项目源码
 ├─assets #图片库 页面中用到的图标，如病历状态，暂无数据等
 ├─component #组建库
 │ ├─Authorized #antd框架权限验证相关---开发环境需要
 │ ├─CdnImgs #图片显示
 │ ├─ImageCard #图片转换
 │ ├─InforConsent #关联知情同意书
 │ ├─LookPopupCommon #一般检查预览
 │ │ ├─editTooth #编辑牙位
 │ │ index.jsx
 │ │ index.less
 │ ├─LookPopupPeriodontal #牙周检查预览
 │ │ index.jsx
 │ │ index.less 
 │ ├─MedicalRecord #病历
 │ │ index.jsx
 │ │ index.less
 │ ├─PageLoading #页面加载中
 │ │ index.jsx
 │ ├─PatientInfo #今日就诊-患者头部信息
 │ │ index.jsx
 │ │ index.less
 │ ├─PopupCommon # 牙齿组件
 │ │ ├─components 
 │ │ │ ├─dentalFaceDialog #牙面选择
 │ │ │ ├─editableTable #口腔检查
 │ │ │ ├─editDialg #编辑口腔牙位
 │ │ │ ├─editTooth #编辑牙位
 │ │ index.jsx
 │ │ index.less
 │ ├─PopupPeriodontal #新建牙周检查
 │ │ index.jsx
 │ │ style.less
 │ ├─PrintContent #打印病历预览
 │ │ index.jsx
 │ │ style.less
 │ ├─Screenage #关联影像
 │ │ index.jsx
 │ │ style.less
 │ ├─SystemSetupMenu #关联步骤菜单
 │ │ index.jsx
 │ ├─ToothSelect #牙位选择
 │ │ index.jsx
 │ │ style.less 
 │ ├─ToothShow #牙位展示
 │ │ index.jsx
 │ │ style.less
 │ │ common.less #常用样式
 ├─layouts #框架布局
 │   │ BasicLayout.jsx
 │   │ BlankLayout.jsx
 │   │ SecurityLayout.jsx
 │   │ UserLayout.jsx
 │   └─UserLayout.less
 ├─pages #页面目录
 │ ├─ArriveToday #今日到诊
 │ │ │ ├─components
 │ │ │ │ ├─HistoryPatient #历史患者
 │ │ │ │ │ index.jsx
 │ │ │ │ ├─TodaySeeDoctor #今日到诊医生
 │ │ │ │ │ index.jsx
 │ │ │ │   style.less
 │ │ │ ├─index.jsx #今日到诊
 │ │ │ └─style.less #今日到诊样式
 │ ├─ContentCenter #内容下载中心
 │ │ ├─BasicTreatment #历史患者
 │ │ │ │ index.jsx
 │ │ │ │ style.less
 │ │ ├─components #公共组件
 │ │ │ └─complexMenu #菜单目录
 │ │ │   index.js
 │ │ ├─DownLoad #内容下载
 │ │ │ │ index.jsx
 │ │ │ │ style.less
 │ │ ├─Entry #词条
 │ │ ├─components
 │ │ │ │ ├─Auxiliary # 辅助检查
 │ │ │ │ │ ├─index.jsx
 │ │ │ │ │ └─style.less
 │ │ │ │ ├─Dispose   #处置
 │ │ │ │ │ ├─index.jsx
 │ │ │ │ │ └─style.less
 │ │ │ │ ├─Examine #检查
 │ │ │ │ │ ├─index.jsx
 │ │ │ │ │ └─style.less
 │ │ │ │ ├─HistoryPresent #既往史
 │ │ │ │ │ ├─index.jsx
 │ │ │ │ │ └─style.less
 │ │ │ │ ├─MainSuit #主诉
 │ │ │ │ │ ├─index.jsx
 │ │ │ │ │ └─style.less
 │ │ │ │ ├─NowPresent #现病史
 │ │ │ │ │ ├─index.jsx
 │ │ │ │ │ └─style.less
 │ │ │ │ └─WholeBody #全身状况
 │ │ │ │   ├─index.jsx
 │ │ │ │   └─style.less
 │ │ │ ├─index.jsx
 │ │ │ └─style.less
 │ │ ├─InforConsent #关联知情同意书
 │ │ │ ├─components #组件
 │ │ │ │ ├─editor #CT
 │ │ │ │ │ ├─index.jsx
 │ │ │ │ │ └─style.less
 │ │ │ │ │ index.js
 │ │ │ │ └─style.less
 │ │ ├─MedicalModel #病历模板管理
 │ │ │ │ index.jsx
 │ │ │ │ style.less
 │ │ ├─Standard #内容管理-标准诊断词条
 │ │ │ │ index.jsx
 │ │ │ │ style.less
 │ │ │ ├─index.jsx #内容管理
 │ │ │ └─style.less #内容管理样式
 │ ├─DoctorManage #医生管理
 │ │ ├─components #公共组件
 │ │ │ ├─complexMenu #菜单目录
 │ │ │ │ index.jsx
 │ │ │ ├─ModalContent # 病历编辑、详情中的上半部分
 │ │ │ │ index.jsx
 │ │ │ └─style.less 
 │ │ ├─EleSignature #
 │ │ │ │ index.jsx
 │ │ │ │ style.less
 │ │ ├─MedicalRecordAuthority #病历权限
 │ │ │ │ index.jsx
 │ │ │ └─style.less
 │ │ │   index.js #医生管理
 │ │ │   style.js #医生管理样式
 │ ├─exception #访问页
 │ │ ├─404  #访问的页面不存在
 │ │ ├─403  #无权访问该页面
 │ │ └─500  #服务器出错
 ├─loading #中间页
 │ ├─index.jsx
 │ └─style.less
 ├─MedicalCenter #病历管理
 │ ├─AllPanel  #完整病历
 │ │ ├─components
 │ │ │ │ ├─ImageData # 影像资料
 │ │ │ │ │ ├─index.jsx
 │ │ │ │ │ ├─style.less
 │ │ │ │ │ ├─TimeRecord.jsx #按时间显示
 │ │ │ │ │ └─TypeRecord.jsx #按类型显示
 │ │ │ │ ├─medicalRecord  #病历记录
 │ │ │ │ │ ├─AllPatientRecord.jsx #全部病历
 │ │ │ │ │ ├─index.jsx # 病历记录
 │ │ │ │ │ ├─MedicalRecordHomePage.jsx #病历首页
 │ │ │ │ │ └─style.less # 病历记录样式
 │ │ │ │ ├─visitToday #今日就诊左侧列表
 │ │ │ │ │ ├─index.jsx
 │ │ │ │ │ └─style.less
 │ │ │ ├─index.jsx  
 │ │ │ └─style.less
 │ ├─CaseReport  #病历首页
 │ │ ├─components
 │ │ │ ├─WriteMedical # 写病历
 │ │ │ │ ├─components
 │ │ │ │ │ ├─TemplateList #病历模板,同意书模板切换页
 │ │ │ │ │ ├─index.jsx
 │ │ │ │ │ └─style.less
 │ │ │ │ ├─index.jsx
 │ │ │ │ └─style.less  # 写病历样式
 │ │ │ ├─index.jsx
 │ │ │ └─style.less  #病历首页样式
 │ ├─Menu #菜单
 │ │  ├─index.jsx 
 │ │  └─style.less
 │ ├─SystemSetup #基础字典设置
 │ │ ├─ImageManage  #影像类型管理
 │ │ │  ├─index.jsx 
 │ │ │  └─style.less
 │ │ ├─MajorManage  #专业字典管理
 │ │ │  ├─index.jsx
 │ │ │  └─style.less
 │ │ │   index.jsx
 │ │ │ └─style.less
 │ │ ├─user #登录相关  开发环境使用必须保留
 │ │ │ └─login 
 │ │ │   ├─index.jsx
 │ │ │   └─index.less
 │ │ ├─404.jsx #框架自带
 │ │ ├─Admin.jsx #框架自带
 │ │ ├─Authorized.jsx #框架自带
 │ │ ├─document.ejs #框架自带
 │ │ ├─Welcome.jsx #框架自带
 │ │ └─Welcome.less #框架自带
 │ ├─services #接口
 │ │ ├─BasicTreatment.js # 基础治疗
 │ │ ├─contentSyn.js #同步诊断 词条 书写模板等
 │ │ ├─doctorManage.js #医生管理
 │ │ ├─entryManage.js #词条
 │ │ ├─homePage.js #首页
 │ │ ├─ImageManage.js #影像
 │ │ ├─InforConsent.js #知情同意书
 │ │ ├─login.js #登录---开发环境需要
 │ │ ├─MajorManage.js #查询专业列表
 │ │ ├─medicalModel.js #病历模版
 │ │ ├─public.js #文件上传等公用
 │ │ ├─standard.js #内容管理-标准诊断词条等
 │ │ ├─todayVisit.js #今日就诊
 │ │ └─writeMedical.js #写病历
 │ ├─utils
 │ │ ├─arrailUrl.js #配置文件
 │ │ ├─authority.js #antd 自带权限
 │ │ ├─Authorized.js #antd 自带权限
 │ │ ├─common.js #初诊状态 历史操作记录类型 编辑中状态
 │ │ ├─passport.js #引用AES源码js
 │ │ ├─request.js #请求
 │ │ ├─StringUtils.js #验证字符，空格等
 │ │ ├─toothUtils.js #牙位js文件
 │ │ ├─ToothSelect.js #牙位选择js文件
 │ │ ├─ToothUtils.less #牙位样式
 │ │ ├─utils.js #官方工具
 │ │ ├─utils.less #官方工具
 │ │ └─utils.test.js #官方工具
 │ └─models
 │ │ ├─BasicTreatment.js # 基础治疗
 │ │ ├─contentSyn.js #同步诊断 词条 书写模板等
 │ │ ├─doctorManage.js #医生管理
 │ │ ├─entryManage.js #词条
 │ │ ├─homePage.js #首页
 │ │ ├─ImageManage.js #影像
 │ │ ├─InforConsent.js #知情同意书
 │ │ ├─login.js #登录---开发环境需要
 │ │ ├─MajorManage.js #查询专业列表
 │ │ ├─medicalModel.js #病历模版
 │ │ ├─public.js #文件上传等公用
 │ │ ├─standard.js #内容管理-标准诊断词条等
 │ │ ├─todayVisit.js #今日就诊
 │ │ └─writeMedical.js #写病历
 └─config 
   ├─config.js  #配置文件路由
   ├─defaultSettings.js  #默认设置
   ├─proxy.js  #生产环境
   └─routes.js  #路由


package.json: 没有用到：no  用到：yes
1.reqwest: no
2.react-router-dom : no
3.react-webworker: no
4.react-fittext: yes -适合文本插件 第三方依赖
5.react-helmet-async :yes  -antd pro 自带
6.react-router : yes  config.js中存在路由 
7.react-beautiful-dnd : yes  -拖拽功能
8.react-umeditor : yes  -编辑功能
9.base-64 : yes  -Base64编码转换工具
10.bizcharts : yes  -一款基 于antv 的 G2 进行 react 封装的组件,是以数据为驱动的可视化图表


拆包分包：拆包使用 umi+webpack，具体配置请见 config/config.js。运行 npm run build 打包时执行拆包相关操作 
1.umi：umi 框架相关必须引入，否则运行时白屏 
2.react：react 依赖 
3.react-dom：react-dom 相关 
4.react-router： react 路由相关 
5.antdesigns：antd 脚手架相关 
6.vendors：第三方依赖库 
7.react_module：其余 react 相关
注：将 react 等依赖包上传到 CDN 后，需修改 index.html 页面中的引入路径


