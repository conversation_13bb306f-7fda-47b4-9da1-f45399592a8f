@import '~antd/lib/style/themes/default.less';

@nav-header-height: 50px;

.logo {
  height: @nav-header-height;
  position: relative;
  line-height: @nav-header-height;
  padding-left: (@menu-collapsed-width - 32px) / 2;
  transition: all 0.3s;
  background: #002140;
  overflow: hidden;
  img {
    display: inline-block;
    vertical-align: middle;
    height: 32px;
  }
  h1 {
    color: white;
    display: inline-block;
    vertical-align: middle;
    font-size: 20px;
    margin: 0 0 0 12px;
    font-family: Avenir, Arial, Helvetica, sans-serif;
    font-weight: 600;
  }
}

.sider {
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  position: relative;
  z-index: 10;
  &.fixSiderbar {
    position: fixed;
    top: 0;
    left: 0;
    :global(.ant-menu-root) {
      overflow-y: auto;
      height: ~'calc(100vh - @{nav-header-height})';
    }
  }
  &.light {
    box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
    background-color: white;
    .logo {
      background: white;
      box-shadow: 1px 1px 0 0 @border-color-split;
      h1 {
        color: @primary-color;
      }
    }
    :global(.ant-menu-light) {
      border-right-color: transparent;
    }
  }
}

.icon {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

:global {
  .top-nav-menu li.ant-menu-item {
    height: @nav-header-height;
    line-height: @nav-header-height;
  }
  .drawer .drawer-content {
    background: #001529;
  }
  .ant-menu-inline-collapsed {
    & > .ant-menu-item .sider-menu-item-img + span,
    &
      > .ant-menu-item-group
      > .ant-menu-item-group-list
      > .ant-menu-item
      .sider-menu-item-img
      + span,
    & > .ant-menu-submenu > .ant-menu-submenu-title .sider-menu-item-img + span {
      max-width: 0;
      display: inline-block;
      opacity: 0;
    }
  }
  .ant-menu-item .sider-menu-item-img + span,
  .ant-menu-submenu-title .sider-menu-item-img + span {
    transition: opacity 0.3s @ease-in-out, width 0.3s @ease-in-out;
    opacity: 1;
  }
  .ant-layout-sider{
    background-color: rgba(0,0,0,0);
  }
}
.box{
  :global{
    .ant-drawer-content-wrapper{
        width: 650px !important;
        height: auto;
        .antd-pro\\components\\-sider-menu\\index-sider{
          max-width: 100% !important;
          width: 100% !important;
          min-width: 100% !important;
        }
        .ant-drawer-content{
          background:rgba(0,0,0,0);
        }
      }
    .anticon{
      vertical-align: -0.45em;
      margin-right:10px;
    }
    .anticon-Home{
        width:32px;
        height:30px;
        background:url("../../assets/menu/Home.png") no-repeat center;
        background-size: 32px 30px;
    }
    .anticon-Subscribe{
      width:30px;
      height:30px;
      background:url("../../assets/menu/Subscribe.png") no-repeat center;
      background-size: 30px 30px;
    }
    .anticon-Symptoms{
      width:32px;
      height:27px;
      background:url("../../assets/menu/Symptoms.png") no-repeat center;
      background-size: 32px 27px;
    }
    .anticon-TaskHistory{
      width:27px;
      height:26px;
      background:url("../../assets/menu/TaskHistory.png") no-repeat center;
      background-size: 27px 26px;
    }
    .anticon-Custom{
      width:29px;
      height:29px;
      background:url("../../assets/menu/Custom.png") no-repeat center;
      background-size: 29px 29px;
    }
    .anticon-Defcustom{
      width:34px;
      height:28px;
      background:url("../../assets/menu/Defcustom.png") no-repeat center;
      background-size: 34px 28px;
    }
    .anticon-Settlement{
      width:27px;
      height:30px;
      background:url("../../assets/menu/Settlement.png") no-repeat center;
      background-size: 27px 30px;
    }
    .anticon-DailyJob{
      width:28px;
      height:28px;
      background:url("../../assets/menu/DailyJob.png") no-repeat center;
      background-size: 28px 28px;
    }
    .anticon-Customerfile{
      width:28px;
      height:29px;
      background:url("../../assets/menu/Customerfile.png") no-repeat center;
      background-size: 28px 29px;
    }
    .anticon-Setting{
      width:30px;
      height:30px;
      background:url("../../assets/menu/Setting.png") no-repeat center;
      background-size: 30px 30px;
    }
    .anticon-ResourceModel{
      width:25px;
      height:26px;
      background:url("../../assets/menu/ResourceModel.png") no-repeat center;
      background-size: 25px 26px;
    }
    .anticon-Scheduling{
      width:27px;
      height:27px;
      background:url("../../assets/menu/Scheduling.png") no-repeat center;
      background-size: 27px 27px;
    }
    .anticon-Nurses{
       width:28px;
       height:28px;
       background:url("../../assets/menu/Nurses.png") no-repeat center;
       background-size: 28px 28px;
     }
    .anticon-Gestational{
      width:28px;
      height:27px;
      background:url("../../assets/menu/Gestational.png") no-repeat center;
      background-size: 28px 27px;
    }
    .anticon-Pharmacy{
      width:20px;
      height:27px;
      background:url("../../assets/menu/Pharmacy.png") no-repeat center;
      background-size: 20px 27px;
    }
    .anticon-Paygopulya{
      width:24px;
      height:31px;
      background:url("../../assets/menu/Paygopulya.png") no-repeat center;
      background-size: 24px 31px;
    }
    .anticon-Inspection{
      width:30px;
      height:28px;
      background:url("../../assets/menu/Inspection.png") no-repeat center;
      background-size: 30px 28px;
    }
    .anticon-TaskStatus{
      width:27px;
      height:27px;
      background:url("../../assets/menu/TaskStatus.png") no-repeat center;
      background-size: 27px 27px;
    }
    .anticon-Repairfees{
      width:27px;
      height:27px;
      background:url("../../assets/menu/Repairfees.png") no-repeat center;
      background-size: 27px 27px;
    }
  }
}
.parent{
  display: flex;
  justify-content: start;
  flex-wrap: wrap;
  background-color: rgba(13,33,54,0.8);
  .menu{
    text-align: center;
    line-height: 100px;
    width: 33.3%;
    margin-bottom: 10px;
    color: #ffffff;
    &>a{
      display: inline-block;
      padding: 0 5%;
      background-color: #4ba2ff;
      width: 95%;
      height: 100%;
      color: #ffffff;
      text-decoration: none;
      font-size: 16px;
     .icon{
        margin-right: 5px;
      }
    }
  }
  .header{
    text-align: center;
    line-height: 100px;
    width: 100%;
    background-color: #4ba2ff;
    margin-bottom: 10px;
    color: #ffffff;
    height: 100px;
    font-size: 16px;
    &>a{
      display: inline-block;
      width: 100%;
      height: 100%;
      color: #ffffff;
      text-decoration: none;
      font-size: 16px;
     .icon{
        margin-right: 5px;
      }
    }
  }
}
.menu{
  width: 70px;
  background-color: #34343C;
  border: none;
  :global{
    .ant-menu-item{
      padding: 0 !important;
      text-align: center;
      margin: 0;
      height: 70px;
      width: 70px;
      line-height: 70px;
    }
    .ant-menu-item-selected{
      background-color: #EE6E52 !important;
    }
    .ant-menu-item::after{
      border-right:none;
    }
  }
  .icon{
    height: 28px;
    width: auto;
    margin: 0 auto;
  }
}

