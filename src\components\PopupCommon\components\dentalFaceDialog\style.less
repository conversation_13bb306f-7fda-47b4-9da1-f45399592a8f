@import '~antd/es/style/themes/default.less';
.big_circle{
  border: 1px solid #D9D9D9;
  border-radius: 50%;
  position: relative;
  width: 360px;
  height: 360px;
  margin: auto;

  .circle{
    position: absolute;
    top: 50%;
    z-index: 9999;
    left:50%;
    transform: translate(-50%,-50%);
    border: 1px solid #D9D9D9;
    width: 125px;
    height: 125px;
    border-radius: 50%;
    text-align: center;
    line-height: 117px;
    background-color: #ffffff;
  }
  .circle_hover{
    position: absolute;
    top: 50%;
    z-index: 9999;
    left:50%;
    transform: translate(-50%,-50%);
    border: 1px solid #D9D9D9;
    width: 125px;
    height: 125px;
    border-radius: 50%;
    text-align: center;
    line-height: 117px;
    background-color: #F5F5F5;
  }
  .fan_j{
    position: absolute;
    top: -39px;
    z-index: 9999;
    left: 25%;
    //transform: translate(-50%);
    border: 1px solid #D9D9D9;
    width: 177px;
    height: 177px;
    border-radius: 183px 0 0 0;
    /* background-color: red; */
    transform: rotate(-315deg);
    div{
      transform: rotateZ(-46deg);
      top: 47%;
      left: 45%;
      position: absolute;
    }
  }
  .fan_j:hover,.fan_s:hover,
  .fan_jz:hover,.fan_y:hover,
  .circle:hover{
    background-color: #F5F5F5;
  }
  .fan_j_hover{
    position: absolute;
    top: -39px;
    z-index: 9999;
    left: 25%;
    //transform: translate(-50%);
    border: 1px solid #D9D9D9;
    width: 177px;
    height: 177px;
    border-radius: 183px 0 0 0;
    background-color: #F5F5F5;
    /* background-color: red; */
    transform: rotate(-315deg);
    div{
      transform: rotateZ(-46deg);
      top: 47%;
      left: 45%;
      position: absolute;
    }
  }
  .fan_s{
    position: absolute;
    bottom: -37px;
    z-index: 90;
    left: 24.2%;
    border: 1px solid #D9D9D9;
    width: 183px;
    height: 180px;
    border-radius: 0px 0 192px 0;
    transform: rotate(-315deg);
    div{
      transform: rotateZ(-46deg);
      top: 39%;
      left: 39%;
      position: absolute;
    }
  }
  .fan_s_hover{
    position: absolute;
    bottom: -37px;
    z-index: 90;
    left: 24.2%;
    border: 1px solid #D9D9D9;
    width: 183px;
    height: 180px;
    border-radius: 0px 0 192px 0;
    transform: rotate(-315deg);
    background-color: #F5F5F5;
    div{
      transform: rotateZ(-46deg);
      top: 39%;
      left: 39%;
      position: absolute;
    }
  }
  .fan_jz{
    position: absolute;
    top: 24.5%;
    z-index: 85;
    right: -39.3px;
    border: 1px solid #D9D9D9;
    width: 185px;
    height: 181px;
    border-radius: 0px 189px 0 0;
    transform: rotate(-315deg);
    div{
      transform: rotateZ(-46deg);
      top: 41%;
      left: 39%;
      position: absolute;
    }
  }
  .fan_jz_hover{
    position: absolute;
    top: 24.5%;
    z-index: 85;
    right: -39.3px;
    border: 1px solid #D9D9D9;
    width: 185px;
    height: 181px;
    border-radius: 0px 189px 0 0;
    transform: rotate(-315deg);
    background-color: #F5F5F5;
    div{
      transform: rotateZ(-46deg);
      top: 41%;
      left: 39%;
      position: absolute;
    }
  }
  .fan_y{
    position: absolute;
    top: 24%;
    z-index: 9999;
    left: -36px;
    //transform: translateY(-50%);
    border: 1px solid #D9D9D9;
    width: 178px;
    height: 182px;
    border-radius: 0px 0px 0 192px;
    transform: rotate(-315deg);
    div{
      transform: rotateZ(-46deg);
      top: 41%;
      left: 39%;
      position: absolute;
    }
  }
  .fan_y_hover{
    position: absolute;
    top: 24%;
    z-index: 9999;
    left: -36px;
    //transform: translateY(-50%);
    border: 1px solid #D9D9D9;
    width: 178px;
    height: 182px;
    border-radius: 0px 0px 0 192px;
    transform: rotate(-315deg);
    background-color: #F5F5F5;
    div{
      transform: rotateZ(-46deg);
      top: 41%;
      left: 39%;
      position: absolute;
    }
  }
}
































