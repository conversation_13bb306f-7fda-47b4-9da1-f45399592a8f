@import '~antd/lib/style/themes/default.less';
.imageTypecontent{
  :global{
    .ant-menu-item{
      text-align: center;
      box-shadow: 0 1px 0 0 #EEE;
      margin-bottom: 0 !important;
      margin-top: 0 !important;
      height: 48px;
      line-height: 48px;
    }
    .ant-menu-item-selected{
      border-left: 4px solid #4292FF;
    }
  }
}
.header_pop{
  :global{
    .ant-card-body{
      cursor: pointer;
    }
    .ant-modal-body{
      background-color: #F2F2F2;
      border: 1px solid #C2C2C2 ;
      box-shadow: 2px 1px 7px rgba(0,0,0,.2);
    }
    .ant-modal-footer{
      display: none;
    }
    .ant-modal-header{
      display: none;
    }
    .ant-modal-close-x{
      display: none;
    }
    .ant-row {
      justify-content: space-between;
    }
    .ant-card{
      margin-top: 18px;
    }
  }
}
.menuCard{
  width: 200px;
  margin:20px;
}
.content{
  padding: 30px 0;
  margin: 0;
  text-align: center;
  box-shadow: 3px 3px 3px #9F9F9F;
}
