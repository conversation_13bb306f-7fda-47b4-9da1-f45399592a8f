import React,{ Component } from "react";
import {Button, Modal} from "antd";

export default class MinimizeGoBeyondTips extends Component {
  closeTips = () => {
    const { closeTips } = this.props
    closeTips && closeTips()
  }
  render() {
    const { goBeyondTipsType } = this.props
    return (
      <div>
        <Modal
          visible={goBeyondTipsType}
          title={'提示'}
          footer={
            <Button type={'primary'} style={{padding: '0 12px', backgroundColor: '#1890ff'}} onClick={this.closeTips}>我知道了</Button>
          }
          onCancel={this.closeTips}
          width={480}
          bodyStyle={{ padding: '0px' }}
          destroyOnClose={true}
          maskClosable={false}
          zIndex={1100}
        >
          <div style={{padding: 24}}>
            <div>您的最小化卡片数量已达到上限，请调整卡片数量后再尝试</div>
          </div>
        </Modal>
      </div>
    );
  }
}
