import React, {Component} from 'react';
import { Modal,} from 'antd';
import styles from './style.less';

import {connect} from "dva";

//牙面选择
class EdittableDialg extends Component {
  constructor(props) {
    super(props);
    this.state = {
      //M-近中，D远中，O-颌面，B-颊侧，L-舌侧
      selectedInvert:true,
      selectedInvertD:true,//D远中
      selectedInvertM:true,//M-近中
      selectedInvertO:true,//O-颌面
      selectedInvertL:true,//L-舌侧
      selectedPosition: '',//选中牙位数据
      checkResult: '',//检查结果数据
      show:false,//显示隐藏状态
      show2:false,//显示隐藏状态
      show3:false,//显示隐藏状态
      show4:false,//显示隐藏状态
      show5:false,//显示隐藏状态
      toothDesc:{
        ToothPosition:null,//牙位
        BSelected:false,//颊侧
        LSelected:false,//舌侧
        MSelected:false,//近中
        DSelected:false,//远中
        OSelected:false,//颌面
      },
      key:"",
      selectedCode:[]//选中状态
    };

  }


  /**牙位合面**/
  toothPosition(code,val,s,key,){
    if(key==1){
      this.setState({selectedInvert:!s})
      if(s!=true){//false
        this.setState({show:false});
        this.state.toothDesc.BSelected=s

        let index = this.state.selectedCode.indexOf(code)

        this.state.selectedCode.splice( index, 1 );
      }else{
        this.setState({show:true});
        this.state.toothDesc.BSelected=s
        this.state.selectedCode.push(code)
      }
    }else if(key==2){
      this.setState({selectedInvertD:!s})
      if(s!=true){//false
        this.setState({ show2:false});
        this.state.toothDesc.DSelected=s
        let index = this.state.selectedCode.indexOf(code)

        this.state.selectedCode.splice( index, 1 );
      }else{
        this.setState({show2:true});
        this.state.toothDesc.DSelected=s
        this.state.selectedCode.push(code)
      }
    }else if(key==3){
      this.setState({selectedInvertO:!s})
      if(s!=true){//false
        this.setState({show3:false});
        this.state.toothDesc.OSelected=s
        let index = this.state.selectedCode.indexOf(code)

        this.state.selectedCode.splice( index, 1 );
      }else{
        this.setState({show3:true});
        this.state.toothDesc.OSelected=s
        this.state.selectedCode.push(code)
      }
    }else if(key==4){
      this.setState({selectedInvertL:!s})
      if(s!=true){//false
        this.setState({show4:false});
        this.state.toothDesc.LSelected=s
        let index = this.state.selectedCode.indexOf(code)

        this.state.selectedCode.splice( index, 1 );
      }else{
        this.setState({show4:true});
        this.state.toothDesc.LSelected=s
        this.state.selectedCode.push(code)
      }
    }else if(key==5){
      this.setState({selectedInvertM:!s})
      if(s!=true){//false
        this.setState({show5:false});
        this.state.toothDesc.MSelected=s
        let index = this.state.selectedCode.indexOf(code)

        this.state.selectedCode.splice( index, 1 );
      }else{
        this.setState({show5:true});
        this.state.toothDesc.MSelected=s
        this.state.selectedCode.push(code)
      }
    }
  }
  render() {
    const {selectedInvert,
      selectedInvertD,
      selectedInvertM,
      selectedInvertO,
      selectedInvertL,
    } = this.state;
    return (
      <div>
        <Modal
          title={this.props.title}
          key={this.props.newKey}
          okText="确定"
          cancelText="取消"
          visible={this.props.dentalFaceVisible}
          onOk={()=>this.props.ok(this.state.toothDesc,this.state.selectedCode)}
          onCancel={this.props.cancel}
          maskClosable={false}
          width={500}
          height={600}
          destroyOnClose
        >
          <div className={styles.big_circle} >
            <div className={this.state.show?(styles.fan_j_hover):(styles.fan_j)} onClick={()=>this.toothPosition("B",this.state.show,selectedInvert,1)}><div>颊面</div></div>
            <div className={this.state.show2?(styles.fan_y_hover):(styles.fan_y)} onClick={()=>this.toothPosition("D",this.state.show2,selectedInvertD,2)}><div>远中面</div></div>
            <div className={this.state.show3?(styles.circle_hover):(styles.circle)} onClick={()=>this.toothPosition("O",this.state.show3,selectedInvertO,3)}><div>颌面</div></div>
            <div className={this.state.show4?(styles.fan_s_hover):(styles.fan_s)} onClick={()=>this.toothPosition("L",this.state.show4,selectedInvertL,4)}><div>舌面</div></div>
            <div className={this.state.show5?(styles.fan_jz_hover):(styles.fan_jz)} onClick={()=>this.toothPosition("M",this.state.show5,selectedInvertM,5)}><div>近中面</div></div>
          </div>
        </Modal>
      </div>
    );
  }
}

export default connect(({Public, loading,}) => ({
  Public,
}))(EdittableDialg);
