import { stringify } from 'qs';
import request from '@/utils/request';

/**今日就诊**/
export async function findByUserIdToday(params) {
  return request(`/api/emr/externalCall/findByUserIdAndSearchFiled`, {
    method: 'POST',
    data: params,
  });
}

/** 全部病历**/
export async function findAllMedicalRecords(params) {
  return request(`/api/emr/med/findPatAllEmrs`, {
    method: 'POST',
    data: params,
  });
}

/** 病历页面右上方患者信息**/
export async function ermPatientInfo(params) {
  return request(`/api/emr/externalCall/ermPatientOverview`, {
    method: 'POST',
    data: params,
  });
}
/**查询单个病历详情**/
export async function findMedicalRecordDetail(params) {
  return request(`/api/emr/med/getEmrInfoBySubId`, {
    method: 'POST',
    data: params,
  });
}

// med/archivedEmr

/**归档操作**/
export async function toFile(params) {
  return request(`/api/emr/med/archivedEmr`, {
    method: 'POST',
    data: params,
  });
}

/**获取客户信息**/
export async function findCustomerBasicById(params) {
  return request(`/api/emr/externalCall/findCustomerBasicById`, {
    method: 'POST',
    data: params,
  });
}

/**病历首页--更新患者信息 请求C端的需要在C段接口前加externalCall**/
export async function updateCustomerBasicInfo(params) {
  return request(
    `/api/emr/externalCall/healthPreference/updateCustomerBasicInfo`,
    {
      method: 'POST',
      data: params,
    },
  );
}

/**病历首页--健康状况**/
export async function ermPatHealthCondition(params) {
  return request(`/api/emr/externalCall/getHealthInfo?${stringify(params)}`, {
    method: 'POST',
    data: params,
  });
}

// 编辑时回显健康状况  请求C端的需要在C段接口前加externalCall
export async function editHealth(params) {
  return request(`/api/emr/externalCall/healthPreference/label`, {
    method: 'POST',
    data: params,
  });
}
// 保存健康状况  请求C端的需要在C段接口前加externalCall
export async function saveHealth(params) {
  return request(`/api/emr/externalCall/healthPreference/save`, {
    method: 'POST',
    data: params,
  });
}
/**
 * 查看健康状况编辑操作记录  请求C端的需要在C段接口前加externalCall
 * @param {*} params
 * @returns
 */

export async function findHistoryByCustomerId(params) {
  return request(`/api/emr/externalCall/healthPreference/findHistoryByCustomerId`,{
      method: 'POST',
      data: params,
    });
}
/**病历首页--病历记录**/
export async function findMedicalRecords(params) {
  return request(`/api/emr/med/findMedicalRecords`, {
    method: 'POST',
    data: params,
  });
}

/**影像资料--按分类**/
export async function findCheckImgsByClass(params) {
  return request(`/api/emr/check/findCheckImgsByClass`, {
    method: 'POST',
    data: params,
  });
}
/**影像资料--按时间**/
export async function findCheckImgsByDate(params) {
  return request(`/api/emr/check/findCheckImgsByDate`, {
    method: 'POST',
    data: params,
  });
}
/**影像资料-预览影像**/
export async function findCheckById(params) {
  return request(`/api/emr/check/findCheckById`, {
    method: 'POST',
    data: params,
  });
}

/** 删除影像及其关联*/
export async function deleteCheckImg(params) {
  return request(`/api/emr/check/deleteCheckImg`, {
    method: 'POST',
    data: params,
  });
}

/**查询影像关联 */
export async function findLinkEmrs(params) {
  return request(`/api/emr/check/findLinkEmrs`, {
    method: 'POST',
    data: params,
  });
}

//  保存影像数据
export async function saveCheckImgs(params) {
  return request(`/api/emr/check/saveCheckImgs`, {
    method: 'POST',
    data: params,
  });
}



