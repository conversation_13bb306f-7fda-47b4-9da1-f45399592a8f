import React, {Component} from 'react';
import {Card,Radio,Popover,message,Button} from 'antd';
import $ from 'jquery';
import moment from 'moment';
import FullCalendar from '@fullcalendar/react'
import dayGridPlugin from '@fullcalendar/daygrid'
import resourceTimeGridPlugin from '@fullcalendar/resource-timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import './index2.less' // webpack must be configured to do this
import classNames from 'classnames';
import styles from './TimeAgendaCalendar.less'
import commonStyles from './phoneServiceCalendar.less'
import PropTypes from 'prop-types';
import Tooltip from 'tooltip.js';
import {findDOMNode} from 'react-dom'
import './Popper.less'
import AppointmentParticulars from '@/pages/Appointment/appointmentParticulars/index'
import EventElement from '../Elements/phoneServiceEventElement.js'
import ReactDOM from 'react-dom';
import { getScrollbarWidth,minTime,getDateClickTime } from '@/utils/CalendarUtils'
import { serilizeURL } from '@/utils/utils'
import Immutable from 'immutable'



const workStatusForDayOff =  2        // 休息日
const workStatusForOther  =  3        // 走诊医生
const workStatusForNot    =  4                           // 未排班医生
const workStatusForOrganizationStatus  =  5             // 走诊机构
const rowTotal = 7                    // 列表项总列数
const lableClickClassName = 'labelTh' // 表头时间行点击className

/**
 * @type 1:老患者预约 2:新患者预约 3:会诊 4:个人时间占用 5:已到诊 6:上班时间
 * @NewType 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
 */
const typeClassName = {
  1:'oldPatientClass',
  2:'newPatientClass',
  3:'consultationClass',
  4:'personalTimeClass',
  5:'diagnosisClass',
  6:'officeHoursClass',
}

const typeEventCommonName = {
  1:'notClinicType.', // 未到诊
  2:'comeClinicType', // 已到诊
  3:'lateType',       // 过预约时间未到诊
  4:'finishType'      // 已结算
}

import {
  getEventElement,
  getChairNum,
  newCustomerEventType,
  oldCustomerEventType,
  otherEventType,
  backgroundType,
  LarageHeight,
  MediumHeight,
  SmallHeight,
  tittleHight,
  slotLabelFormat,
  schedulerLicenseKey,
  eventPatientNameEllipsis // 患者名称添加省略号
} from '@/utils/CalendarUtils'

import  TimeCalendarHeader  from './phoneServiceTiemHeader.js'

/**
 * 时间制 预约组件
 * 全屏或半屏 使用时间制度 预约组件
 */
export default class TimeAgendaCalendar extends Component {

  static propTypes = {
    onRemoveEvent:PropTypes.func,     // 点击删除事件方法
    onEventClick:PropTypes.func,      // 点击Event预约事件的回调方法
    onTimeBlankClick:PropTypes.func,  // 点击空白区域回调事件的方法
    goNextIcon:PropTypes.func,        // 点击下一页的回调方法
    goLastIcon:PropTypes.func,        // 点击上一页的回调方法
    calendarId:PropTypes.string,      // 当前预约组件绑定的ID名称
    Option:PropTypes.object,          // 需要扩展的预约组件配置字段
    calendarTableList:PropTypes.array, // 预约组件表格表头格式
    EventList:PropTypes.array,         // 预约组件预约事件集合
    onRef:PropTypes.func,
    AgendaHeadType: PropTypes.string,  // 当前组件是姓名头 还是 时间头
    onClickDateByHead:PropTypes.func,   // 点击时间头 获取会诊时间
    appointmentPageInfo:PropTypes.object, // 预约头列表分页信息
    consultationId:PropTypes.any,         // 会诊医生id
    onEventDrop:PropTypes.any,            // 回调函数，当拖动结束且日程移动另一个时间时触发
    onEventResize:PropTypes.any,          // 当日程事件调整（resize）结束并且事件被改变时触发
    onClickTitleForDoctor:PropTypes.func, // 点击表格头中的医生回调
    onClickTitleForData:PropTypes.func,    // 点击表格头中的日期回调
    onEventDragStart:PropTypes.func,       // 开始预约拖拽的回调
  };
  static defaultProps = {
    onRemoveEvent:()=>{},
    onEventClick:()=>{},
    onTimeBlankClick:()=>{},
    goNextIcon:()=>{},
    goLastIcon:()=>{},
    consultationId:null,
    calendarId:'calendar',   // 默认组件ID
    Option:{},               // 默认组件公共配置项目
    calendarTableList:[],
    EventList:[],
    onRef:()=>{},
    AgendaHeadType:'1',       // 姓名展示:1  事件展示:2
    appointmentPageInfo:{},   // 分页信息初始化参数
    onClickDateByHead:()=>{}, // 点击时间头获取时间
    onEventDrop:()=>{},       // 回调函数，当拖动结束且日程移动另一个时间时触发
    onEventResize:()=>{},      // 当日程事件调整（resize）结束并且事件被改变时触发
    onClickTitleForDoctor:()=>{},
    onClickTitleForData:()=>{},
    onEventDragStart:()=>{},   // 开始预约拖拽的回调
  };

  /**
   * 删除Event事件
   * @param eventId
   */
  onRemoveEvent=(taskId)=>{
    this.props.onRemoveEvent(taskId)
  }
  /**
   * 点击Event事件
   * @param calEvent
   */
  onEventClick=(calEvent)=>{
    //type 1:老患者预约   2:新患者预约  3:会诊   4:个人时间占用  5:已到诊  6:上班时间
    // @NewType 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊

    const {
      eventExtendedProps,
      id,
      newResourceId,
    } = calEvent || {}

    const {
      appointmentCount,
      appointmentInfoOfTableDto,
      checked,
      consultationInfoOfTableDto,
      operationType,
      remarks,
      reservationRule,
      resourceIdValue,
      titleInfo,
      type,
    } = eventExtendedProps || {}

    /**
     * 获取event 组件中的内容
     * || type == 4
     */
    if (type == 1){
      //this.AppointmentParticulars&&this.AppointmentParticulars.LabelData&&this.AppointmentParticulars.LabelData(); ///  点击 event 事件 通知手工标签 调取数据函数
      this.props.onEventClick(calEvent);
    }
  }
  /**
   * 点击空白事件
   * @param date
   */
  onTimeBlankClick=({dateMonent,resourceObj:res,info})=>{
    let resourceObj = getChairNum(res.id,this.props.calendarTableList);
    this.props.onTimeBlankClick({dateMonent,resourceObj,info})
  }

  /**
   * 点击时间头
   */
  onClickDateByHead=(value)=>{
    let dateClickStatus = this.props.consultationId // 会诊医生id
    this.props.onClickDateByHead(value)
  }

  /**
   * 点击下一页
   */
  goNextIcon=()=>{
    let { current,pageSize,total,pageCount} = this.props.appointmentPageInfo;
    current = current || 1
    pageSize = pageSize || 0
    total = total || 0
    pageCount = pageCount || 0
    let nextPage = current >= pageCount ? pageCount : ++current

    this.props.goNextIcon(nextPage);
  }

  /**
   * 点击上一页
   */
  goLastIcon=()=>{
    let { current,pageSize,total,pageCount} = this.props.appointmentPageInfo;
    current = current || 1
    pageSize = pageSize || 0
    total = total || 0
    pageCount = pageCount || 0
    let lastPage = current <= 1 ? 1 : --current

    this.props.goLastIcon(lastPage);
  }

  /**
   * 获取resources列表项目数据
   * 遍历列表结构的同时添加椅位资源列事件
   * @returns {Array}
   */
  getResources=()=>{
    let calendarTableList = this.state.calendarTableList;
    let resources = [];                         // 椅位资源列
    let groupTotal = calendarTableList.length;  // 医生总数
    let EventListPlue = [];
    calendarTableList.forEach((val,idx)=>{
      if(val.resources){
        val.resources.forEach((res,resIdx)=>{
          const {
            staffStatus,
            chairTotal,
            type,
            workStatus,
            workTimeEnd,
            workTimeStart,
            date,
            doctorId,
            groupId,
            name,
            organizationName,
            operationType,
            doctorUserId,
          } = val || {}

          resources.push({
            ...res,
            chairTotal,
            workStatus:res.organizationStatus == 2 ? 5 : workStatus,
            workTimeEnd,
            workTimeStart,
            staffStatus,
            type,
            date,
            doctorId,
            groupId,
            name,
            organizationName:res.organizationStatus == 2 ? res.organizationName : organizationName,
            operationType,
            doctorUserId
          });
        })
      }
    })
    return resources;
  }

  /**
   * 列表中 休息日 走诊 event事件添加
   */
  setDayOffEvent=(list)=>{
    let eventListPlue = []
    list.forEach((res)=>{
      let { workStatus, workTimeEnd, workTimeStart, staffStatus, type, date, doctorId, groupId, name, organizationName, id, chairNum, title } = res || {}
      /*if (
        res.workStatus == workStatusForDayOff ||
        res.workStatus == workStatusForOther ||
        res.workStatus == workStatusForNot
      ){
        eventListPlue.push({
          resourceId:res.id,
          start: '00:00:00.008',
          end: '24:00:00.008',
          backgroundColor: "#5f5c6b",
          rendering: "background",
          titleInfo: "休息",
          type: 7,
          workStatus,
          color: null,
          id: `dayOff${res.id}`
        })
      }*/

      if(workStatus == workStatusForOrganizationStatus) { // 其他诊所椅位出诊
        eventListPlue.push({
          start: moment('00:00:00','HH:mm:ss').format(),
          end: moment('24:00:00','HH:mm:ss').format(),
          title: organizationName,
          resourceId:res.id,
          resourceIdValue: res.id,
          backgroundColor: '#DCDCDC',
          className:'surplusBackgroundEvent',
          //backgroundColor: "#5f5c6b",
          rendering: "background",
          titleInfo: organizationName,
          type: 7,
          workStatus,
          color: null,
          id: `dayOff${res.id}`
        })
      }

    })
    return eventListPlue
  }

  closeTooltip=()=>{
    this.AppointmentParticulars&&this.AppointmentParticulars.clearTooltipDATA&&this.AppointmentParticulars.clearTooltipDATA()
    if (this.tooltip) {
      this.tooltip.hide();
      this.tooltip.dispose();
      this.tooltip = null
    }
  }

  /**
   * 绑定外部点击事件方法
   */
  bindClickForCalendar=()=>{
    const _this = this
    $('body').on("click",function(event){
      let target = $(event.target);
      let thisTargetState = target.attr('class') && target.attr('class').indexOf('fc-event') != -1
      let parentsTarState = target.parents('.fc-event').attr('class') && target.parents('.fc-event').attr('class').indexOf('fc-event') != -1
      if (!(thisTargetState || parentsTarState)) {
        let _con = $('#patientAppointmentById');   // 设置目标区域
        let _PopoverText = $('#PopoverText')
        if (
          !_con.is(event.target) && _con.has(event.target).length === 0 &&
          !_PopoverText.is(event.target) && _PopoverText.has(event.target).length === 0
        ) {
          _this.closeTooltip()
        };
      }
    })
  }

  /**
   * 过滤事件方法
   * @param eventList
   * @returns {*}
   */
  filterEventList=(eventList)=>{
    let doctorIdentification = localStorage.getItem('doctorIdentification') == 1
    eventList = eventList.filter((val)=>{
      if((val.type != 6)){
        if((val.workStatus == workStatusForDayOff && doctorIdentification)) {
        }else {
          return val
        }
      }
    })
    return eventList
  }

  /**
   * 刷新列表组件方法
   */
  refreshBespeakComponent=(nextProps)=>{
    let calendarTableList = nextProps.calendarTableList;
    let resources = [];                         // 椅位资源列
    let EventListPlue = [];
    let groupTotal = Array.isArray(nextProps.calendarTableList) ? calendarTableList.length : 0;  // 医生总数
    if (Array.isArray(calendarTableList)) {
      calendarTableList.forEach((val, idx) => {
        if (val.resources) {
          val.resources.forEach((res, resIdx) => {
            const {
              staffStatus,
              type,
              workStatus,
              workTimeEnd,
              workTimeStart,
              date,
              doctorId,
              groupId,
              name,
              organizationName,
              operationType,
              doctorUserId,
            } = val || {}

            resources.push({
              ...res,
              workStatus:res.organizationStatus == 2 ? 5 : workStatus,
              workTimeEnd,
              workTimeStart,
              staffStatus,
              type,
              date,
              doctorId,
              groupId,
              name,
              organizationName:res.organizationStatus == 2 ? res.organizationName : organizationName,
              operationType,
              doctorUserId
            });
          })
        }
      })
    }
    let tableList = Array.isArray(nextProps.calendarTableList) ? [...nextProps.calendarTableList] : []
    let {
      current,
      pageCount,
      pageSize,
      total,
    } = nextProps.appointmentPageInfo || {}
    let surplusNum = 0;
    if(pageCount && pageSize) {
      surplusNum = pageSize - resources.length;
    }
    let surplusObj = null;

    if(surplusNum > 0){
      let surplusReslist= []
      for (let x = 1;x <= surplusNum; x++){
        surplusReslist.push({
          chairNum: x,
          id: `surplusNum_${x}`,
          title: null
        })
      }

      /**
       date: "2019-07-24"
       doctorId: 53
       doctorUserId: 3304
       groupId: 1
       name: "岳丽丽医生"
       operationType: 1
       organizationName: null
       resources: (2) [{…}, {…}]
       staffStatus: "0"
       type: 1
       workStatus: 1
       workTimeEnd: null
       workTimeStart: null
       * */

      /**
       * chairNum: 1
       id: 3
       title: "椅位1"
       * @type {{date: null, doctorId: null, doctorUserId: null, groupId: string, name: null, operationType: number, organizationName: null, resources: Array, staffStatus: string, type: number, workStatus: number, workTimeEnd: null, workTimeStart: null}}
       */
      surplusObj = {
        date: null,
        doctorId: null,
        doctorUserId: null,
        groupId: 'suplus',
        name: null,
        operationType: 1,
        organizationName: null,
        resources: surplusReslist,
        staffStatus: "0",
        type: 1,
        workStatus: 5,
        workTimeEnd: null,
        workTimeStart: null,
      }
    }

    let surplusBackageEvent = []
    surplusObj && surplusObj.resources.forEach((val)=>{
      surplusBackageEvent.push({
        rendering: 'background',
        id: 'surplusEvent' + val.id,
        start: moment('00:00:00','HH:mm:ss').format(),
        end: moment('24:00:00','HH:mm:ss').format(),
        titleInfo: '',
        title: '',
        resourceId:val.id,
        resourceIdValue: val.id,
        backgroundColor: '#DCDCDC',
        className:'surplusBackgroundEvent'
      })
    })


    let surplusList = nextProps.calendarTableList
    let surplusEventList = nextProps.EventList
    if (surplusObj && nextProps.calendarTableList){
      surplusList = [...nextProps.calendarTableList,surplusObj]
      surplusEventList = [...nextProps.EventList,...surplusBackageEvent]
    }

    // 添加其他机构椅位置灰
    let resList = this.setDayOffEvent(resources)


    this.setState({
      commonOption:{
        ...this.state.commonOption,
        ...nextProps.Option,
      },
      calendarTableList:surplusList,
      EventList:[...surplusEventList,...resList],
      contrastCalendarTableList:nextProps.calendarTableList,  // 用于判断是否需要更新组件的原数据
      contrastEventList:nextProps.EventList,                  // 用于判断是否需要更新组件的原数据
      option:nextProps.Option,
      AgendaHeadType:nextProps.AgendaHeadType,
      appointmentPageInfo:nextProps.appointmentPageInfo,
    },()=>{
      //this.refreshBespeakComponent();
    })
  };


  /**
   * 回调函数，当拖动结束且日程移动另一个时间时触发：
   * @param info
   */
  eventDrop=(info)=>{
    //_this.props.onEventDrop && _this.props.onEventDrop(event,delta)
    /*const {
      appointmentInfoDto, // 主诉信息
      end,                // 结束时间
      start,              // 开始时间
      resourceId,         // 改变的垂直资源列id
      id,                 // 当前事件ID
    } = event*/

    const {
      el,
      event,
      oldEvent,
      view,
      delta,
      newResource,
      oldResource
    } = info || {}
    /* extendedProps 事件扩展中包含信息
        appointmentCount: null
        appointmentInfoDto: {appointmentId: 73426, patientId: 1115583, patientName: "大闹天宫", sex: 1, sexDescribe: "先生"}
        appointmentInfoDtoList: null
        canceled: false
        operationType: 1
        reservationRule: null
        titleInfo: "Event Title1"
        type: 5
     */
    const {
      extendedProps:eventExtendedProps, // event事件中的扩展信息
      start,         // 事件开始时间
      end,           // 事件结束时间
      id:EventId,    // 事件ID
      groupId,       // 事件组ID
      title,         // 事件标题
      extendedProps
    } = event || {}
    const {
      appointmentInfoDto,
      resourceIdValue
    } = eventExtendedProps || {}
    /*
     newResourceExtendedProps 资源列中包含信息
     chairNum: 1
     date: "2019-05-07"
     doctorId: 405
     groupId: 2
     name: "王医生小组测试别动"
     organizationName: null
     staffStatus: "0"
     type: 1
     workStatus: 4
     workTimeEnd: null
     workTimeStart: null
     */
    const {
      extendedProps:newResourceExtendedProps,  // 新资源列信息
      id:newResourceId,                        // 新资源列ID
      title:newResourceTitle,                  // 新资源列标题
    } = newResource || {}


    let stateMoment = moment(start) // 获取新event的开始时间
    let endMoment = moment(end)     // 获取新event的结束时间


    let eventObj = {
      start:stateMoment,        // 开始时间
      end:endMoment,            // 结束时间
      id:EventId,               // 事件id
      eventExtendedProps,       // 扩展Event携带参数
      newResource,              // 移动到的资源列对象
      newResourceId,            // 资源列ID
      newResourceTitle,         // 资源列title
      newResourceExtendedProps, // 资源列扩展对象
    }

    const { _def:def } = event || {}


    let resources = this.getResources()
    let thisResource = resources.find((res)=>{
      if(res.id == resourceIdValue){
        return res
      }
    })


    //info.revert()
    // 抛出拖拽事件回调
    this.props.onEventDrop && this.props.onEventDrop({...info,thisResource})
  }

  /**
   * 当日程事件调整（resize）结束并且事件被改变时触发：
   * 官方组件返回缺少 resourceId
   */
  eventResize=(info)=>{
    if(this.tooltip){
      this.tooltip.dispose();
      this.tooltip = null
    }

    const {
      el,
      endDelta,
      event,
      jsEvent,
      prevEvent,
      revert,
      startDelta,
      view
    } = info || {}

    const {
      extendedProps:eventExtendedProps, // event事件中的扩展信息
      start,                            // 事件开始时间
      end,                              // 事件结束时间
      id:EventId,                       // 事件ID
      groupId,                          // 事件组ID
      title,                            // 事件标题
      _def:def,
    } = event || {}

    const {
      resourceIdValue:newResourceId
    } = eventExtendedProps || {}

    let stateMoment = moment(start) // 获取新event的开始时间
    let endMoment = moment(end)     // 获取新event的结束时间

    let eventObj = {
      start:stateMoment,        // 开始时间
      end:endMoment,            // 结束时间
      id:EventId,               // 事件id
      newResourceId,            // event 事件中对应的列资源id
      eventExtendedProps,       // event事件中扩展信息
    }
    info.revert();
    let resources = this.getResources()
    let thisResource = resources.find((res)=>{
      return res.id == newResourceId
    })
    info.thisResource = thisResource
    this.props.onEventResize && this.props.onEventResize(info)

  }

  /**
   * 确定是否允许拖拽和调整大小的事件可以互相重叠
   */
  eventOverlap=(stillEvent, movingEvent)=>{
    const { extendedProps:stillEventExtendedProps } = stillEvent;
    const { extendedProps:movingEventExtendedProps } = movingEvent;
    const { type:typeFoStill } = stillEventExtendedProps;
    const { type:typeFoMoving } = movingEventExtendedProps;


    return true
  };



  //

  /**
   * 当点击日历中某个事件的时候触发回调
   */
  eventClick=(eventClickInfo)=>{
   // this.AppointmentParticulars&&this.AppointmentParticulars.clearTooltipDATA&&this.AppointmentParticulars.clearTooltipDATA()
    const {
      el,
      event,
      jsEvent,
      view,
    } = eventClickInfo

    const {
      extendedProps:eventExtendedProps, // event事件中的扩展信息
      start,                            // 事件开始时间
      end,                              // 事件结束时间
      id:EventId,                       // 事件ID
      groupId,                          // 事件组ID
      title,                            // 事件标题
    } = event || {}



    /*eventExtendedProps.appointmentInfoOfTableDto.patientInfoDto.name = 'hahahaha'
    this.upDataEventById('3',eventExtendedProps)*/

    if(EventId == 'checkedBackEvent'){return}
    const {
      resourceIdValue:newResourceId,     // 选中newResourceId
      operationType,                     // 当前用户是否可编辑此条预约 1:可以编辑 0:不可编辑
      type,                              // 当前点击Event的类型
    } = eventExtendedProps || {}
    let stateMoment = moment(start) // 获取新event的开始时间
    let endMoment = moment(end)     // 获取新event的结束时间
    let eventObj = {
      start:stateMoment,        // 开始时间
      end:endMoment,            // 结束时间
      id:EventId,               // 事件id
      newResourceId,            // event 事件中对应的列资源id
      eventExtendedProps,       // event 事件中扩展信息
      el,                       // event 中的Dom
    }

    let resources = this.getResources()
    let thisResource = resources.find((res)=>{
      return res.id == newResourceId
    })
    eventClickInfo.thisResource = thisResource

    this.onEventClick(eventObj);

    if (type == 4){  // 当前点击事件为个人占用时间事件
      this.onPopperShow(eventClickInfo, () => {
        this.AppointmentParticulars.updateCurrentClickEvent(eventClickInfo)
      });
      return
    }

  };

  // 展示悬窗的样式
  applyReactStyle=(data)=>{
    this.setState({
      visibleAppointmentParticularsStyle:{
        position:'absolute',
        top:'1px',
        left:'1px'
      },
      visibleAppointmentParticulars:true,
    })
  }
  onPopperShow=({el, event, jsEvent, view,},callBack)=> {
    const {
      extendedProps: eventExtendedProps, // event事件中的扩展信息
      start,                            // 事件开始时间
      end,                              // 事件结束时间
      id: EventId,                       // 事件ID
      groupId,                          // 事件组ID
      title,                            // 事件标题
    } = event || {}
    const {
      type
    } = eventExtendedProps || {}

    /**
     * 通过EventId获取到点击的EventDom结构
     */

    // 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
    // type

    if(this.tooltip){
      const { popperInstance } = this.tooltip;
      const { popper } = popperInstance || {}
      const { id } = popper || {}
      //let myElement = document.getElementById(id+'');
      //myElement && myElement.removeNode(true);
      //this.closeTooltip();
      this.AppointmentParticulars&&this.AppointmentParticulars.clearTooltipDATA&&this.AppointmentParticulars.clearTooltipDATA()
      $('#' + id).remove()
      $('body').remove('.tooltip')
      //this.tooltip.hide()
      //this.tooltip.dispose();
      this.tooltip = null
    }

    if (!this.tooltip) {
      if (type == 4) {
        let calendarApi = this.calendarRef.current.getApi()
        let changeEvent = calendarApi.getEventById(EventId)

        let elByEvnetId = $(`#event_${EventId}`).parents('.fc-event');
        let popperContent = document.getElementById('droppableById')
        this.tooltip = new Tooltip(elByEvnetId, {
          title: '',
          html: true,
          //placement: 'left',
          trigger: 'click',
          container: '.fc-body',
          //container: '#scrollBox',
          //container: '.FullCalendarWarp',
          popperOptions: {
            modifiers: {
              flip: {behavior: ['left', 'right']},
            }
          },
          placement: 'left-start',
        });
        this.tooltip.updateTitleContent(findDOMNode(this.refs.foo))
        this.tooltip.show();
        this.tooltip.evnetId = EventId; // 对悬窗对象添加唯一标识
      }
    }
    callBack && callBack()
  }

  /**
   * 获取空白区域覆盖的event
   */
  getBackEvent=({eventList,id,type,dateStr})=>{
    let eventFilterWork = eventList.filter((res)=> {  //资源的上班时间
      if (res.resourceIdValue == id) {
        if (moment(dateStr).isBetween(res.start, res.end, 'minutes') || moment(dateStr).isSame(res.start,'minutes')) {
          if (Array.isArray(type)) {
            let typeList = type.filter((val)=>{
              return val == res.type
            })
            return typeList.length != 0
          }else {
            if (res.type == type) {
              return res
            }
          }
        }
      }
    })
    console.log(eventFilterWork);
    return eventFilterWork
  }

  /**
   * 当点击日历上面的某一时间触发
   */
  dateClick=(info)=>{

    let { dateStr,resource } = info
    let origindateStr = dateStr; // 保存记录当前点击时间

    const {
      id,
      title,
    } = resource;


    const {
      chairNum,
      date,
      doctorId,
      groupId,
      name,
      organizationName,
      staffStatus,
      type,
      workStatus,
      workTimeEnd,
      workTimeStart,
      operationType,
      doctorUserId,
      organizationStatus,
      chairStatus
    } = resource.extendedProps || {}



    let resourceObj = {
      id,
      title,
      chairNum,
      date,
      doctorId,
      groupId,
      name,
      organizationName,
      staffStatus,
      type,
      workStatus,     // 是否上班 1:上班 2:休息
      workTimeEnd,
      workTimeStart,
      operationType,  // 判断当前列表是否可操作 1可操作 0不可操作
      doctorUserId,
      chairStatus
    }




    /**
     * 处理点击区域所在时间边界
     * @type {Array}
     *
     * 00 15 30 45 边界
     */
    dateStr = getDateClickTime(dateStr)

    let eventList = this.state.EventList;
    let eventFilter = eventList.filter((res)=>{
      if (res.resourceIdValue == id) {
        if (moment(dateStr).isBetween(res.start, res.end, 'minutes')) {
          // @NewType 1:预约 2:会诊 3:资源排班上班时间 4:个人占用时间 5:已过时间 6:休息 只有状态无具体时间 7:走诊
          //return res
          if (res.type == 7) {
            return res
          }
        }
      }
    })

    // 获取当前点击事件包含的Event 判断当前点击时间是否包含预约或占用
    let eventFilterTypeUPTiem = this.getBackEvent({
      eventList,id,type:[1,2,4],dateStr
    })
    if (eventFilterTypeUPTiem.length != 0) {
      dateStr = moment(dateStr).add(15,'minute').format()
    }
    let eventFilterTypeDownTiem = this.getBackEvent({
      eventList,id,type:[1,2,4],dateStr
    })
    console.log('eventFilterTypeDownTiem :: ',eventFilterTypeDownTiem);
    if (eventFilterTypeDownTiem.length != 0) {
      dateStr = moment(origindateStr).format()
    }

    let dateTimeText = moment(dateStr).format('HH:mm:ss')
    let dataDayText = moment(date).format('YYYY-MM-DD')
    let dataText = dataDayText + ' ' + dateTimeText;
    let dateMonent = moment(dataText,'YYYY-MM-DD HH:mm:ss')
    info.dateStr = dateStr


    //当前已过日期 点击空不展示弹框回调
    let resourceMonent = moment(resourceObj.date,'YYYY-MM-DD')
    let isBefore = false
    isBefore = moment().isSame(resourceMonent,'day')
    if(!isBefore) {
      isBefore = moment().isBefore(resourceMonent,'day')
    }

    // 禁用外诊机构椅位点击事件
    if(organizationStatus == 2 || workStatus == 5){
      return
    }

    // 判断当前医生是否离职 离职医生不可预约
    if(staffStatus == 1){
      message.warning('当前医生已离职,不可新建预约')
      return
    }

    if (eventFilter.length == 0) {
      const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1  // 1医生  2非医生
      if (doctorIdentificationState) { // 医生登录
        if (operationType == 1) {
          if (isBefore) {
            this.onTimeBlankClick({dateMonent, resourceObj, info});
          }else {
            //message.warning('当前时间已过')
          }
        } else {
          message.warning('当前医生无操作权限', 1)
        }
      } else { // 客服登录
        if (isBefore) {
          if (chairStatus == 2){
            message.warning('当前椅位状态为休息不可新建预约')
            return
          }
          if(chairStatus == 1) {
            this.onTimeBlankClick({dateMonent, resourceObj, info});
          }
        }else {
          //message.warning('当前时间已过')
        }
      }
    }
  }

  /**
   * 组件窗口改变大小时触发
   */
  windowResize=(view)=>{
    // eventPatientNameEllipsis()
  }


  /**
   * 渲染Event事件方法
   */
  eventRender=(info)=>{
    const { event, el, view} = info
    /*event.formatRange({
      hour: 'numeric',
      minute: '2-digit',
      hour12: false,
      omitZeroMinute: false,
      meridiem: false
    })*/

    const {
      extendedProps,
      id,
      start,
      end,
    } = event || {} //changeEvent
    const {
      appointmentInfoOfTableDto,
      type,
      operationType,
      checked,
      EventData,
    } = extendedProps || {}

    let {
      appointmentId,
      isComeVisit,  // 0未到诊 1已到诊
      isLate,       // 过预约时间未到诊 0没有迟到  1迟到
      isSettlement, // 结算状态 0:未结算 1:已结算
      isShuangAbout, // 1:爽约 0:未爽约
    } = appointmentInfoOfTableDto || {}

    const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1  // 1医生  2非医生


    let element = $(el)

    if(type == 2){
      let diffMinutes = 0;
      if(start && end){
        let startMoment = moment(start);
        let endMoment = moment(end);
        diffMinutes = endMoment.diff(startMoment,'minutes');
      }
      if(diffMinutes <= 5) {
        element.addClass('unset')
      }
    }

    element.addClass(typeClassName[type])

    if(checked == 1){element.addClass('checkedEvent')} // 圈住当前搜索到的预约
    if(id != 'changeEvent') {
      ReactDOM.render(<EventElement slotLabelInterval={this.props.Option.slotLabelInterval} eventObj={info}/>, el)
      //ReactDOM.render(<div></div>, el)
    }
    if (type == 1 && appointmentId) {
      let activeKey = this.getUrlParamsActiveKey()
      if(activeKey == appointmentId){ // 圈住改约中当前预约的逻辑
        element.addClass('checkedEvent')
      }
      if (isSettlement == 0 && isComeVisit == 0 && isShuangAbout == 0){
        element.append(`<div class='fc-resizer fc-end-resizer'></div>`)
      }
    }

    // 其他占用时间展示
    if(type == 4){
      element.addClass('eventOtherEvent')
      ReactDOM.render(<EventElement slotLabelInterval={this.props.Option.slotLabelInterval} eventObj={info}/>, el)
      //if(doctorIdentificationState) {  // 客服登录不可以下拉拖拽占用时间
      //客服或医生登录都可下拉拖动 400不可修改医生占用时间
      //element.append(`<div class='fc-resizer fc-end-resizer'></div>`)
      //}
    }
    /*if(EventData){
      element.hide()
    }*/

    // 判断是否可拖动类型样式
    /*if(operationType == 0 || (type != 1 &&  type != 2 && type != 6)){
      element.css({cursor:'no-drop'})
    }*/
  }

  /**
   * 获取
   */
  getResourcesItem=(id)=>{
    let resList = this.getResources()
    if (id) {
      return resList.find((res) => {
        return res.id == id
      })
    }else {
      return null
    }

    /*let calendarTableList = this.state.calendarTableList;
    /!**
     * staffStatus,
     * type,
     * workStatus,
     * workTimeEnd,
     * workTimeStart,
     * date,
     * doctorId,
     * groupId,
     * name,
     * organizationName
     *!/
    if(id) {
      return calendarTableList.find((res) => {
        if (res.groupId == id) {
          return res
        }
      })
    }else {
      return null
    }*/
  }


  /**
   * 从外部事件拖动回调
   * @param info
   */
  eventReceive=(info)=>{
    const { event } = info || {}
    const { _def:def } = event || {}
    const { resourceIds } = def
    const resourceId = Array.isArray(resourceIds) && resourceIds.length != 0 ? resourceIds[0] : null
    let resourcesItem = this.getResourcesItem(resourceId)
    this.props.onEventReceive && this.props.onEventReceive({
      info,
      resourcesItem
    })
  }

  /**
   * 开始拖拽
   * @param info
   */
  eventDragStart=(info)=>{
    this.props.onEventDragStart && this.props.onEventDragStart(info)
    if(this.tooltip){
      this.tooltip.dispose();
      this.tooltip = null
    }
  }

  /**
   * 拖拽是否允许回调
   * @param dropInfo
   * @param draggedEvent
   */
  eventAllow=(dropInfo, draggedEvent)=>{
    const { resource } = dropInfo || {};
    const { extendedProps } = resource || {}
    const { operationType,workStatus,type,doctorId,organizationStatus,chairStatus } = extendedProps || {};
    console.log('extendedProps :: ',extendedProps);
    const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1  // 1医生  2非医生

    const { extendedProps:draggedEventExtendedProps } = draggedEvent;
    const { type:draggedEventExtendedPropsType } = draggedEventExtendedProps;

    // 外部椅子位列禁止拖拽
    if(organizationStatus == 2 || workStatus == 5){
      return false
    }

    if (doctorIdentificationState) { // 医生登录
      return operationType == 1
    }else { // 客服登录  客服登录不可以拖拽个人占用时间 && draggedEventExtendedPropsType != 4
      if(draggedEventExtendedPropsType == 4){
        // 拖拽个人占用时间 不可拖拽到其他医生下 只可拖拽到当前医生下的所有椅位置
        const { occupyTimeDto } = draggedEventExtendedProps || {}
        const { resourceId:EventResourceId } = occupyTimeDto || {};
        return operationType == 1 && chairStatus == 1 && EventResourceId == doctorId
      }
      return operationType == 1 && chairStatus == 1
    }
  }

  // 清空组件中changeEvent
  upDataCalendar=()=>{
    let calendarApi = this.calendarRef.current.getApi()
    //calendarApi.render()
    //calendarApi.rerenderEvents()
    let changeEvent = calendarApi.getEventById('changeEvent')
    changeEvent && changeEvent.remove()
  }

  /**
   * 单独更改刷新一条预约
   * @param id
   * @param extendedProps
   */
  upDataEventById=(id,extendedProps)=>{
    let calendarApi = this.calendarRef.current.getApi()
    let changeEvent = calendarApi.getEventById(id)
    if (changeEvent) {
      //extendedProps&&extendedProps.appointmentInfoOfTableDto.patientInfoDto.isFirstVisit,
      const {appointmentInfoOfTableDto} = extendedProps || {}
      const {appointmentIconDto, patientInfoDto} = appointmentInfoOfTableDto || {}
      const {vipClient} = appointmentIconDto || {}
      const {vipGrade} = patientInfoDto || {}
      if (vipClient != vipGrade) {
        if (extendedProps && appointmentInfoOfTableDto && patientInfoDto) {
          extendedProps.appointmentInfoOfTableDto.patientInfoDto.vipGrade = vipClient
        }
      }
      changeEvent.setProp('extendedProps', {
        ...extendedProps
      })
    }
  }

  clearTooltip=()=>{
    this.closeTooltip()
    /*if (this.tooltip) {
      this.tooltip.hide()
      this.tooltip.dispose();
      this.tooltip = null
    }*/
  }

  getUrlParamsActiveKey=()=>{
    let href = window.location.href;
    let regArr = ['/subscribe/therearepatient','/customerservice/subscribe/appointment']
    let urlState = false
    regArr.forEach((res)=>{if (href.indexOf(res) != -1){urlState = true}})
    if (urlState) {
      // 首先判断当前页面是否输入半屏改约页面
      //let urlIndex = href.indexOf('therearepatient')
      let urlIndexParams = href.indexOf('?')
      let urlParamsActiveKey = serilizeURL(href).activeKey
      return urlParamsActiveKey
    }else {
      return null
    }
  }

  /**
   * ---------------------------------------------生命周期---------------------------------------------
   * @param props
   */
  constructor(props) {
    super(props);
    this.state = {
      calendarTableList:[...props.calendarTableList],
      EventList:[...props.EventList],
      visibleAppointmentParticulars :false,           // 弹窗状态是否展示
      visibleAppointmentParticularsStyle:{},          // 弹窗定位样式
      currentClickEvent:{},                           // 当前点击的预约数据
      showFullCalendar:true,                          // 展示预约数据,
      AgendaHeadType:null,                            // 回显列表头类型数据
      commonOption:{},                                // 时间颗粒度改变
      appointmentPageInfo:{}
    };
    this.calendarRef = React.createRef();
  }

  updateStatus = () =>{
    this.props.updateStatuss && this.props.updateStatuss()
    //this.upDataEventById('1',);
  };


  /**
   * 搜索结果定位添加
   * */
  setPageScrollTop=()=> {
    let checkEvent = this.state.EventList.find((event)=>{return event.checked == 1})
    if (checkEvent && checkEvent.start ) {
      let minTiem = minTime()
      let eventStartTiem = moment(checkEvent.start).format('HH:mm:ss')
      let tiemDiffNum = moment(eventStartTiem,'HH:mm:ss').diff(moment(minTiem, 'HH:mm:ss'), 'minute');
      if (tiemDiffNum > 0){
        let scrollTopNum = (tiemDiffNum / 15) * 28
        $('.FullCalendarWarp').animate({
          scrollTop:scrollTopNum
        })
      }
    }
  }


  render(){
    let href = window.location.href;
    let customerserviceState = href.indexOf('customerservice') != -1
    return (
      <div id="AppintmentCalendar" className={'AppintmentCalendar'}>
        <TimeCalendarHeader
          resources={this.getResources()}
          calendarTableList={this.state.calendarTableList}
          AgendaHeadType={this.props.AgendaHeadType}
          appointmentPageInfo={this.props.appointmentPageInfo}
          goLast={this.goLastIcon}
          goNext={this.goNextIcon}
          isDisenableClickTitle={true}
          //onClickTitleForDoctor={this.props.onClickTitleForDoctor}
          //onClickTitleForData={this.props.onClickTitleForData}
        />
        <div id="scrollBox" className={commonStyles.scrollPhoneBox}>
          <div className={classNames({
            [commonStyles.scroll]:true,
            [commonStyles.calendar]:true,
            [commonStyles.calendarBy60min]:this.props.Option.slotLabelInterval == '01:00:00',
            [commonStyles.calendarBy30min]:this.props.Option.slotLabelInterval == '00:30:00',
            [commonStyles.calendarBy15min]:this.props.Option.slotLabelInterval == '00:15:00',
            'FullCalendarWarp':true,
          },)} style={{width: `calc(100% + ${this.scrollbarWidth}px)`}}
          >

            {this.state.showFullCalendar &&
            <FullCalendar
              ref={this.calendarRef}
              id={`${this.props.calendarId}`}
              defaultView="resourceTimeGridDay"
              height={'auto'}
              contentHeight={'auto'}
              //className={commonStyles.calendar}
              style={{maxHigth: '900px', margin: '0 auto'}}
              plugins={[resourceTimeGridPlugin, interactionPlugin]}
              header={false}
              allDaySlot={false}
              resources={this.getResources()}
              events={[...this.state.EventList]}
              schedulerLicenseKey={schedulerLicenseKey}
              snapDuration={'00:05:00'}
              slotLabelFormat={{
                hour: 'numeric',
                minute: '2-digit',
                hour12: false,
                omitZeroMinute: false,
                meridiem: false
              }}
              eventTimeFormat={{
                hour: 'numeric',
                minute: '2-digit',
                meridiem: false,
                hour12: false,
              }}
              editable={true}
              droppable={true}
              now={(info) => {
                return moment().subtract(1, 'year').format('YYYY-MM-DD')
              }}
              {...this.props.Option}
              slotEventOverlap={false}         // 禁止重叠Event
              eventRender={this.eventRender}   // 渲染Event时间回调函数
              eventDrop={this.eventDrop}
              eventResize={this.eventResize}
              eventOverlap={this.eventOverlap}
              eventClick={this.eventClick}
              dateClick={this.dateClick}
              windowResize={this.windowResize}
              eventDragStart={this.eventDragStart}
              eventReceive={this.eventReceive}
              eventAllow={this.props.eventAllow ? this.props.eventAllow : this.eventAllow}
            />
            }
          </div>
        </div>
        {/* 全屏预约点击详情功能弹窗 */}
        {
          <div className={classNames({[styles.poppperWarp]:true})}>
            <div ref='foo'>
              {!customerserviceState &&
              <AppointmentParticulars
                className={'eventShowPopper'}
                currentClickEvent={this.state.currentClickEvent}
                onRef={(that)=>{this.AppointmentParticulars = that}}
                upDataEventById={this.upDataEventById}
                updateStatus={()=>{this.updateStatus()}}
                clearTooltip={()=>{this.clearTooltip()}}
                handleCancel={()=>{this.clearTooltip()}}
              >
              </AppointmentParticulars>
              }

              {customerserviceState &&
              <AppointmentParticularsCS
                className={'eventShowPopper'}
                currentClickEvent={this.state.currentClickEvent}
                onRef={(that) => {this.AppointmentParticulars = that}}
                updateStatus={() => {this.updateStatus()}}
                clearTooltip={() => {this.clearTooltip()}}
                handleCancel={() => {this.clearTooltip()}}
              >
              </AppointmentParticularsCS>
              }
            </div>
          </div>
        }
      </div>
    )
  }


  componentDidMount() {
    const _this = this;
    this.props.onRef && this.props.onRef(this);
    this.bindClickForCalendar()
    this.refreshBespeakComponent(this.props);
    this.scrollbarWidth = getScrollbarWidth()
    //this.setPageScrollTop()
  }

  shouldComponentUpdate(nextProps,nextState){
    let stateDlend = true

    // 判断如果预约list相同无需更新
    if (Array.isArray(nextProps.EventList)) {
      let nextPropsList = nextProps.EventList
      let stateEventList = this.state.EventList;
      let map1 = Immutable.fromJS(nextPropsList);
      let map2 = Immutable.fromJS(stateEventList);

      let appointmentPageInfoImmutable = nextProps.appointmentPageInfo;
      let appointmentPageInfoStateImmutable = this.state.appointmentPageInfo;

      stateDlend = !Immutable.is(map1, map2);
      stateDlend = !Immutable.is(appointmentPageInfoImmutable, appointmentPageInfoStateImmutable);
    }
    // 判断如果头部列表相同无需更新

    // 判断如果切换了事件颗粒度则需要更新
    if(stateDlend == false) {
      let nextslotLabel = nextProps.Option.slotLabelInterval
      let currentSlotLabel = this.state.commonOption.slotLabelInterval
      if (nextslotLabel != currentSlotLabel) {
        stateDlend = true
      }
    }

    // 判断如果Option更改则需要更新
    if (stateDlend == false) {
      let mapOption1 = Immutable.fromJS(this.props.Option);
      let mapOption2 = Immutable.fromJS(nextProps.Option);
      stateDlend = !Immutable.is(mapOption1, mapOption2);
    }

    /*if (nextProps.AgendaHeadType != this.state.AgendaHeadType){
      stateDlend = true
    }*/
    return stateDlend
  }



  componentWillReceiveProps(nextProps){
    this.refreshBespeakComponent(nextProps);
  }



  //组件销毁回调
  componentWillUnmount(){
    if (this.tooltip) {
      this.tooltip.hide()
      this.tooltip.dispose();
      this.tooltip = null
    }
    $('body').unbind()
    this.setState = (state,callback)=>{
      return;
    };
  }
}
