import React, { Component } from 'react';
import {
  Row,
  Col,
  Form,
  Image, Modal, Tooltip
} from 'antd';
import styles from './style.less';//样式
import commonStyle from '@/components/common.less';//样式

import { toothUtils } from '@/utils/toothUtils';//牙位
import { StringUtils } from "@/utils/StringUtils";//验证
import CdnImgs from '@/components/CdnImgs'//获取图片路径展示
//图片
import Preview from "@/assets/<EMAIL>";
import narrow from '@/assets/narrow.png';
import enlarge from '@/assets/enlarge.png';
import rotate from '@/assets/rotate.png';
import { connect } from "dva";
import yzImg from "@/assets/<EMAIL>";
import ybImg from "@/assets/<EMAIL>";

import ToothShow from "@/components/ToothShow";//图片显示
import LookPopupPeriodontal from "@/components/LookPopupPeriodontal";//牙周检查
import LookPopupCommon from "@/components/LookPopupCommon";  // 牙齿组件数据

/**form表单控制布局**/
const layoutNotOption = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};
//病历
class MedicalRecord extends Component {
  constructor(props) {
    super(props);
    this.state = {
      previewVisible: false, //预览的弹窗
      imageData: {},//预览弹窗的数据
      R: 0, //做旋转操作的时候旋转参数
      SS: 1, //做放大缩小操作的时候的参数
      S: 1,
      i: 0,
      previewUrl: '', //预览的图片url
      Imgvisible: false,//弹框是否显示状态
      bigImgurl: "",//获取图片路径
      LookperiodontalStatus: false,//查看牙周检查状态
      LookordinaryStatus: false,//一般检查预览状态
    };
  }
  //生命周期初始化
  componentDidMount() {
    // this.props.getAllMedicalRecords();
  }

  // 鼠标移入移除影像事件
  chooseonMouseIns = (mouseIn, id) => {
    this.setState({
      ['chooseonMouseIns' + id]: mouseIn
    })
  }
  // 知情同意书鼠标移入移除影像事件
  chooseonMouseIn = (mouseIn, id) => {
    this.setState({
      ['chooseonMouseIn' + id]: mouseIn
    })
  }
  //查看大图
  LookImg = (url) => {
    const { dispatch } = this.props;
    let params = {
      filePath: url,
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/dbPathTransformService',
        payload: params,
        callback: (res) => {
          this.setState({
            Imgvisible: true,
            bigImgurl: res,
          });
        },
      });
    }
  }
  //牙周检查鼠标移入移出
  preview = (previewStatus) => {
    this.setState({
      ['previewStatus']: previewStatus
    })
  }
  //一般检查鼠标移入移出
  preview1 = (previewStatus1) => {
    this.setState({
      ['previewStatus1']: previewStatus1
    })
  }
  //查看牙周检查
  LookExamin = () => {
    this.setState({
      LookperiodontalStatus: true,
    });
  }
  //查看牙周检查关闭
  cancelLookperiodontal = () => {
    this.setState({
      LookperiodontalStatus: false,
    });
  }
  //一般检查打开
  popupCommon = () => {
    this.setState({
      LookordinaryStatus: true,
    });
  }
  //一般检查关闭
  CancelLookordinary = () => {
    this.setState({
      LookordinaryStatus: false,
    });
  }


  // 关闭影像预览弹窗
  hideModal = () => {
    this.setState({
      previewVisible: false,
    });
  };
  // 打开影像预览弹窗
  setVisible = (value, item) => {
    this.cdnImg(value.url);
    this.setState(
      {
        imageData: value,
        previewVisible: true,
        R: 0,
        SS: 1,
      },
      () => {
        this.imgstyle.style.transform =
          'rotate(' + this.state.R + 'deg) scale(' + this.state.SS + ',' + this.state.SS + ')';
      },
    );
  };

  // 缩小图片
  narrowImg = () => {
    const { S = 1, i, SS, R } = this.state;
    if (i <= 0) {
      this.setState(
        {
          S: S + 1,
          i: i - 1,
          SS: 1 / (S + 1),
        },
        () => {
          // 解决异步改变state的值
          // console.log(this.state.S, this.state.i, this.state.R, this.state.SS, 'mmmmm');
          this.imgstyle.style.transform =
            'rotate(' + this.state.R + 'deg) scale(' + this.state.SS + ',' + this.state.SS + ')';
        },
      );
    } else {
      this.setState(
        () => ({
          S: S - 1,
          i: i - 1,
          SS: 1 * (S - 1),
        }),
        () =>
        (this.imgstyle.style.transform =
          'rotate(' + this.state.R + 'deg) scale(' + this.state.SS + ',' + this.state.SS + ')'),
      );
    }
  };

  // 放大图片
  showbig = () => {
    const { S, i, SS, R } = this.state;
    // console.log('放大', this.imgstyle, S, i, SS, R);
    if (i >= 0) {
      this.setState(
        () => ({
          S: S + 1,
          i: i + 1,
          SS: 1 * (S + 1),
        }),
        () =>
        (this.imgstyle.style.transform =
          'rotate(' + this.state.R + 'deg) scale(' + this.state.SS + ',' + this.state.SS + ')'),
      );
    } else {
      this.setState(
        () => ({
          S: S - 1,
          i: i + 1,
          SS: 1 / (S - 1),
        }),
        () =>
        (this.imgstyle.style.transform =
          'rotate(' + this.state.R + 'deg) scale(' + this.state.SS + ',' + this.state.SS + ')'),
      );
    }
  };
  // 顺时针旋转
  rotateright = () => {
    const { R, SS } = this.state;
    this.setState(
      () => ({ R: R + 90 }),
      () =>
      (this.imgstyle.style.transform =
        'rotate(' + this.state.R + 'deg) scale(' + this.state.SS + ',' + this.state.SS + ')'),
    );
  };

  // cdn图片转换
  cdnImg = (url) => {
    const { dispatch } = this.props;
    let params = {
      // tenantId:localStorage.getItem('tenantId'),
      filePath: url, //this.state.imageData.url,
    };
    if (dispatch) {
      dispatch({
        type: 'WriteMedical/dbPathTransformService',
        payload: params,
        callback: (res) => {
          this.setState({
            previewUrl: res,
          });
        },
      });
    }
  };
  render() {
    const { data, notOptions } = this.props;
    const { previewVisible, R, SS, imageData,
      previewUrl
    } = this.state;
    // console.log(this.props, data, notOptions, 'medicalRecord9999-----')
    return (
      <div className={styles.list_bottom} style={{ position: 'relative', marginLeft: notOptions != false ? '-3%' : '' }}>
        <Form
          {...notOptions != false ? { ...layoutNotOption } : { ...layoutNotOption }} className={`${styles.width100}`}>
          {/* {item.illnessDesc ? */}
          <Form.Item
            className={styles.contentinner}
            labelAlign="right"
            label="主诉"
          >
            <div style={{ width: '75%', marginLeft: '8px' }}>{data.illnessDesc}</div>
          </Form.Item>
          <Form.Item
            className={styles.contentinner}
            labelAlign="right"
            label="现病史"
          >
            <div style={{ width: '75%', marginLeft: '8px' }}>{data.preIllness}</div>
          </Form.Item>
          <Form.Item
            className={styles.contentinner}
            labelAlign="right"
            label="既往史"
          >
            <div style={{ width: '75%', marginLeft: '8px' }}>{data.pastHist}</div>
          </Form.Item>
          <Form.Item
            className={styles.contentinner}
            labelAlign="right"
            label="全身情况"
          >
            <div style={{ width: '75%', marginLeft: '8px' }}>{data.genCond}</div>
          </Form.Item>
          <Form.Item
            className={`${styles.contentinner} ${styles.middleStyle}`}
            labelAlign="right"
            label="检查"
          >
            {(data.examine || []).map((item, i) => (
              <div className={styles.check} key={i}>
                {/*  */}
                <table className={styles.tooth_position}>
                  <tbody>
                    <tr>
                      <td className={styles.first}>
                        {toothUtils.showTooth(1, item.toothPosition)}
                      </td>
                      <td className={styles.second}>
                        {toothUtils.showTooth(2, item.toothPosition)}
                      </td>
                    </tr>
                  </tbody>
                  <tbody>
                    <tr>
                      <td className={styles.third}>
                        {toothUtils.showTooth(4, item.toothPosition)}
                      </td>
                      <td className={styles.fourth}>
                        {toothUtils.showTooth(3, item.toothPosition)}
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div style={{ width: 430, marginLeft: 5,marginTop:'-6px',display: 'flex', alignItems: 'center' }}>{item.value}</div>
              </div>
            ))}
            <div className={styles.examineLookAll}>
              <div>
                {data.preCheck ?
                  <div className={styles.examineLooks}>
                    <div className={styles.examines}>
                      <div>牙周检查结果</div>
                    </div>
                    <div className={styles.examineBottom}>
                      <div
                        style={{ position: 'relative' }}
                        onMouseOver={() => this.preview(true)}
                        onMouseOut={() => this.preview(false)}
                      >
                        <img className={styles.ctimgStyle} style={{ height: 120 }} src={yzImg} alt="" />
                        <div
                          hidden={!this.state['previewStatus']}
                          className={styles.ctimgdelete}
                        >
                          <div style={{ marginTop: '38%' }}>
                            <div style={{ cursor: 'pointer' }} onClick={this.LookExamin}>
                              <img src={Preview} className={styles.icon_delete} style={{ marginLeft: 5 }} alt="" />
                              <span className={styles.deleteFont}>预览</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div style={{ maxHeight: 120, overflowY: "hidden", padding: 5 }}>
                        <div>
                          {data.preCheck.toothDesc ? Object.keys((data.preCheck.toothDesc.occRelation || {})).map((key, index) =>
                            <Form.Item
                              name={key}
                              key={index}
                              hidden={StringUtils.isBlank(data.preCheck.toothDesc.occRelation[key].toothPosition) && StringUtils.isBlank(data.preCheck.toothDesc.occRelation[key].value)}
                            >
                              <div className={styles.fontLineStyle} style={{ display: 'flex', minWidth: 0 }}>
                                <div style={{ width: 120 }}>
                                  <ToothShow tooth={data.preCheck.toothDesc.occRelation[key].toothPosition} />
                                </div>
                                <Tooltip title={data.preCheck.toothDesc.occRelation[key].name + '：' + data.preCheck.toothDesc.occRelation[key].value}>
                                  <div style={{ width: 30, marginTop: 15 }} className={styles.fontLineStyle}>{data.preCheck.toothDesc.occRelation[key].name}：{data.preCheck.toothDesc.occRelation[key].value}</div>
                                </Tooltip>
                              </div>
                            </Form.Item>
                          ) : null}
                          {data.preCheck.toothDesc ? Object.keys((data.preCheck.toothDesc.other || {})).map((key, index) =>
                            <Form.Item
                              name={key}
                              key={index}
                              hidden={StringUtils.isBlank(data.preCheck.toothDesc.other[key].toothPosition) && StringUtils.isBlank(data.preCheck.toothDesc.other[key].value)}
                            >
                              <div className={styles.fontLineStyle} style={{ display: 'flex', minWidth: 0 }}>
                                <div style={{ width: 120 }}>
                                  <ToothShow tooth={data.preCheck.toothDesc.other[key].toothPosition} />
                                </div>
                                <Tooltip title={data.preCheck.toothDesc.other[key].name + '：' + data.preCheck.toothDesc.other[key].value}>
                                  <div style={{ width: 30, marginTop: 15 }} className={styles.fontLineStyle}>{data.preCheck.toothDesc.other[key].name}：{data.preCheck.toothDesc.other[key].value}</div>
                                </Tooltip>
                              </div>
                            </Form.Item>
                          ) : null}
                        </div>
                        <div>
                          <Tooltip title={data.preCheck.examRemark}>
                            <div className={styles.fontLineStyle}>
                              检查描述：{data.preCheck.examRemark}
                            </div>
                          </Tooltip>
                        </div>
                      </div>
                    </div>
                  </div>
                  : null}
              </div>
              {/*牙周检查预览*/}
              <Modal
                title="查看牙周检查"
                visible={this.state.LookperiodontalStatus}
                destroyOnClose={true}
                onCancel={this.cancelLookperiodontal}
                width={1400}
                footer={[]}
              >
                <LookPopupPeriodontal
                  data={data.preCheck}
                />
              </Modal>
              {data.genCheck && data.genCheck.length > 0 ?
                <div className={styles.examineLooks}>
                  <div className={styles.examines}>
                    <div>一般检查结果</div>
                  </div>
                  <div className={styles.examineBottom}>
                    <div
                      style={{ position: 'relative' }}
                      onMouseOver={() => this.preview1(true)}
                      onMouseOut={() => this.preview1(false)}
                    >
                      <img className={styles.ctimgStyle} style={{ height: 120 }} src={ybImg} alt="" />
                      <div
                        hidden={!this.state['previewStatus1']}
                        className={styles.ctimgdelete}
                      >
                        <div style={{ marginTop: '38%' }}>
                          <div style={{ cursor: 'pointer' }} onClick={this.popupCommon}>
                            <img src={Preview} className={styles.icon_delete} style={{ marginLeft: 5 }} alt="" />
                            <span className={styles.deleteFont}>预览</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div style={{ maxHeight: 120, overflowY: "hidden", padding: 5 }}>
                      {(data.genCheck || []).map((item, index) =>
                        <div key={index} style={{ display: 'flex', marginBottom: 8 }}>
                          <ToothShow tooth={item.toothPosition} />
                          <Tooltip title={item.examName}>
                            <div style={{ width: 30, marginTop: 15 }} className={styles.fontLineStyle}>
                              <span>{item.examName}</span>
                              {item.toothDesc ? <span>{item.toothDesc.BSelected == true ? ",颊侧" : ""}
                                {item.toothDesc.MSelected == true ? ",近中" : ""}
                                {item.toothDesc.DSelected == true ? ",远中" : ""}
                                {item.toothDesc.OSelected == true ? ",颌面" : ""}
                                {item.toothDesc.LSelected == true ? ",舌侧" : ""}</span> : null}
                            </div>
                          </Tooltip>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                : null
              }
              {/*查看一般检查*/}
              <Modal
                title="查看一般检查"
                visible={this.state.LookordinaryStatus}
                destroyOnClose={true}
                onCancel={this.CancelLookordinary}
                width={1087}
                footer={[]}
              >
                <LookPopupCommon
                  data={data.genCheck}
                />
              </Modal>
            </div>
          </Form.Item>
          <Form.Item
            className={styles.contentinner}
            labelAlign="right"
            label="辅助检查"
          >
            <div style={{ width: '75%', marginLeft: '8px' }}>
              {data.auxiExam}
            </div>
            {/* } */}
          </Form.Item>
          <Form.Item
            // name={['user', 'introduction']}
            className={`${styles.contentinner} ${styles.middleStyle}`}
            labelAlign="right"
            label="诊断"
          >
            {(data.diag || []).map((item, i) => (
              <div className={styles.check} key={i}>
                <table className={styles.tooth_position}>
                  <tbody>
                    <tr>
                      <td className={styles.first}>
                        {/* 1 */}
                        {toothUtils.showTooth(1, item.toothPosition)}
                      </td>
                      <td className={styles.second}>
                        {/* 2 */}
                        {toothUtils.showTooth(2, item.toothPosition)}
                      </td>
                    </tr>
                  </tbody>
                  <tbody>
                    <tr>
                      <td className={styles.third}>
                        {/* 4 */}
                        {toothUtils.showTooth(4, item.toothPosition)}
                      </td>
                      <td className={styles.fourth}>
                        {/* 3 */}
                        {toothUtils.showTooth(3, item.toothPosition)}
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div style={{ width: 430, marginLeft: 5,marginTop: '-6px', display: 'flex', alignItems: 'center' }}>{item.value}</div>
              </div>
            ))}
          </Form.Item>

          <Form.Item
            // name={['user', 'treatPlan']}
            className={`${styles.contentinner} ${styles.middleStyle}`}
            labelAlign="right"
            label="本次治疗"
          >
            {data.treat && data.treat.length > 0 && data.treat[0].value !== ''
              ? (data.treat || []).map((item, i) => (
                <div key={i} className={styles.check}>
                  <table className={styles.tooth_position}>
                    <tbody>
                      <tr>
                        <td className={styles.first}>
                          {toothUtils.showTooth(1, item.toothPosition)}
                        </td>
                        <td className={styles.second}>
                          {toothUtils.showTooth(2, item.toothPosition)}
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr>
                        <td className={styles.third}>
                          {toothUtils.showTooth(4, item.toothPosition)}
                        </td>
                        <td className={styles.fourth}>
                          {toothUtils.showTooth(3, item.toothPosition)}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <div style={{ width: 430, marginLeft: 5,marginTop: '-6px', display: 'flex', alignItems: 'center' }}>{item.value}</div>
                </div>
              ))
              : null}
          </Form.Item>
          {/* : null */}
          {/* } */}
          <Form.Item
            // name={['user', 'website']}
            className={`${styles.contentinner} ${styles.middleStyle}`}
            labelAlign="right"
            label="处置"
          >
            {data.dispose && data.dispose.length > 0 && data.dispose[0].value !== ''
              ? (data.dispose || []).map((item, i) => (
                <div key={i} className={styles.check}>
                  <table className={styles.tooth_position}>
                    <tbody>
                      <tr>
                        <td className={styles.first}>
                          {toothUtils.showTooth(1, item.toothPosition)}
                        </td>
                        <td className={styles.second}>
                          {toothUtils.showTooth(2, item.toothPosition)}
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr>
                        <td className={styles.third}>
                          {toothUtils.showTooth(4, item.toothPosition)}
                        </td>
                        <td className={styles.fourth}>
                          {toothUtils.showTooth(3, item.toothPosition)}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <div style={{ width: 430, marginLeft: 5,marginTop: '-6px', display:'flex',alignItems: 'center' }}>{item.value}</div>
                </div>
              ))
              : null}
          </Form.Item>
          <Form.Item
            className={styles.contentinner}
            labelAlign="right"
            label="医嘱"
          >
            <div style={{ width: '75%', marginLeft: '8px' }}>
              {data.docOrder}
            </div>
          </Form.Item>
          <Form.Item
            // name={['user', 'website']}
            className={styles.contentinner}
            labelAlign="right"
            label="影像资料"
          >
            <div className={styles.videoImg}>
              {data.linkImgs
                ? (data.linkImgs || []).map((item, index) => (
                  <>
                    <div className={styles.videomargin} key={index}>
                      <div style={{ margin: '0 0 5px' }}>{item.className}</div>
                      <div className={styles.images}>
                        <div
                          className={styles.imgborder}
                          style={{ width: 118 }}
                          onMouseOver={() => this.chooseonMouseIns(true, item.id)}
                          onMouseOut={() => this.chooseonMouseIns(false, item.id)}
                        >
                          <CdnImgs onRef={ref => this.child = ref} fileUrl={item.url} height={'88px'} />
                          <div
                            hidden={!this.state['chooseonMouseIns' + item.id]}
                            className={styles.ctimgdelete}
                          >
                            <div style={{ marginTop: '27%', cursor: 'pointer' }} onClick={this.setVisible.bind(this, item)}>
                              <img src={Preview} className={styles.icon_delete} alt="" />
                              <span className={styles.deleteFont}>预览</span>
                            </div>
                          </div>
                        </div>
                        <div className={styles.ctimgInfo}>
                          <div className={styles.lineHeightStyle}>
                            拍摄时间：{item.shootingTime}
                          </div>
                          <div className={styles.lineHeightStyle}>
                            上传时间：{item.createdGmtAt}
                          </div>
                          <Tooltip title={item.fileDesc}>
                            <div className={`${styles.fontLineStyle} ${styles.lineHeightStyle}`}>影像分析：{item.fileDesc}</div>
                          </Tooltip>
                        </div>
                      </div>
                    </div>


                  </>
                ))


                : null}
            </div>
          </Form.Item>
          <Form.Item className={styles.contentinner} labelAlign="right" label="知情同意书">
            {data.mrcs
              ? (data.mrcs || []).map((item, index) => (
                <Col key={index} style={{ display: 'inline-block', marginRight: 10 }}>
                  <Row style={{ margin: '5px 0' }}>{item.tmptName}</Row>
                  <Row>
                    <Row>
                      <Col>
                        <div
                          style={{ width: '132px' }}
                          className={styles.imgborder}
                          onMouseOver={() => this.chooseonMouseIn(true, item.id)}
                          onMouseOut={() => this.chooseonMouseIn(false, item.id)}
                        >
                          <CdnImgs fileUrl={item.url} />
                          <div
                            hidden={!this.state['chooseonMouseIn' + item.id]}
                            className={styles.ctimgdelete}
                          >
                            <div style={{ marginTop: '32%', cursor: 'pointer' }} onClick={this.LookImg.bind(this, item.url)}>
                              <img src={Preview} className={styles.icon_delete} alt="" />
                              <span className={styles.deleteFont}>预览</span>
                            </div>
                          </div>
                        </div>
                      </Col>
                    </Row>
                  </Row>
                </Col>
              ))
              : null}
          </Form.Item>
          <Image
            width={200}
            style={{
              display: 'none',
            }}
            preview={{
              visible: this.state.Imgvisible,
              src: this.state.bigImgurl,
              onVisibleChange: (value) => {
                this.setState({
                  Imgvisible: false
                })
              },
            }}
          />
        </Form>

        <Modal
          width={790}
          title="影像预览"
          visible={previewVisible}
          onOk={this.hideModal}
          onCancel={this.hideModal}
          footer={null}
        >
          <Row gutter={16}>
            <Col className="gutter-row" span={14}>
              <div>
                <div className={styles.title}>
                  <Tooltip title={imageData.className}>
                    <Col
                      span={12}
                      className={commonStyle.font_16}
                      style={{
                        fontWeight: 600, marginBottom: '8px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        width: '174px',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      {imageData.className}
                    </Col>
                  </Tooltip>
                  <Col>
                    <span style={{ cursor: 'pointer' }} onClick={this.narrowImg}>
                      <img src={narrow} alt="" className={styles.image} />
                      <span className={styles.tipsFont}>缩小</span>
                    </span>
                    <span style={{ cursor: 'pointer' }} onClick={this.showbig}>
                      <img src={enlarge} alt="" className={styles.image} />
                      <span className={styles.tipsFont}>放大</span>
                    </span>
                    <span style={{ cursor: 'pointer' }} onClick={this.rotateright}>
                      <img src={rotate} alt="" className={styles.image} />
                      <span className={styles.tipsFont}>旋转</span>
                    </span>
                  </Col>
                </div>
                <div className={styles.ListHeight}>
                  <div className={styles.imageBorder}>
                    <div>
                      <img
                        id="img"
                        ref={(dom) => {
                          this.imgstyle = dom;
                        }}
                        src={previewUrl}
                        alt=""
                        style={{
                          transform: 'rotate(' + R + 'deg) scale(' + SS + ',' + SS + ')',
                          width: '100%', objectFit: 'cover'
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </Col>
            <Col className="gutter-row" span={9}>
              <div style={{ marginLeft: '20px' }}>
                <div
                  className={commonStyle.font_16}
                  style={{ fontWeight: 600, marginBottom: '8px' }}
                >
                  影像信息
                </div>
                <Form
                  name="basic"
                  colon={false}
                  labelCol={{ span: 7, }}
                  wrapperCol={{ span: 17, }}
                >
                  <Form.Item label="拍摄时间：" name="type"
                  >
                    <span style={{ color: 'rgba(0,0,0,0.45)' }}> {imageData.shootingTime}</span>
                  </Form.Item>
                  <Form.Item label="上传时间：" name="type" style={{ marginTop: '-10px' }}>
                    <span style={{ color: 'rgba(0,0,0,0.45)' }}>{imageData.createdGmtAt}
                    </span>
                  </Form.Item>
                </Form>
                <div
                  className={commonStyle.font_16}
                  style={{ fontWeight: 600, marginTop: '12px', marginBottom: '8px' }}
                >
                  影像分析
                </div>
                <Form name="basic" colon={false}
                  // labelCol={{ span: 7, }}
                  wrapperCol={{ span: 24, }}
                >
                  <Form.Item
                    label=""
                    name="type"
                  >
                    <span style={{ display: 'block', height: '200px', overflowY: 'auto', color: 'rgba(0,0,0,0.45)', fontFamily: 'PingFangSC-Regular, PingFang SC' }}>{imageData.fileDesc}</span>
                  </Form.Item>
                </Form>
              </div>
            </Col>
          </Row>
        </Modal>
      </div>
    );
  }
}


export default connect(() => ({}))(MedicalRecord);
