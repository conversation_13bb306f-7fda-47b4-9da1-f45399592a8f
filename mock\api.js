import mockjs from 'mockjs';

const titles = [
  '<PERSON><PERSON><PERSON>',
  'Angular',
  'Ant Design',
  'Ant Design Pro',
  'Bootstrap',
  'React',
  'Vue',
  'Webpack',
];
const avatars = [
  'https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png', // Alipay
  'https://gw.alipayobjects.com/zos/rmsportal/zOsKZmFRdUtvpqCImOVY.png', // Angular
  'https://gw.alipayobjects.com/zos/rmsportal/dURIMkkrRFpPgTuzkwnB.png', // Ant Design
  'https://gw.alipayobjects.com/zos/rmsportal/sfjbOqnsXXJgNCjCzDBL.png', // Ant Design Pro
  'https://gw.alipayobjects.com/zos/rmsportal/siCrBXXhmvTQGWPNLBow.png', // Bootstrap
  'https://gw.alipayobjects.com/zos/rmsportal/kZzEzemZyKLKFsojXItE.png', // React
  'https://gw.alipayobjects.com/zos/rmsportal/ComBAopevLwENQdKWiIn.png', // Vue
  'https://gw.alipayobjects.com/zos/rmsportal/nxkuOJlFJuAUhzlMTCEe.png', // Webpack
];

const avatars2 = [
  'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png',
  'https://gw.alipayobjects.com/zos/rmsportal/cnrhVkzwxjPwAaCfPbdc.png',
  'https://gw.alipayobjects.com/zos/rmsportal/gaOngJwsRYRaVAuXXcmB.png',
  'https://gw.alipayobjects.com/zos/rmsportal/ubnKSIfAJTxIgXOKlciN.png',
  'https://gw.alipayobjects.com/zos/rmsportal/WhxKECPNujWoWEFNdnJE.png',
  'https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png',
  'https://gw.alipayobjects.com/zos/rmsportal/psOgztMplJMGpVEqfcgF.png',
  'https://gw.alipayobjects.com/zos/rmsportal/ZpBqSxLxVEXfcUNoPKrz.png',
  'https://gw.alipayobjects.com/zos/rmsportal/laiEnJdGHVOhJrUShBaJ.png',
  'https://gw.alipayobjects.com/zos/rmsportal/UrQsqscbKEpNuJcvBZBu.png',
];

const covers = [
  'https://gw.alipayobjects.com/zos/rmsportal/uMfMFlvUuceEyPpotzlq.png',
  'https://gw.alipayobjects.com/zos/rmsportal/iZBVOIhGJiAnhplqjvZW.png',
  'https://gw.alipayobjects.com/zos/rmsportal/iXjVmWVHbCJAyqvDxdtx.png',
  'https://gw.alipayobjects.com/zos/rmsportal/gLaIAoVWTtLbBWZNYEMg.png',
];
const desc = [
  '那是一种内在的东西， 他们到达不了，也无法触及的',
  '希望是一个好东西，也许是最好的，好东西是不会消亡的',
  '生命就像一盒巧克力，结果往往出人意料',
  '城镇中有那么多的酒馆，她却偏偏走进了我的酒馆',
  '那时候我只会想自己想要什么，从不想自己拥有什么',
];

const user = [
  '付小小',
  '曲丽丽',
  '林东东',
  '周星星',
  '吴加好',
  '朱偏右',
  '鱼酱',
  '乐哥',
  '谭小仪',
  '仲尼',
];

function fakeList(count) {
  const list = [];
  for (let i = 0; i < count; i += 1) {
    list.push({
      id: `fake-list-${i}`,
      owner: user[i % 10],
      title: titles[i % 8],
      avatar: avatars[i % 8],
      cover: parseInt(i / 4, 10) % 2 === 0 ? covers[i % 4] : covers[3 - (i % 4)],
      status: ['active', 'exception', 'normal'][i % 3],
      percent: Math.ceil(Math.random() * 50) + 50,
      logo: avatars[i % 8],
      href: 'https://ant.design',
      updatedAt: new Date(new Date().getTime() - 1000 * 60 * 60 * 2 * i),
      createdAt: new Date(new Date().getTime() - 1000 * 60 * 60 * 2 * i),
      subDescription: desc[i % 5],
      description:
        '在中台产品的研发过程中，会出现不同的设计规范和实现方式，但其中往往存在很多类似的页面和组件，这些类似的组件会被抽离成一套标准规范。',
      activeUser: Math.ceil(Math.random() * 100000) + 100000,
      newUser: Math.ceil(Math.random() * 1000) + 1000,
      star: Math.ceil(Math.random() * 100) + 100,
      like: Math.ceil(Math.random() * 100) + 100,
      message: Math.ceil(Math.random() * 10) + 10,
      content:
        '段落示意：蚂蚁金服设计平台 ant.design，用最小的工作量，无缝接入蚂蚁金服生态，提供跨越设计与开发的体验解决方案。蚂蚁金服设计平台 ant.design，用最小的工作量，无缝接入蚂蚁金服生态，提供跨越设计与开发的体验解决方案。',
      members: [
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ZiESqWwCXBRQoaPONSJe.png',
          name: '曲丽丽',
          id: 'member1',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/tBOxZPlITHqwlGjsJWaF.png',
          name: '王昭君',
          id: 'member2',
        },
        {
          avatar: 'https://gw.alipayobjects.com/zos/rmsportal/sBxjgqiuHMGRkIjqlQCd.png',
          name: '董娜娜',
          id: 'member3',
        },
      ],
    });
  }

  return list;
}

let sourceData;

function getFakeList(req, res) {
  const params = req.query;

  const count = params.count * 1 || 20;

  const result = fakeList(count);
  sourceData = result;
  return res.json(result);
}

function postFakeList(req, res) {
  const { /* url = '', */ body } = req;
  // const params = getUrlParams(url);
  const { method, id } = body;
  // const count = (params.count * 1) || 20;
  let result = sourceData;

  switch (method) {
    case 'delete':
      result = result.filter(item => item.id !== id);
      break;
    case 'update':
      result.forEach((item, i) => {
        if (item.id === id) {
          result[i] = Object.assign(item, body);
        }
      });
      break;
    case 'post':
      result.unshift({
        body,
        id: `fake-list-${result.length}`,
        createdAt: new Date().getTime(),
      });
      break;
    default:
      break;
  }

  return res.json(result);
}

const getNotice = [
  {
    id: 'xxx1',
    title: titles[0],
    logo: avatars[0],
    description: '那是一种内在的东西，他们到达不了，也无法触及的',
    updatedAt: new Date(),
    member: '科学搬砖组',
    href: '',
    memberLink: '',
  },
  {
    id: 'xxx2',
    title: titles[1],
    logo: avatars[1],
    description: '希望是一个好东西，也许是最好的，好东西是不会消亡的',
    updatedAt: new Date('2017-07-24'),
    member: '全组都是吴彦祖',
    href: '',
    memberLink: '',
  },
  {
    id: 'xxx3',
    title: titles[2],
    logo: avatars[2],
    description: '城镇中有那么多的酒馆，她却偏偏走进了我的酒馆',
    updatedAt: new Date(),
    member: '中二少女团',
    href: '',
    memberLink: '',
  },
  {
    id: 'xxx4',
    title: titles[3],
    logo: avatars[3],
    description: '那时候我只会想自己想要什么，从不想自己拥有什么',
    updatedAt: new Date('2017-07-23'),
    member: '程序员日常',
    href: '',
    memberLink: '',
  },
  {
    id: 'xxx5',
    title: titles[4],
    logo: avatars[4],
    description: '凛冬将至',
    updatedAt: new Date('2017-07-23'),
    member: '高逼格设计天团',
    href: '',
    memberLink: '',
  },
  {
    id: 'xxx6',
    title: titles[5],
    logo: avatars[5],
    description: '生命就像一盒巧克力，结果往往出人意料',
    updatedAt: new Date('2017-07-23'),
    member: '骗你来学计算机',
    href: '',
    memberLink: '',
  },
  {
    id: 'xxx7',
    title: titles[5],
    logo: avatars[5],
    description: '生命就像一盒巧克力，结果往往出人意料',
    updatedAt: new Date('2017-07-23'),
    member: '骗你来学计算机',
    href: '',
    memberLink: '',
  },
  {
    id: 'xxx8',
    title: titles[5],
    logo: avatars[5],
    description: '生命就像一盒巧克力，结果往往出人意料',
    updatedAt: new Date('2017-07-23'),
    member: '骗你来学计算机',
    href: '',
    memberLink: '',
  },
  {
    id: 'xxx9',
    title: titles[5],
    logo: avatars[5],
    description: '生命就像一盒巧克力，结果往往出人意料',
    updatedAt: new Date('2017-07-23'),
    member: '骗你来学计算机',
    href: '',
    memberLink: '',
  },
  {
    id: 'xxx10',
    title: titles[5],
    logo: avatars[5],
    description: '生命就像一盒巧克力，结果往往出人意料',
    updatedAt: new Date('2017-07-23'),
    member: '骗你来学计算机',
    href: '',
    memberLink: '',
  },
  {
    id: 'xxx11',
    title: titles[5],
    logo: avatars[5],
    description: '生命就像一盒巧克力，结果往往出人意料',
    updatedAt: new Date('2017-07-23'),
    member: '骗你来学计算机',
    href: '',
    memberLink: '',
  },
];

const getActivities = [
  {
    id: 'trend-1',
    updatedAt: new Date(),
    user: {
      name: '曲丽丽',
      avatar: avatars2[0],
    },
    group: {
      name: '高逼格设计天团',
      link: 'http://github.com/',
    },
    project: {
      name: '六月迭代',
      link: 'http://github.com/',
    },
    template: '在 @{group} 新建项目 @{project}',
  },
  {
    id: 'trend-2',
    updatedAt: new Date(),
    user: {
      name: '付小小',
      avatar: avatars2[1],
    },
    group: {
      name: '高逼格设计天团',
      link: 'http://github.com/',
    },
    project: {
      name: '六月迭代',
      link: 'http://github.com/',
    },
    template: '在 @{group} 新建项目 @{project}',
  },
  {
    id: 'trend-3',
    updatedAt: new Date(),
    user: {
      name: '林东东',
      avatar: avatars2[2],
    },
    group: {
      name: '中二少女团',
      link: 'http://github.com/',
    },
    project: {
      name: '六月迭代',
      link: 'http://github.com/',
    },
    template: '在 @{group} 新建项目 @{project}',
  },
  {
    id: 'trend-4',
    updatedAt: new Date(),
    user: {
      name: '周星星',
      avatar: avatars2[4],
    },
    project: {
      name: '5 月日常迭代',
      link: 'http://github.com/',
    },
    template: '将 @{project} 更新至已发布状态',
  },
  {
    id: 'trend-5',
    updatedAt: new Date(),
    user: {
      name: '朱偏右',
      avatar: avatars2[3],
    },
    project: {
      name: '工程效能',
      link: 'http://github.com/',
    },
    comment: {
      name: '留言',
      link: 'http://github.com/',
    },
    template: '在 @{project} 发布了 @{comment}',
  },
  {
    id: 'trend-6',
    updatedAt: new Date(),
    user: {
      name: '乐哥',
      avatar: avatars2[5],
    },
    group: {
      name: '程序员日常',
      link: 'http://github.com/',
    },
    project: {
      name: '品牌迭代',
      link: 'http://github.com/',
    },
    template: '在 @{group} 新建项目 @{project}',
  },
];

// function getFakeCaptcha(req, res) {
//   return res.json('captcha-xxx');
// }

let testList = {
  "code": 200,
  "content": [{
    "organizationId": "0dcc7a63e75c49a5b95cd24e11838fe2",
    "organizationName": "虹桥诊所",
    "tenantId": "ba67e6cf30dc4f9c9c46adef188bbd04",
    "isDoctorMajor": 1,
    "appointmentWork": {
      "organizationId": "0dcc7a63e75c49a5b95cd24e11838fe2",
      "startworkTime": "09:00",
      "endstartworkTime": "17:30"
    }
  }, {
    "organizationId": "35aac51ca00642e8833ae94de43176ae",
    "organizationName": "万象城诊所",
    "tenantId": "ba67e6cf30dc4f9c9c46adef188bbd04",
    "isDoctorMajor": 1,
    "appointmentWork": {
      "organizationId": "35aac51ca00642e8833ae94de43176ae",
      "startworkTime": "10:00",
      "endstartworkTime": "18:00"
    }
  }, {
    "organizationId": "2f6af1e865fc4b348e6996ec5000dd3a",
    "organizationName": "徐家汇诊所",
    "tenantId": "ba67e6cf30dc4f9c9c46adef188bbd04",
    "isDoctorMajor": 1,
    "appointmentWork": {
      "organizationId": "2f6af1e865fc4b348e6996ec5000dd3a",
      "startworkTime": "09:00",
      "endstartworkTime": "17:30"
    }
  }, {
    "organizationId": "8da6be554df348eda93256495a7f3367",
    "organizationName": "静安公园诊所",
    "tenantId": "ba67e6cf30dc4f9c9c46adef188bbd04",
    "isDoctorMajor": 1,
    "appointmentWork": {
      "organizationId": "8da6be554df348eda93256495a7f3367",
      "startworkTime": "10:00",
      "endstartworkTime": "18:00"
    }
  }, {
    "organizationId": "9a1e1d3923fc41ab8b9dc0d440dbed62",
    "organizationName": "世纪大道诊所",
    "tenantId": "ba67e6cf30dc4f9c9c46adef188bbd04",
    "isDoctorMajor": 1,
    "appointmentWork": {
      "organizationId": "9a1e1d3923fc41ab8b9dc0d440dbed62",
      "startworkTime": "09:00",
      "endstartworkTime": "17:30"
    }
  }, {
    "organizationId": "878e756114284e83b19d585b460a9d3b",
    "organizationName": "联洋诊所",
    "tenantId": "ba67e6cf30dc4f9c9c46adef188bbd04",
    "isDoctorMajor": 1,
    "appointmentWork": {
      "organizationId": "878e756114284e83b19d585b460a9d3b",
      "startworkTime": "09:00",
      "endstartworkTime": "17:30"
    }
  }, {
    "organizationId": "df8ebdfba0dd47458f53440fb87625d3",
    "organizationName": "名人诊所",
    "tenantId": "ba67e6cf30dc4f9c9c46adef188bbd04",
    "isDoctorMajor": 1,
    "appointmentWork": {
      "organizationId": "df8ebdfba0dd47458f53440fb87625d3",
      "startworkTime": "10:00",
      "endstartworkTime": "18:00"
    }
  }, {
    "organizationId": "273393edd04e4aa4afe9347bb8a2da21",
    "organizationName": "正大诊所",
    "tenantId": "ba67e6cf30dc4f9c9c46adef188bbd04",
    "isDoctorMajor": 1,
    "appointmentWork": {
      "organizationId": "273393edd04e4aa4afe9347bb8a2da21",
      "startworkTime": "08:30",
      "endstartworkTime": "18:30"
    }
  }, {
    "organizationId": "f806ca11952e46d69f03a9a13eff8992",
    "organizationName": "大宁诊所",
    "tenantId": "ba67e6cf30dc4f9c9c46adef188bbd04",
    "isDoctorMajor": 0,
    "appointmentWork": {
      "organizationId": "f806ca11952e46d69f03a9a13eff8992",
      "startworkTime": "09:00",
      "endstartworkTime": "17:30"
    }
  }, {
    "organizationId": "ed538f07af054b3ab989806de804b3b7",
    "organizationName": "美学诊所",
    "tenantId": "77057aed269f4a14957ae0ad0eff359a",
    "isDoctorMajor": 1,
    "appointmentWork": {
      "organizationId": "ed538f07af054b3ab989806de804b3b7",
      "startworkTime": "09:00",
      "endstartworkTime": "17:30"
    }
  }, {
    "organizationId": "2242dae1481a4887b82349cba2827af6",
    "organizationName": "碧云诊所",
    "tenantId": "77057aed269f4a14957ae0ad0eff359a",
    "isDoctorMajor": 0,
    "appointmentWork": {
      "organizationId": "2242dae1481a4887b82349cba2827af6",
      "startworkTime": "10:00",
      "endstartworkTime": "18:00"
    }
  }, {
    "organizationId": "92a2b511bfe845848251b210b4b90d19",
    "organizationName": "长风诊所",
    "tenantId": "77057aed269f4a14957ae0ad0eff359a",
    "isDoctorMajor": 0,
    "appointmentWork": {
      "organizationId": "92a2b511bfe845848251b210b4b90d19",
      "startworkTime": "09:15",
      "endstartworkTime": "17:30"
    }
  }, {
    "organizationId": "8b14da1f25b94183a18985eb6d5a2d83",
    "organizationName": "新天地诊所",
    "tenantId": "77057aed269f4a14957ae0ad0eff359a",
    "isDoctorMajor": 1,
    "appointmentWork": {
      "organizationId": "8b14da1f25b94183a18985eb6d5a2d83",
      "startworkTime": "09:00",
      "endstartworkTime": "17:30"
    }
  }, {
    "organizationId": "ef066d0a59424fec8460901b3f7235c2",
    "organizationName": "上海中心诊所",
    "tenantId": "77057aed269f4a14957ae0ad0eff359a",
    "isDoctorMajor": 1,
    "appointmentWork": {
      "organizationId": "ef066d0a59424fec8460901b3f7235c2",
      "startworkTime": "09:00",
      "endstartworkTime": "19:00"
    }
  }, {
    "organizationId": "7d2b2dc685464a14b25afd7ee0378193",
    "organizationName": "来福士诊所",
    "tenantId": "77057aed269f4a14957ae0ad0eff359a",
    "isDoctorMajor": 1,
    "appointmentWork": {
      "organizationId": "7d2b2dc685464a14b25afd7ee0378193",
      "startworkTime": "09:00",
      "endstartworkTime": "22:00"
    }
  }],
  "msg": null
}

export default {
  'POST /api/project/notice': (req, res) => {
    let getNotice1 = getNotice.slice(
      ((req.body.current ? req.body.current : 1) - 1) * req.body.pageSize,
      ((req.body.current ? req.body.current : 1) - 1) * req.body.pageSize + req.body.pageSize
    );
    res.json(getNotice1);
  },
  'GET /api/activities': getActivities,
  'GET /api/testlist/list': testList,
  'POST /api/forms': (req, res) => {
    res.send({ message: 'Ok' });
  },
  'GET /api/tags': mockjs.mock({
    'list|100': [{ name: '@city', 'value|1-100': 150, 'type|0-2': 1 }],
  }),
  'GET /api/fake_list': getFakeList,
  'POST /api/fake_list': postFakeList,
};
