import React, { Fragment } from 'react';
import { Layout, Icon } from 'antd';
import GlobalFooter from '@/components/GlobalFooter';
import BeianLogo from '@/assets/common/BeianLogo.png'

const { Footer } = Layout;
const FooterView = (props) => (
  <Footer style={props.getFooterStyle ? props.getFooterStyle : { padding: 0,background:'#DCDCDC'}}>
    <GlobalFooter
      /**
      * 去除友情链接
      */
      // links={[
      //   {
      //     key: '口腔综合管理系统',
      //     title: '口腔综合管理系统',
      //     href: 'https://pro.ant.design',
      //     blankTarget: true,
      //   },
      //   {
      //     key: '口腔综合管理系统',
      //     title: <Icon type="github" />,
      //     href: 'https://github.com/ant-design/ant-design-pro',
      //     blankTarget: true,
      //   },
      //   {
      //     key: 'Ant Design',
      //     title: 'Ant Design',
      //     href: 'https://ant.design',
      //     blankTarget: true,
      //   },
      // ]}
      copyright={
        <Fragment>
          <a style={{color:"#999",fontSize:"12px"}} target="_blank" href="https://beian.miit.gov.cn">
            <Icon type="copyright" />2021-2031 FRIDAY.tech 版权所有 ICP证：京ICP备17024434号-2
          </a>
          <br />
          <a
            style={{color:"#999",fontSize:"12px"}}
            target="_blank"
            href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010502045024"
          >
            <img style={{verticalAlign:'bottom',marginRight:'5px'}} src={BeianLogo} />京公网安备 11010502045024号
          </a>
        </Fragment>
      }
    />
  </Footer>
);
export default FooterView;
