import { Row, Col, Tooltip, Tag, Card, Spin, Space, message } from 'antd';
import React, { useState, useRef, Component } from 'react';
import { useIntl, FormattedMessage, history } from 'umi';
import { GridContent } from '@ant-design/pro-layout';
import styles from './style.less';//样式
import commonStyle from "@/pages/common.less";//样式
//图片
import noData from "@/assets/<EMAIL>";
import diamond from '@/assets/<EMAIL>';
import boy from "@/assets/<EMAIL>";
import girl2 from "@/assets/girl2.png";
import moment from 'moment';
import { connect } from 'dva';
//今日就诊
class TodaySeeDoctor extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,//加载中
      clientHeight: document.documentElement.clientHeight, // 屏幕高度
      todayWaiteParams: {
        pageNum: 1, //当前页
        pageSize: 9999, //限制页
        tenantId: localStorage.getItem("tenantId"), //平台标识
        userId: localStorage.getItem("userId"), //当前登录医生标识
        // visitStatus: 3, //就诊状态1未到诊、2到诊、3已接诊、4全部有效预约 结束就诊visitStatus这个就不用传了
        organizationId: localStorage.getItem("organizationId"), //JSON.parse(localStorage.getItem('organizationlnfo')).id,//机构id
        appointmentDates: [moment().format('YYYY-MM-DD')], //预约日期 date,当list.size=1时查询天数据，当list.size=2时按顺序安排start和end,前后包含
        // emrStatus:[], //就诊状态
        sortRule: {sort: 2, rule: 2}//排序 正序
      },
      visitList: [], //到诊列表数据
      visitCount: 0, //到诊列表数据条数
      visitedList: [],//已接诊数据
      visitedCount: 0,//已接诊数据条数
      missDiagnosisList: [], //未到诊数据
      missDiagnosisCount: 0, //未到诊数据条数
    };
    this.resize = this.resize.bind(this);
  }
  //初始化
  componentDidMount() {
    this.getTodayWaitInfo(5); //到诊
    this.getTodayWaitInfo(1);  //未到
    this.getTodayWaitInfo(3);  //结束就诊 --已接诊
    window.addEventListener("resize", this.resize); //监听屏幕高度
  }
  //在组件卸载及销毁之前直接调用
  componentWillUnmount() {
    window.removeEventListener("resize", this.resize); //监听屏幕高度
  }
  //屏幕高度
  resize() {
    this.setState({ clientHeight: document.documentElement.clientHeight });
  }
  // 点击卡片跳转医疗中心
  goHomePage(item, status) {
    history.push({
      pathname: '/emr/MedicalCenter/AllPanel',
      state: {
        patientId: item.patientId,
        tenantId: item.tenantId,
        patientData: item,
        // status:status  //就诊状态 1：未到 2： 到诊， 3：结束就诊
      }
    })
  }

  /**今日就诊列表**/
  getTodayWaitInfo = (visitStatus) => {
    //visitStatus：  2:到诊 1：未到 3：结束
    const { dispatch } = this.props;
    let params = {
      ...this.state.todayWaiteParams,
      // visitStatus: visitStatus,
    }
    // 预约未到 1 不用传emrStatus  结束就诊3 visitStatus这个就不用传了
    if (visitStatus == 5) {
      params.emrStatus = [1, 2]
      params.visitStatus = visitStatus
    } else if (visitStatus == 1) {
      params.visitStatus = visitStatus
    } else if (visitStatus == 3) {
      params.emrStatus = [3, 4]
    }
    this.setState({
      loading: true
    })
    if (dispatch) {
      dispatch({
        type: 'todayVisit/todaySeePending',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            if (visitStatus == 5) {  // 到诊数据
              this.setState({
                visitList: res.content.resultList,
                visitCount: res.content.total,
                loading: false
              });
            } else if (visitStatus == 1) {// 未到诊数据
              this.setState({
                missDiagnosisList: res.content.resultList,
                missDiagnosisCount: res.content.total,
                loading: false
              });
            } else if (visitStatus == 3) { // 已接诊数据
              this.setState({
                visitedList: res.content.resultList,
                visitedCount: res.content.total,
                loading: false
              });
            }
          }
        },
      });
    }
  };

  render() {
    const { clientHeight, visitCount, visitList = [], visitedCount, visitedList, missDiagnosisCount, missDiagnosisList = [], loading } = this.state;
    return (
      <GridContent className={styles.TodaySeecards}>
        <Space className={styles.spinLoading}>
          <Spin size='large' spinning={this.state.loading} />
        </Space>

        <Row gutter={24}>
          <Col span={8}>
            <div className={styles.cardContent} style={{ minHeight: clientHeight - 100 }}>
              <div className={styles.cardTitle}>
                <span>患者到诊</span>
                <div className={styles.reminder}>
                  {visitCount}
                </div>
              </div>
              <div>
                {
                  visitList.length == 0 || visitList == null ? (
                    <div>
                      {/*暂无数据*/}
                      <div className={commonStyle.nodataContent} style={{ marginTop: '45%' }}>
                        <img src={noData} className={commonStyle.imgStyle} alt="" />
                        <div className={commonStyle.fontStyle}>暂无数据</div>
                      </div>
                    </div>
                  ) : (
                    <div style={{ overflowY: 'auto', height: clientHeight - 120 }}>
                      {visitList && visitList.map((item, index) => (
                        <div style={{
                          margin: '8px',
                          // border: '1px solid #f0f0f0',
                          borderRadius: 3,
                          marginBottom: '16px',
                          borderLeft: this.state.appointId === item.id || this.props.appointmentId == item.id ? 0 : 1,
                        }}
                          className={styles.cardHover}
                        >
                          <Card style={{ cursor: 'pointer', border: '1px solid #f0f0f0', borderLeft: '4px solid #4292FF' }} onClick={() => this.goHomePage(item, 2)} key={index}>
                            <Col className={styles.border_bot} span={24}>
                              <div style={{ marginBottom: 5 }}>
                                <Row className={styles.topFlex}>
                                  <div style={{ display: 'flex', width: '70%' }}>
                                    <Tooltip placement="bottom" title={item.name}>
                                      <span className={`${styles.fontWeight} ${styles.name} ${styles.ellipse}`}>
                                        {item.name}
                                      </span>
                                    </Tooltip>
                                    <div className={`${styles.pd_lr} ${styles.marginSpan}`}> {item.fileNumber ? `(${item.fileNumber})` : ''}</div>
                                    {item.vipGrade == 1 ? <img src={diamond} alt="" className={styles.icon_diamond} /> : <></>}
                                  </div>
                                  <span style={{ float: 'right' }}>
                                    <Tag color="success" className={item.appointmentStatus === 6 ? styles.tagSuccess : styles.tagRed}>
                                      {item.appointmentStatus === 6 ? "已收费" : '未收费'}
                                    </Tag>
                                    <Tag color="red" className={item.emrStatus == 1 ? `${styles.marginTag} ${styles.tagRed}` :
                                      item.emrStatus == 2 ? `${styles.marginTag} ${styles.tagRed}` : item.emrStatus == 3 || item.emrStatus == 4 ?
                                        `${styles.marginTag} ${styles.tagSuccess}` : null} >
                                      病历
                                    </Tag>
                                  </span>
                                </Row>
                              </div>
                              <Row>
                                <span className={styles.pd_lr} style={{ paddingLeft: 0 }}>{item.age ? item.age : ''}{/\d$/.test(item.age) ? '岁' : ''}</span>
                                {item.sex == 1 ?
                                  <img src={boy} alt="" className={styles.icon_boy} />
                                  : item.sex == 2 ?
                                    <img src={girl2} alt="" className={styles.icon_girl} /> : ""
                                }
                                <div className={styles.line}></div>
                                <span className={styles.pd_lr}>{item.oftenTel}</span>
                              </Row>
                            </Col>
                            <Row className={styles.bottom}>
                              <Tooltip title={item.currentCure}>
                                <div className={styles.fontLeftSize}>主诉：{item.currentCure}</div>
                              </Tooltip>
                              <div className={commonStyle.font_14}>{`${item.appointmentStart}-${item.appointmentEnd}`}</div>
                            </Row>
                          </Card>
                        </div>
                      ))
                      }
                    </div>
                  )
                }
              </div>
            </div>
          </Col>
          <Col span={8}>
            <div className={styles.cardContent} style={{ minHeight: clientHeight - 100 }}>
              <div className={styles.cardTitle}>
                <span>预约未到</span>
                <div className={styles.reminder}>
                  {missDiagnosisCount}
                </div>
              </div>
              <div>
                {
                  missDiagnosisList.length == 0 || missDiagnosisList == null ? (
                    <div >
                      {/*暂无数据*/}
                      <div className={commonStyle.nodataContent} style={{ marginTop: '45%' }}>
                        <img src={noData} className={commonStyle.imgStyle} alt="" />
                        <div className={commonStyle.fontStyle}>暂无数据</div>
                      </div>
                    </div>
                  ) : (
                    <div style={{ overflowY: 'auto', height: clientHeight - 120 }}>
                      {missDiagnosisList && missDiagnosisList.map((item, index) => (
                        <div style={{
                          margin: '8px',
                          borderRadius: 3,
                          marginBottom: '16px',
                          borderLeft: this.state.appointId === item.id || this.props.appointmentId == item.id ? 0 : 1,
                        }}
                          className={styles.cardHover}
                        >
                          <Card style={{ cursor: 'pointer', border: '1px solid #f0f0f0', borderLeft: '4px solid #98CF5E' }}  onClick={() => this.goHomePage(item, 1)} key={index}>
                            <Col className={styles.border_bot} span={24}>
                              <div style={{ marginBottom: 5 }}>
                                <Row className={styles.topFlex}>
                                  <div style={{ display: 'flex', width: '70%' }}>
                                    <Tooltip placement="bottom" title={item.name}>
                                      <span className={`${styles.fontWeight} ${styles.name} ${styles.ellipse}`}>
                                        {item.name}
                                      </span>
                                    </Tooltip>
                                    <div className={`${styles.pd_lr} ${styles.marginSpan}`}> {item.fileNumber ? `(${item.fileNumber})` : ''}</div>
                                    {item.vipGrade == 1 ? <img src={diamond} className={styles.icon_diamond} /> : <></>}
                                  </div>
                                  <span style={{ float: 'right' }}>
                                    <Tag color="success" className={item.appointmentStatus === 6 ? styles.tagSuccess : styles.tagRed}>
                                      {item.appointmentStatus === 6 ? "已收费" : '未收费'}
                                    </Tag>
                                    <Tag color="red" className={item.emrStatus == 1 ? `${styles.marginTag} ${styles.tagRed}` : item.emrStatus == 2 ? `${styles.marginTag} ${styles.tagWarn}` : `${styles.marginTag} ${styles.tagSuccess}`} >
                                      病历
                                    </Tag>
                                  </span>
                                </Row>
                              </div>
                              <Row>
                                <span className={styles.pd_lr} style={{ paddingLeft: 0 }}>{item.age ? item.age : ''}{/\d$/.test(item.age) ? '岁' : ''}</span>
                                {item.sex == 1 ?
                                  <img src={boy} alt="" className={styles.icon_boy} />
                                  : item.sex == 2 ?
                                    <img src={girl2} alt="" className={styles.icon_girl} /> : ""
                                }
                                <div className={styles.line}></div>
                                <span className={styles.pd_lr}>{item.oftenTel}</span>
                              </Row>
                            </Col>
                            <Row className={styles.bottom}>
                              <Tooltip title={item.currentCure}>
                                <div className={styles.fontLeftSize}>主诉：{item.currentCure}</div>
                              </Tooltip>
                              <div className={commonStyle.font_14}>{`${item.appointmentStart}-${item.appointmentEnd}`}</div>
                            </Row>
                          </Card>
                        </div>
                      ))}
                    </div>
                  )
                }
              </div>
            </div>
          </Col>
          <Col span={8}>
            <div className={styles.cardContent} style={{ minHeight: clientHeight - 100 }}>
              <div className={styles.cardTitle}>
                <span>结束就诊</span>
                <div className={styles.reminder}>
                  {visitedCount}
                </div>
              </div>
              <div>
                {
                  visitedList.length == 0 || visitedList == null ? (
                    <div>
                      {/*暂无数据*/}
                      <div className={commonStyle.nodataContent} style={{ marginTop: '45%' }}>
                        <img src={noData} className={commonStyle.imgStyle} />
                        <div className={commonStyle.fontStyle}>暂无数据</div>
                      </div>
                    </div>
                  ) : (
                    <div style={{ overflowY: 'auto', height: clientHeight - 120 }}>
                      {visitedList && visitedList.map((item, index) => (
                        <div style={{
                          margin: '8px',
                          // border: '1px solid #f0f0f0',
                          borderRadius: 3,
                          marginBottom: '16px',
                          borderLeft: this.state.appointId === item.id || this.props.appointmentId == item.id ? 0 : 1,
                        }}
                          className={styles.cardHover}
                        >
                          <Card style={{ cursor: 'pointer', border: '1px solid #f0f0f0', borderLeft: '4px solid #EECA5D' }} onClick={() => this.goHomePage(item, 3)} key={index}>
                            <Col className={styles.border_bot} span={24}>
                              <div style={{ marginBottom: 5 }}>
                                <Row className={styles.topFlex}>
                                  <div style={{ display: 'flex', width: '70%' }}>
                                    <Tooltip placement="bottom" title={item.name}>
                                      <span className={`${styles.fontWeight} ${styles.name} ${styles.ellipse}`}>
                                        {item.name}
                                      </span>
                                    </Tooltip>
                                    <div className={`${styles.pd_lr} ${styles.marginSpan}`}> {item.fileNumber ? `(${item.fileNumber})` : ''}</div>
                                    {item.vipGrade == 1 ? <img src={diamond} className={styles.icon_diamond} /> : <></>}
                                  </div>
                                  <span style={{ float: 'right' }}>
                                    <Tag color="success" className={item.appointmentStatus === 6 ? styles.tagSuccess : styles.tagRed}>
                                      {item.appointmentStatus === 6 ? "已收费" : '未收费'}
                                    </Tag>
                                    <Tag color="red" className={item.emrStatus == 1 ? `${styles.marginTag} ${styles.tagRed}` : item.emrStatus == 2 ? `${styles.marginTag} ${styles.tagWarn}` : `${styles.marginTag} ${styles.tagSuccess}`} >
                                      病历
                                    </Tag>
                                  </span>
                                </Row>
                              </div>
                              <Row>
                                <span className={styles.pd_lr} style={{ paddingLeft: 0 }}>{item.age ? item.age : ''} {/\d$/.test(item.age) ? '岁' : ''}</span>
                                {item.sex == 1 ?
                                  <img src={boy} alt="" className={styles.icon_boy} />
                                  : item.sex == 2 ?
                                    <img src={girl2} alt="" className={styles.icon_girl} /> : ""
                                }
                                <div className={styles.line}></div>
                                <span className={styles.pd_lr}>{item.oftenTel}</span>
                              </Row>
                            </Col>
                            <Row className={styles.bottom}>
                              <Tooltip title={item.currentCure}>
                                <div className={styles.fontLeftSize}>主诉：{item.currentCure}</div>
                              </Tooltip>
                              <div className={commonStyle.font_14}>{`${item.appointmentStart}-${item.appointmentEnd}`}</div>
                            </Row>
                          </Card>
                        </div>
                      ))}
                    </div>
                  )
                }
              </div>
            </div>
          </Col>
        </Row>
      </GridContent>
    );
  }
}

export default connect(({ PendingInfo }) => ({
  PendingInfo
}))(TodaySeeDoctor);
