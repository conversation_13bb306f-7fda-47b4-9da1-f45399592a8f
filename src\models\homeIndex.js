import {
  getTaskCount, // 获取接诊、收费未完成数量
  getMeetDiagnosisList,// 获取今日接诊列表
  getTodaysChargeList,// 获取今日收费列表
  cancelVisiting,// 取消接诊
  getTodayOverview,// 获取今日概览数据
  getDoctorList,// 获取明日预约医生列表
  getAppointmentForTomorrow,// 获取明日预约
  getAppointmentConfirmationModal,// 获取预约确认弹窗数据
  appointmentConfirm,// 预约确认弹窗确认
  appointmentInfoCard,// 获取取消预约弹窗数据
  cancelConfirmation,// 取消确认
  meetDiagnosisClick,// 是否暂不接诊状态
  appointmentShortMsgSend,// 短信发送
  getTaskStatus,// 获取结算状态
  jurisdictionExhibition, // 接待/回访/投诉权限
  followUpReturnVisit, // 查看回访
  getResourceListAll, // 获取全部医生
  getTodayVisitList, // 获取今日就诊列表
  cancelVisitButtonOk, // 取消到诊
  getReturnVisitTaskList, // 回访任务 - 筛选字段列表
  getAllTaskList, // 回访列表
  getConsultingList, // 咨询列表
  getConsultantAll, // 咨询——咨询师列表
} from '@/services/HomeIndex';

import {
  setSettlementStatus,// 暂不结算
} from '@/services/newSettlement'


export default {
  namespace: 'homeIndex',

  state: {
    getTaskListState: false,      // 从web推送调用任务首页列表  true刷新  false 不刷新
    getTaskListCount: false,
    todaysVisitTabs: null,              // 今日到诊tab切换标识
    todaysVisitDoctorVal: null,         // 今日到诊医生选择的值
    todaysVisitInputVal: null,          // 今日到诊 input 搜索框值
    todaysMeetDiagnosisSelectVal: null, // 今日接诊下拉选中的值
    todaysMeetDiagnosisInputVal: null,  // 今日接诊 input 搜索框值
    todaysChargeSelectVal: null,    // 今日收费 下拉选中的值
    todaysChargeInputVal: null,     // 今日收费 input 搜索框值
    todaysSwitchTabKey: null,       // 今日收费 切换收费状态
    tomorrowSubscribeDoctorId: [],  // 明日预约 表格中的 医生
    tomorrowSubscribeResult: [],    // 明日预约 表格中的 确认结果
    returnVisiInputVal: null,   // 回访任务 input 搜索框值
    returnVisiStartDate: null,  // 回访任务 开始日期
    returnVisiEndDate: null,    // 回访任务 结束日期
    returnVisiSuggestedRevisiter: null,   // 回访任务 建议回访人
    returnVisiSuggestedRevisiterID: null, // 回访任务 建议回访人Id
    consultantInputVal: null,   // 咨询任务 input 搜索框值
    consultantStartDate: null,  // 咨询任务 开始日期
    consultantEndDate: null,    // 咨询任务 结束日期
    consultantName: '',         // 咨询任务 咨询师姓名
    consultantID: null,         // 咨询任务 咨询师ID
    returnVisitTypeID: null, // 回访类型ID
    returnVisitTypeStr: null, // 回访类型
    allReturnVisitTaskChecked: false, // 查看所有回访任务
  },

  effects: {
    // 取消到诊
    *cancelVisitButtonOk({ payload }, { call }) {
      const res = yield call(cancelVisitButtonOk, payload);
      return res;
    },
    // 获取今日就诊列表
    *getResourceListAll({ payload }, { call }) {
      const res = yield call(getResourceListAll, payload);
      return res;
    },
    // 获取今日就诊列表
    *getTodayVisitList({ payload }, { call }) {
      const res = yield call(getTodayVisitList, payload);
      return res;
    },
    // 获取接诊、收费未完成数量
    *getTaskCount({ payload }, { call }) {
      const res = yield call(getTaskCount, payload);
      return res;
    },
    // 获取今日概览数据
    *getTodayOverview({ payload }, { call }) {
      const res = yield call(getTodayOverview, payload);
      return res;
    },
    // 获取今日接诊列表
    *getMeetDiagnosisList({ payload }, { call }) {
      const res = yield call(getMeetDiagnosisList, payload);
      return res;
    },
    // 获取今日收费列表
    *getTodaysChargeList({ payload }, { call }) {
      const res = yield call(getTodaysChargeList, payload);
      return res;
    },
    // 取消接诊
    *cancelVisiting({ payload }, { call }) {
      const res = yield call(cancelVisiting, payload);
      return res;
    },
    // 暂不结算
    *setSettlementStatus({ payload: params }, { call }) {
      const res = yield call(setSettlementStatus, params);
      return res;
    },
    // 获取明日预约医生列表
    *getDoctorList({ payload: params }, { call }) {
      const res = yield call(getDoctorList, params);
      return res;
    },
    // 获取明日预约
    *getAppointmentForTomorrow({ payload: params }, { call }) {
      const res = yield call(getAppointmentForTomorrow, params);
      return res;
    },
    // 获取预约确认弹窗数据
    *getAppointmentConfirmationModal({ payload: params }, { call }) {
      const res = yield call(getAppointmentConfirmationModal, params);
      return res;
    },
    // 预约确认弹窗确认
    *appointmentConfirm({ payload: params }, { call }) {
      const res = yield call(appointmentConfirm, params);
      return res;
    },
    // 获取取消预约弹窗数据
    *appointmentInfoCard({ payload: params }, { call }) {
      const res = yield call(appointmentInfoCard, params);
      return res;
    },
    // 取消确认
    *cancelConfirmation({ payload: params }, { call }) {
      const res = yield call(cancelConfirmation, params);
      return res;
    },
    // 是否暂不接诊状态
    *meetDiagnosisClick({ payload: params }, { call }) {
      const res = yield call(meetDiagnosisClick, params);
      return res;
    },
    // 短信发送
    *appointmentShortMsgSend({ payload: params }, { call }) {
      const res = yield call(appointmentShortMsgSend, params);
      return res;
    },
    // 获取结算状态
    *getTaskStatus({ payload: params }, { call }) {
      const res = yield call(getTaskStatus, params);
      return res;
    },
    // 接待/回访/投诉权限
    *jurisdictionExhibition({ payload: params }, { call }) {
      const res = yield call(jurisdictionExhibition, params);
      return res;
    },
    // 查看回访
    *followUpReturnVisit({ payload: params }, { call }) {
      const res = yield call(followUpReturnVisit, params);
      return res;
    },
    // 回访任务 - 筛选字段列表
    *getReturnVisitTaskList({ payload: params }, { call }) {
      const res = yield call(getReturnVisitTaskList, params);
      return res;
    },
    // 回访任务列表
    *getAllTaskList({ payload: params }, { call }) {
      const res = yield call(getAllTaskList, params);
      return res;
    },
    // 咨询任务列表
    *getConsultingList({ payload: params }, { call }) {
      const res = yield call(getConsultingList, params);
      return res;
    },
    // 咨询任务 - 咨询师
    *getConsultantAll({ payload: params }, { call }) {
      const res = yield call(getConsultantAll, params);
      return res;
    },
  },

  reducers: {
    // 更新状态值数据
    setTaskListState(state, { payload }) {
      return {
        ...state,
        ...payload
      }
    },

    // 清空数据
    clean(state, { payload }){
      return {
        ...state,
        todaysVisitTabs: null,              // 今日到诊tab切换标识
        todaysVisitDoctorVal: null,         // 今日到诊医生选择的值
        todaysVisitInputVal: null,          // 今日到诊 input 搜索框值
        todaysMeetDiagnosisSelectVal: null, // 今日接诊下拉选中的值
        todaysMeetDiagnosisInputVal: null,  // 今日接诊 input 搜索框值
        todaysChargeSelectVal: null,    // 今日收费下拉选中的值
        todaysChargeInputVal: null,     // 今日收费 input 搜索框值
        todaysSwitchTabKey: null,       // 今日收费 切换收费状态
        tomorrowSubscribeDoctorId: [],  // 明日预约 表格中的 医生
        tomorrowSubscribeResult: [],    // 明日预约 表格中的 确认结果
        returnVisiInputVal: null,   // 回访任务 input 搜索框值
        returnVisiStartDate: null,  // 回访任务 开始日期
        returnVisiEndDate: null,    // 回访任务 结束日期
        returnVisiSuggestedRevisiter: null,   // 回访任务 建议回访人
        returnVisiSuggestedRevisiterID: null, // 回访任务 建议回访人Id
        consultantInputVal: null,   // 咨询任务 input 搜索框值
        consultantStartDate: null,  // 咨询任务 开始日期
        consultantEndDate: null,    // 咨询任务 结束日期
        consultantID: null,         // 咨询任务 咨询师ID
        returnVisitTypeID: null,    // 回访类型ID
        returnVisitTypeStr: null,   // 回访类型
        allReturnVisitTaskChecked: false, // 查看所有回访任务
      }
    }
  },
  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname, search }) => {
        if (!((/\/settlement/.test(pathname)) || (/\/doctorworkbenchsimplify/.test(pathname)) || (/\/subscribe/.test(pathname)) || (/\/homeIndex/.test(pathname)))) {
          dispatch({
            type: "clean",
            payload: {}
          })
        }
      })
    }
  }
};
