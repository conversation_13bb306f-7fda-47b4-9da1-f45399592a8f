@import '~antd/lib/style/themes/default.less';

.imageDiv {
  position: relative;
  width: 204px;
  height: 200px;
  margin-right: 16px;
  margin-bottom: 16px;
  border: 1px solid #ccc;
  transition: all 0.5s;
  border-radius: 2px;
  &:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    transform: translate3d(0, -3px, 0);
    cursor: pointer;
  }
}

.ellipse {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.pLineHeight {
  margin: 0;
  line-height: 20px;
}

.deleteImg {
  position: absolute;
  top: 171px;
  right: 8px;
  width: 20px;
  height: 20px;
}

.title {
  display: flex;
  justify-content: space-between;
  // width: 450px;
  height: 32px;
  margin-bottom: 8px;
  padding: 0 12px;
  color: #212121;
  line-height: 32px;
  background: #f7f8fa;
  border-radius: 2px;
}

.image {
  width: 16px;
  height: 16px;
  margin-right: 2px;
}

.tipsFont {
  margin-right: 16px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  font-family: PingFang SC;
}

// 预览
.images {
  display: flex;
  width: 280px;
  border: 1px solid rgba(0, 0, 0, 0.06);
}
  .imgborder {
    position: relative;
  }

  .showImg {
    width: 100%;
    height: 142px;
    margin-right: 8px;
    // margin-bottom: 8px;
    border-radius: 2px;
  }

  .ctimgStyle {
    width: 118px;
    height: 88px;
  }

  .ctimgInfo {
    padding-top: 5px;
    padding-left: 8px;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 400;
    font-size: 14px;
  }

  .ctimgdelete {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
    align-items: center;
    width: 100%;
    height: 142px;
    color: #f0f0f0;
    text-align: center;
    background: rgba(0, 0, 0, 0.3);
  }

  .icon_delete {
    width: 20px;
    height: 20px;
  }

  .deleteFont {
    margin-top: 1px;
    margin-left: 4px;
    font-size: 14px;
  }
// }

.imageBorder {
  // width: 450px;
  height: 309px;
  overflow: auto;
}
