import React, { Component, useState } from 'react';
import styles from "./style.less";//样式
import {toothUtils} from "@/utils/toothUtils";//牙面选择工具类
//牙位展示
class ToothShow extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      tooth: props.tooth,//牙位
    };
  }


  render() {
    const {tooth} = this.state;
    return (
     <>
       <table className={styles.table_row} >
         <tbody>
         <tr>
           <td className={styles.line_th_row}>{toothUtils.showTooth(1,tooth)}</td>
           <td className={styles.line_th_col}>{toothUtils.showTooth(2,tooth)}</td>
         </tr>
         </tbody>
         <tbody>
         <tr>
           <td className={styles.line_row}>{toothUtils.showTooth(4,tooth)}</td>
           <td className={styles.line_col}>{toothUtils.showTooth(3,tooth)}</td>
         </tr>
         </tbody>
       </table>
     </>
    );
  }
}
export default ToothShow;
