/*@media screen and (-webkit-min-device-pixel-ratio:0){
  .closeImg{right:0px;}
}*/

.deakTd {
  //height: 4.2em;
  //height: 56.8px;
  text-align: center;
  //border: 1px solid #ccc;
  border-top: 1px solid #E3E3E3;
  border-right: 1px solid #E3E3E3;
  border-left: 1px solid #E3E3E3;
  //box-sizing: content-box;
  border-spacing: 0px;
  padding: 0px;
}

.deskBox {
  width: 100%;
}

.deskBoxMarginTop {
  margin-top: 1px;
}

.deskBoxLeft {
  margin-right: -3px;
}

.deskBoxRight {
  margin-left: -3px;
}

.deakTdhour {
  background: #F7F7F7;
  color: rgba(0,0,0,0.35);
}

.deskTdcallCotent {
  position: relative;
  // top: -21px;
  color: rgba(0,0,0,0.30);
}

.hourTimeBorder {
  border-top-color: #E3E3E3 !important;
  border-top-width: 1px;
  border-top-style: solid;
}
