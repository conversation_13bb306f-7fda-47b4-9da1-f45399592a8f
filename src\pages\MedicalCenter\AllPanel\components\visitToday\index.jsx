import {
  Tabs, Row, Col, Tag, Card, Tooltip, message,
  // Menu, Dropdown,Space
} from 'antd';
import React, {
  Component,
} from 'react';
import { PageContainer, GridContent } from '@ant-design/pro-layout';
import commonStyle from '@/components/common.less'; //引入公共样式

import styles from './style.less';//样式
//引入图片
// import FilterState from '@/assets/FilterState.png';
import boy from '@/assets/<EMAIL>';
import diamond from '@/assets/<EMAIL>';
import noData from '@/assets/<EMAIL>';
import girl2 from "@/assets/girl2.png";
import moment from 'moment';

const { TabPane } = Tabs;

class VisitToday extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // statusType: props.todyWaite.visitStatus,// 筛选就诊状态
      arrowFlag: false,
      clientHeight: document.documentElement.clientHeight, // 屏幕高度
      todyWaite: {
        pageNum: 1, //当前页
        pageSize: 10, //限制页
        tenantId: localStorage.getItem('tenantId'), //平台标识
        userId: localStorage.getItem('userId'), //当前登录医生标识
        visitStatus: 3, //就诊状态1未到诊、2到诊、3已接诊、4全部有效预约
        organizationId: localStorage.getItem('organizationId'), //JSON.parse(localStorage.getItem('organizationlnfo')).id,//机构id
        appointmentDates: [moment().format('YYYY-MM-DD')], //预约日期 date,当list.size=1时查询天数据，当list.size=2时按顺序安排start和end,前后包含
        sortRule: {sort: 2, rule: 2}//排序 正序
        // userId: localStorage.getItem("roleCodes").indexOf(1) !== -1 ? localStorage.getItem("id") : null,
        // userId: localStorage.getItem("id"),//用户id
      },
      waitTodayData: [], //今日就诊列表
      waitCount: 0, //今日就诊数
      clickItem: {}, //点击的今日就诊card
      appointId: null,
      screenWidth:'',// 屏幕宽度
    };
    this.resize = this.resize.bind(this);//监听屏幕高度
  }
  //初始化
  componentDidMount() {
    window.addEventListener('resize', this.resize); //增加
    this.setState({
      screenWidth: window.innerWidth
    })
  }
  //监听数据变化
  componentWillReceiveProps(props) {
    // let screenWidth = window.innerWidth
    this.setState({
      screenWidth: window.innerWidth
    })

    // console.log("patientDatapatientData==",JSON.stringify(props.patientData))
    if (props && props.patientData && props.patientData.id || props && props.patientInfo && props.patientInfo.id) {
      this.refreshShoeList(props);
    }

  }
  //刷新数据变化
  refreshShoeList = (props) => {
    this.setState({
      appointId: props.patientData.id || props.patientInfo.id,
      // statusType: props.todyWaite.visitStatus,// 筛选就诊状态

    })
  }
  //在组件卸载及销毁之前直接调用
  componentWillUnmount() {
    window.removeEventListener('resize', this.resize); //取消
  }
  //监听
  resize() {
    this.setState({ clientHeight: document.documentElement.clientHeight }); //监听
  }
  // 点击左侧列表的箭头
  setArrowFlag = () => {
    const { arrowFlag } = this.state;
    this.setState({
      arrowFlag: !arrowFlag,
    });
    // console.log("hahahah",arrowFlag)
    // listHidden = !listHidden
    this.props.setHidden();
  };

  // 点击患者 获取全部病历
  getPatientInfo = (item) => {
    // 传值给父组件 父组件中调用接口查询全部病历
    this.props.allPanelClick(item);
  };
  // 修改状态
  // changeStatus = (value) => {
  //   // console.log(value,'选择的状态999---')
  //   const { allPanelParams, } = this.state;

  //   this.setState({
  //     statusType: value.key,
  //   });
  //   this.props.todyWaite.pageNum = 1;
  //   this.props.todyWaite.visitStatus = value ? parseInt(value.key) : null;
  //   this.props.getTodayWaitInfo(allPanelParams)

  // };
  render() {
    const { arrowFlag, clientHeight, statusType, screenWidth } = this.state;
    const { waitTodayData, } = this.props;
    // const statusOptions = {
    //   right:<div>
    //     <span className={styles.pd_lr} style={{paddingRight:16 }}>
    //       <span className={`${styles.statusFlag} ${styles.status_bg1}`}></span>
    //       <span style={{ fontSize: 12, paddingLeft:4 }}>患者到诊</span>
    //    </span>
    //     <span className={styles.pd_lr} style={{ paddingRight: 16 }}>
    //       <span className={`${styles.statusFlag} ${styles.status_bg2}`}></span>
    //       <span style={{ fontSize: 12, paddingLeft: 4 }}>预约未到</span>
    //    </span>
    //     <span className={styles.pd_lr} style={{ paddingRight: 16 }}>
    //       <span className={`${styles.statusFlag} ${styles.status_bg3}`}></span>
    //       <span style={{ fontSize: 12, paddingLeft: 4 ,marginRight: 6 }}>结束就诊</span>
    //    </span>

    //   </div>

    // <Dropdown
    //   overlay={
    //     <Menu onClick={this.changeStatus.bind(this)}>
    //       <Menu.Item key={null}>全部状态</Menu.Item>
    //       <Menu.Item key="1">预约未到</Menu.Item>
    //       <Menu.Item key="2">患者到诊</Menu.Item>
    //       <Menu.Item key="3">结束就诊</Menu.Item>
    //     </Menu>
    //   }
    // >
    //   <a>
    //     <Space>
    //       <span style={{ marginRight: 10 }} className={styles.txt11}>
    //         <img src={FilterState} className={styles.siftImg} />
    //         {statusType == 0
    //           ? '全部状态'
    //           : statusType == '1'
    //             ? '预约未到'
    //             : statusType == '2'
    //               ? '患者到诊'
    //               : statusType == '3'
    //                 ? '结束就诊'
    //                 : '全部状态'}
    //       </span>
    //     </Space>
    //   </a>
    // </Dropdown>
    // }
    return (
      <GridContent>
        <div
          style={{ width: '100%' }}
          className={
            !arrowFlag
              ? `${styles.tabsShow} ${styles.tabs} `
              : `${styles.tabsHidden} ${styles.tabs} `
          }
        >
          {/* <Tabs defaultActiveKey="1"
          tabBarExtraContent={statusOptions}
            style={{ overflow: 'hidden', font: 'rgba(0, 0, 0, 0.85)'}}
          className={styles.VisitTodayTabs}
          > */}
          <div className={styles.topStyle}>
            <span style={{ fontSize: '16px', color: 'rgba(0,0,0,0.85)', }}>今日就诊</span>
            <div >
              <Tooltip title='患者到诊'>
                <span className={styles.titleRightFont} >
                  <span className={`${styles.statusFlag} ${styles.status_bg1}`}></span>
                  {screenWidth > '1345 '? <span style={{ fontSize: 12, paddingLeft: 4 }}>患者到诊</span>:<></>}
                </span>
              </Tooltip>
              <Tooltip title='预约未到'>
                <span className={styles.titleRightFont} >
                  <span className={`${styles.statusFlag} ${styles.status_bg2}`}></span>
                  {screenWidth > '1345 ' ? <span style={{ fontSize: 12, paddingLeft: 4 }}>预约未到</span>:<></>}
                </span>
              </Tooltip>
              <Tooltip title='结束就诊'>
                <span className={styles.titleRightFont} >
                  <span className={`${styles.statusFlag} ${styles.status_bg3}`}></span>
                  {screenWidth > '1345 ' ? <span style={{ fontSize: 12, paddingLeft: 4, marginRight: 6 }}>结束就诊</span> : <></>}
                  </span>
              </Tooltip>
            </div>
          </div>


          {/* <TabPane tab="" key="1" > */}
          <div style={{ overflowY: 'auto', height: clientHeight - 70, padding: '0 11px 16px', width: '100%' }}>
            <div style={{ width: '100%', marginTop: '3px' }}>
              {waitTodayData == null
                || waitTodayData.length === 0
                ? (
                  <div className={styles.no_info_box}>
                    <div className={commonStyle.nodataContent} style={{ marginTop: clientHeight / 3 }}>
                      <img src={noData} className={commonStyle.imgStyle} alt="" />
                      <div className={commonStyle.fontStyle}>暂无数据</div>
                    </div>
                  </div>
                ) : (
                  <div>
                    {waitTodayData && waitTodayData.map((item, index) =>
                      <div style={{
                        border: this.state.appointId === item.id || this.props.appointmentId == item.id ? '1px solid #108EE9' : '1px solid #f0f0f0',
                        borderRadius: this.state.appointId === item.id || this.props.appointmentId == item.id ? 3 : '',
                        marginBottom: '16px',
                        borderLeft: this.state.appointId === item.id || this.props.appointmentId == item.id ? 0 : 1,
                      }}
                      >
                        <Card
                          key={index}
                          onClick={() => this.getPatientInfo(item)}
                          style={{

                            cursor: 'pointer',
                            borderLeft: item.emrStatus >= 3 ? '4px solid #EECA5D' : item.appointmentStatus >= 4 ? '4px solid #4292FF' : item.appointmentStatus < 4 ? '4px solid #98CF5E' : '',
                          }}

                        // appointmentStatus >= 4 为已到诊  4292FF，
                        // appointmentStatus < 4为未到诊  98CF5E
                        // 结束就诊 EECA5D 是用emrStatus判断 emrStatus>= 3时为结束就诊

                        >
                          <Col className={styles.border_bot} span={24}>
                            <div>
                              <Row className={styles.topFlex}>
                                <div style={{ display: 'flex', width: '66%' }}>
                                  <Tooltip placement="bottom" title={item.name}>
                                    <span
                                      className={`${styles.fontWeight} ${styles.name} ${styles.ellipse}`}
                                    >
                                      {item.name}
                                    </span>
                                  </Tooltip>
                                  <span className={`${styles.pd_lr} ${styles.marginSpan}`}>
                                    {item.fileNumber ? `(${item.fileNumber})` : ''}
                                  </span>
                                  {item.vipGrade == 1 ?
                                    <img src={diamond} alt="" className={styles.icon_diamond} /> : <></>}

                                </div>
                                <span style={{ float: 'right' }}>
                                  <Tag color="success"
                                    className={item.appointmentStatus === 6 ? styles.tagSuccess : styles.tagRed}>
                                    {item.appointmentStatus === 6 ? "已收费" : '未收费'}
                                  </Tag>
                                  <Tag color="red"
                                    className={item.emrStatus == 1 ? `${styles.marginTag} ${styles.tagRed}` : item.emrStatus == 2 ? `${styles.marginTag} ${styles.tagWarn}` : `${styles.marginTag} ${styles.tagSuccess}`}>
                                    病历
                                  </Tag>
                                </span>
                              </Row>
                            </div>
                            <Row>
                              <span className={styles.pd_lr}>
                                {item.age ? item.age : ''}{/\d$/.test(item.age)? '岁' : ''}
                                {item.sex == 1 ?
                                  <img src={boy} alt="" className={styles.icon_boy} />
                                  : item.sex == 2 ?
                                  <img src={girl2} className={styles.icon_girl} />:""
                                }
                              </span>
                              <div className={styles.line}></div>
                              <span className={styles.pd_lr}>{item.oftenTel}</span>
                            </Row>
                          </Col>
                          <Row className={styles.bottom}>
                            <Tooltip title={item.currentCure}>
                              <div className={styles.fontLeftSize}>主诉：{item.currentCure}</div>
                            </Tooltip>
                            <div className={commonStyle.font_14}>
                              {/* {${item.appointmentDate} } */}
                              {`${item.appointmentStart}-${item.appointmentEnd}`}</div>
                          </Row>
                        </Card>
                      </div>
                    )}
                  </div>
                )}
            </div>
          </div>
          {/* 向左的箭头 */}
          <div
            className={`${styles.tabButton} ${styles.hideButton}`}
            style={{ top: clientHeight / 2 }}
            onClick={this.setArrowFlag}
          >
            <div className={styles.arrowLeft}></div>
          </div>
        </div>
        {/* 向右展开的箭头 */}

        <div
          className={arrowFlag ? `${styles.showButton} ${styles.tabButton} ${styles.rightTabButton}` : ''}
          style={{ top: clientHeight / 2 }}
          onClick={this.setArrowFlag}
        >
          <div className={arrowFlag ? styles.arrowRight : ''}></div>
        </div>
      </GridContent>
    );
  }
}

export default VisitToday;
