import { connect } from 'dva';

const { GridContent } = require('@ant-design/pro-layout');
import React, { Component, useState } from 'react';
import { Row, Col, Spin } from 'antd';
import ImageCard from '@/components/ImageCard';  //单个影像卡片组件
// 样式文件
import styles from './style.less';
import commonStyle from '@/components/common.less';
// 图片
import upArrow from '@/assets/<EMAIL>';
import downArrow from '@/assets/downArrow.png';
import noData from '@/assets/<EMAIL>';
import {StringUtils} from "@/utils/StringUtils";

class TimeRecord extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isShow: 'up', // 显示隐藏的小箭头
      key: '1', //点击箭头的key
      patientData:props.patientData,
      imageListParams: {
        //影像列表参数
        tenantId: localStorage.getItem('tenantId'), //平台标识
        emrId: props.patientData?props.patientData.fileNumber:null, //大病历号
        pageNum: 1,
        pageSize: 10,
      },
      newImgByDate: [], // 新的数据列表
      emrLoading: false, //分页加载数据
      emrNextStatus: false, //分页加载数据
      total: 0,  //总条数
    };
  }
// 初始化调用
  componentDidMount() {
    this.props.onRef(this);
    if(this.state.patientData){
      this.getCheckImgByDate();// 请求影像资料--按时间
    }

  }
  //监听患者信息数据变化
  componentWillReceiveProps(nextProps) {
    if (nextProps.patientData) {
      if (!this.state.patientData || nextProps.patientData.patientId != this.state.patientData.patientId) {
        // 页面中的操作都初始化一下
        this.setState( {
          isShow: 'up', // 显示隐藏的小箭头
          key: '1', //点击箭头的key
          imageListParams: {
            //影像列表参数
            tenantId: localStorage.getItem('tenantId'),
            emrId: nextProps.patientData?nextProps.patientData.fileNumber:null,
            pageNum: 1,
            pageSize: 10,
          },
          newImgByDate: [], // 新的数据列表
          emrLoading: false, //分页加载数据
          emrNextStatus: false, //分页加载数据
          total: 0,
        },()=>this.getCheckImgByDate(1));// 请求影像资料--按时间
      }
    }
  }

  // 请求影像资料--按时间
  getCheckImgByDate = (pageNum) => {
    const { dispatch } = this.props;
    const { imageListParams } = this.state;
    if (pageNum) {
      imageListParams.pageNum = pageNum;
    }


    if(StringUtils.isBlank(imageListParams.emrId)){
      this.setState({
        newImgByDate: [],
        total: 0,
      });
      return false;
    }
    this.setState({
      emrLoading: true,
    });

    if (dispatch) {
      dispatch({
        type: 'homePage/checkImgByDate',
        payload: imageListParams,
        callback: (res) => {
          if (res.code == 200) {
            if (!res.rows || res.rows.length === 0) {
              this.setState({
                emrNextStatus: false,
                emrLoading: false,
                newImgByDate:[],
                total:0
              });
              return;
            }

            (res.rows || []).map((item) => {
              item.isShow = 'up';
            });
            let arr;
            if (imageListParams.pageNum == 1) {
              arr = res.rows;
            } else {
              arr = [...this.state.newImgByDate, ...res.rows];
            }
            this.setState({
              newImgByDate: arr,
              total: res.total,
              emrLoading: false,
            });
            // console.log("^^^^^newImgByDate",JSON.stringify(this.state.newImgByDate))

          }

        },
      });
    }
  };

  // 加载更多
  emrNext = () => {
    this.setState({
      emrLoading: true,
    });
    const { imageListParams, total } = this.state;
    if (imageListParams.pageNum <= total / imageListParams.pageSize) {
      imageListParams.pageNum++;
      this.getCheckImgByDate();
    }
  };
  // 箭头展开折起事件
  isShow = (key, value) => {
    const { newImgByDate } = this.state;

    this.setState({
      key: key,
      newImgByDate: newImgByDate.map((item, index) =>
        index == key ? { ...item, isShow: value } : item,
      ),
    });
  };
  render() {
    const { isShow, key, newImgByDate, emrLoading, imageListParams, total , emrNextStatus, } = this.state;

    return (
      <GridContent>
        <div>
          {newImgByDate.length > 0 ? (
            <>
              {newImgByDate.map((item, index) => (
                <div key={index}>
                    <Row className={`${styles.width100}`}>
                      <Col span={20} className={styles.displayFlex}>
                        <h3 style={{ fontWeight: 900, fontSize: '16', marginRight: '8px', display: 'inline-block' }}>
                          {item.createdGmtAt}
                        </h3>
                        {item.isShow == 'up' ? (
                          <img
                            src={upArrow}
                            alt=""
                            className={styles.icon_boy}
                            onClick={() => this.isShow(index, 'down')}
                          />
                        ) : (
                          <img
                            src={downArrow}
                            alt=""
                            className={styles.icon_boy}
                            onClick={() => this.isShow(index, 'up')}
                          />
                        )}
                      </Col>
                    </Row>
                    {item.isShow == 'up' ? (
                      <div>
                        {item.classes &&
                        item.classes.map((childItem, childIndex) => (
                          <Row key={childIndex}>
                            <Row className={styles.width100}>
                              <p className={styles.typeTitle}>
                                {childItem.className || 'CT的影像'}
                              </p>
                            </Row>
                            <Row style={{ display: 'flex' }}>
                              {/* {console.log('子项里都有啥',childItem)} */}
                              {childItem.checkList &&
                              childItem.checkList.map((secondItem, secondIndex) => (
                                <ImageCard
                                  type={'dateType'}
                                  key={secondItem.id}
                                  connected={secondItem.isRelation}
                                  imageData={secondItem}
                                  getList={this.getCheckImgByDate}
                                />
                              ))}

                              {/* <ImageCard type="dateType"
                            // data={item}
                            getCheckImgByDate={getCheckImgByDate} />
                          <ImageCard type="dateType"
                            // data={item}
                            getCheckImgByDate={getCheckImgByDate} /> */}
                            </Row>
                          </Row>
                        ))}
                      </div>
                    ) : (
                      <></>
                    )}
                </div>
              ))}

              <Spin spinning={emrLoading} tip="加载中...">
                {imageListParams.pageNum >= total / imageListParams.pageSize ?
                  <div
                    className={styles.loading_more}
                    hidden={emrLoading}
                  >
                    <Row className={styles.width100}>
                      <p className={styles.typeTitle}>
                        没有更多啦！
                      </p>
                    </Row>
                  </div>
                  : <div
                    className={styles.loading_more}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                    hidden={emrLoading}
                    onClick={this.emrNext.bind(this) }
                  >
                    加载更多
                  </div>}
              </Spin>
            </>

          ) : (
            <Spin spinning={emrLoading} tip="加载中...">
              <div className={commonStyle.nodataContent} style={{ marginTop: '25%' }}>
                <img src={noData} className={commonStyle.imgStyle} alt="" />
                <div className={commonStyle.fontStyle}>暂无数据</div>
              </div>
            </Spin>
          )}
        </div>
      </GridContent>
    );
  }
}
export default connect(({ TimeRecord, loading }) => ({
  TimeRecord,
  loading: loading.models.TimeRecord,
}))(TimeRecord);
