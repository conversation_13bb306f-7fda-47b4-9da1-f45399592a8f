import { fakeAccountLogin, queryNotices } from '@/services/api';
import { CheckArrailRytime, getPageQuery, goBack5i5yaHome, setOrganizationInfo } from '@/utils/utils';
import { setAuthority } from '@/utils/authority';
import { checkInfoAllReady, getLoginInfo } from '@/services/user';
import { routerRedux } from 'dva/router';
import { message, Modal } from 'antd';
import { orgDeployInfoList } from '@/services/Setup';


export default {
  namespace: 'global',

  state: {
    collapsed: true,
    menuCollapsed: true,

    notices: [],
    getStatus: false,             // websocket页面跳转 是否刷新页面
    taskType: false,             // websocket页面跳转 推送任务类型
    status: false,             // websocket页面跳转 推送任务状态
    // 预付款websocket 信息集合 settlementId 结算id   transactionId 支付id transactionStatus //状态 0设置成功 1修改成功  2支付成功  3密码冻结 patientId 患者id
    // taskType:21  任务类型
    payment: {},
    isSignature: false,           // 是否是明细单签名
    intentCountDto: null,         // 预约意向表数据数量
    isDefineoffers: false,        // 是否定向邀约任务
    visibleByModalByGoJwsmed:false,  // 是否展开前往佳沃思采购系统欢迎弹窗
    moduleIdByModalByGoJwsmed:null,  // 前往系统的moduleId模块id
    visibleByModalByConsumablesMall:false, // 是否展开前往耗材商城欢迎弹窗

    noTaskContent: {},                           // 非任务推送（isTask=0）的数据，字段包括taskType，及其他
  },

  effects: {
    *fetchNotices(_, { call, put, select }) {
      const data = yield call(queryNotices);
      yield put({
        type: 'saveNotices',
        payload: data,
      });
      const unreadCount = yield select(
        state => state.global.notices.filter(item => !item.read).length
      );
      yield put({
        type: 'user/changeNotifyCount',
        payload: {
          totalCount: data.length,
          unreadCount,
        },
      });
    },
    *clearNotices({ payload }, { put, select }) {
      yield put({
        type: 'saveClearedNotices',
        payload,
      });
      const count = yield select(state => state.global.notices.length);
      const unreadCount = yield select(
        state => state.global.notices.filter(item => !item.read).length
      );
      yield put({
        type: 'user/changeNotifyCount',
        payload: {
          totalCount: count,
          unreadCount,
        },
      });
    },
    *changeNoticeReadState({ payload }, { put, select }) {
      const notices = yield select(state =>
        state.global.notices.map(item => {
          const notice = { ...item };
          if (notice.id === payload) {
            notice.read = true;
          }
          return notice;
        })
      );
      yield put({
        type: 'saveNotices',
        payload: notices,
      });
      yield put({
        type: 'user/changeNotifyCount',
        payload: {
          totalCount: notices.length,
          unreadCount: notices.filter(item => !item.read).length,
        },
      });
    },
    // 重定向到登录页
    *goLogin({ payload }, { put, call }) {
      if (CheckArrailRytime && CheckArrailRytime()) {
        const URL = goBack5i5yaHome();
        window.location.replace(URL);
      } else {
        window.location.replace(`/user/login?tenantId=${payload.TenantId}`);
      }
    },

    // 公共平台登录完成去首页or完善信息页
    *goDentalHome({ payload }, { put, call }) {
      const response = yield call(getLoginInfo, payload.uidKey); // 获取传过来的信息
      if (response != "error" && response.content) {
        localStorage.setItem('tenantId', response.content.tenantId);  //  存租户ID
        localStorage.setItem('access_token', response.content.accessToken);  //  存access_token
        localStorage.setItem('token_username', response.content.username)  // 存用户名
        const res = yield call(checkInfoAllReady); // 验证是否需要完善信息
        if (res != "error" && res.code == 200) {
          if (res.content.flag) {
            const resAll = yield call(fakeAccountLogin, { id: '123123' }); // 获取用户信息
            // 添加接口报错提示～！
            if (resAll == undefined) {
              message.error('数据加载失败！');
              return false;
            }

            //  判断有没有开通业务端权限
            if (resAll.code == 401) {
              return resAll;
            }
            // 正常登录
            if (resAll.content && resAll.content.status == 1) {
              Modal.error({
                title: '提醒',
                content: '您的账号已被禁用，如需启用请联系管理员!',
                okText: '确定',
                onOk() {
                  const URL = goBack5i5yaHome();
                  window.location.replace(URL);
                  return false;
                }
              });
              return false;
            }
            if (resAll.code == 200) {
              setOrganizationInfo(resAll.content.organizationInfo);  // 保存所属机构信息
              localStorage.setItem('organizationInfoId', resAll.content.organizationInfo && resAll.content.organizationInfo.id); // 保存机构Id
              localStorage.setItem('organizationSize', resAll.content.organizationSize); // 用户下有多少机构
              const responseOrgDeployInfoList = yield call(orgDeployInfoList, payload);
              if(responseOrgDeployInfoList && responseOrgDeployInfoList.code == 200) {
                const { chargeModel,waitOvertime } = responseOrgDeployInfoList.content || {}
                setOrganizationInfo({
                  ...resAll.content.organizationInfo,
                  chargeModel,  // 保存当前机构的收费模式
                  waitOvertime, // 保存当前机构的等待超时时间
                });  // 保存所属机构信息
              }
              // 拿到每个权限的id通过 item.platformId
              let scope = [];

              resAll.content.menuInfoList && resAll.content.menuInfoList.map((item) => {
                scope.push(item.platformId)
              })

              setAuthority(scope);
              // 角色
              let role = [];
              resAll.content.userRoleList && resAll.content.userRoleList.map(item => {

                role.push({
                  roleCode: item.roleCode,
                  roleName: item.roleName,
                  tenantId: item.tenantId,
                  roleId: item.roleId
                })
              })

              // 登录呼叫中心
              // moorCall.moortools.m7BeginLogon('8000@ruier', '8000', 'Local', '0');
              localStorage.setItem('scope', JSON.stringify(scope)) // 权限
              localStorage.setItem('role', JSON.stringify(role)) // 多角色
              localStorage.setItem('doctorIdentification', resAll.content.doctorIdentification)  // 1医生  2非医生
              localStorage.setItem("accountNumber", resAll.content.accountNumber); // 账号
              localStorage.setItem("id", resAll.content.id); // 用户id
              localStorage.setItem("userTenantId", response.content.tenantId); // 登录用户的租户Id（登录第三方400时用的）
              localStorage.setItem("tenantId", resAll.content.tenantId); // 租户id
              localStorage.setItem("phone", resAll.content.phone); // 用户手机号
              localStorage.setItem("email", resAll.content.email); // 用户邮箱
              localStorage.setItem("avatar", resAll.content.headUrlView); // 用户头像
              localStorage.setItem("userName", resAll.content.userName); // 用户名字
              localStorage.setItem("isResources", resAll.content.isResources); // 是否是资源  可以进入其他设置  1是
              localStorage.setItem("fourCustomer", resAll.content.fourCustomer); // 是否是400客服
              localStorage.setItem('tenantName', resAll.content.tenantName);     // 品牌名称
              localStorage.setItem('isRead', resAll.content.isRead);     // 品牌名称
              localStorage.setItem('isNeedSignRecord', resAll.content.organizationInfo && resAll.content.organizationInfo.isNeedSignRecord);     // 判断到诊图例小程序预约患者图例是否显示  0需要 1不需要
              localStorage.setItem('fourOnState', resAll.content.fourOnState);    //   400列表开启状态(0：关闭，1：开启)
              localStorage.setItem('procurementAuth',resAll.content.procurementAuth);    // 登录后的授权信息

              if (resAll.content.fourCustomer == 1) {
                if (scope && scope.length > 0) {
                  let path = Math.min.apply(Math, scope);
                  switch (path) {
                    case 76:
                      routerRedux.replace("/customerService/appointment");
                      break;
                    case 77:
                      routerRedux.replace("/customerService/patient");
                      break;
                    case 78:
                      routerRedux.replace("/customerService/set");
                      break;
                    case 79:
                      routerRedux.replace("/customerService/welfare");
                      break;
                    case 108:
                      routerRedux.replace("/customerService/complaint/list");
                      break;
                    case 120:
                      routerRedux.replace("/customerService/searchChargeItem");
                      break;
                    default:
                      break;
                  }
                  return true
                } else {
                  Modal.error({
                    title: '暂无权限',
                    content: '该用户暂无权限，请联系400管理员!',
                    okText: "确定"
                  });
                  return false;
                }

              } else {
                return true;
              }
            }
          } else {
            sessionStorage.setItem('email', res.content.loginInfo.email);
            sessionStorage.setItem('tenantCode', res.content.loginInfo.tenantCode);
            yield put(routerRedux.replace({
              pathname: '/userinfo/information',
              params: {
                tenantCode: res.content.loginInfo.tenantCode,
                email: res.content.loginInfo.email,
              }
            })); // 去完善信息页面
          }
        } else {

          const URL = goBack5i5yaHome();
          window.location.replace(URL);
        }
      } else {

        const URL = goBack5i5yaHome();
        window.location.replace(URL);
        //   获取用户信息失败，返回原来登录页
      }
    },

    //  市场化返回首页
    *goBcakHome() {
      window.location.replace(`/market`);
    },
  },

  reducers: {
    changeLayoutCollapsed(state, { payload }) {
      return {
        ...state,
        collapsed: payload,
      };
    },
    changeLayoutMenuCollapsed(state, { payload }) {
      return {
        ...state,
        menuCollapsed: payload,
      };
    },
    saveNotices(state, { payload }) {
      return {
        ...state,
        notices: payload,
      };
    },
    saveClearedNotices(state, { payload }) {
      return {
        ...state,
        notices: state.notices.filter(item => item.type !== payload),
      };
    },
    saveTenantId(state, { payload }) {
      return {
        ...state,
        tenantId: payload
      };
    },
    saveTenantName(state, { payload }) {
      return {
        ...state,
        tenantName: payload
      };
    },
    // 更新状态值数据
    setStatus(state, { payload }) {
      return {
        ...state,
        ...payload
      };
    },
    // 更新预付款等状态
    setPayStatus(state, { payload }) {
      return {
        ...state,
        ...payload
      };
    },

    // 是否展开前往佳沃思采购系统欢迎弹窗的状态
    setVisibleByModalByGoJwsmed(state, { payload }) {
      return {
        ...state,
        ...payload
      };
    },

    // 是否展开耗材商城弹窗
    setVisibleByModalByConsumablesMall(state, { payload }) {
      return {
        ...state,
        ...payload
      };
    }
  },

  subscriptions: {
    setup({ dispatch, history }) {
      const tenantId = getPageQuery().tenantId || localStorage.getItem('tenantId');
      const UidKey = getPageQuery().UidKey || localStorage.getItem('UidKey');
      const UrlName = ['/market', '/market/home', '/market/CloudClinic', '/marketPlan'];
      // 非市场化首页与登录页面  没有 tenantId 和Uidkey返回登录
      if (UrlName.indexOf(window.location.pathname) == -1) {
        if (tenantId == null || UidKey == null) {
          const URL = goBack5i5yaHome();
          window.location.replace(URL);
        }
      }
    },
  },
};
