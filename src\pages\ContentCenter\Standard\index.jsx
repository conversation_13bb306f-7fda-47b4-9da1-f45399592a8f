import React, { Component } from 'react';
import { GridContent } from '@ant-design/pro-layout';
import Search from 'antd/es/input/Search';
import {Button,Modal, Form, Input, Popconfirm, Radio, Space, Table, Menu, Dropdown, message, Pagination} from 'antd';
import siftImg from '@/assets/FilterState.png';//引入图标
import styles from './style.less';//引入样式
import { color } from 'mockjs/src/mock/random/color';
import { connect } from 'dva';
import {StringUtils} from "@/utils/StringUtils";//公共验证
const { TextArea } = Input;
class Standard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      mainSuitStatus: false,//新增/编辑诊断状态
      loading:false,//表格更改数据的loading
      StandList:[],//诊断列表
      StandMsg:{
        "tenantId": "",//平台标识
        "diagCode": "",//编码
        "stdCode": "",//编码
        "diagName": "",//中文名称
        "id": "",
        "status": "",//状态 字典表 状态（0正常 1停用）
        "diagNameEn":"",//英文名称
        "pinyinCode":""//拼音码
      },//诊断列表详情
      params:{
        tenantId:localStorage.getItem('tenantId'),//租户标识
        diagName:"",//中文名称
        oneselfBuild:"",//是否只显示自建诊断 0是显示
        pageNum:1,//分页页码
        pageSize:10,//分页大小
        status:"",//状态
        className:"",//类型
      },
      totals:1,//诊断列表数量
      diagCodeStatus:"",//判断是新增还是编辑的标准
      menus:[],//下拉框筛选列表内容
      editorLoading:false,//新增/编辑诊断按钮loading
      clickoneStatus:false,//筛选类型
      clicktwoStatus:false//筛选状态
    };
  }
  componentDidMount() {
    this.getStandList() //获取诊断列表
    this.getTypeList()//获取类型列表
  }
  //获取诊断列表
  getStandList=()=>{
    const {params}=this.state;
    this.setState({
      loading:true
    })
    const {dispatch} = this.props
    if (dispatch) {
      dispatch({
        type: 'standard/findDiagsService',
        payload: params,
        callback: (res) => {
          if (res.code == 200) {
            this.setState({
              totals:res.total,
              StandList:res.rows,
              loading:false
            })
          }else{
            this.setState({
              loading:false
            })
          }
        }
      });
    }
  }
  //获取类型列表
  getTypeList=()=>{
    const {dispatch} = this.props
    let param={
      tenantId:localStorage.getItem('tenantId')
    }
    if (dispatch) {
      dispatch({
        type: 'standard/fetchFindDiag',
        payload: param,
        callback: (res) => {
          if (res.code == 200) {
            let arr={"total":4,"rows":["ICD-9","ICD-10","ICD-11","自建"],"code":200,"msg":null}
            let arrs=[];
            arr.rows.forEach((key,index)=>{
              arrs.push({
                key:key,
                label:key,
              })
            })
            arrs.unshift({
              key:"",
              label:"不限",
            })
            this.setState({
              menus:arrs
            })
          }
        }
      });
    }
  }
  //分页数据
  pageNumberOnChange = (pageNum,pageSize) => {
    this.state.params.pageNum=pageNum;
    this.state.params.pageSize=pageSize;
    this.getStandList()
  };
  //搜索
  onSearch = (value) => {
    this.state.params.diagName=StringUtils.trimToEmpty(value);
    this.state.params.pageNum=1;
    this.getStandList()
  };

  // 打开新增编辑弹窗
  openAdd = (record) => {
    this.setState({
      StandMsg:{},
      diagCodeStatus:""
    })
    if(record.id){
      const {dispatch} = this.props
      let param={
        tenantId: localStorage.getItem('tenantId'),//平台标识
        id: record.id,//主键
      }
      if (dispatch) {
        dispatch({
          type: 'standard/fetchDiagInfo',
          payload: param,
          callback: (res) => {
            if (res.code == 200) {
              this.setState({
                StandMsg:res.content,
                mainSuitStatus: true,
                diagCodeStatus:record.id,
              })
            }
          }
        });
      }
    }else{
      this.setState({
        mainSuitStatus: true,
      })
    }
  };

  // 新增编辑保存
  handlemainSuit = () => {
    const {diagCodeStatus,StandMsg} =this.state;
    const { dispatch } = this.props;
    let params;
    if(diagCodeStatus){
      params = {
        tenantId: localStorage.getItem('tenantId'),//平台标识
        diagCode: StandMsg.diagCode,//编码
        stdCode: StandMsg.stdCode,//编码
        diagName: StandMsg.diagName,//中文名称
        id: StandMsg.id,//词条标识
        status: StandMsg.status,//状态：0正常，1停用,
        updateName:localStorage.getItem('userName'),//更新者
        updateId:localStorage.getItem('userId'),//修改人id,
        diagNameEn:StandMsg.diagNameEn,//英文名称
        pinyinCode:StandMsg.pinyinCode//拼音码
      };
    }else{
      params = {
        tenantId: localStorage.getItem('tenantId'),//平台标识
        diagCode: StandMsg.diagCode,//编码
        stdCode: StandMsg.stdCode,//编码
        diagName: StandMsg.diagName,//中文名称
        status: StandMsg.status,//状态：0正常，1停用,
        createName:localStorage.getItem('userName'),//创建者
        createId:localStorage.getItem('userId'),//创建人id
        diagNameEn:StandMsg.diagNameEn,//英文名称
        pinyinCode:StandMsg.pinyinCode//拼音码
      };
    }
    if(!params.stdCode){
      message.warning({
        content: '请输入编码',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if(params.stdCode.length>32){
      message.warning({
        content: '编码最大长度为32',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if(!params.diagName){
      message.warning({
        content: '请输入中文名称',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if(params.diagName.length>100){
      message.warning({
        content: '中文名称超长，最大长度100',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if(params.diagNameEn&&params.diagNameEn.length>200){
      message.warning({
        content: '英文名称超长，最大长度200',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    if(params.pinyinCode&&params.pinyinCode.length>50){
      message.warning({
        content: '拼音码超长，最大长度50',
        className: 'custom-class',
        style: {
          marginTop: '20vh',
        },
      });
      return;
    }
    this.setState({
      editorLoading:true
    })
    if (dispatch) {
      dispatch({
        type: 'standard/fetchSaveDiag',
        payload: params,
        callback: (res) => {
          if (res.code === 200) {
            message.success({
              content: '操作成功',
            });
            this.setState({
              mainSuitStatus: false,
              editorLoading:false
            });
            this.getStandList()
          } else {
            message.error({
              content: '操作失败',
            });
            this.setState({
              editorLoading:false
            })
          }
        },
      });
    }
  };
  // 新增编辑取消
  handlemainSuitCancel = () => {
    this.setState({
      editorLoading:false,
      mainSuitStatus: false,
    });
  };

  //确定删除
  confirm = (record) => {
    const { dispatch } = this.props;
    let params = {
      id:record.id,
      tenantId: localStorage.getItem('tenantId'),//平台标识
    };
    if (dispatch) {
      dispatch({
        type: 'standard/fetchDeleteDiag',
        payload: params,
        callback: (res) => {
          if (res.code === 200) {
            message.success({
              content: '删除成功',
            });
            this.getStandList()
          } else {
            message.error({
              content: '删除失败',
            });
          }
        },
      });
    }
  };
  //取消删除
  cancel = () => {
  };
  //点击类型筛选条件
  menuItemClick=(e)=>{
    this.state.params.status=e.key;
    this.state.params.pageNum=1;
    this.setState({
      clicktwoStatus:true
    })
    this.getStandList()
  }
  //点击状态筛选条件
  menusItemClick=(e)=>{
    this.state.params.className=e.key;
    this.state.params.pageNum=1;
    this.setState({
      clickoneStatus:true
    })
    this.getStandList()
  }
  render() {
    const { mainSuitStatus,StandList,loading,StandMsg,totals,params ,editorLoading,clickoneStatus,clicktwoStatus} = this.state;
    const column = [
      {
        title: '类型',
        dataIndex: 'className',
        key: 'className',
        render: (text, record) => {
          let span = '';
          if (record.status === 0) {
            span = <span>{record.className}</span>;
          } else if (record.status === 1) {
            span = <span style={{ color: 'rgba(0, 0, 0, 0.45)' }}>{record.className}</span>;
          }
          return <span>{span}</span>;
        },
      },
      {
        title: '编码',
        dataIndex: 'stdCode',
        key: 'stdCode',
        render: (text, record) => {
          let span = '';
          if (record.status === 0) {
            span = <span>{record.stdCode}</span>;
          } else if (record.status === 1) {
            span = <span style={{ color: 'rgba(0, 0, 0, 0.45)' }}>{record.stdCode}</span>;
          }
          return <span>{span}</span>;
        },
      },
      {
        title: '中文名称',
        dataIndex: 'name',
        width:'20%',
        key: 'name',
        render: (text, record) => {
          let span = '';
          if (record.status === 0) {
            span = <span>{record.diagName}</span>;
          } else if (record.status === 1) {
            span = <span style={{ color: 'rgba(0, 0, 0, 0.45)' }}>{record.diagName}</span>;
          }
          return <span>{span}</span>;
        },
      },
      {
        title: '英文名称',
        width:'20%',
        dataIndex: 'name',
        key: 'name',
        render: (text, record) => {
          let span = '';
          if (record.status === 0) {
            span = <span>{record.diagNameEn}</span>;
          } else if (record.status === 1) {
            span = <span style={{ color: 'rgba(0, 0, 0, 0.45)' }}>{record.diagNameEn}</span>;
          }
          return <span>{span}</span>;
        },
      },
      {
        title: '拼音码',
        dataIndex: 'name',
        width:'100px',
        key: 'name',
        render: (text, record) => {
          let span = '';
          if (record.status === 0) {
            span = <span>{record.pinyinCode}</span>;
          } else if (record.status === 1) {
            span = <span style={{ color: 'rgba(0, 0, 0, 0.45)' }}>{record.pinyinCode}</span>;
          }
          return <span>{span}</span>;
        },
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text, record) => {
          let span = '';
          if (record.status === 0) {
            span = <span style={{ color: '#00B42A' }}>使用中</span>;
          } else if (record.status === 1) {
            span = <span style={{ color: 'rgba(0,0,0,0.45)' }}>已停用</span>;
          }
          return <span>{span}</span>;
        },
      },
      {
        title: '操作',
        width: '15%',
        key: 'action',
        dataIndex: 'action',
        render: (text, record) => (
          <Space size="middle">
            <a
              onClick={() => {
                this.openAdd(record);
              }}
            >
              编辑
            </a>
            <Popconfirm
              title="确定删除?"
              onConfirm={()=>this.confirm(record)}
              onCancel={()=>this.cancel}
              okText="是"
              cancelText="否"
            >
              <a>删除</a>
            </Popconfirm>
          </Space>
        ),
      },
    ];
    const menu = (
      <Menu
        onClick={e=>this.menuItemClick(e)}
        items={[
          {
            key: '',
            label: <a>不限</a>,
          },
          {
            key: '0',
            label: <a>使用中</a>,
          },
          {
            key: '1',
            label: <a>已停用</a>,
          },
        ]}
      />
    );
    const menus = (
      <Menu
        onClick={e=>this.menusItemClick(e)}
        items={this.state.menus}
      />
    );
    return (
      <GridContent>
        <div className={styles.titleBox}>
          <Search
            placeholder="搜索"
            onSearch={this.onSearch}
            style={{ width: '230px', height: '32px' }}
          />
          <div style={{ float: 'right' }}>
            <Dropdown overlay={menus} placement="bottom">
              <span className={styles.txt11}>
                <img src={siftImg} alt="" className={styles.siftImg} />
                {clickoneStatus && params.className==""?'不限':params.className==""?'筛选类型':params.className}
              </span>
            </Dropdown>
            <Dropdown overlay={menu}  placement="bottom">
              <span className={styles.txt11}>
                <img src={siftImg} alt="" className={styles.siftImg} />
                {clicktwoStatus && params.status==""?'不限':params.status==""?'筛选状态':params.status==0?'使用中':params.status==1?'已停用':""}
              </span>
            </Dropdown>
            <Button
              type="primary"
              style={{ marginLeft: '24px' }}
              onClick={this.openAdd}
            >
              新增
            </Button>
          </div>
        </div>
        <Table
          rowKey={StandList => StandList.id}
          columns={column}
          dataSource={StandList}
          loading={loading}
          pagination={false}
          style={{ margin: '16px' }} />
        <Pagination
          style={{ float: 'right' }}
          total={totals}
          showTotal={(totals) => `共 ${totals} 条记录`}
          defaultPageSize={params.pageSize}
          defaultCurrent={params.pageNum}
          onChange={(pageNum,pageSize) => this.pageNumberOnChange(pageNum,pageSize)}
        />
        <Modal
          title="新增/编辑诊断"
          visible={mainSuitStatus}
          destroyOnClose={true}
          onOk={this.handlemainSuit}
          onCancel={this.handlemainSuitCancel}
          okText="保存"
          cancelText="取消"
          width={600}
          maskClosable={false}
          confirmLoading={editorLoading}
        >
          <Form
            name="basic"
            labelCol={{
              span: 4,
            }}
            wrapperCol={{
              span: 20,
            }}
            initialValues={{
              remember: true,
              diagCode:StandMsg.diagCode,
              stdCode:StandMsg.stdCode,
              diagName:StandMsg.diagName,
              diagNameEn:StandMsg.diagNameEn,
              pinyinCode:StandMsg.pinyinCode,
              status:StandMsg.status?StandMsg.status:0,
            }}
            onFinish={this.onFinish}
            autoComplete="off"
          >
            <Form.Item
              label="编码"
              name="stdCode"
              rules={[
                {
                  required: true,
                  message: '请输入编码'
                },{
                  max:32,
                  message: '最多可输入32个文字'
                }
              ]}
            >
              <TextArea
                autoSize={{minRows:1,maxRows:10}}
                onChange={e=>{StandMsg.stdCode=e.target.value}}
                placeholder="请输入编码" />
            </Form.Item>
            <Form.Item
              label="中文名称"
              name="diagName"
              rules={[
                {
                  required: true,
                  message: '请输入中文名称'
                },{
                  max:100,
                  message: '最多可输入100个文字'
                }
              ]}
            >
              <TextArea
                autoSize={{minRows:1,maxRows:10}}
                onChange={e=>{StandMsg.diagName=e.target.value}}
                placeholder="请输入中文名称" />
            </Form.Item>
            <Form.Item
              label="英文名称"
              name="diagNameEn"
              rules={[
                {
                  max:200,
                  message: '最多可输入200个文字'
                }
              ]}
            >
              <TextArea
                autoSize={{minRows:1,maxRows:10}}
                onChange={e=>{StandMsg.diagNameEn=e.target.value}}
                placeholder="请输入英文名称" />
            </Form.Item>
            <Form.Item
              label="拼音码"
              name="pinyinCode"
              rules={[
                {
                  max:50,
                  message: '最多可输入50个文字'
                }
              ]}
            >
              <TextArea
                autoSize={{minRows:1,maxRows:10}}
                onChange={e=>{StandMsg.pinyinCode=e.target.value}}
                placeholder = "请输入拼音码" />
            </Form.Item>
            <Form.Item label="状态" name="status">
              <Radio.Group onChange={e=>{StandMsg.status=e.target.value}}>
                <Radio value={0}>启用</Radio>
                <Radio value={1}>停用</Radio>
              </Radio.Group>
            </Form.Item>
          </Form>
        </Modal>
      </GridContent>
    );
  }
}

export default connect(() => ({}))(Standard);
