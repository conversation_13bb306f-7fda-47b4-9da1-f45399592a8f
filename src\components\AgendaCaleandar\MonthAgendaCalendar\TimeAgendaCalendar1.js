import React, { Component } from 'react';
import {Card,Radio,message} from 'antd';
import $ from 'jquery';
import moment from 'moment';
import 'fullcalendar';
import 'fullcalendar-scheduler';
import 'fullcalendar/dist/locale/zh-cn';
/*import 'fullcalendar/dist/fullcalendar.css';*/
import classNames from 'classnames';
import styles from './TimeAgendaCalendar.less'
import stylesHeader from './TimeCalendarHeader.less'
import commonStyles from '../AgendaCaleander.less'
import PropTypes from 'prop-types';
import Ellipsis from '@/components/Ellipsis';
import chairIcon from '@/assets/registerAndArrival/chair.png'
import doctorIcon  from '@/assets/registerAndArrival/icon_doctor.png'

const workStatusForDayOff =  2 // 休息日
const workStatusForOther  =  3 // 走诊医生
const workStatusForNot    =  4 // 未排班医生

const rowTotal = 7              // 列表项总列数
const lableClickClassName = 'labelTh' // 表头时间行点击className
import {
  getChairNum,
  newCustomerEventType,
  oldCustomerEventType,
  otherEventType,
  backgroundType,
  LarageHeight,
  MediumHeight,
  SmallHeight,
  tittleHight,
  slotLabelFormat,
  schedulerLicenseKey,
  eventPatientNameEllipsis // 患者名称添加省略号
} from '@/utils/CalendarUtils'


/**
 * 区分使用 拆分 表格头部组件 和 表格列组件
 */
export default class TimeCalendarHeader extends Component {

  static propTypes = {
    calendarTableList:PropTypes.any,
    goNext:PropTypes.func,          // 下一页
    goLast:PropTypes.func,          // 上一页
    onClickTitleForDoctor:PropTypes.func,
    onClickTitleForData:PropTypes.func
  }

  static defaultProps = {
    calendarTableList:null,
    goNext:()=>{},
    goLast:()=>{},
    onClickTitleForDoctor:()=>{},
    onClickTitleForData:()=>{},
  }

  /**
   * 渲染列表方法
   * @returns {XML}
   */
  applyCalendarHeader=(props)=>{
    this.setState({
      calendarTableList:props.calendarTableList,
      resources:this.getResources(props.calendarTableList)
    })
  }

  /**
   * 获取resources列表项目数据
   * 遍历列表结构的同时添加椅位资源列事件
   * @returns {Array}
   */
  getResources=(calendarTableList)=>{
    let resources = [];                         // 椅位资源列
    let groupTotal = calendarTableList.length;  // 医生总数
    let EventListPlue = [];
    calendarTableList.forEach((val,idx)=>{
      if(val.resources){
        val.resources.forEach((res,resIdx)=>{
          const { staffStatus,chairTotal, type, workStatus, workTimeEnd, workTimeStart, date, doctorId, groupId, name, organizationName,
          } = val || {}
          //id: 4, chairNum: 2, title: "椅位2"
          resources.push({
            ...res,chairTotal,workStatus, workTimeEnd, workTimeStart, staffStatus, type, date, doctorId, groupId, name, organizationName,
            resLength:val.resources.length
          });
        })
      }
    })
    return resources;
  }

  /**
   * 上一页
   */
  goToLast=()=>{
    this.props.goLast(); // 调用前往上一页方法
  }

  /**
   * 下一页
   */
  goToNext=()=>{
    this.props.goNext()
  }

  /**
   * 点击表格头的医生
   */
  onClickTitleForDoctor=(res)=>{
    this.props.onClickTitleForDoctor(res)
    // 原逻辑医生与客服有不同相应权限，后调整为拥有同样操作权限
    // const doctorIdentificationState = localStorage.getItem('doctorIdentification') == 1;
    // if (doctorIdentificationState) { //   医生登录
    //   this.props.onClickTitleForDoctor(res)
    //   if (res.operationType == 1) {
    //     this.props.onClickTitleForDoctor(res)
    //   } else {
    //     message.warning('当前医生无操作权限', 1)
    //   }
    // }else { // 客服登录
    //   this.props.onClickTitleForDoctor(res)
    // }
  }

  /**
   * 点击表格头的日期
   */
  onClickTitleForData=(res)=>{
    this.props.onClickTitleForData(res)
  }


  /**
   * ------------------生命周期---------------
   * @param props
   * <i id="goDisableLastIcon" class="goDisableLastIcon"></i>
   * <i id="goNextIcon" class="goNextIcon"></i>
   */

  constructor(props){
    super(props);
    this.state = {
      resources:[],         // 全部椅位列表
      calendarTableList:[]  // 医生和椅位列表
    }
  }

  render(){
    const {current, pageSize, total, pageCount } = this.props.appointmentPageInfo

    return (
      <div className={stylesHeader.HeaderTableWeap}>
        <table>
          <thead styles={{border: 1}}>
          <tr className="DoctorNameTr">
            {/*fc-axis fc-widget-header*/}
            <th className={classNames({
              'fc-axis fc-widget-header':true,
              [stylesHeader.doctorIconTh]:true,
            })} style={{width: 68, textAlign: 'center',paddingLeft:'0px'}}>
              <img src={doctorIcon}  className={stylesHeader.doctorIcon}/>
            </th>
            {
              this.state.calendarTableList && this.state.calendarTableList.map((res,idx)=>{

                let conSpanNum = res.resources.length;
                let workStatusText = res.workStatus ==  workStatusForDayOff ?  '(休息)' : ''
                workStatusText = res.workStatus ==  workStatusForOther?  `(${res.organizationName})` : workStatusText
                workStatusText = res.workStatus ==  workStatusForNot ?  `(未排班)` : workStatusText

                if (res.groupId != 'suplus') {
                  if (res.name && this.props.AgendaHeadType == 1) {
                    return (<th key={'AgendaTitile' + idx} className={idx} onClick={() => {
                      this.onClickTitleForDoctor(res)
                    }
                    } colSpan={conSpanNum}>
                      <div className={stylesHeader.AgendaHeadName}>
                        {res.name}{workStatusText}
                      </div>
                    </th>)
                  } else {
                    let titleDate = moment(res.date, "YYYY-MM-DD").format('YYYY-MM-DD')
                    let titleWeek = moment(res.date, "YYYY-MM-DD").format('dddd')
                    //let dateClickStyle = dateClickStatus ? 'cursor: pointer' : ''
                    return (
                      <th key={'head' + idx} onClick={() => {
                        this.onClickTitleForData(res)
                      }} colSpan={conSpanNum} className={lableClickClassName} date={titleDate}>
                        <div className={stylesHeader.lableTitleHead}>
                          <p className={stylesHeader.labelTdsTitle}>{titleDate}</p>
                          <p className={stylesHeader.labelTdsTitle}>{titleWeek}</p>
                          <p className={stylesHeader.labelTdsTitle}>{workStatusText}</p>
                        </div>
                      </th>
                    )
                  }
                  // 不足当前页数条数 补充资源头
                }else {
                  return (
                    <th key={'head' + idx} colSpan={conSpanNum} className={classNames({
                      [lableClickClassName]:true,
                      [stylesHeader.suplusHeader]:true,
                    })}>
                      <div className={stylesHeader.lableTitleHead}></div>
                    </th>
                  )
                }
              })
            }
          </tr>
          <tr>
            <th className="fc-axis fc-widget-header" style={{width: 68, textAlign: 'center'}}>
              <img src={chairIcon}  className={stylesHeader.chairIcon}/>
            </th>
            {
              this.state.resources && this.state.resources.map((res,idx)=>{
                console.log('fc-resource-cell :: ',res);
                if (res.groupId != 'suplus') {
                  return (
                    <th key={'cell' + idx} className="fc-resource-cell" data-resource-id={res.id}>
                      <div className={styles.chairIconWarp}><i className={styles.chairIcon}></i></div>
                      {res.resLength == 1 ? <span className={styles.chiarText}>( <span>{res.resLength}</span> )</span> :
                        <span className={styles.chiarText}>( <span className={styles.redChiarNum}>{res.chairNum}</span> <span>/</span> <span>{res.resLength}</span> )</span>}
                      {/*{res.title}*/}
                    </th>
                  )
                }else {
                  //fc-resource-cell
                  return (
                    <th key={'cell' + idx} className={classNames({
                      'fc-resource-cell':true,
                      [stylesHeader.suplusHeader]:true
                    })}>

                    </th>
                  )
                }
              })
            }
          </tr>
          </thead>
        </table>
        { current > 1 && <div onClick={this.goToLast} className={stylesHeader.goToLast}> <i id="goDisableLastIcon" className="goDisableLastIcon"></i></div>}
        { current < pageCount && <div onClick={this.goToNext} className={stylesHeader.goToNext}> <i id="goNextIcon" className="goNextIcon"></i></div>}
      </div>
    )
  }

  componentWillReceiveProps(nextProps){
    this.applyCalendarHeader(nextProps)
  }

  componentDidMount() {
    this.applyCalendarHeader(this.props)
  }
}


/**
 * 时间制 预约组件
 * 全屏或半屏 使用时间制度 预约组件

 export default class TimeAgendaCalendar extends Component {

} */
