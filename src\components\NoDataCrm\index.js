import React,{ Component } from 'react';
import Nodata from '@/assets/nodata.png';

export default class NoDataCrm extends Component {
  static defaultProps = {
    paddingStyle: '100px 0',
    contentText: '暂无数据'
  };
  constructor(props){
    super(props);
    this.state = {
      paddingStyle: this.props.paddingStyle,
      contentText: this.props.contentText
    };
  }
  render() {
    const { paddingStyle, contentText } = this.state
    return (
      <div style={{textAlign: 'center', padding: paddingStyle}}>
        <img src={Nodata} alt='' style={{width: 48,height: 48,}} />
        <p style={{color: 'rgba(0, 0, 0, 0.45)', marginTop: 12}}>{contentText}</p>
      </div>
    )
  }
}
